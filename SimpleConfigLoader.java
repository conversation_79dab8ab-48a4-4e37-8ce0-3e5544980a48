import java.util.*;

/**
 * 简化的配置加载器
 * 提供预定义的配置模板，支持通过配置名称快速切换
 */
public class SimpleConfigLoader {
    
    /**
     * 根据配置名称加载预定义配置
     */
    public static DynamicStrategyConfig loadConfig(String configName) {
        switch (configName.toLowerCase()) {
            case "ab":
            case "strategy-config.json":
                return createABConfig();
            case "abc":
            case "strategy-config-abc.json":
                return createABCConfig();
            case "abcd":
            case "strategy-config-abcd.json":
                return createABCDConfig();
            case "complex":
                return createComplexConfig();
            default:
                System.out.println("未知配置名称，使用默认AB配置");
                return createABConfig();
        }
    }
    
    /**
     * AB双策略配置
     */
    private static DynamicStrategyConfig createABConfig() {
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("A", new StrategyInfo(1, 150));
        strategies.put("B", new StrategyInfo(3, 150));
        
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("A", 0.1);
        targetWeights.put("B", 0.9);
        
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("A", 0.9);
        emergencyWeights.put("B", 0.1);
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("高负载保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31854", "AB_TEST | *", true, "RTP",
            "ab_strategy_test_v1_0_0", 1000, 0.9, 0.1, 50,
            strategies, targetWeights, emergencyConfig
        );
    }
    
    /**
     * ABC三策略配置
     */
    private static DynamicStrategyConfig createABCConfig() {
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("A", new StrategyInfo(1, 100));
        strategies.put("B", new StrategyInfo(3, 150));
        strategies.put("C", new StrategyInfo(5, 200));
        
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("A", 0.2);
        targetWeights.put("B", 0.3);
        targetWeights.put("C", 0.5);
        
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("A", 0.8);
        emergencyWeights.put("B", 0.15);
        emergencyWeights.put("C", 0.05);
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("系统过载保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31855", "ABC_TEST | *", true, "RTP",
            "abc_strategy_test_v1_0_0", 1200, 0.85, 0.05, 60,
            strategies, targetWeights, emergencyConfig
        );
    }
    
    /**
     * ABCD四策略配置
     */
    private static DynamicStrategyConfig createABCDConfig() {
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("A", new StrategyInfo(1, 80));
        strategies.put("B", new StrategyInfo(2, 120));
        strategies.put("C", new StrategyInfo(4, 180));
        strategies.put("D", new StrategyInfo(6, 250));
        
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("A", 0.15);
        targetWeights.put("B", 0.25);
        targetWeights.put("C", 0.35);
        targetWeights.put("D", 0.25);
        
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("A", 0.7);
        emergencyWeights.put("B", 0.2);
        emergencyWeights.put("C", 0.08);
        emergencyWeights.put("D", 0.02);
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("极限负载保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31856", "ABCD_COMPLEX | *", true, "RTP",
            "abcd_strategy_test_v2_0_0", 1500, 0.8, 0.08, 80,
            strategies, targetWeights, emergencyConfig
        );
    }
    
    /**
     * 复杂多策略配置（5个策略）
     */
    private static DynamicStrategyConfig createComplexConfig() {
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("Fast", new StrategyInfo(1, 50));      // 超快策略
        strategies.put("Light", new StrategyInfo(2, 100));    // 轻量策略
        strategies.put("Standard", new StrategyInfo(3, 150)); // 标准策略
        strategies.put("Heavy", new StrategyInfo(5, 200));    // 重量策略
        strategies.put("Ultra", new StrategyInfo(8, 300));    // 超重策略
        
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("Fast", 0.1);
        targetWeights.put("Light", 0.2);
        targetWeights.put("Standard", 0.3);
        targetWeights.put("Heavy", 0.3);
        targetWeights.put("Ultra", 0.1);
        
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("Fast", 0.6);
        emergencyWeights.put("Light", 0.25);
        emergencyWeights.put("Standard", 0.1);
        emergencyWeights.put("Heavy", 0.04);
        emergencyWeights.put("Ultra", 0.01);
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("复杂场景保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31857", "COMPLEX_MULTI | *", true, "RTP",
            "complex_multi_strategy_v3_0_0", 2000, 0.75, 0.06, 100,
            strategies, targetWeights, emergencyConfig
        );
    }
    
    /**
     * 显示所有可用配置
     */
    public static void showAvailableConfigs() {
        System.out.println("可用配置:");
        System.out.println("  ab     - AB双策略配置 (A:轻量级, B:中等资源消耗)");
        System.out.println("  abc    - ABC三策略配置 (A:轻量级, B:中等, C:重量级)");
        System.out.println("  abcd   - ABCD四策略配置 (A:超轻量, B:轻量, C:中重量, D:重量级)");
        System.out.println("  complex- 复杂五策略配置 (Fast, Light, Standard, Heavy, Ultra)");
    }
    
    /**
     * 验证配置
     */
    public static void validateConfig(DynamicStrategyConfig config) {
        System.out.println("✅ 配置验证:");
        System.out.println("  场景ID: " + config.getScenId());
        System.out.println("  策略数量: " + config.getStrategies().size());
        System.out.println("  策略列表: " + config.getStrategies().keySet());
        
        // 验证权重总和
        double totalWeight = config.getTargetWeights().values().stream().mapToDouble(Double::doubleValue).sum();
        System.out.println("  目标权重总和: " + String.format("%.3f", totalWeight));
        
        double emergencyTotalWeight = config.getEmergencyConfig().getWeights().values().stream().mapToDouble(Double::doubleValue).sum();
        System.out.println("  紧急权重总和: " + String.format("%.3f", emergencyTotalWeight));
        
        // 计算目标权重倍数
        double targetMultiplier = 0.0;
        for (Map.Entry<String, Double> entry : config.getTargetWeights().entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            targetMultiplier += weight * batchNum;
        }
        System.out.println("  目标权重倍数: " + String.format("%.2f", targetMultiplier));
        
        // 计算紧急权重倍数
        double emergencyMultiplier = 0.0;
        for (Map.Entry<String, Double> entry : config.getEmergencyConfig().getWeights().entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            emergencyMultiplier += weight * batchNum;
        }
        System.out.println("  紧急权重倍数: " + String.format("%.2f", emergencyMultiplier));
        
        System.out.println("  配置验证完成 ✅");
    }
}
