import java.util.Map;

/**
 * 调整步骤类
 */
public class AdjustmentStep {
    private String stepDescription;
    private Map<String, Double> weights;
    private double multiplier;
    private double expectedQps;
    private boolean feasible;

    public AdjustmentStep() {}

    public AdjustmentStep(String stepDescription, Map<String, Double> weights, 
                         double multiplier, double expectedQps, boolean feasible) {
        this.stepDescription = stepDescription;
        this.weights = weights;
        this.multiplier = multiplier;
        this.expectedQps = expectedQps;
        this.feasible = feasible;
    }

    public String getStepDescription() {
        return stepDescription;
    }

    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }

    public Map<String, Double> getWeights() {
        return weights;
    }

    public void setWeights(Map<String, Double> weights) {
        this.weights = weights;
    }

    public double getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(double multiplier) {
        this.multiplier = multiplier;
    }

    public double getExpectedQps() {
        return expectedQps;
    }

    public void setExpectedQps(double expectedQps) {
        this.expectedQps = expectedQps;
    }

    public boolean isFeasible() {
        return feasible;
    }

    public void setFeasible(boolean feasible) {
        this.feasible = feasible;
    }

    @Override
    public String toString() {
        return "AdjustmentStep{" +
                "stepDescription='" + stepDescription + '\'' +
                ", weights=" + weights +
                ", multiplier=" + String.format("%.2f", multiplier) +
                ", expectedQps=" + String.format("%.0f", expectedQps) +
                ", feasible=" + feasible +
                '}';
    }
}
