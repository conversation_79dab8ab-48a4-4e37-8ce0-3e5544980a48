#!/usr/bin/env python3
"""
HTML to Markdown 转换器
将 xiaobot.json 中的 HTML 内容转换为 Markdown 格式并生成独立文件
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, Any
import html2text
from datetime import datetime

class XiaobotMarkdownConverter:
    def __init__(self, input_file: str, output_dir: str = "markdown_posts"):
        self.input_file = input_file
        self.output_dir = Path(output_dir)
        self.setup_html2text()
        
    def setup_html2text(self):
        """配置 html2text 转换器"""
        self.h = html2text.HTML2Text()
        
        # 基础配置
        self.h.ignore_links = False  # 保留链接
        self.h.ignore_images = False  # 保留图片
        self.h.ignore_emphasis = False  # 保留强调格式
        
        # 链接处理
        self.h.inline_links = True  # 使用内联链接格式
        self.h.protect_links = True  # 保护链接不被破坏
        
        # 格式配置
        self.h.body_width = 0  # 不限制行宽
        self.h.unicode_snob = True  # 使用Unicode字符
        self.h.escape_snob = True  # 转义特殊字符
        
        # 列表和表格
        self.h.ul_item_mark = '-'  # 无序列表标记
        self.h.emphasis_mark = '*'  # 强调标记
        self.h.strong_mark = '**'  # 加粗标记
        
    def sanitize_filename(self, title: str) -> str:
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        title = re.sub(r'[<>:"/\\|?*]', '', title)
        title = re.sub(r'\s+', ' ', title).strip()
        
        # 限制文件名长度
        if len(title) > 100:
            title = title[:100].rsplit(' ', 1)[0]
            
        return title or "untitled"
    
    def convert_html_to_markdown(self, html_content: str) -> str:
        """将HTML内容转换为Markdown"""
        if not html_content or html_content.strip() == "":
            return ""
            
        try:
            # 预处理HTML内容
            html_content = self.preprocess_html(html_content)
            
            # 转换为Markdown
            markdown = self.h.handle(html_content)
            
            # 后处理Markdown内容
            markdown = self.postprocess_markdown(markdown)
            
            return markdown.strip()
            
        except Exception as e:
            print(f"转换HTML时出错: {e}")
            return html_content  # 如果转换失败，返回原始内容
    
    def preprocess_html(self, html: str) -> str:
        """预处理HTML内容"""
        # 处理常见的HTML实体
        html = html.replace('&amp;', '&')
        html = html.replace('&lt;', '<')
        html = html.replace('&gt;', '>')
        html = html.replace('&quot;', '"')
        html = html.replace('&#39;', "'")
        
        # 处理特殊的链接格式
        html = re.sub(r'target="_blank"\s+rel="[^"]*"', 'target="_blank"', html)
        
        return html
    
    def postprocess_markdown(self, markdown: str) -> str:
        """后处理Markdown内容"""
        # 清理多余的空行
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        
        # 修复链接格式
        markdown = re.sub(r'\]\s*\(\s*([^)]+)\s*\)', r'](\1)', markdown)
        
        # 确保标题前后有空行
        markdown = re.sub(r'(?<!^)(?<!\\n)(#{1,6}\s)', r'\n\1', markdown)
        
        return markdown.strip()
    
    def create_markdown_file(self, post: Dict[str, Any], index: int) -> str:
        """为单个post创建Markdown文件"""
        title = post.get('title', f'Post_{index}')
        content = post.get('content', '')
        created_at = post.get('created_at', '')
        tags = post.get('tag_names', [])
        uuid = post.get('uuid', '')
        
        # 转换HTML内容为Markdown
        markdown_content = self.convert_html_to_markdown(content)
        
        # 创建文件名
        filename = self.sanitize_filename(title)
        
        # 构建完整的Markdown内容
        full_markdown = self.build_full_markdown(
            title, markdown_content, created_at, tags, uuid
        )
        
        # 保存文件
        file_path = self.output_dir / f"{filename}.md"
        
        # 处理重名文件
        counter = 1
        original_path = file_path
        while file_path.exists():
            stem = original_path.stem
            suffix = original_path.suffix
            file_path = self.output_dir / f"{stem}_{counter}{suffix}"
            counter += 1
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(full_markdown)
            return str(file_path)
        except Exception as e:
            print(f"保存文件 {file_path} 时出错: {e}")
            return ""
    
    def build_full_markdown(self, title: str, content: str, created_at: str, 
                           tags: list, uuid: str) -> str:
        """构建完整的Markdown文件内容"""
        # 创建前置元数据
        frontmatter = f"""---
title: "{title}"
created_at: "{created_at}"
uuid: "{uuid}"
tags: {tags}
---

"""
        
        # 添加标题
        markdown_title = f"# {title}\n\n"
        
        # 如果有标签，添加标签信息
        if tags:
            tags_section = f"**标签**: {', '.join(tags)}\n\n"
        else:
            tags_section = ""
        
        # 组合完整内容
        full_content = frontmatter + markdown_title + tags_section + content
        
        return full_content
    
    def convert_all_posts(self) -> Dict[str, Any]:
        """转换所有文章并生成统计信息"""
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 读取JSON文件
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            raise Exception(f"读取JSON文件失败: {e}")
        
        posts = data.get('posts', [])
        if not posts:
            raise Exception("JSON文件中没有找到posts数据")
        
        # 转换统计
        stats = {
            'total_posts': len(posts),
            'converted_posts': 0,
            'failed_posts': 0,
            'created_files': [],
            'failed_files': [],
            'start_time': datetime.now().isoformat()
        }
        
        print(f"开始转换 {len(posts)} 篇文章...")
        
        for index, post in enumerate(posts, 1):
            try:
                file_path = self.create_markdown_file(post, index)
                if file_path:
                    stats['converted_posts'] += 1
                    stats['created_files'].append(file_path)
                    print(f"✓ [{index}/{len(posts)}] 已转换: {post.get('title', 'Untitled')}")
                else:
                    stats['failed_posts'] += 1
                    stats['failed_files'].append(post.get('title', f'Post_{index}'))
                    print(f"✗ [{index}/{len(posts)}] 转换失败: {post.get('title', 'Untitled')}")
                    
            except Exception as e:
                stats['failed_posts'] += 1
                stats['failed_files'].append(post.get('title', f'Post_{index}'))
                print(f"✗ [{index}/{len(posts)}] 转换出错: {post.get('title', 'Untitled')} - {e}")
        
        stats['end_time'] = datetime.now().isoformat()
        
        # 保存转换统计
        stats_file = self.output_dir / 'conversion_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        return stats

def main():
    """主函数"""
    input_file = "xiaobot.json"
    output_dir = "markdown_posts"
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return
    
    try:
        # 创建转换器
        converter = XiaobotMarkdownConverter(input_file, output_dir)
        
        # 执行转换
        stats = converter.convert_all_posts()
        
        # 打印结果
        print(f"\n{'='*60}")
        print("转换完成!")
        print(f"{'='*60}")
        print(f"总文章数: {stats['total_posts']}")
        print(f"成功转换: {stats['converted_posts']}")
        print(f"转换失败: {stats['failed_posts']}")
        print(f"输出目录: {output_dir}")
        print(f"统计文件: {output_dir}/conversion_stats.json")
        
        if stats['failed_posts'] > 0:
            print(f"\n失败的文章:")
            for failed in stats['failed_files']:
                print(f"  - {failed}")
                
    except Exception as e:
        print(f"转换过程中出现错误: {e}")

if __name__ == "__main__":
    main()
