#!/usr/bin/env python3
import json
import re
from html import unescape
from datetime import datetime

def extract_specific_links(json_file, domains):
    """
    提取包含特定域名的链接
    domains: 要搜索的域名列表，如 ['netlify.app', 'pan.quark.cn', 'notion.site']
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = {domain: [] for domain in domains}
    
    for post in data['posts']:
        content = post.get('content', '')
        title = post.get('title', '')
        created_at = post.get('created_at', '')
        uuid = post.get('uuid', '')
        tag_names = post.get('tag_names', [])
        
        for domain in domains:
            if domain in content:
                # 使用正则表达式匹配包含特定域名的链接
                # 匹配 <a> 标签
                link_pattern = rf'<a[^>]*href="([^"]*{re.escape(domain)}[^"]*)"[^>]*>(.*?)</a>'
                matches = re.findall(link_pattern, content, re.DOTALL | re.IGNORECASE)
                
                for url, link_text in matches:
                    # 清理链接文本，移除HTML标签
                    clean_text = re.sub(r'<[^>]+>', '', link_text).strip()
                    clean_text = unescape(clean_text)  # 解码HTML实体
                    
                    results[domain].append({
                        'uuid': uuid,
                        'title': title,
                        'url': url,
                        'link_text': clean_text,
                        'created_at': created_at,
                        'tags': tag_names
                    })
                
                # 也搜索纯文本链接（不在<a>标签中的）
                text_link_pattern = rf'https?://[^\s<>"]*{re.escape(domain)}[^\s<>"]*'
                text_matches = re.findall(text_link_pattern, content, re.IGNORECASE)
                
                for url in text_matches:
                    # 检查是否已经在<a>标签中找到了这个链接
                    if not any(existing['url'] == url for existing in results[domain]):
                        results[domain].append({
                            'uuid': uuid,
                            'title': title,
                            'url': url,
                            'link_text': '(纯文本链接)',
                            'created_at': created_at,
                            'tags': tag_names
                        })
    
    return results

def print_domain_results(domain, results):
    """打印特定域名的结果"""
    print(f"\n{'='*120}")
    print(f"包含 {domain} 链接的文章详细信息")
    print(f"{'='*120}")
    
    if not results:
        print(f"未找到包含 {domain} 的链接")
        return
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. 文章标题: {result['title']}")
        print(f"   创建时间: {result['created_at']}")
        print(f"   标签: {', '.join(result['tags']) if result['tags'] else '无标签'}")
        print(f"   链接地址: {result['url']}")
        print(f"   链接文本: {result['link_text']}")
        print(f"   文章UUID: {result['uuid']}")
        print("-" * 80)

def save_results_to_files(all_results):
    """保存结果到不同格式的文件"""
    
    # 保存JSON格式
    with open('all_links_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    # 保存CSV格式
    import csv
    with open('all_links_results.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # 写入表头
        writer.writerow(['域名类型', '文章标题', '链接地址', '链接文本', '创建时间', '标签', 'UUID'])
        
        # 写入数据
        for domain, results in all_results.items():
            for item in results:
                writer.writerow([
                    domain,
                    item['title'],
                    item['url'],
                    item['link_text'],
                    item['created_at'],
                    ', '.join(item['tags']) if item['tags'] else '无标签',
                    item['uuid']
                ])
    
    print(f"\n结果已保存到:")
    print(f"- all_links_results.json (JSON格式)")
    print(f"- all_links_results.csv (CSV格式)")

def print_summary(all_results):
    """打印总结信息"""
    print(f"\n{'='*60}")
    print("总结统计")
    print(f"{'='*60}")
    
    total_links = 0
    total_articles = set()
    
    for domain, results in all_results.items():
        count = len(results)
        articles = len(set(r['title'] for r in results))
        total_links += count
        total_articles.update(r['title'] for r in results)
        
        print(f"\n{domain}:")
        print(f"  - 链接数量: {count}")
        print(f"  - 涉及文章: {articles} 篇")
        
        if results:
            # 分析子域名/路径
            domains_set = set()
            for result in results:
                domain_match = re.search(r'https?://([^/]+)', result['url'])
                if domain_match:
                    domains_set.add(domain_match.group(1))
            
            if domains_set:
                print(f"  - 涉及的域名/子域名:")
                for d in sorted(domains_set):
                    print(f"    * {d}")
    
    print(f"\n总计:")
    print(f"- 总链接数: {total_links}")
    print(f"- 总涉及文章: {len(total_articles)} 篇")

if __name__ == "__main__":
    # 要搜索的域名
    domains_to_search = ['netlify.app', 'pan.quark.cn', 'notion.site']
    
    # 提取链接
    all_results = extract_specific_links('xiaobot.json', domains_to_search)
    
    # 打印每个域名的详细结果
    for domain in domains_to_search:
        print_domain_results(domain, all_results[domain])
    
    # 保存结果到文件
    save_results_to_files(all_results)
    
    # 打印总结
    print_summary(all_results)
