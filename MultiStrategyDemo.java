import java.util.*;

/**
 * 多策略动态调整演示程序
 * 支持通过命令行参数切换不同的策略配置
 */
public class MultiStrategyDemo {
    
    public static void main(String[] args) {
        MultiStrategyDemo demo = new MultiStrategyDemo();
        
        // 检查命令行参数
        String configName = "ab"; // 默认使用AB配置
        if (args.length > 0) {
            configName = args[0];
        }
        
        System.out.println("==========================================");
        System.out.println("     多策略动态调整演示程序");
        System.out.println("==========================================");
        
        if (configName.equals("help") || configName.equals("-h")) {
            demo.showHelp();
            return;
        }
        
        try {
            // 加载配置
            System.out.println("使用配置: " + configName);
            DynamicStrategyConfig config = SimpleConfigLoader.loadConfig(configName);
            
            // 验证配置
            SimpleConfigLoader.validateConfig(config);
            
            // 创建服务实例
            UniversalWeightCalculationService service = new UniversalWeightCalculationService();
            
            // 显示配置详情
            demo.displayConfigDetails(config);
            
            // 运行测试场景
            demo.runTestScenarios(service, config);
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
            demo.showHelp();
        }
    }
    
    /**
     * 显示帮助信息
     */
    private void showHelp() {
        System.out.println("\n使用说明:");
        System.out.println("java MultiStrategyDemo [配置名称]");
        System.out.println();
        SimpleConfigLoader.showAvailableConfigs();
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java MultiStrategyDemo ab      # 使用AB双策略配置");
        System.out.println("  java MultiStrategyDemo abc     # 使用ABC三策略配置");
        System.out.println("  java MultiStrategyDemo abcd    # 使用ABCD四策略配置");
        System.out.println("  java MultiStrategyDemo complex # 使用复杂五策略配置");
        System.out.println("  java MultiStrategyDemo help    # 显示此帮助信息");
    }
    
    /**
     * 显示配置详情
     */
    private void displayConfigDetails(DynamicStrategyConfig config) {
        System.out.println("\n📋 配置详情:");
        System.out.println("==========================================");
        System.out.println("场景ID: " + config.getScenId());
        System.out.println("业务名称: " + config.getBizName());
        System.out.println("最大QPS: " + config.getMaxQPS());
        System.out.println("安全缓冲: " + config.getSafetyBuffer() + " (安全容量: " + 
            (int)(config.getMaxQPS() * config.getSafetyBuffer()) + ")");
        System.out.println("调整步长: " + config.getAdjustmentStep());
        
        System.out.println("\n📊 策略配置:");
        System.out.println("┌──────────┬─────────────┬─────────────┬─────────────┬─────────────┐");
        System.out.println("│ 策略名称 │  batchNum   │    count    │  目标权重   │  紧急权重   │");
        System.out.println("├──────────┼─────────────┼─────────────┼─────────────┼─────────────┤");
        
        for (Map.Entry<String, StrategyInfo> entry : config.getStrategies().entrySet()) {
            String name = entry.getKey();
            StrategyInfo info = entry.getValue();
            double targetWeight = config.getTargetWeights().get(name);
            double emergencyWeight = config.getEmergencyConfig().getWeights().get(name);
            
            System.out.printf("│ %-8s │ %-11d │ %-11d │ %-10.1f%% │ %-10.1f%% │\n", 
                name, info.getBatchNum(), info.getCount(), 
                targetWeight * 100, emergencyWeight * 100);
        }
        System.out.println("└──────────┴─────────────┴─────────────┴─────────────┴─────────────┘");
        
        // 计算权重倍数
        double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
        double emergencyMultiplier = calculateMultiplier(config.getEmergencyConfig().getWeights(), config);
        
        System.out.println("\n📈 权重倍数分析:");
        System.out.println("  目标权重倍数: " + String.format("%.2f", targetMultiplier));
        System.out.println("  紧急权重倍数: " + String.format("%.2f", emergencyMultiplier));
        System.out.println("  倍数降低幅度: " + String.format("%.2f (%.1f%%)", 
            targetMultiplier - emergencyMultiplier, 
            (targetMultiplier - emergencyMultiplier) / targetMultiplier * 100));
    }
    
    /**
     * 运行测试场景
     */
    private void runTestScenarios(UniversalWeightCalculationService service, DynamicStrategyConfig config) {
        // 计算测试QPS
        double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
        double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
        
        // 定义测试场景
        TestScenario[] scenarios = {
            new TestScenario("🔵 正常负载", safeMaxQps / targetMultiplier * 0.8, "目标权重直接可用"),
            new TestScenario("🟡 中等负载", safeMaxQps / targetMultiplier * 1.3, "需要权重优化"),
            new TestScenario("🔴 高负载", safeMaxQps / targetMultiplier * 2.0, "可能需要紧急保护"),
            new TestScenario("🚨 极高负载", safeMaxQps / targetMultiplier * 3.0, "必须启用紧急保护")
        };
        
        List<WeightCalculationResult> results = new ArrayList<>();
        
        // 执行测试场景
        for (int i = 0; i < scenarios.length; i++) {
            TestScenario scenario = scenarios[i];
            
            System.out.println("\n" + scenario.name + " (场景" + (i + 1) + ")");
            System.out.println("==========================================");
            System.out.println("测试QPS: " + String.format("%.0f", scenario.qps));
            System.out.println("预期: " + scenario.expectedResult);
            
            WeightCalculationResult result = service.calculateOptimalWeights(scenario.qps, config);
            results.add(result);
            
            printResult(result, config);
        }
        
        // 结果汇总
        System.out.println("\n📊 测试结果汇总:");
        System.out.println("==========================================");
        printResultSummary(scenarios, results);
    }
    
    /**
     * 计算权重倍数
     */
    private double calculateMultiplier(Map<String, Double> weights, DynamicStrategyConfig config) {
        double multiplier = 0.0;
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            multiplier += weight * batchNum;
        }
        return multiplier;
    }
    
    /**
     * 打印单个结果
     */
    private void printResult(WeightCalculationResult result, DynamicStrategyConfig config) {
        System.out.println("\n✅ 计算结果:");
        System.out.println("  状态: " + getStatusDescription(result.getStatus()));
        System.out.println("  最终权重: " + formatWeights(result.getFinalWeights()));
        System.out.println("  预期QPS: " + String.format("%.0f", result.getExpectedQps()));
        System.out.println("  资源利用率: " + String.format("%.1f%%", result.getResourceUtilization() * 100));
        System.out.println("  计算耗时: " + result.getCalculationTimeMs() + "ms");
        System.out.println("  调整步骤: " + (result.getAdjustmentSteps() != null ? result.getAdjustmentSteps().size() : 0) + "步");
        
        // 显示权重变化
        if (!result.getFinalWeights().equals(config.getTargetWeights())) {
            System.out.println("  权重变化:");
            for (Map.Entry<String, Double> entry : result.getFinalWeights().entrySet()) {
                String strategy = entry.getKey();
                double finalWeight = entry.getValue();
                double targetWeight = config.getTargetWeights().get(strategy);
                double change = finalWeight - targetWeight;
                
                System.out.printf("    %s: %.2f → %.2f (%+.2f)\n", 
                    strategy, targetWeight, finalWeight, change);
            }
        }
    }
    
    /**
     * 获取状态描述
     */
    private String getStatusDescription(CalculationStatus status) {
        switch (status) {
            case TARGET_ACHIEVED: return "目标权重可达成";
            case CAPACITY_OPTIMIZED: return "容量优化成功";
            case EMERGENCY_PROTECTION: return "紧急保护启用";
            case CALCULATION_FAILED: return "计算失败";
            default: return status.toString();
        }
    }
    
    /**
     * 打印结果汇总
     */
    private void printResultSummary(TestScenario[] scenarios, List<WeightCalculationResult> results) {
        System.out.println("┌──────────┬─────────────────┬─────────────┬─────────────┬─────────────┐");
        System.out.println("│   场景   │      状态       │   预期QPS   │ 资源利用率  │   耗时(ms)  │");
        System.out.println("├──────────┼─────────────────┼─────────────┼─────────────┼─────────────┤");
        
        for (int i = 0; i < scenarios.length; i++) {
            WeightCalculationResult result = results.get(i);
            
            System.out.printf("│ 场景%-4d │ %-15s │ %-11.0f │ %-10.1f%% │ %-11d │\n",
                i + 1,
                getStatusDescription(result.getStatus()),
                result.getExpectedQps(),
                result.getResourceUtilization() * 100,
                result.getCalculationTimeMs()
            );
        }
        
        System.out.println("└──────────┴─────────────────┴─────────────┴─────────────┴─────────────┘");
        
        // 统计信息
        long totalTime = results.stream().mapToLong(WeightCalculationResult::getCalculationTimeMs).sum();
        double avgUtilization = results.stream().mapToDouble(WeightCalculationResult::getResourceUtilization).average().orElse(0);
        long successCount = results.stream().mapToLong(r -> r.getStatus() != CalculationStatus.CALCULATION_FAILED ? 1 : 0).sum();
        
        System.out.println("\n📈 统计信息:");
        System.out.println("  总计算耗时: " + totalTime + "ms");
        System.out.println("  平均资源利用率: " + String.format("%.1f%%", avgUtilization * 100));
        System.out.println("  成功率: " + successCount + "/" + results.size() + " (" + 
            String.format("%.1f%%", (double)successCount / results.size() * 100) + ")");
    }
    
    /**
     * 格式化权重显示
     */
    private String formatWeights(Map<String, Double> weights) {
        if (weights == null || weights.isEmpty()) return "{}";
        
        return weights.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(e -> e.getKey() + "=" + String.format("%.2f", e.getValue()))
            .reduce((a, b) -> a + ", " + b)
            .map(s -> "{" + s + "}")
            .orElse("{}");
    }
    
    /**
     * 测试场景类
     */
    private static class TestScenario {
        String name;
        double qps;
        String expectedResult;
        
        TestScenario(String name, double qps, String expectedResult) {
            this.name = name;
            this.qps = qps;
            this.expectedResult = expectedResult;
        }
    }
}
