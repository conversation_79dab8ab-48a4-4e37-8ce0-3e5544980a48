# HTML to Markdown 转换器使用指南

## 📋 功能概述

这个工具可以将 `xiaobot.json` 文件中的 HTML 格式内容转换为 Markdown 格式，并为每篇文章生成独立的 `.md` 文件。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install html2text>=2020.1.16
```

### 2. 运行转换

```bash
python html_to_markdown_converter.py
```

### 3. 查看结果

转换完成后，会在 `markdown_posts/` 目录下生成：
- 每篇文章对应的 `.md` 文件
- `conversion_stats.json` 转换统计文件

## 📁 输出结构

```
markdown_posts/
├── 文章标题1.md
├── 文章标题2.md
├── ...
└── conversion_stats.json
```

## 📝 Markdown 文件格式

每个生成的 Markdown 文件包含：

```markdown
---
title: "文章标题"
created_at: "2025-05-13T07:01:39.000000Z"
uuid: "29f3a7bc-095e-47e3-92bd-8e22e8933529"
tags: ["自动化赚钱"]
---

# 文章标题

**标签**: 自动化赚钱

文章的 Markdown 内容...
```

## ⚙️ 配置选项

### HTML2Text 配置

转换器使用以下配置：

- `inline_links = True`: 使用内联链接格式
- `protect_links = True`: 保护链接完整性
- `body_width = 0`: 不限制行宽
- `unicode_snob = True`: 支持Unicode字符
- `ul_item_mark = '-'`: 无序列表使用 `-` 标记

### 自定义配置

可以在 `XiaobotMarkdownConverter.setup_html2text()` 方法中修改配置：

```python
def setup_html2text(self):
    self.h = html2text.HTML2Text()
    self.h.ignore_links = False      # 是否忽略链接
    self.h.ignore_images = False     # 是否忽略图片
    self.h.body_width = 80          # 设置行宽限制
    # ... 更多配置
```

## 🔧 高级用法

### 自定义输出目录

```python
converter = XiaobotMarkdownConverter("xiaobot.json", "custom_output_dir")
stats = converter.convert_all_posts()
```

### 单独转换某篇文章

```python
converter = XiaobotMarkdownConverter("xiaobot.json")
converter.setup_html2text()

# 假设有一个 post 对象
markdown_content = converter.convert_html_to_markdown(post['content'])
```

## 🛠️ 故障排除

### 常见问题

1. **文件名冲突**
   - 自动添加数字后缀 (如 `title_1.md`, `title_2.md`)

2. **特殊字符处理**
   - 自动清理文件名中的非法字符
   - 限制文件名长度为100字符

3. **编码问题**
   - 统一使用 UTF-8 编码
   - 支持中文和特殊字符

4. **HTML 解析错误**
   - 如果转换失败，会保留原始HTML内容
   - 错误信息会在控制台显示

### 调试模式

在代码中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 转换统计

`conversion_stats.json` 包含详细的转换统计：

```json
{
  "total_posts": 233,
  "converted_posts": 230,
  "failed_posts": 3,
  "created_files": ["path1.md", "path2.md", ...],
  "failed_files": ["failed_title1", "failed_title2", ...],
  "start_time": "2025-06-24T10:30:00",
  "end_time": "2025-06-24T10:32:15"
}
```

## 🔍 支持的 HTML 元素

转换器支持以下 HTML 元素：

- **文本格式**: `<p>`, `<br>`, `<strong>`, `<em>`, `<b>`, `<i>`
- **标题**: `<h1>` 到 `<h6>`
- **链接**: `<a href="...">` (保持完整性)
- **图片**: `<img src="...">` 
- **列表**: `<ul>`, `<ol>`, `<li>`
- **引用**: `<blockquote>`
- **代码**: `<code>`, `<pre>`
- **表格**: `<table>`, `<tr>`, `<td>`, `<th>`

## 🎯 最佳实践

1. **备份原始数据**: 转换前备份 `xiaobot.json`
2. **检查输出**: 转换后检查几个示例文件
3. **批量处理**: 适合大量文章的批量转换
4. **版本控制**: 将生成的 Markdown 文件纳入版本控制

## 📈 性能优化

- 对于大文件，转换过程可能需要几分钟
- 内存使用量与文章数量成正比
- 可以考虑分批处理超大文件

## 🤝 贡献

如果您发现问题或有改进建议，请：

1. 检查现有的问题和解决方案
2. 提供详细的错误信息和复现步骤
3. 考虑提交改进的代码

---

*最后更新: 2025-06-24*
