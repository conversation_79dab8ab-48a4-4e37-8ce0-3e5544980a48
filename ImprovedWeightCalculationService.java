import java.util.*;

/**
 * 改进的权重计算服务
 * 
 * 主要改进：
 * 1. 使用adjustmentStep参数生成缩放因子
 * 2. 支持多策略扩展
 * 3. 可配置的缩放策略
 * 4. 自适应调整算法
 */
public class ImprovedWeightCalculationService {
    
    /**
     * 改进的权重计算算法
     */
    public WeightCalculationResult calculateOptimalWeights(double currentQps, DynamicStrategyConfig config) {
        long startTime = System.currentTimeMillis();
        List<AdjustmentStep> steps = new ArrayList<>();
        
        try {
            System.out.println("\n=== 改进版权重计算开始 ===");
            
            // 1. 计算安全容量
            double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
            
            // 2. 验证目标权重可行性
            double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
            double targetQps = currentQps * targetMultiplier;
            
            if (targetQps <= safeMaxQps) {
                return buildSuccessResult(config.getTargetWeights(), targetQps, safeMaxQps, 
                    CalculationStatus.TARGET_ACHIEVED, "目标权重可直接使用", steps, startTime);
            }
            
            // 3. 使用改进的容量优化算法
            Map<String, Double> optimizedWeights = optimizeWeightsImproved(currentQps, safeMaxQps, config, steps);
            
            if (optimizedWeights != null) {
                double optimizedQps = currentQps * calculateMultiplier(optimizedWeights, config);
                return buildSuccessResult(optimizedWeights, optimizedQps, safeMaxQps, 
                    CalculationStatus.CAPACITY_OPTIMIZED, "通过改进算法优化权重", steps, startTime);
            }
            
            // 4. 紧急保护
            Map<String, Double> emergencyWeights = config.getEmergencyConfig().getWeights();
            double emergencyQps = currentQps * calculateMultiplier(emergencyWeights, config);
            
            return buildSuccessResult(emergencyWeights, emergencyQps, safeMaxQps, 
                CalculationStatus.EMERGENCY_PROTECTION, "启用紧急保护权重", steps, startTime);
                
        } catch (Exception e) {
            return buildFailureResult(e.getMessage(), startTime);
        }
    }
    
    /**
     * 改进的权重优化算法 - 使用adjustmentStep参数
     */
    private Map<String, Double> optimizeWeightsImproved(double currentQps, double safeMaxQps, 
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {
        
        double maxMultiplier = safeMaxQps / currentQps;
        Map<String, Double> currentWeights = new HashMap<>(config.getTargetWeights());
        
        // 1. 生成自适应缩放因子
        List<Double> scaleFactors = generateAdaptiveScaleFactors(currentQps, safeMaxQps, config);
        System.out.println("自适应缩放因子: " + scaleFactors);
        
        // 2. 获取策略优先级（按batchNum降序）
        List<StrategyPriority> priorities = getStrategyPriorities(config);
        System.out.println("策略优先级: " + priorities);
        
        // 3. 逐个尝试调整方案
        for (double scaleFactor : scaleFactors) {
            // 优先调整资源消耗最大的策略
            for (StrategyPriority priority : priorities) {
                if (priority.currentWeight <= 0.1) continue; // 保留最小权重
                
                Map<String, Double> adjustedWeights = adjustSingleStrategy(
                    currentWeights, priority.strategyName, scaleFactor);
                
                double multiplier = calculateMultiplier(adjustedWeights, config);
                double expectedQps = currentQps * multiplier;
                boolean feasible = multiplier <= maxMultiplier;
                
                // 记录调整步骤
                steps.add(new AdjustmentStep(
                    String.format("调整%s策略权重至%.1f%%", priority.strategyName, scaleFactor * 100),
                    new HashMap<>(adjustedWeights),
                    multiplier,
                    expectedQps,
                    feasible
                ));
                
                System.out.printf("尝试调整%s策略(缩放%.1f): 倍数=%.2f, QPS=%.0f, 可行=%s\n", 
                    priority.strategyName, scaleFactor, multiplier, expectedQps, feasible ? "✅" : "❌");
                
                if (feasible) {
                    return adjustedWeights;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 生成自适应缩放因子
     */
    private List<Double> generateAdaptiveScaleFactors(double currentQps, double safeMaxQps, 
            DynamicStrategyConfig config) {
        
        List<Double> factors = new ArrayList<>();
        double adjustmentStep = config.getAdjustmentStep();
        
        // 计算负载压力
        double loadPressure = currentQps / safeMaxQps;
        
        if (loadPressure < 0.7) {
            // 低负载：细粒度调整，步长减半
            double fineStep = adjustmentStep / 2;
            for (double factor = 1.0 - fineStep; factor >= 0.1; factor -= fineStep) {
                factors.add(factor);
            }
        } else if (loadPressure < 0.9) {
            // 中负载：标准调整
            for (double factor = 1.0 - adjustmentStep; factor >= 0.1; factor -= adjustmentStep) {
                factors.add(factor);
            }
        } else {
            // 高负载：粗粒度快速调整，步长加倍
            double coarseStep = adjustmentStep * 2;
            for (double factor = 1.0 - coarseStep; factor >= 0.1; factor -= coarseStep) {
                factors.add(factor);
            }
        }
        
        // 确保包含关键节点
        if (!factors.contains(0.5)) factors.add(0.5);
        if (!factors.contains(0.2)) factors.add(0.2);
        if (!factors.contains(0.1)) factors.add(0.1);
        
        // 排序
        factors.sort(Collections.reverseOrder());
        
        return factors;
    }
    
    /**
     * 获取策略优先级（按资源消耗降序）
     */
    private List<StrategyPriority> getStrategyPriorities(DynamicStrategyConfig config) {
        List<StrategyPriority> priorities = new ArrayList<>();
        
        for (Map.Entry<String, StrategyInfo> entry : config.getStrategies().entrySet()) {
            String strategyName = entry.getKey();
            int batchNum = entry.getValue().getBatchNum();
            double currentWeight = config.getTargetWeights().get(strategyName);
            
            priorities.add(new StrategyPriority(strategyName, batchNum, currentWeight));
        }
        
        // 按batchNum降序排列（优先调整高资源消耗策略）
        priorities.sort((a, b) -> Integer.compare(b.batchNum, a.batchNum));
        
        return priorities;
    }
    
    /**
     * 调整单个策略的权重
     */
    private Map<String, Double> adjustSingleStrategy(Map<String, Double> originalWeights, 
            String targetStrategy, double scaleFactor) {
        
        Map<String, Double> adjustedWeights = new HashMap<>(originalWeights);
        
        double originalWeight = originalWeights.get(targetStrategy);
        double newWeight = originalWeight * scaleFactor;
        double weightReduction = originalWeight - newWeight;
        
        // 设置目标策略的新权重
        adjustedWeights.put(targetStrategy, newWeight);
        
        // 将减少的权重平均分配给其他策略
        int otherStrategiesCount = originalWeights.size() - 1;
        double weightIncreasePerStrategy = weightReduction / otherStrategiesCount;
        
        for (Map.Entry<String, Double> entry : originalWeights.entrySet()) {
            if (!entry.getKey().equals(targetStrategy)) {
                double currentWeight = entry.getValue();
                adjustedWeights.put(entry.getKey(), currentWeight + weightIncreasePerStrategy);
            }
        }
        
        return adjustedWeights;
    }
    
    /**
     * 计算权重倍数
     */
    private double calculateMultiplier(Map<String, Double> weights, DynamicStrategyConfig config) {
        double multiplier = 0.0;
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            multiplier += weight * batchNum;
        }
        return multiplier;
    }
    
    /**
     * 构建成功结果
     */
    private WeightCalculationResult buildSuccessResult(Map<String, Double> finalWeights, 
            double expectedQps, double safeMaxQps, CalculationStatus status, String reason,
            List<AdjustmentStep> steps, long startTime) {
        
        long calculationTime = System.currentTimeMillis() - startTime;
        double resourceUtilization = expectedQps / safeMaxQps;
        
        return new WeightCalculationResult(status, finalWeights, expectedQps, 
            resourceUtilization, reason, calculationTime, steps);
    }
    
    /**
     * 构建失败结果
     */
    private WeightCalculationResult buildFailureResult(String errorMessage, long startTime) {
        long calculationTime = System.currentTimeMillis() - startTime;
        return new WeightCalculationResult(CalculationStatus.CALCULATION_FAILED, 
            new HashMap<>(), 0, 0, errorMessage, calculationTime, new ArrayList<>());
    }
}

/**
 * 策略优先级类
 */
class StrategyPriority {
    String strategyName;
    int batchNum;
    double currentWeight;
    
    public StrategyPriority(String strategyName, int batchNum, double currentWeight) {
        this.strategyName = strategyName;
        this.batchNum = batchNum;
        this.currentWeight = currentWeight;
    }
    
    @Override
    public String toString() {
        return String.format("%s(batchNum=%d, weight=%.2f)", strategyName, batchNum, currentWeight);
    }
}
