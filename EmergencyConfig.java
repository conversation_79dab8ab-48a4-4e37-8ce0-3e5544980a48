import java.util.Map;

/**
 * 紧急配置类
 */
public class EmergencyConfig {
    private String condition;
    private Map<String, Double> weights;

    public EmergencyConfig() {}

    public EmergencyConfig(String condition, Map<String, Double> weights) {
        this.condition = condition;
        this.weights = weights;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public Map<String, Double> getWeights() {
        return weights;
    }

    public void setWeights(Map<String, Double> weights) {
        this.weights = weights;
    }

    @Override
    public String toString() {
        return "EmergencyConfig{" +
                "condition='" + condition + '\'' +
                ", weights=" + weights +
                '}';
    }
}
