#!/usr/bin/env python3
import json
import re
from html import unescape
from datetime import datetime

def extract_netlify_links_detailed(json_file):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = []
    
    for post in data['posts']:
        content = post.get('content', '')
        title = post.get('title', '')
        created_at = post.get('created_at', '')
        uuid = post.get('uuid', '')
        tag_names = post.get('tag_names', [])
        
        if 'netlify.app' in content:
            # 使用正则表达式匹配包含 netlify.app 的链接
            link_pattern = r'<a[^>]*href="([^"]*netlify\.app[^"]*)"[^>]*>(.*?)</a>'
            matches = re.findall(link_pattern, content, re.DOTALL | re.IGNORECASE)
            
            for url, link_text in matches:
                # 清理链接文本，移除HTML标签
                clean_text = re.sub(r'<[^>]+>', '', link_text).strip()
                clean_text = unescape(clean_text)  # 解码HTML实体
                
                results.append({
                    'uuid': uuid,
                    'title': title,
                    'url': url,
                    'link_text': clean_text,
                    'created_at': created_at,
                    'tags': tag_names
                })
    
    return results

def print_detailed_table(results):
    if not results:
        print("未找到包含 netlify.app 的链接")
        return
    
    print("=" * 120)
    print("包含 netlify.app 链接的文章详细信息")
    print("=" * 120)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. 文章标题: {result['title']}")
        print(f"   创建时间: {result['created_at']}")
        print(f"   标签: {', '.join(result['tags']) if result['tags'] else '无标签'}")
        print(f"   链接地址: {result['url']}")
        print(f"   链接文本: {result['link_text']}")
        print(f"   文章UUID: {result['uuid']}")
        print("-" * 80)

def save_to_json(results, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n结果已保存到 {output_file}")

if __name__ == "__main__":
    results = extract_netlify_links_detailed('xiaobot.json')
    
    print_detailed_table(results)
    
    # 保存详细结果到JSON文件
    save_to_json(results, 'netlify_links_results.json')
    
    print(f"\n总结:")
    print(f"- 总共找到 {len(results)} 个包含 netlify.app 的链接")
    print(f"- 涉及 {len(set(r['title'] for r in results))} 篇文章")
    
    # 分析链接域名
    domains = set()
    for result in results:
        domain_match = re.search(r'https?://([^/]+)', result['url'])
        if domain_match:
            domains.add(domain_match.group(1))
    
    print(f"- 涉及的 netlify.app 子域名:")
    for domain in sorted(domains):
        print(f"  * {domain}")
