#!/usr/bin/env python3
"""
内部链接分析器 - 类似 Obsidian 双链笔记系统
分析 Markdown 文件之间的内部链接关系，构建知识图谱
"""

import json
import os
import re
import yaml
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import jieba
from fuzzywuzzy import fuzz, process
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pyvis.network import Network
import pandas as pd

@dataclass
class LinkInfo:
    """链接信息数据类"""
    target_uuid: str
    target_title: str
    link_type: str  # 'exact_match', 'fuzzy_match', 'markdown_link', 'keyword_match'
    confidence: float
    context: str
    position: int

@dataclass
class Article:
    """文章信息数据类"""
    uuid: str
    title: str
    file_path: str
    content: str
    tags: List[str]
    created_at: str
    outbound_links: List[LinkInfo]
    inbound_links: List[str]  # UUIDs of articles that link to this one

class ArticleIndex:
    """文章索引管理器"""
    
    def __init__(self, markdown_dir: str):
        self.markdown_dir = Path(markdown_dir)
        self.articles: Dict[str, Article] = {}
        self.title_to_uuid: Dict[str, str] = {}
        self.title_variants: Dict[str, Set[str]] = defaultdict(set)
        self.external_domains = {
            'netlify.app', 'pan.quark.cn', 'notion.site', 'github.com',
            'youtube.com', 'youtu.be', 'twitter.com', 'x.com',
            'linkedin.com', 'medium.com', 'substack.com'
        }
        
    def load_articles(self) -> None:
        """加载所有 Markdown 文件"""
        print("正在加载文章...")
        
        for md_file in self.markdown_dir.glob("*.md"):
            if md_file.name == "conversion_stats.json":
                continue
                
            try:
                article = self._parse_markdown_file(md_file)
                if article:
                    self.articles[article.uuid] = article
                    self.title_to_uuid[article.title] = article.uuid
                    self._build_title_variants(article.title, article.uuid)
                    
            except Exception as e:
                print(f"解析文件 {md_file} 时出错: {e}")
        
        print(f"成功加载 {len(self.articles)} 篇文章")
    
    def _parse_markdown_file(self, file_path: Path) -> Optional[Article]:
        """解析单个 Markdown 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析 YAML 前置元数据
            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    frontmatter = yaml.safe_load(parts[1])
                    main_content = parts[2].strip()
                    
                    return Article(
                        uuid=frontmatter.get('uuid', ''),
                        title=frontmatter.get('title', ''),
                        file_path=str(file_path),
                        content=main_content,
                        tags=frontmatter.get('tags', []),
                        created_at=frontmatter.get('created_at', ''),
                        outbound_links=[],
                        inbound_links=[]
                    )
        except Exception as e:
            print(f"解析文件 {file_path} 出错: {e}")
        
        return None
    
    def _build_title_variants(self, title: str, uuid: str) -> None:
        """构建标题变体用于模糊匹配"""
        variants = set()
        
        # 原标题
        variants.add(title)
        
        # 去除标点符号
        clean_title = re.sub(r'[^\w\s]', '', title)
        variants.add(clean_title)
        
        # 去除数字编号
        no_numbers = re.sub(r'^\d+\.?\s*', '', title)
        variants.add(no_numbers)
        
        # 去除常见前缀
        prefixes = ['Make', 'n8n', 'HTTP第三方API调用：', '翔宇工作流', '视频']
        for prefix in prefixes:
            if title.startswith(prefix):
                variants.add(title[len(prefix):].strip())
        
        # 分词后的关键词组合
        words = list(jieba.cut(title))
        if len(words) > 1:
            # 取前几个重要词
            important_words = [w for w in words if len(w) > 1 and w not in ['的', '与', '和', '或']][:3]
            if important_words:
                variants.add(' '.join(important_words))
        
        for variant in variants:
            if variant.strip():
                self.title_variants[variant.strip()].add(uuid)

class LinkExtractor:
    """链接提取器"""
    
    def __init__(self, article_index: ArticleIndex):
        self.index = article_index
        self.fuzzy_threshold = 85  # 模糊匹配阈值
        
    def extract_links(self, article: Article) -> List[LinkInfo]:
        """提取文章中的所有内部链接"""
        links = []
        content = article.content
        
        # 1. 精确标题匹配
        links.extend(self._extract_exact_matches(content, article.uuid))
        
        # 2. Markdown 链接解析
        links.extend(self._extract_markdown_links(content, article.uuid))
        
        # 3. 模糊标题匹配
        links.extend(self._extract_fuzzy_matches(content, article.uuid))
        
        # 4. 关键词匹配
        links.extend(self._extract_keyword_matches(content, article.uuid))
        
        # 去重和排序
        return self._deduplicate_links(links)
    
    def _extract_exact_matches(self, content: str, source_uuid: str) -> List[LinkInfo]:
        """提取精确标题匹配"""
        links = []
        
        for title, target_uuid in self.index.title_to_uuid.items():
            if target_uuid == source_uuid:  # 跳过自引用
                continue
                
            # 查找标题在内容中的位置
            for match in re.finditer(re.escape(title), content, re.IGNORECASE):
                context = self._extract_context(content, match.start(), match.end())
                
                # 检查是否在外部链接中
                if not self._is_in_external_link(content, match.start(), match.end()):
                    links.append(LinkInfo(
                        target_uuid=target_uuid,
                        target_title=title,
                        link_type='exact_match',
                        confidence=1.0,
                        context=context,
                        position=match.start()
                    ))
        
        return links
    
    def _extract_markdown_links(self, content: str, source_uuid: str) -> List[LinkInfo]:
        """提取 Markdown 格式链接"""
        links = []
        
        # 匹配 [text](link) 格式
        markdown_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        
        for match in re.finditer(markdown_pattern, content):
            link_text = match.group(1)
            link_url = match.group(2)
            
            # 检查是否为外部链接
            if self._is_external_link(link_url):
                continue
            
            # 尝试匹配链接文本到文章标题
            best_match = self._find_best_title_match(link_text)
            if best_match and best_match[1] >= self.fuzzy_threshold:
                target_uuid = self.index.title_to_uuid.get(best_match[0])
                if target_uuid and target_uuid != source_uuid:
                    context = self._extract_context(content, match.start(), match.end())
                    
                    links.append(LinkInfo(
                        target_uuid=target_uuid,
                        target_title=best_match[0],
                        link_type='markdown_link',
                        confidence=0.9,
                        context=context,
                        position=match.start()
                    ))
        
        return links
    
    def _extract_fuzzy_matches(self, content: str, source_uuid: str) -> List[LinkInfo]:
        """提取模糊匹配的标题"""
        links = []
        
        # 对于每个标题变体，进行模糊搜索
        for variant, uuids in self.index.title_variants.items():
            if len(variant) < 4:  # 跳过太短的变体
                continue
                
            for uuid in uuids:
                if uuid == source_uuid:
                    continue
                
                # 在内容中搜索这个变体
                for match in re.finditer(re.escape(variant), content, re.IGNORECASE):
                    if not self._is_in_external_link(content, match.start(), match.end()):
                        context = self._extract_context(content, match.start(), match.end())
                        
                        # 计算相似度
                        similarity = fuzz.ratio(variant, self.index.articles[uuid].title)
                        if similarity >= self.fuzzy_threshold:
                            links.append(LinkInfo(
                                target_uuid=uuid,
                                target_title=self.index.articles[uuid].title,
                                link_type='fuzzy_match',
                                confidence=similarity / 100.0,
                                context=context,
                                position=match.start()
                            ))
        
        return links
    
    def _extract_keyword_matches(self, content: str, source_uuid: str) -> List[LinkInfo]:
        """提取基于关键词的匹配"""
        links = []
        
        # 这里可以实现更复杂的关键词匹配逻辑
        # 暂时跳过，避免过多的误匹配
        
        return links
    
    def _is_external_link(self, url: str) -> bool:
        """判断是否为外部链接"""
        if url.startswith(('http://', 'https://')):
            for domain in self.index.external_domains:
                if domain in url:
                    return True
        return url.startswith(('mailto:', 'tel:', 'ftp://'))
    
    def _is_in_external_link(self, content: str, start: int, end: int) -> bool:
        """检查文本是否在外部链接中"""
        # 检查前后文是否有链接标记
        before = content[max(0, start-50):start]
        after = content[end:min(len(content), end+50)]
        
        # 检查是否在 Markdown 链接中
        if '](http' in before + after:
            return True
        
        # 检查是否在 HTML 链接中
        if '<a href=' in before or '</a>' in after:
            return True
            
        return False
    
    def _find_best_title_match(self, text: str) -> Optional[Tuple[str, int]]:
        """找到最佳的标题匹配"""
        titles = list(self.index.title_to_uuid.keys())
        result = process.extractOne(text, titles, scorer=fuzz.ratio)
        return result if result and result[1] >= self.fuzzy_threshold else None
    
    def _extract_context(self, content: str, start: int, end: int, context_length: int = 100) -> str:
        """提取链接周围的上下文"""
        context_start = max(0, start - context_length)
        context_end = min(len(content), end + context_length)
        context = content[context_start:context_end].strip()
        
        # 清理上下文
        context = re.sub(r'\s+', ' ', context)
        return context
    
    def _deduplicate_links(self, links: List[LinkInfo]) -> List[LinkInfo]:
        """去重和排序链接"""
        # 按目标UUID和位置去重
        seen = set()
        unique_links = []
        
        # 按置信度排序
        links.sort(key=lambda x: (-x.confidence, x.position))
        
        for link in links:
            key = (link.target_uuid, link.position // 50)  # 允许位置有小幅差异
            if key not in seen:
                seen.add(key)
                unique_links.append(link)
        
        return unique_links


class RelationshipBuilder:
    """关系构建器"""

    def __init__(self, article_index: ArticleIndex):
        self.index = article_index
        self.link_extractor = LinkExtractor(article_index)

    def build_relationships(self) -> Dict:
        """构建所有文章之间的关系"""
        print("正在分析文章间的链接关系...")

        # 提取所有链接
        for uuid, article in self.index.articles.items():
            print(f"分析文章: {article.title[:50]}...")
            links = self.link_extractor.extract_links(article)
            article.outbound_links = links

            # 更新被引用文章的入站链接
            for link in links:
                if link.target_uuid in self.index.articles:
                    self.index.articles[link.target_uuid].inbound_links.append(uuid)

        # 构建关系数据
        relationships = []
        for uuid, article in self.index.articles.items():
            for link in article.outbound_links:
                relationships.append({
                    'source_uuid': uuid,
                    'source_title': article.title,
                    'target_uuid': link.target_uuid,
                    'target_title': link.target_title,
                    'link_type': link.link_type,
                    'confidence': link.confidence,
                    'context': link.context
                })

        # 计算统计信息
        stats = self._calculate_statistics()

        return {
            'articles': {uuid: self._article_to_dict(article)
                        for uuid, article in self.index.articles.items()},
            'relationships': relationships,
            'statistics': stats
        }

    def _article_to_dict(self, article: Article) -> Dict:
        """将文章对象转换为字典"""
        return {
            'uuid': article.uuid,
            'title': article.title,
            'file_path': article.file_path,
            'tags': article.tags,
            'created_at': article.created_at,
            'outbound_count': len(article.outbound_links),
            'inbound_count': len(set(article.inbound_links)),  # 去重
            'outbound_links': [asdict(link) for link in article.outbound_links],
            'inbound_links': list(set(article.inbound_links))  # 去重
        }

    def _calculate_statistics(self) -> Dict:
        """计算统计信息"""
        total_articles = len(self.index.articles)
        total_links = sum(len(article.outbound_links) for article in self.index.articles.values())

        # 计算度分布
        outbound_counts = [len(article.outbound_links) for article in self.index.articles.values()]
        inbound_counts = [len(set(article.inbound_links)) for article in self.index.articles.values()]

        # 找出最受欢迎的文章（被引用最多）
        most_referenced = sorted(
            self.index.articles.items(),
            key=lambda x: len(set(x[1].inbound_links)),
            reverse=True
        )[:10]

        # 找出引用最多的文章
        most_referencing = sorted(
            self.index.articles.items(),
            key=lambda x: len(x[1].outbound_links),
            reverse=True
        )[:10]

        return {
            'total_articles': total_articles,
            'total_links': total_links,
            'avg_outbound_links': sum(outbound_counts) / total_articles if total_articles > 0 else 0,
            'avg_inbound_links': sum(inbound_counts) / total_articles if total_articles > 0 else 0,
            'max_outbound_links': max(outbound_counts) if outbound_counts else 0,
            'max_inbound_links': max(inbound_counts) if inbound_counts else 0,
            'most_referenced_articles': [
                {'uuid': uuid, 'title': article.title, 'inbound_count': len(set(article.inbound_links))}
                for uuid, article in most_referenced
            ],
            'most_referencing_articles': [
                {'uuid': uuid, 'title': article.title, 'outbound_count': len(article.outbound_links)}
                for uuid, article in most_referencing
            ]
        }


class NetworkVisualizer:
    """网络可视化器"""

    def __init__(self, relationship_data: Dict):
        self.data = relationship_data
        self.graph = self._build_networkx_graph()

    def _build_networkx_graph(self) -> nx.DiGraph:
        """构建 NetworkX 图"""
        G = nx.DiGraph()

        # 添加节点
        for uuid, article in self.data['articles'].items():
            G.add_node(uuid,
                      title=article['title'],
                      inbound_count=article['inbound_count'],
                      outbound_count=article['outbound_count'],
                      tags=article['tags'])

        # 添加边
        for rel in self.data['relationships']:
            G.add_edge(rel['source_uuid'], rel['target_uuid'],
                      weight=rel['confidence'],
                      link_type=rel['link_type'],
                      context=rel['context'][:100])  # 限制上下文长度

        return G

    def generate_interactive_html(self, output_path: str = "knowledge_graph.html") -> None:
        """生成交互式 HTML 网络图"""
        print(f"正在生成交互式网络图: {output_path}")

        net = Network(height="800px", width="100%", bgcolor="#222222", font_color="white")
        net.barnes_hut()

        # 添加节点
        for uuid, article in self.data['articles'].items():
            # 节点大小基于被引用次数
            size = max(10, min(50, article['inbound_count'] * 3 + 10))

            # 节点颜色基于标签
            color = self._get_node_color(article['tags'])

            # 节点标题
            title = f"{article['title']}\n入链: {article['inbound_count']}\n出链: {article['outbound_count']}"

            net.add_node(uuid,
                        label=article['title'][:30] + "..." if len(article['title']) > 30 else article['title'],
                        title=title,
                        size=size,
                        color=color)

        # 添加边
        for rel in self.data['relationships']:
            # 边的粗细基于置信度
            width = max(1, rel['confidence'] * 3)

            net.add_edge(rel['source_uuid'], rel['target_uuid'],
                        width=width,
                        title=f"类型: {rel['link_type']}\n置信度: {rel['confidence']:.2f}\n上下文: {rel['context'][:100]}")

        # 设置物理引擎参数
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "barnesHut": {
              "gravitationalConstant": -8000,
              "centralGravity": 0.3,
              "springLength": 95,
              "springConstant": 0.04,
              "damping": 0.09
            }
          }
        }
        """)

        net.save_graph(output_path)
        print(f"交互式网络图已保存到: {output_path}")

    def _get_node_color(self, tags: List[str]) -> str:
        """根据标签确定节点颜色"""
        color_map = {
            'Make基础教程': '#FF6B6B',
            'n8n 基础教程': '#4ECDC4',
            '资源包': '#45B7D1',
            'API': '#96CEB4',
            '自动化赚钱': '#FFEAA7',
            '快捷工具': '#DDA0DD',
            '会员独家': '#98D8C8',
            'AI教程与资源': '#F7DC6F'
        }

        for tag in tags:
            if tag in color_map:
                return color_map[tag]

        return '#95A5A6'  # 默认颜色

    def generate_static_graph(self, output_path: str = "knowledge_graph_static.png") -> None:
        """生成静态网络图"""
        print(f"正在生成静态网络图: {output_path}")

        plt.figure(figsize=(20, 16))

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 计算布局
        pos = nx.spring_layout(self.graph, k=3, iterations=50)

        # 绘制节点
        node_sizes = [self.data['articles'][node]['inbound_count'] * 100 + 200 for node in self.graph.nodes()]
        node_colors = [self._get_node_color_numeric(self.data['articles'][node]['tags']) for node in self.graph.nodes()]

        nx.draw_networkx_nodes(self.graph, pos,
                              node_size=node_sizes,
                              node_color=node_colors,
                              alpha=0.7)

        # 绘制边
        edge_weights = [self.graph[u][v]['weight'] for u, v in self.graph.edges()]
        nx.draw_networkx_edges(self.graph, pos,
                              width=[w * 2 for w in edge_weights],
                              alpha=0.5,
                              edge_color='gray',
                              arrows=True,
                              arrowsize=20)

        # 添加标签（只显示重要节点）
        important_nodes = {node: self.data['articles'][node]['title'][:20]
                          for node in self.graph.nodes()
                          if self.data['articles'][node]['inbound_count'] > 2}

        nx.draw_networkx_labels(self.graph, pos,
                               labels=important_nodes,
                               font_size=8,
                               font_weight='bold')

        plt.title("翔宇工作流知识图谱", fontsize=20, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"静态网络图已保存到: {output_path}")

    def _get_node_color_numeric(self, tags: List[str]) -> float:
        """根据标签返回数值颜色"""
        color_map = {
            'Make基础教程': 0.1,
            'n8n 基础教程': 0.3,
            '资源包': 0.5,
            'API': 0.7,
            '自动化赚钱': 0.9,
        }

        for tag in tags:
            if tag in color_map:
                return color_map[tag]

        return 0.0

    def generate_statistics_report(self, output_path: str = "link_analysis_report.md") -> None:
        """生成统计分析报告"""
        print(f"正在生成分析报告: {output_path}")

        stats = self.data['statistics']

        report = f"""# 翔宇工作流知识图谱分析报告

## 📊 总体统计

- **文章总数**: {stats['total_articles']} 篇
- **链接总数**: {stats['total_links']} 个
- **平均出链数**: {stats['avg_outbound_links']:.2f}
- **平均入链数**: {stats['avg_inbound_links']:.2f}
- **最大出链数**: {stats['max_outbound_links']}
- **最大入链数**: {stats['max_inbound_links']}

## 🏆 最受欢迎文章 (被引用最多)

"""

        for i, article in enumerate(stats['most_referenced_articles'], 1):
            report += f"{i}. **{article['title']}** (被引用 {article['inbound_count']} 次)\n"

        report += f"""

## 📝 引用最多文章 (引用他人最多)

"""

        for i, article in enumerate(stats['most_referencing_articles'], 1):
            report += f"{i}. **{article['title']}** (引用 {article['outbound_count']} 篇)\n"

        report += f"""

## 🔗 链接类型分布

"""

        # 统计链接类型
        link_types = Counter(rel['link_type'] for rel in self.data['relationships'])
        for link_type, count in link_types.most_common():
            report += f"- **{link_type}**: {count} 个\n"

        report += f"""

## 📈 网络特性

- **连通性**: {'强连通' if nx.is_strongly_connected(self.graph) else '弱连通'}
- **节点数**: {self.graph.number_of_nodes()}
- **边数**: {self.graph.number_of_edges()}
- **平均度**: {sum(dict(self.graph.degree()).values()) / self.graph.number_of_nodes():.2f}

---

*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"分析报告已保存到: {output_path}")


def main():
    """主函数"""
    print("🔗 翔宇工作流内部链接分析器")
    print("=" * 50)

    # 配置路径
    markdown_dir = "markdown_posts"
    output_dir = "link_analysis_results"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    try:
        # 1. 加载文章索引
        print("\n📚 第1步: 加载文章索引")
        article_index = ArticleIndex(markdown_dir)
        article_index.load_articles()

        if not article_index.articles:
            print("❌ 没有找到任何文章，请检查路径是否正确")
            return

        # 2. 构建关系
        print("\n🔍 第2步: 分析链接关系")
        relationship_builder = RelationshipBuilder(article_index)
        relationship_data = relationship_builder.build_relationships()

        # 3. 保存关系数据
        print("\n💾 第3步: 保存分析结果")
        with open(f"{output_dir}/relationships.json", 'w', encoding='utf-8') as f:
            json.dump(relationship_data, f, ensure_ascii=False, indent=2)
        print(f"关系数据已保存到: {output_dir}/relationships.json")

        # 4. 生成可视化
        print("\n🎨 第4步: 生成可视化图表")
        visualizer = NetworkVisualizer(relationship_data)

        # 生成交互式图表
        visualizer.generate_interactive_html(f"{output_dir}/knowledge_graph.html")

        # 生成静态图表
        visualizer.generate_static_graph(f"{output_dir}/knowledge_graph.png")

        # 生成分析报告
        visualizer.generate_statistics_report(f"{output_dir}/analysis_report.md")

        # 5. 打印总结
        print("\n✅ 分析完成!")
        print(f"📊 分析了 {relationship_data['statistics']['total_articles']} 篇文章")
        print(f"🔗 发现了 {relationship_data['statistics']['total_links']} 个内部链接")
        print(f"📁 结果保存在: {output_dir}/")

        print("\n📋 生成的文件:")
        print(f"  - relationships.json: 完整的关系数据")
        print(f"  - knowledge_graph.html: 交互式网络图")
        print(f"  - knowledge_graph.png: 静态网络图")
        print(f"  - analysis_report.md: 分析报告")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
