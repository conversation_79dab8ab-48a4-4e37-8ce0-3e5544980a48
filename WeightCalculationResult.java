import java.util.List;
import java.util.Map;

/**
 * 权重计算结果类
 */
public class WeightCalculationResult {
    private CalculationStatus status;
    private Map<String, Double> finalWeights;
    private double expectedQps;
    private double resourceUtilization;
    private String adjustmentReason;
    private long calculationTimeMs;
    private List<AdjustmentStep> adjustmentSteps;

    public WeightCalculationResult() {}

    public WeightCalculationResult(CalculationStatus status, Map<String, Double> finalWeights, 
                                 double expectedQps, double resourceUtilization, String adjustmentReason,
                                 long calculationTimeMs, List<AdjustmentStep> adjustmentSteps) {
        this.status = status;
        this.finalWeights = finalWeights;
        this.expectedQps = expectedQps;
        this.resourceUtilization = resourceUtilization;
        this.adjustmentReason = adjustmentReason;
        this.calculationTimeMs = calculationTimeMs;
        this.adjustmentSteps = adjustmentSteps;
    }

    // Getter和Setter方法
    public CalculationStatus getStatus() { return status; }
    public void setStatus(CalculationStatus status) { this.status = status; }

    public Map<String, Double> getFinalWeights() { return finalWeights; }
    public void setFinalWeights(Map<String, Double> finalWeights) { this.finalWeights = finalWeights; }

    public double getExpectedQps() { return expectedQps; }
    public void setExpectedQps(double expectedQps) { this.expectedQps = expectedQps; }

    public double getResourceUtilization() { return resourceUtilization; }
    public void setResourceUtilization(double resourceUtilization) { this.resourceUtilization = resourceUtilization; }

    public String getAdjustmentReason() { return adjustmentReason; }
    public void setAdjustmentReason(String adjustmentReason) { this.adjustmentReason = adjustmentReason; }

    public long getCalculationTimeMs() { return calculationTimeMs; }
    public void setCalculationTimeMs(long calculationTimeMs) { this.calculationTimeMs = calculationTimeMs; }

    public List<AdjustmentStep> getAdjustmentSteps() { return adjustmentSteps; }
    public void setAdjustmentSteps(List<AdjustmentStep> adjustmentSteps) { this.adjustmentSteps = adjustmentSteps; }

    @Override
    public String toString() {
        return "WeightCalculationResult{" +
                "status=" + status +
                ", finalWeights=" + finalWeights +
                ", expectedQps=" + expectedQps +
                ", resourceUtilization=" + String.format("%.1f%%", resourceUtilization * 100) +
                ", adjustmentReason='" + adjustmentReason + '\'' +
                ", calculationTimeMs=" + calculationTimeMs +
                '}';
    }
}

/**
 * 计算状态枚举
 */
enum CalculationStatus {
    TARGET_ACHIEVED,      // 直接使用目标权重
    CAPACITY_OPTIMIZED,   // 通过优化满足容量约束
    EMERGENCY_PROTECTION, // 启用紧急保护
    CALCULATION_FAILED    // 计算失败
}
