# WeightCalculationService 核心执行流程详解

## 1. 整体流程概览

WeightCalculationService是动态策略调整系统的核心组件，负责根据当前QPS和系统容量约束计算最优权重分配。

### 主要执行步骤：
1. **输入验证与初始化**：接收当前QPS和配置参数
2. **安全容量计算**：计算系统安全容量上限
3. **目标权重验证**：检查目标权重是否可行
4. **容量优化算法**：如果目标权重不可行，启动优化算法
5. **紧急保护机制**：如果优化失败，启用紧急保护
6. **结果构建**：生成完整的计算结果对象

## 2. 权重调整算法详细过程

### 2.1 目标权重可行性验证

```java
// 步骤1：计算安全容量
double safeMaxQps = maxQPS × safetyBuffer;
// 示例：1000 × 0.9 = 900

// 步骤2：计算目标权重倍数
double targetMultiplier = A权重×1 + B权重×3;
// 示例：0.1×1 + 0.9×3 = 2.8

// 步骤3：计算目标QPS
double targetQps = currentQps × targetMultiplier;
// 示例：300 × 2.8 = 840

// 步骤4：可行性判断
if (targetQps ≤ safeMaxQps) {
    return TARGET_ACHIEVED; // 840 ≤ 900 ✅
}
```

### 2.2 容量优化算法逐步调整

当目标权重不可行时，系统启动容量优化算法：

#### 调整策略：
- **优先级1**：减少B策略权重（因为B策略batchNum=3，资源消耗更大）
- **优先级2**：增加A策略权重（保持总权重=1.0）
- **调整方式**：按预定义缩放因子逐步尝试

#### 缩放因子序列：
```java
double[] scaleFactors = {0.8, 0.6, 0.4, 0.2, 0.1};
```

#### 权重调整计算：
```java
// B策略权重调整
double newBWeight = originalBWeight × scaleFactor;
// 示例：0.9 × 0.6 = 0.54

// A策略权重调整（保持总权重=1.0）
double weightDiff = originalBWeight - newBWeight;
double newAWeight = originalAWeight + weightDiff;
// 示例：0.1 + (0.9 - 0.54) = 0.46
```

### 2.3 调整过程示例（场景2：QPS=400）

| 调整轮次 | 缩放因子 | B权重 | A权重 | 权重倍数 | 预期QPS | 最大允许倍数 | 是否可行 |
|---------|---------|-------|-------|----------|---------|-------------|---------|
| 原始 | - | 0.9 | 0.1 | 2.8 | 1120 | 2.25 | ❌ |
| 第1轮 | 0.8 | 0.72 | 0.28 | 2.44 | 976 | 2.25 | ❌ |
| 第2轮 | 0.6 | 0.54 | 0.46 | 2.08 | 832 | 2.25 | ✅ |

## 3. 决策分支逻辑

### 3.1 决策树

```
当前QPS + 配置
    ↓
计算目标QPS
    ↓
目标QPS ≤ 安全容量？
    ├─ 是 → TARGET_ACHIEVED（直接使用目标权重）
    └─ 否 → 启动容量优化
              ↓
          找到可行解？
              ├─ 是 → CAPACITY_OPTIMIZED（使用优化权重）
              └─ 否 → EMERGENCY_PROTECTION（启用紧急保护）
```

### 3.2 决策条件详解

| 决策分支 | 触发条件 | 权重来源 | 资源利用率 |
|---------|---------|----------|-----------|
| TARGET_ACHIEVED | targetQps ≤ safeMaxQps | 目标权重 | < 100% |
| CAPACITY_OPTIMIZED | 存在可行的调整方案 | 优化权重 | ≈ 100% |
| EMERGENCY_PROTECTION | 所有调整都无效 | 紧急权重 | > 100% |

## 4. 关键计算公式

### 4.1 权重倍数计算公式

**核心公式**：
```
权重倍数 = Σ(策略权重 × 策略batchNum)
         = A权重×1 + B权重×3
```

**物理意义**：
- 权重倍数反映了当前权重配置下的总体资源消耗水平
- batchNum表示策略的资源消耗系数
- 倍数越高，系统资源消耗越大

### 4.2 容量约束公式

```
最大允许倍数 = 安全容量 / 当前QPS
预期QPS = 当前QPS × 权重倍数
可行性条件：权重倍数 ≤ 最大允许倍数
```

### 4.3 资源利用率计算

```
资源利用率 = 预期QPS / 安全容量 × 100%
```

## 5. 紧急保护触发条件

### 5.1 触发场景

紧急保护在以下情况下触发：

1. **高负载场景**：当前QPS接近或超过安全容量
2. **优化失败**：所有缩放因子都无法满足容量约束
3. **极端情况**：系统负载过高，常规调整无效

### 5.2 触发条件分析

以场景3（QPS=900）为例：
```
安全容量 = 900
当前QPS = 900
最大允许倍数 = 900/900 = 1.0

即使最小调整（B权重缩放至10%）：
- 调整后权重：A=0.91, B=0.09
- 权重倍数 = 0.91×1 + 0.09×3 = 1.18
- 1.18 > 1.0 ❌ 仍然不可行

触发紧急保护：使用A=90%, B=10%
```

### 5.3 紧急保护效果

| 场景 | 当前QPS | 紧急权重倍数 | 预期QPS | 资源利用率 |
|------|---------|-------------|---------|-----------|
| 场景3 | 900 | 1.2 | 1080 | 120% |
| 场景4 | 950 | 1.2 | 1140 | 126.7% |

## 6. 结果构建过程

### 6.1 WeightCalculationResult对象结构

```java
public class WeightCalculationResult {
    private CalculationStatus status;           // 计算状态
    private Map<String, Double> finalWeights;  // 最终权重
    private double expectedQps;                 // 预期QPS
    private double resourceUtilization;         // 资源利用率
    private String adjustmentReason;            // 调整原因
    private long calculationTimeMs;             // 计算耗时
    private List<AdjustmentStep> adjustmentSteps; // 调整步骤
}
```

### 6.2 关键指标计算

#### 资源利用率计算：
```java
double resourceUtilization = expectedQps / safeMaxQps;
```

#### 执行耗时计算：
```java
long calculationTime = System.currentTimeMillis() - startTime;
```

#### 调整步骤记录：
每次权重调整都会记录：
- 步骤描述
- 调整后权重
- 权重倍数
- 预期QPS
- 是否可行

## 7. 算法特点总结

### 7.1 核心优势

1. **容量优先**：始终确保系统在安全容量范围内运行
2. **智能调整**：优先减少高资源消耗策略的权重
3. **渐进优化**：通过多轮调整寻找最优解
4. **兜底保护**：提供紧急保护机制确保系统可用性

### 7.2 算法复杂度

- **时间复杂度**：O(n)，其中n为缩放因子数量（固定为5）
- **空间复杂度**：O(m)，其中m为调整步骤数量
- **执行效率**：平均耗时1-15ms

### 7.3 适用场景

- ✅ 双策略权重调整
- ✅ 基于QPS的容量控制
- ✅ 实时动态调整
- ✅ 紧急保护机制
- ✅ 详细的调整过程记录
