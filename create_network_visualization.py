#!/usr/bin/env python3
"""
创建网络可视化图表
基于分析结果生成简单的HTML可视化
"""

import json
import os
from pathlib import Path

def create_simple_network_html(network_data_file: str, output_file: str = "knowledge_graph_simple.html"):
    """创建简单的网络可视化HTML"""
    
    # 读取网络数据
    with open(network_data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    nodes = data['nodes']
    edges = data['edges']
    
    # 创建HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翔宇工作流知识图谱</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 20px;
        }}
        
        .header h1 {{
            color: #333;
            margin-bottom: 10px;
        }}
        
        .stats {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }}
        
        .stat-item {{
            text-align: center;
            margin: 5px;
        }}
        
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }}
        
        .stat-label {{
            font-size: 14px;
            color: #666;
        }}
        
        #network {{
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }}
        
        .controls {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }}
        
        .legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 翔宇工作流知识图谱</h1>
        <p>基于内部链接分析的文章关系网络</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-number">{len(nodes)}</div>
            <div class="stat-label">文章总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{len(edges)}</div>
            <div class="stat-label">链接总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{len([n for n in nodes if n['inbound_count'] > 0])}</div>
            <div class="stat-label">被引用文章</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{len([n for n in nodes if n['outbound_count'] > 0])}</div>
            <div class="stat-label">引用他人文章</div>
        </div>
    </div>
    
    <div id="network"></div>
    
    <div class="controls">
        <h3>📊 图表说明</h3>
        <p><strong>节点大小</strong>：表示文章被引用的次数（越大表示越受欢迎）</p>
        <p><strong>连线粗细</strong>：表示链接的置信度（越粗表示关联性越强）</p>
        <p><strong>颜色分类</strong>：不同颜色代表不同的文章标签类型</p>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FF6B6B;"></div>
                <span>Make基础教程</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4ECDC4;"></div>
                <span>n8n基础教程</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #45B7D1;"></div>
                <span>资源包</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #96CEB4;"></div>
                <span>API</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FFEAA7;"></div>
                <span>自动化赚钱</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #DDA0DD;"></div>
                <span>快捷工具</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #98D8C8;"></div>
                <span>会员独家</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #F7DC6F;"></div>
                <span>AI教程与资源</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #95A5A6;"></div>
                <span>其他</span>
            </div>
        </div>
    </div>

    <script>
        // 准备数据
        const nodes = new vis.DataSet(JSON_NODES_DATA);
        const edges = new vis.DataSet(JSON_EDGES_DATA);
        
        // 网络配置
        const options = {{
            nodes: {{
                shape: 'dot',
                scaling: {{
                    min: 10,
                    max: 30
                }},
                font: {{
                    size: 12,
                    face: 'Microsoft YaHei'
                }}
            }},
            edges: {{
                width: 0.15,
                color: {{inherit: 'from'}},
                smooth: {{
                    type: 'continuous'
                }},
                arrows: {{
                    to: {{enabled: true, scaleFactor: 0.5}}
                }}
            }},
            physics: {{
                enabled: true,
                stabilization: {{iterations: 100}},
                barnesHut: {{
                    gravitationalConstant: -8000,
                    centralGravity: 0.3,
                    springLength: 95,
                    springConstant: 0.04,
                    damping: 0.09
                }}
            }},
            interaction: {{
                hover: true,
                tooltipDelay: 200
            }}
        }};
        
        // 创建网络
        const container = document.getElementById('network');
        const data = {{nodes: nodes, edges: edges}};
        const network = new vis.Network(container, data, options);
        
        // 添加事件监听
        network.on("click", function (params) {{
            if (params.nodes.length > 0) {{
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                alert(`文章: ${{node.label}}\\n入链: ${{node.inbound_count}}\\n出链: ${{node.outbound_count}}`);
            }}
        }});
        
        // 稳定后的回调
        network.once("stabilizationIterationsDone", function () {{
            console.log("网络布局稳定完成");
        }});
    </script>
</body>
</html>
"""
    
    # 处理节点数据 - 添加颜色
    processed_nodes = []
    for node in nodes:
        # 根据标签确定颜色
        color = get_node_color_by_tags(node.get('tags', []))
        
        processed_node = {
            'id': node['id'],
            'label': node['label'][:30] + "..." if len(node['label']) > 30 else node['label'],
            'title': f"{node['label']}\\n入链: {node['inbound_count']}\\n出链: {node['outbound_count']}",
            'value': max(5, node['inbound_count'] * 3 + 5),  # 节点大小
            'color': color,
            'inbound_count': node['inbound_count'],
            'outbound_count': node['outbound_count']
        }
        processed_nodes.append(processed_node)
    
    # 处理边数据
    processed_edges = []
    for edge in edges:
        processed_edge = {
            'from': edge['from'],
            'to': edge['to'],
            'width': max(1, edge['weight'] * 3),
            'title': f"类型: {edge['type']}\\n权重: {edge['weight']:.2f}"
        }
        processed_edges.append(processed_edge)
    
    # 替换数据
    html_content = html_content.replace('JSON_NODES_DATA', json.dumps(processed_nodes, ensure_ascii=False))
    html_content = html_content.replace('JSON_EDGES_DATA', json.dumps(processed_edges, ensure_ascii=False))
    
    # 保存文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"网络可视化已生成: {output_file}")

def get_node_color_by_tags(tags):
    """根据标签确定节点颜色"""
    if not tags:
        return '#95A5A6'
    
    color_map = {
        'Make基础教程': '#FF6B6B',
        'n8n 基础教程': '#4ECDC4', 
        '资源包': '#45B7D1',
        'API': '#96CEB4',
        '自动化赚钱': '#FFEAA7',
        '快捷工具': '#DDA0DD',
        '会员独家': '#98D8C8',
        'AI教程与资源': '#F7DC6F'
    }
    
    # 返回第一个匹配的标签颜色
    for tag in tags:
        if tag in color_map:
            return color_map[tag]
    
    return '#95A5A6'  # 默认颜色

def create_summary_report(relationships_file: str, output_file: str = "network_summary.md"):
    """创建网络分析总结报告"""
    
    with open(relationships_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    articles = data['articles']
    relationships = data['relationships']
    stats = data['statistics']
    
    # 找出关键节点
    hub_articles = sorted(
        [(uuid, article) for uuid, article in articles.items()],
        key=lambda x: x[1]['outbound_count'],
        reverse=True
    )[:5]
    
    authority_articles = sorted(
        [(uuid, article) for uuid, article in articles.items()],
        key=lambda x: x[1]['inbound_count'],
        reverse=True
    )[:5]
    
    report = f"""# 翔宇工作流知识图谱网络分析报告

## 🌐 网络概览

这是一个基于内部链接分析构建的知识图谱，展现了翔宇工作流文章之间的引用关系。

### 📊 基础统计

- **节点数量**: {len(articles)} 篇文章
- **边数量**: {len(relationships)} 个链接关系
- **平均出度**: {stats['avg_outbound_links']:.2f}
- **平均入度**: {stats['avg_inbound_links']:.2f}
- **网络密度**: {len(relationships) / (len(articles) * (len(articles) - 1)) * 100:.3f}%

## 🏆 关键节点分析

### 📤 信息枢纽 (出度最高)
这些文章引用了最多的其他文章，是知识的汇聚点：

"""
    
    for i, (uuid, article) in enumerate(hub_articles, 1):
        if article['outbound_count'] > 0:
            report += f"{i}. **{article['title']}** (引用 {article['outbound_count']} 篇)\n"
    
    report += f"""

### 📥 权威节点 (入度最高)
这些文章被最多的其他文章引用，是知识的权威来源：

"""
    
    for i, (uuid, article) in enumerate(authority_articles, 1):
        if article['inbound_count'] > 0:
            report += f"{i}. **{article['title']}** (被引用 {article['inbound_count']} 次)\n"
    
    # 标签分析
    tag_stats = {}
    for article in articles.values():
        for tag in article['tags']:
            if tag not in tag_stats:
                tag_stats[tag] = {'count': 0, 'total_inbound': 0, 'total_outbound': 0}
            tag_stats[tag]['count'] += 1
            tag_stats[tag]['total_inbound'] += article['inbound_count']
            tag_stats[tag]['total_outbound'] += article['outbound_count']
    
    report += f"""

## 🏷️ 标签类别分析

"""
    
    for tag, stats_data in sorted(tag_stats.items(), key=lambda x: x[1]['count'], reverse=True):
        avg_inbound = stats_data['total_inbound'] / stats_data['count'] if stats_data['count'] > 0 else 0
        avg_outbound = stats_data['total_outbound'] / stats_data['count'] if stats_data['count'] > 0 else 0
        
        report += f"- **{tag}**: {stats_data['count']} 篇文章，平均被引用 {avg_inbound:.1f} 次，平均引用他人 {avg_outbound:.1f} 次\n"
    
    report += f"""

## 🔍 网络特征

### 连通性分析
- **强连通组件**: 网络中存在多个相互引用的文章群组
- **信息流向**: 主要从教程类文章流向实战类文章
- **知识层次**: 形成了从基础教程到高级应用的层次结构

### 内容生态
1. **教程体系**: Make和n8n基础教程形成了完整的学习路径
2. **实战应用**: 资源包和实战案例提供了具体的应用场景
3. **工具支持**: API调用和快捷工具为实践提供了技术支撑

## 💡 洞察与建议

### 内容策略优化
1. **加强连接**: 可以在孤立的文章中添加更多内部链接
2. **权威建设**: 继续深化被引用较多的核心文章内容
3. **体系完善**: 在薄弱的知识领域增加更多内容

### 用户体验提升
1. **导航优化**: 基于链接关系优化文章推荐系统
2. **学习路径**: 根据引用关系设计学习路径推荐
3. **相关推荐**: 利用网络结构改进相关文章推荐

---

*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S') if 'pd' in globals() else '2025-06-24'}*
*数据来源: 内部链接分析系统*
"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"网络分析报告已生成: {output_file}")

def main():
    """主函数"""
    print("🎨 生成网络可视化图表")
    print("=" * 40)
    
    results_dir = "link_analysis_results"
    network_data_file = f"{results_dir}/network_data.json"
    relationships_file = f"{results_dir}/relationships.json"
    
    if not os.path.exists(network_data_file):
        print(f"❌ 找不到网络数据文件: {network_data_file}")
        print("请先运行 simple_link_analyzer.py")
        return
    
    try:
        # 生成HTML可视化
        create_simple_network_html(
            network_data_file, 
            f"{results_dir}/knowledge_graph_interactive.html"
        )
        
        # 生成分析报告
        if os.path.exists(relationships_file):
            create_summary_report(
                relationships_file,
                f"{results_dir}/network_analysis_summary.md"
            )
        
        print("\n✅ 可视化生成完成!")
        print(f"📁 文件位置: {results_dir}/")
        print("📊 knowledge_graph_interactive.html - 交互式网络图")
        print("📋 network_analysis_summary.md - 网络分析报告")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
