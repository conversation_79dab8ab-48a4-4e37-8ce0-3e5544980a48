import java.util.*;

/**
 * 缩放因子对比演示程序
 * 对比原始算法和改进算法的效果
 */
public class ScaleFactorComparisonDemo {
    
    public static void main(String[] args) {
        ScaleFactorComparisonDemo demo = new ScaleFactorComparisonDemo();
        
        // 创建测试配置
        DynamicStrategyConfig config = demo.createTestConfig();
        
        // 创建服务实例
        WeightCalculationService originalService = new WeightCalculationService();
        ImprovedWeightCalculationService improvedService = new ImprovedWeightCalculationService();
        
        System.out.println("==========================================");
        System.out.println("        缩放因子算法对比演示");
        System.out.println("==========================================");
        
        // 测试场景：中等负载
        double testQps = 400;
        
        System.out.println("\n🔍 测试场景：QPS=" + testQps);
        System.out.println("目标权重：A=10%, B=90%");
        System.out.println("adjustmentStep：" + config.getAdjustmentStep());
        
        // 原始算法
        System.out.println("\n📊 原始算法结果：");
        System.out.println("==========================================");
        WeightCalculationResult originalResult = originalService.calculateOptimalWeights(testQps, config);
        demo.printResult(originalResult);
        
        // 改进算法
        System.out.println("\n📊 改进算法结果：");
        System.out.println("==========================================");
        WeightCalculationResult improvedResult = improvedService.calculateOptimalWeights(testQps, config);
        demo.printResult(improvedResult);
        
        // 对比分析
        System.out.println("\n📈 对比分析：");
        System.out.println("==========================================");
        demo.compareResults(originalResult, improvedResult);
        
        // 缩放因子分析
        System.out.println("\n🔬 缩放因子分析：");
        System.out.println("==========================================");
        demo.analyzeScaleFactors(config);
    }
    
    /**
     * 创建测试配置
     */
    private DynamicStrategyConfig createTestConfig() {
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("A", new StrategyInfo(1, 150));
        strategies.put("B", new StrategyInfo(3, 150));
        
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("A", 0.1);
        targetWeights.put("B", 0.9);
        
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("A", 0.9);
        emergencyWeights.put("B", 0.1);
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("高负载保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31854", "XX,YY | *", true, "RTP",
            "test_biz", 1000, 0.9, 0.1, 50,
            strategies, targetWeights, emergencyConfig
        );
    }
    
    /**
     * 打印结果
     */
    private void printResult(WeightCalculationResult result) {
        System.out.println("状态: " + result.getStatus());
        System.out.println("最终权重: " + formatWeights(result.getFinalWeights()));
        System.out.println("预期QPS: " + String.format("%.0f", result.getExpectedQps()));
        System.out.println("资源利用率: " + String.format("%.1f%%", result.getResourceUtilization() * 100));
        System.out.println("计算耗时: " + result.getCalculationTimeMs() + "ms");
        System.out.println("调整步骤数: " + (result.getAdjustmentSteps() != null ? result.getAdjustmentSteps().size() : 0));
    }
    
    /**
     * 对比结果
     */
    private void compareResults(WeightCalculationResult original, WeightCalculationResult improved) {
        System.out.println("算法对比：");
        System.out.println("┌─────────────┬─────────────┬─────────────┐");
        System.out.println("│    指标     │   原始算法   │   改进算法   │");
        System.out.println("├─────────────┼─────────────┼─────────────┤");
        System.out.printf("│ 最终状态    │ %-11s │ %-11s │\n", original.getStatus(), improved.getStatus());
        System.out.printf("│ A策略权重   │ %-11.2f │ %-11.2f │\n", 
            original.getFinalWeights().get("A"), improved.getFinalWeights().get("A"));
        System.out.printf("│ B策略权重   │ %-11.2f │ %-11.2f │\n", 
            original.getFinalWeights().get("B"), improved.getFinalWeights().get("B"));
        System.out.printf("│ 预期QPS     │ %-11.0f │ %-11.0f │\n", 
            original.getExpectedQps(), improved.getExpectedQps());
        System.out.printf("│ 资源利用率  │ %-10.1f%% │ %-10.1f%% │\n", 
            original.getResourceUtilization() * 100, improved.getResourceUtilization() * 100);
        System.out.printf("│ 调整步骤数  │ %-11d │ %-11d │\n", 
            original.getAdjustmentSteps().size(), improved.getAdjustmentSteps().size());
        System.out.printf("│ 计算耗时    │ %-10dms │ %-10dms │\n", 
            original.getCalculationTimeMs(), improved.getCalculationTimeMs());
        System.out.println("└─────────────┴─────────────┴─────────────┘");
    }
    
    /**
     * 分析缩放因子
     */
    private void analyzeScaleFactors(DynamicStrategyConfig config) {
        System.out.println("缩放因子设计分析：");
        System.out.println();
        
        // 原始硬编码缩放因子
        double[] originalFactors = {0.8, 0.6, 0.4, 0.2, 0.1};
        System.out.println("原始硬编码缩放因子: " + Arrays.toString(originalFactors));
        
        // 基于adjustmentStep生成的缩放因子
        List<Double> stepBasedFactors = generateStepBasedFactors(config.getAdjustmentStep());
        System.out.println("基于adjustmentStep生成: " + stepBasedFactors);
        
        // 自适应缩放因子（不同负载下）
        System.out.println("\n自适应缩放因子（不同负载压力下）：");
        double[] loadPressures = {0.5, 0.8, 0.95};
        String[] loadLabels = {"低负载", "中负载", "高负载"};
        
        for (int i = 0; i < loadPressures.length; i++) {
            List<Double> adaptiveFactors = generateAdaptiveFactors(loadPressures[i], config.getAdjustmentStep());
            System.out.printf("%s(%.1f): %s\n", loadLabels[i], loadPressures[i], adaptiveFactors);
        }
        
        // 权重倍数变化分析
        System.out.println("\n权重倍数变化分析：");
        analyzeMultiplierChanges(originalFactors, config);
    }
    
    /**
     * 生成基于步长的缩放因子
     */
    private List<Double> generateStepBasedFactors(double adjustmentStep) {
        List<Double> factors = new ArrayList<>();
        for (double factor = 1.0 - adjustmentStep; factor >= 0.1; factor -= adjustmentStep) {
            factors.add(Math.round(factor * 100.0) / 100.0); // 保留两位小数
        }
        if (!factors.contains(0.1)) {
            factors.add(0.1);
        }
        return factors;
    }
    
    /**
     * 生成自适应缩放因子
     */
    private List<Double> generateAdaptiveFactors(double loadPressure, double baseStep) {
        List<Double> factors = new ArrayList<>();
        double step;
        
        if (loadPressure < 0.7) {
            step = baseStep / 2; // 细粒度
        } else if (loadPressure < 0.9) {
            step = baseStep; // 标准粒度
        } else {
            step = baseStep * 2; // 粗粒度
        }
        
        for (double factor = 1.0 - step; factor >= 0.1; factor -= step) {
            factors.add(Math.round(factor * 100.0) / 100.0);
        }
        
        // 限制数量，避免过多
        if (factors.size() > 8) {
            factors = factors.subList(0, 8);
        }
        
        return factors;
    }
    
    /**
     * 分析权重倍数变化
     */
    private void analyzeMultiplierChanges(double[] scaleFactors, DynamicStrategyConfig config) {
        double originalA = 0.1, originalB = 0.9;
        double previousMultiplier = originalA * 1 + originalB * 3; // 2.8
        
        System.out.println("缩放因子 | B权重 | A权重 | 权重倍数 | 倍数降幅 | 降幅率");
        System.out.println("-------|-------|-------|----------|----------|--------");
        
        for (double scale : scaleFactors) {
            double newB = originalB * scale;
            double newA = originalA + (originalB - newB);
            double multiplier = newA * 1 + newB * 3;
            double reduction = previousMultiplier - multiplier;
            double reductionRate = reduction / previousMultiplier * 100;
            
            System.out.printf("%.1f     | %.2f  | %.2f  | %.2f     | %.2f     | %.1f%%\n", 
                scale, newB, newA, multiplier, reduction, reductionRate);
            
            previousMultiplier = multiplier;
        }
    }
    
    /**
     * 格式化权重显示
     */
    private String formatWeights(Map<String, Double> weights) {
        StringBuilder sb = new StringBuilder("{");
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            sb.append(entry.getKey()).append("=").append(String.format("%.2f", entry.getValue())).append(", ");
        }
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 2);
        }
        sb.append("}");
        return sb.toString();
    }
}
