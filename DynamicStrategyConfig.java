import java.util.Map;

/**
 * 动态策略配置类
 */
public class DynamicStrategyConfig {
    private String scenId;
    private String abId;
    private boolean switchOn;
    private String type;
    private String bizName;
    private int maxQPS;
    private double safetyBuffer;
    private double adjustmentStep;
    private int maxRT;
    private Map<String, StrategyInfo> strategies;
    private Map<String, Double> targetWeights;
    private EmergencyConfig emergencyConfig;

    // 构造函数
    public DynamicStrategyConfig() {}

    public DynamicStrategyConfig(String scenId, String abId, boolean switchOn, String type, 
                               String bizName, int maxQPS, double safetyBuffer, double adjustmentStep, 
                               int maxRT, Map<String, StrategyInfo> strategies, 
                               Map<String, Double> targetWeights, EmergencyConfig emergencyConfig) {
        this.scenId = scenId;
        this.abId = abId;
        this.switchOn = switchOn;
        this.type = type;
        this.bizName = bizName;
        this.maxQPS = maxQPS;
        this.safetyBuffer = safetyBuffer;
        this.adjustmentStep = adjustmentStep;
        this.maxRT = maxRT;
        this.strategies = strategies;
        this.targetWeights = targetWeights;
        this.emergencyConfig = emergencyConfig;
    }

    // Getter和Setter方法
    public String getScenId() { return scenId; }
    public void setScenId(String scenId) { this.scenId = scenId; }

    public String getAbId() { return abId; }
    public void setAbId(String abId) { this.abId = abId; }

    public boolean isSwitchOn() { return switchOn; }
    public void setSwitchOn(boolean switchOn) { this.switchOn = switchOn; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getBizName() { return bizName; }
    public void setBizName(String bizName) { this.bizName = bizName; }

    public int getMaxQPS() { return maxQPS; }
    public void setMaxQPS(int maxQPS) { this.maxQPS = maxQPS; }

    public double getSafetyBuffer() { return safetyBuffer; }
    public void setSafetyBuffer(double safetyBuffer) { this.safetyBuffer = safetyBuffer; }

    public double getAdjustmentStep() { return adjustmentStep; }
    public void setAdjustmentStep(double adjustmentStep) { this.adjustmentStep = adjustmentStep; }

    public int getMaxRT() { return maxRT; }
    public void setMaxRT(int maxRT) { this.maxRT = maxRT; }

    public Map<String, StrategyInfo> getStrategies() { return strategies; }
    public void setStrategies(Map<String, StrategyInfo> strategies) { this.strategies = strategies; }

    public Map<String, Double> getTargetWeights() { return targetWeights; }
    public void setTargetWeights(Map<String, Double> targetWeights) { this.targetWeights = targetWeights; }

    public EmergencyConfig getEmergencyConfig() { return emergencyConfig; }
    public void setEmergencyConfig(EmergencyConfig emergencyConfig) { this.emergencyConfig = emergencyConfig; }

    @Override
    public String toString() {
        return "DynamicStrategyConfig{" +
                "scenId='" + scenId + '\'' +
                ", maxQPS=" + maxQPS +
                ", safetyBuffer=" + safetyBuffer +
                ", targetWeights=" + targetWeights +
                ", emergencyConfig=" + emergencyConfig +
                '}';
    }
}
