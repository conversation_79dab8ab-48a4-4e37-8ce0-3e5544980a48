/**
 * 策略信息类
 */
public class StrategyInfo {
    private int batchNum;
    private int count;

    public StrategyInfo() {}

    public StrategyInfo(int batchNum, int count) {
        this.batchNum = batchNum;
        this.count = count;
    }

    public int getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(int batchNum) {
        this.batchNum = batchNum;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "StrategyInfo{" +
                "batchNum=" + batchNum +
                ", count=" + count +
                '}';
    }
}
