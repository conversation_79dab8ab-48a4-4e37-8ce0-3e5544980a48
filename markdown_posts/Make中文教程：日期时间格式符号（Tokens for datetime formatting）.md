---
title: "Make中文教程：日期时间格式符号（Tokens for date/time formatting）"
created_at: "2024-09-19T09:26:38.000000Z"
uuid: "b4c1b904-0efc-4049-abb2-8665ad97e5a8"
tags: ['Make基础教程']
---

# Make中文教程：日期时间格式符号（Tokens for date/time formatting）

**标签**: Make基础教程

# **Make平台日期时间格式化完全指南**


## **教程说明**

在自动化工作流中，时间格式化就像是一位"时间翻译官"，可以将机器时间翻译成人类易读的格式。本教程将帮助你掌握这门"翻译技术"。


## **学习目标**

  - 掌握Make平台的日期格式化tokens

  - 能够自由转换各种日期时间格式

  - 解决实际工作中的时间展示需求


## **详细内容**


### **1\. 年、月和日格式化**

![](https://static.xiaobot.net/file/2024-11-19/720345/3344f7c3cb4f0825c27ab755bf77c46a.png)

**实用示例**

    // 完整年份-月份
    {{formatDate(now, "YYYY-MM")}}  // 2024-01
    // 季度显示
    {{formatDate(now, "Qo quarter")}}  // 1st quarter
    // 带序数的日期
    {{formatDate(now, "Do MMM YYYY")}}  // 15th Jan 2024


### **2\. 周相关格式化**

![](https://static.xiaobot.net/file/2024-11-19/720345/805b7f493b2a1bb74bcb0b9ea17061fa.png)

**应用场景**

    // 完整星期显示
    {{formatDate(now, "dddd")}}  // Monday
    // 年度第几周
    {{formatDate(now, "wo week")}}  // 2nd week
    // ISO周格式
    {{formatDate(now, "GGGG-[W]WW")}}  // 2024-W02


### **3\. 时间格式化**

![](https://static.xiaobot.net/file/2024-11-19/720345/49861cc46b74ffb8cadc91b2ae77bf78.png)

**常用组合**

    // 12小时制
    {{formatDate(now, "hh:mm A")}}  // 02:30 PM
    // 24小时制
    {{formatDate(now, "HH:mm:ss")}}  // 14:30:25
    // 带时区
    {{formatDate(now, "HH:mm Z")}}  // 14:30 +08:00


## **实战练习**


### **1\. 基础格式转换**

要求：将当前时间转换为以下格式

    // 年月格式
    {{formatDate(now, "YYYY-MM")}}
    // 星期和日期
    {{formatDate(now, "dddd Do")}}
    // 12小时制时间
    {{formatDate(now, "hh:mm A")}}


### **2\. 复杂格式组合**

    // 完整日期时间
    {{formatDate(now, "YYYY-MM-DD dddd HH:mm:ss")}}
    // 美式日期格式
    {{formatDate(now, "MMM Do, YYYY h:mm A")}}
    // ISO标准格式
    {{formatDate(now, "YYYY-MM-DDTHH:mm:ssZ")}}


## 


## **常见问题解答**

  1. **Q: 如何处理不同时区？** A: 使用 Z 或 ZZ tokens，例如：
         
         {{formatDate(now, "YYYY-MM-DD HH:mm Z")}}

  2. **Q: 如何显示本地化的月份名称？** A: 使用 MMMM token：
         
         {{formatDate(now, "MMMM")}}

  3. **Q: 如何处理季度显示？** A: 使用 Q token：
         
         {{formatDate(now, "Qo [quarter of] YYYY")}}


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载** ！

![](https://static.xiaobot.net/file/2024-11-19/720345/f009cb1750762624fef38b50ef0bc778.png)