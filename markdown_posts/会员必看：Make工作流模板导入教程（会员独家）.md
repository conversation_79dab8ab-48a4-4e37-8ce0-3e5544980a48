---
title: "会员必看：Make工作流模板导入教程（会员独家）"
created_at: "2024-08-23T13:49:43.000000Z"
uuid: "03a57a85-017d-4433-8b69-85ac73779039"
tags: ['Make高级应用']
---

# 会员必看：Make工作流模板导入教程（会员独家）

**标签**: Make高级应用

欢迎阅读本教程，这是翔宇工作流频道**Make基础教程** 的配套文档，旨在帮助您在Make平台上导入并设置翔宇工作流提供的模板文件。通过此指南，您将能够快速建立并运行视频中演示的自动化工作流场景。


# 步骤摘要

1\. **登录Make账户并创建新场景**

2\. **导入翔宇工作流的蓝图文件**

3\. **配置各模块的账户和API密钥**

4\. **链接和配置Notion数据库**

5\. **保存并运行工作流**


# 详细操作步骤


## 1\. 登录Make账户并创建新场景

首先，请登录您的Make账户。如果您还没有账户，请先使用该网址[https://www.make.com/en/register?pc=xiangyugongzuoliu](<https://www.make.com/en/register?pc=xiangyugongzuoliu\)。>)注册一个Make账户。

![](https://static.xiaobot.net/file/2024-08-23/720345/3a183e727ec913ce2b2ab73b9c9e2a6e.png)

登录后，点击右上角的“**创建新场景** ”按钮，开始创建一个新的工作流场景。


## 2\. 导入翔宇工作流的蓝图文件

![](https://static.xiaobot.net/file/2024-08-23/720345/a49ce93777ce1c1b0968638a664cbb42.png)

在新场景的界面中，点击右上角的三点按钮，然后选择“**导入蓝图** ”。

![](https://static.xiaobot.net/file/2024-08-23/720345/5f2e8ab4e5b3f06a4782abe692ff6587.png)![](https://static.xiaobot.net/file/2024-08-23/720345/b3b41fcb8d2ac401df6d9837bdc0c91d.png)

选择您从翔宇工作流下载的模板文件，例如 1-从 RSS 到 Notion：如何利用Make实现自动更新知识库-知识库.json，然后点击“**保存** ”。

![](https://static.xiaobot.net/file/2024-08-23/720345/5f09da6d9e64584d4ae8946973a4bead.png)

导入完成后，您将在场景中看到该工作流的所有模块。


## 3\. 配置各模块的账户和API密钥

导入后的场景仍需配置您的账户信息，以便正确运行。

![](https://static.xiaobot.net/file/2024-08-23/720345/4d57050f7449277c775ce237ea8bdeff.png)

点击工作流中的每个模块，并选择您的账户进行授权。如果模块提示没有可用的账户，您需要添加新的账户。

![](https://static.xiaobot.net/file/2024-08-23/720345/c27cf6d4e3c72b7b8b96713a2981cd39.png)

请特别注意，**HTTP模块** 中的API密钥需要替换为您自己的API密钥。请检查每个模块，确保所有的API密钥都已正确配置。

![](https://static.xiaobot.net/file/2024-08-23/720345/1fbd1195007002e2fd8d5c2441b4ff4c.png)


## 4\. 链接和配置Notion数据库

对于涉及Notion模块的工作流，您需要将模块链接到您自己的Notion数据库。

1\. **复制Notion数据库模版**

打开翔宇工作流提供的Notion数据库模版链接，点击右上角的“**复制模版** ”按钮，将数据库复制到您的Notion工作空间。

![](https://static.xiaobot.net/file/2024-08-23/720345/87a0811b6a35d119443255995d1cf8e3.png)![](https://static.xiaobot.net/file/2024-08-23/720345/70be0cf31dc8a66d5a8816aaada0cfcf.png)

2\. **授权并连接Notion账户**

回到Make界面，点击左侧的“**连接** ”按钮，进入连接设置页面。

![](https://static.xiaobot.net/file/2024-08-23/720345/871f1ef004ca3c45d746c5804b23ae3a.png)

在Notion的连接中，选择您的Notion账户进行授权。如果没有已授权的账户，您需要添加一个新的Notion账户授权。

![](https://static.xiaobot.net/file/2024-08-23/720345/bb1ce8a647155326ea9d62735d016bd1.png)

3\. **选择新的Notion数据库**

在出现的页面中，选择新复制的Notion数据库页面，并将其添加到连接中。

![](https://static.xiaobot.net/file/2024-08-23/720345/dc9f178bc0285cc1c9bcb8cb7e23885d.png)![](https://static.xiaobot.net/file/2024-08-23/720345/9fb2569ddc620f1370d0c6121ac9d643.png)

4\. **配置Notion模块**

返回Make场景，对Notion模块进行相应修改，选择正确的账户，并链接到新添加的Notion数据库。

![](https://static.xiaobot.net/file/2024-08-23/720345/9200d1e1eaac98cd28b2764ec8be899d.png)

**5\. 保存并运行工作流**

所有设置完成后，请务必点击“**保存** ”按钮，确保所有配置生效。

![](https://static.xiaobot.net/file/2024-08-23/720345/8b7d4e2f1505d0daee84a908e1cc6c9b.png)

最后，点击运行图标启动工作流，并观察其是否按预期执行。


# 常见问题及排查

1\. **模块未运行或报错** ：检查API密钥是否已正确配置，并确认所有账户已正确授权。

2\. **Notion数据库无法连接** ：确保您已在Notion中正确复制了模版，并已在Make中重新授权并链接到新的数据库。

3\. **工作流执行异常** ：检查每个模块的设置，确保链接和授权配置无误。

通过本教程，您可以顺利导入并配置翔宇工作流提供的蓝图文件，将其转化为高效的自动化工作流。感谢您对翔宇工作流频道的支持！