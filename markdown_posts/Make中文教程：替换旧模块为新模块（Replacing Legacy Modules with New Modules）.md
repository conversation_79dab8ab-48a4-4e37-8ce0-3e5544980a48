---
title: "Make中文教程：替换旧模块为新模块（Replacing Legacy Modules with New Modules）"
created_at: "2024-08-30T01:19:07.000000Z"
uuid: "cbef1790-46c0-4f9f-bcab-481d0f79cf1a"
tags: ['Make基础教程']
---

# Make中文教程：替换旧模块为新模块（Replacing Legacy Modules with New Modules）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于替换旧模块为新模块的教程。

**教程简介**  ： 本教程指导用户在Make.com中将使用旧版API的模块升级为新版模块，确保解决方案继续有效。步骤包括克隆解决方案、选择新模块、替换和设置新模块等。

**学习目标**  ： 学习如何有效替换旧版模块，以保证解决方案在旧版API停用后仍能正常运行。掌握克隆、升级模块及配置步骤，确保解决方案的连续性和稳定性。

**学习内容**  ：

  1. 克隆旧版解决方案

  2. 选择并升级模块

  3. 配置新模块

  4. 替换旧模块

  5. 设置并保存新解决方案

**概念理解**  ：

  1. **克隆解决方案**  ：创建旧解决方案的副本，用于安全升级。

  2. **模块替换**  ：将旧版API模块替换为相应的新模块。

  3. **映射值**  ：复制旧版模块配置到新版模块，确保数据一致性。

**任务练习**  ：

  1. 克隆一个使用旧版模块的解决方案。

  2. 在新解决方案中选择一个旧模块，并用新版模块替换。

  3. 开启旧解决方案标签，复制旧模块配置到新模块。

  4. 移除旧模块，并保存新解决方案。


## **为什么需要更新模块？**

随着外部服务API的演进，旧版API最终会被停用。为确保场景\(scenario\)持续运行，需要及时更新到新版模块。


## **更新步骤详解**


### **1\. 克隆现有场景**

  - 进入场景图表界面

  - 点击右上角选项菜单，选择"Clone"

![](https://static.xiaobot.net/file/2024-11-18/720345/c57e853198dcf4e38666cdc3ceb55e27.png)
  - 修改名称以区分新旧场景


### **2\. 查找需要更新的模块**

  - 找到带有绿色更新箭头的旧版模块

![](https://static.xiaobot.net/file/2024-11-18/720345/475c6aa3a0d5f7db65fb06315b59bd0c.png)


### **3\. 选择对应的新版模块**

![](https://static.xiaobot.net/file/2024-11-18/720345/396f1cd687542a432b140d62663a1d7a.png)

以Google Forms为例的模块对应关系：

**旧版模块新版模块** Watch responsesWatch ResponsesSelect responsesSearch ResponsesAdd a responseAdd a ResponseUpdate a responseUpdate a ResponseDelete a responseDelete a Response-Search Responses \(Advanced\)


### **4\. 替换流程**

触发器模块替换：

![](https://static.xiaobot.net/file/2024-11-18/720345/67726e974266beace75f8f92047c5a9b.png)

  - 复制旧模块配置到新模块

  - 移动时钟图标

  - 重新连接模块关系

其他模块替换：

![](https://static.xiaobot.net/file/2024-11-18/720345/e7ed83c0c13bf183322cdf4e47ae4931.png)


### **5\. 配置新模块**

推荐使用双标签页对比配置：

![](https://static.xiaobot.net/file/2024-11-18/720345/d8b8da1c8e5e8a713c2728e89f319632.png)

配置要点：

  - 复制各字段内容

  - 设置数据映射

  - 配置过滤器

![](https://static.xiaobot.net/file/2024-11-18/720345/6526c0b09ac514ce0c0eb20872533afc.png)


### **6\. 清理与保存**

删除所有旧版模块并保存场景：

![](https://static.xiaobot.net/file/2024-11-18/720345/12eff13ede0aca6f69c254168acfed48.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/69e0bb6d7af71263e1419a56a8455bd4.png)