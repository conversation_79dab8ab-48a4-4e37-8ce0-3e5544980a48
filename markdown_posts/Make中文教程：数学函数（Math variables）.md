---
title: "Make中文教程：数学函数（Math variables）"
created_at: "2024-09-19T09:50:49.000000Z"
uuid: "fe78bec9-691b-492c-9a42-df526ba222f9"
tags: ['Make基础教程']
---

# Make中文教程：数学函数（Math variables）

**标签**: Make基础教程

# **Make平台数学变量使用指南**

**翔宇工作流** 今天为大家带来关于数学变量的教程。

**教程简介** ：本教程介绍如何使用数学变量在Make.com中生成伪随机数，并通过具体示例展示如何将这些随机数应用于函数中，例如掷骰子游戏和生成随机字符串。

**学习目标** ：了解如何使用Make.com的数学变量功能生成随机数，并学习将这些随机数应用于实际场景，提升自动化流程的功能和互动性。

**学习内容** ：主要包括使用random变量生成伪随机数，使用数学公式生成指定范围内的随机数，以及实际应用示例如掷骰子游戏和生成随机字符串。

**概念理解** ：

  1. **random** ：生成一个介于 \[0, 1\) 的伪随机浮点数。

  2. **floor** ：取不大于给定数字的最大整数，在生成随机整数时使用。

  3. **公式** ：\{\{floor\(random \* \(max - min + 1\)\) + min\}\} 用于生成 \[min, max\] 区间内的随机整数。

**任务练习** ：为你的聊天机器人创建一个模拟掷币的功能。设定生成0或1的随机数，0表示正面，1表示反面，并将结果作为机器人的响应消息发送给用户。公式：\{\{floor\(random \* 2\)\}\}。


## **什么是数学变量？**

在Make平台中，数学变量可以帮助你在函数中插入随机数。想象它就像一个数字魔术师，每次都能变出不同的数字！


## **random变量详解**


### **基本概念**

random 变量会生成一个在 0（包含）到 1（不包含）之间的随机小数。

> 小贴士：这就像掷一个特殊的骰子，它能产生 0 到 1 之间的任何小数，但永远不会正好等于1。


### **生成指定范围的随机整数**

如果你想生成在特定范围内的随机整数（比如1到100之间），可以使用下面这个公式：

![](https://static.xiaobot.net/file/2024-11-19/720345/7da9148d33fe3eb4552074d5562c5c24.png)

> 通俗解释：
> 
>   1. 1.max 是你想要的最大数
> 
>   2. 1.min 是你想要的最小数
> 
>   3. 这个公式会确保生成的随机数落在这两个数之间（包含这两个数）
> 
> 


## **实际应用案例**


### **案例1：制作一个骰子游戏**

假设你想为聊天机器人制作一个掷骰子的游戏，可以这样做：

  1. 打开映射面板，找到数学函数选项卡：

![](https://static.xiaobot.net/file/2024-11-19/720345/fb81348a4566f7a5582d61adf734be89.png)

  2. 选择 floor 函数（用于取整）

  3. 按照以下步骤构建表达式：

     - 在括号中插入 random 变量

     - 加上 \* 乘号和数字 6（因为骰子有6面）

     - 最后加上 + 号和数字 1（确保最小值是1）

完整公式：

    {{floor(random * 6) + 1}}

> 小贴士：
> 
>   - 这个公式会生成1到6之间的随机整数
> 
>   - 每次运行都会得到一个新的随机数
> 
>   - 完美模拟了真实骰子的行为
> 
> 


### **案例2：生成随机字符串**

**实用场景举例：**

  1. 生成随机用户密码

  2. 创建验证码

  3. 生成唯一标识符

  4. 制作抽奖程序


## **使用技巧**


### **1\. 范围控制**

  - 想生成1-10的随机数：\{\{floor\(random \* 10\) + 1\}\}

  - 想生成0-9的随机数：\{\{floor\(random \* 10\)\}\}


### **2\. 常见应用**

  - 随机选择数组元素

  - 模拟概率事件

  - 创建测试数据

  - 实现游戏功能


## **注意事项**

  1. 随机数是伪随机的，适用于一般应用场景

  2. 对于需要高度安全性的场景（如加密），建议使用专业的加密服务

  3. 确保使用正确的数学运算符：

     - \* 用于乘法

     - + 用于加法

     - 括号用于控制运算优先级

希望这个指南能帮助你更好地理解和使用Make平台的数学变量！

![](https://static.xiaobot.net/file/2024-11-19/720345/e2781ff4b9a2ebf70ca7836397380d35.png)