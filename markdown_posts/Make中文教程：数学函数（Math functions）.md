---
title: "Make中文教程：数学函数（Math functions）"
created_at: "2024-09-19T08:19:46.000000Z"
uuid: "1aeb2ada-e81f-4914-9e15-807658afe2ed"
tags: ['Make基础教程']
---

# Make中文教程：数学函数（Math functions）

**标签**: Make基础教程

**翔宇工作流今天为大家带来关于数学函数的教程**

**教程简介：**

本教程介绍了Make.com中可用的各种数学函数，如绝对值、平均值、取整、格式化、最大值、最小值、四舍五入等，帮助用户执行不同的数学操作。通过本教程，您将学会如何使用这些函数来处理数据，完成诸如计算平均值、取整和格式化数值等操作。

**学习目标：**

  - 了解Make.com中的数学函数。

  - 学习如何执行常见的数学运算，如平均值计算、数值格式化、取整和计算标准差。

  - 学会如何使用这些函数高效地处理数值数据。

**学习内容：**

  1. **绝对值 \(abs\)**

  2. **平均值 \(average\)**

  3. **向上取整 \(ceil\) 和向下取整 \(floor\)**

  4. **格式化数字 \(formatNumber\)**

  5. **最大值 \(max\) 和最小值 \(min\)**

  6. **中位数 \(median\)**

  7. **数字解析 \(parseNumber\)**

  8. **四舍五入 \(round\)**

  9. **总体标准差 \(stdevP\) 和样本标准差 \(stdevS\)**

  10. **数字总和 \(sum\)**

  11. **截断 \(trunc\)**


### 1\. 绝对值 \(abs\)

**概念：** 返回给定数值的绝对值，也就是无论正负数，结果都是非负数。

**示例结构：**

abs\(number\)

**示例：**

  - abs\(-10\) = 10

  - abs\(10\) = 10

  - abs\(-3.7\) = 3.7

**应用场景：** 计算距离、比较数据大小时，去除负号的影响。


### 2\. 平均值 \(average\)

**概念：** 返回多个数值或数组中的平均值。

**示例结构：**

**average  \(\[array of values\]\) 或average\(value1; \[value2\], ...\)**

**average\(\[array of values\]\)**

• **参数** ：一个包含数值的数组 array of values。

• **功能** ：计算数组中所有数值的平均值。

• **示例** ：

average\(\[10, 20, 30, 40, 50\]\)

• **结果** ：30

• 解释：将数组中的所有数值相加 \(10 + 20 + 30 + 40 + 50 = 150\)，然后除以数组的长度 5，得到平均值 150 / 5 = 30。

**average\(value1; \[value2\], ...\)**

• **参数** ：可以传入任意多个单独的数值参数（用分号分隔）。

• **功能** ：计算所有参数值的平均值。

• **示例** ：

average\(10; 20; 30; 40; 50\)

• **结果** ：30

• 解释：将所有参数相加 \(10 + 20 + 30 + 40 + 50 = 150\)，然后除以参数的个数 5，得到平均值 150 / 5 = 30。

**总结**

• average\(\[array of values\]\) 适用于传入一个数组作为参数。

• average\(value1; \[value2\], ...\) 适用于直接传入多个单独数值。

这两个函数形式上的区别仅在于传入的数据形式不同，但功能都是计算给定数值的平均值。

**应用场景：** 计算班级的平均成绩、商品的平均价格等。


### 3\. 向上取整 \(ceil\) 和向下取整 \(floor\)

**概念：**

  - **ceil\(number\)** ：返回大于等于指定数的最小整数。

  - **floor\(number\)** ：返回小于等于指定数的最大整数。

**示例结构：**

ceil\(number\) 或 floor\(number\)

**示例：**

  - ceil\(1.7\) = 2

  - floor\(1.7\) = 1

**应用场景：** 计算最低或最高成本时使用，如为购物车计算最大值或最小值的整数字。


### 4\. 格式化数字 \(formatNumber\)

**概念：** 按特定格式返回数字，通常用来添加小数点和千位分隔符。

**示例结构：**

**formatNumber**  \(number; decimalPOINTS; \[decimalSeparator\]; \[thousandsSeparator\]\)

该公式是一个用于格式化数字的函数，通常用于将数字转换成具有特定小数位数的字符串表示形式，并应用自定义的千位分隔符和小数分隔符。

**参数说明：**

1\. number：要格式化的数字。

2\. decimalPOINTS：指定保留的小数位数。例如，如果设置为 2，结果将保留两位小数。

3\. decimalSeparator（可选）：用于指定小数分隔符，默认通常是 "."（点）。

4\. thousandsSeparator（可选）：用于指定千位分隔符，默认通常是 ","（逗号）。

**示例：**

  - formatNumber\( 123456789 ; 3 ; , ; . \)= 123.456.789,000

**应用场景：** 财务数据的展示、使数字更易读。


### 5\. 最大值 \(max\) 和最小值 \(min\)

**概念：** 返回数组或多个数值中的最大值或最小值。

**示例结构：**

max \(\[array of values\]\), max\(value1;value2; ...\)

**max\(\[array of values\]\)**

• **参数** ：一个包含数值的数组 \[array of values\]。

• **功能** ：找到数组中的最大值。

• **示例** ：

max\(\[10, 20, 30, 40, 50\]\)

• **结果** ：50

• 解释：在数组 \[10, 20, 30, 40, 50\] 中，最大值是 50。

**max\(value1; value2; ...\)**

• **参数** ：可以传入多个单独的数值参数（用分号分隔）。

• **功能** ：找到这些数值参数中的最大值。

• **示例** ：

max\(10; 20; 30; 40; 50\)

• **结果** ：50

• 解释：在给定的值 10, 20, 30, 40, 50 中，最大值是 50。

**总结**

• max\(\[array of values\]\) 用于传入一个包含数值的数组并找到其中的最大值。

• max\(value1; value2; ...\) 用于传入多个单独的数值参数并找到最大值。

这两种形式的主要区别在于输入数据的方式不同，但功能都是查找给定数值中的最大值。

**应用场景：** 查找最高分、最低价格等。


### 6\. 中位数 \(median\)

**概念：** 返回特定数组或多个数值的中位数。

**示例结构：**

median \(\[array of values\]\)

median\(\[array of values\]\) 是用于计算一组数值中位数的函数。

**参数说明：**

• \[array of values\]：包含一组数值的数组。

**功能：**

该函数会计算数组中的中位数，即将所有数值按升序排列后找到居中的数值。如果数组的元素数量为奇数，中位数是最中间的那个数；如果元素数量为偶数，中位数是中间两个数的平均值。

**示例：**

1\. **数组中元素为奇数个**

• **结果** ：6

• 解释：数组中有 7 个元素，将它们按顺序排列后，第 4 个元素 6 是中位数。

**应用场景：** 计算一组数据的典型值，避免极端值的影响。


### 7\. 数字解析 \(parseNumber\)

**概念：** 解析一个包含数字的字符串，返回该数字。

**示例结构：**

parseNumber \(number; decimal separator\)

parseNumber\(number; decimalSeparator\) 是一个用于将字符串形式的数字解析为数值的函数。它可以处理不同的小数分隔符的格式，使字符串数值能够正确转换为实际的数字格式。

**参数说明：**

1\. number：要解析的字符串格式的数字。例如 "1,234.56" 或 "1.234,56"。

2\. decimalSeparator：指定的小数分隔符字符，表示数字中小数点的位置。常见的有 . 或 ,。

**功能：**

• 将带有特定小数分隔符的字符串解析为一个实际的数值类型。

• 该函数自动忽略千位分隔符，专注于将字符串转换为数字并按照指定的小数分隔符进行解析。

**示例：**

**使用**.**作为小数分隔符**

parseNumber\("1,234.56", "."\)

• **结果** ：1234.56

• 解释：这里的小数分隔符是 .，即字符串 1,234.56 解析为 1234.56，忽略 , 的千位分隔符。

**使用** ,**作为小数分隔符**

• **结果** ：1234.56

• 解释：小数分隔符为 ,，因此 1.234,56 解析为 1234.56，并忽略 . 的千位分隔符。

**总结**

• parseNumber 可将包含自定义小数分隔符的字符串转化为数值格式，方便不同地区数字格式的处理。

• 使用该函数时，确保正确指定小数分隔符，以避免解析错误。

**应用场景：** 从文本中提取数字，特别是用户输入的数据。


### 8\. 四舍五入 \(round\)

**概念：** 将数值四舍五入到最接近的整数。

**示例结构：**

round\(number\)

**示例：**

  - round\(1.2\) = 1

  - round\(1.7\) = 2

**应用场景：** 处理货币数据时，去除多余的小数部分。


### 9\. 总体标准差 \(stdevP\) 和样本标准差 \(stdevS\)

stdevP\(\[array of values\]\) 和 stdevS\(\[array of values\]\) 都是计算一组数的标准差，帮助我们了解数据的分散程度，但它们适用的场景不同：

1\. stdevP\(\[array of values\]\)：

• 用于计算**总体标准差** ，适合当你有一整套完整的数据时。

• 公式里用数据总数量 N 作为分母。

• 例如，如果你测量了每个员工的工资并想知道他们工资的总体标准差，用 stdevP。

2\. stdevS\(\[array of values\]\)：

• 用于计算**样本标准差** ，适合当你只有一部分数据（样本）时。

• 公式里用 N-1 作为分母，稍微调整以更准确地估计总体的分散程度。

• 例如，如果你调查了公司一部分员工的工资，并想估计整个公司的工资分散情况，用 stdevS。

**总结：**

• stdevP：用在完整数据（总体）上。

• stdevS：用在部分数据（样本）上。

**应用场景：** 数据分析中计算波动程度，帮助了解数据的离散情况。


### 10\. 数字总和 \(sum\)

**概念：** 返回特定数组或多个数值的总和。

**示例结构：**

sum \(\[array of values\]\)或sum\(value1;value2; ...\)

**示例：**

  - sum\(\[1, 2, 3, 4\]\) = 10

**应用场景：** 计算总销售额、多个数值相加的结果等。


### 11\. 截断 \(trunc\)

**概念：** 通过移除小数部分，将数字截断为整数。

trunc\(number; \[decimalPlaces\]\) 是一个用于截断数字的小数部分的函数。它可以将数字截断为整数，或者保留指定的小数位数。

**用法：**

1\. trunc\(number\)：将数字截断为整数，去掉小数部分。

2\. trunc\(number; decimalPlaces\)：将数字截断到指定的小数位数。

**参数：**

• number：要截断的数。

• decimalPlaces（可选）：要保留的小数位数。可以为正数或负数。

• **正数** ：保留的小数位数。例如，2 表示保留两位小数。

• **负数** ：将截断到左侧的位数。例如，-2 表示保留到百位。

**示例解释：**

1\. trunc\(3.789\)

• **结果** ：3

• 解释：截去小数部分，只保留整数。

2\. trunc\(3.789; 2\)

• **结果** ：3.78

• 解释：截断到两位小数，不进行四舍五入。

3\. trunc\(-3.789; 2\)

• **结果** ：-3.7

• 解释：截断到一位小数（负数处理方式相同）。

4\. trunc\(123.456; -2\)

• **结果** ：100

• 解释：截断到百位，把十位和个位去掉。 **总结**

trunc 函数可以灵活地去掉小数或指定位数的值，非常适合需要简单截断而不需要四舍五入的情况。

* * *


### **翔宇工作流：一个专注AI与自动化的频道**

翔宇工作流希望通过本教程，让您在Make.com上更轻松地创建和管理自动化工作流。更多关于自动化的内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-20/720345/268b42cc09a27b148de78a3816c59ccd.png)