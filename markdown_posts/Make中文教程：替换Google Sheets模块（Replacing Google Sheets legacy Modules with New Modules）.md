---
title: "Make中文教程：替换Google Sheets模块（Replacing Google Sheets legacy Modules with New Modules）"
created_at: "2024-08-30T01:26:32.000000Z"
uuid: "2e22c214-ccd9-4046-a2e0-304dc8e63212"
tags: ['Make基础教程']
---

# Make中文教程：替换Google Sheets模块（Replacing Google Sheets legacy Modules with New Modules）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于替换Google Sheets模块的教程。

  1. **教程简介**  ：教程介绍了如何将Make.com中的旧版Google Sheets模块替换为新版模块，以应对API升级，确保自动化场景正常运行。

  2. **学习目标**  ：学习如何在Make.com中将旧版Google Sheets模块替换为新版模块，以符合最新的API要求，确保自动化场景持续稳定运行。

  3. **学习内容**  ：主要内容包括克隆旧场景、同时打开旧场景和新场景、在新场景中替换模块、复制配置值、替换映射和过滤器中的旧项目，以及使用Make DevTool工具进行模块迁移。

  4. **概念理解**  ：场景指的是在Make.com中自动化任务的工作流程；模块是任务中的执行单元；API升级后需要更新模块，以确保数据交互的有效性；Make DevTool是一种用于辅助模块迁移的开发者工具。

  5. **任务练习**  ：创建一个使用旧Google Sheets模块的简单场景，按照教程步骤克隆并打开新旧场景，在新场景中替换旧模块为新模块，复制旧模块配置，并验证新场景是否正常运行。


# **Google Sheets旧版模块升级指南**


## **背景说明**

Make平台的大多数应用程序都依赖于外部服务的API。随着服务的发展，新的API版本会定期发布，而旧版API最终会被弃用和关闭。我们密切关注新API版本的发布，并基于最新API实现新的应用版本。为确保场景在旧版API关闭后仍能运行，请务必将所有旧模块更换为新模块。


## **模块对照速查表**

**旧版模块新版模块** Add a RowAdd a RowUpdate a RowUpdate a RowDelete a RowDelete a RowSelect RowsSearch RowsUpdate a CellUpdate a CellAdd a WorksheetAdd a SheetGet a CellGet a CellWatch a WorksheetWatch RowsList WorksheetsList SheetsDelete a WorksheetDelete a Sheet


## **详细升级步骤**


### **1\. 场景克隆准备**

  1. 找到使用旧模块的场景

  2. 克隆场景创建副本：

     - 进入场景图表

     - 点击右上角选项菜单

     - 选择"Clone"选项

     - 修改名称以区分新旧场景

![](https://static.xiaobot.net/file/2024-11-18/720345/6e935f9b1ddd63a5066e1a34a8c545fe.png)


### **2\. 优化工作方式**

建议使用双标签页方式操作：

  1. 在新标签页打开场景

  2. 分别打开新旧场景

![](https://static.xiaobot.net/file/2024-11-18/720345/98e740400e12a6a76e676a67cffbf22e.png)


### **3\. 模块替换流程**

  1. 在新场景中，找到需要升级的旧模块，点击升级箭头

![](https://static.xiaobot.net/file/2024-11-18/720345/bc503fcbbb7c5328d89d1fa74470a81a.png)

  2. 从模块列表中选择对应的新模块

![](https://static.xiaobot.net/file/2024-11-18/720345/981675c0eda9b35799ff6d11018b26a7.png)

  3. 拖放并连接新模块到旧模块之后

![](https://static.xiaobot.net/file/2024-11-18/720345/17cb3c613a26dfafedc6a4c2acddb19a.png)

对于触发器模块：

  - 按照旧模块的方式设置新模块的值

  - 用新模块替换旧模块

![](https://static.xiaobot.net/file/2024-11-18/720345/1e774c455722fb5b012bab76e773c56f.png)


### **4\. 配置迁移**

  1. 复制模块配置：

     - 打开新模块配置

     - 切换到旧场景标签页，打开旧模块配置

     - 复制每个字段的内容

![](https://static.xiaobot.net/file/2024-11-18/720345/0a7a5c46d6a0a16fca02ce683ef8a1e1.png)

  2. 更新后续模块的数据映射：

     - 选择使用旧模块输出的模块

     - 用新模块的对应输出项替换旧模块的输出项

![](https://static.xiaobot.net/file/2024-11-18/720345/add98d3cd94dbd5350e25da2186bb8c1.png)

  3. 更新过滤器中的映射项：

     - 找到使用旧模块输出的过滤器

     - 将条件中的旧模块输出项替换为新模块的对应项


### **5\. 使用Make DevTool快速迁移**

Make DevTool是一个强大的辅助工具：

  1. 安装步骤：

     - 为Chrome浏览器添加Make DevTool扩展

     - 打开Google Sheets \(legacy\)场景

     - 按Ctrl+Shift+I \(Windows\)或Cmd+Option+I \(Mac\)打开开发者工具

     - 转到Make标签页

     - 打开Tools

     - 点击Migrate GS磁贴

     - 点击Run按钮

![](https://static.xiaobot.net/file/2024-11-18/720345/7a448de18a200306c003bade4d78e530.png)


### **重要提醒**

  1. 完成迁移后：

     - 确保删除所有旧模块

     - 仔细测试新场景的所有功能

     - 检查所有数据映射和过滤器是否正确更新

  2. 建议事项：

     - 在实际使用前进行充分测试

     - 保留旧场景直到确认新场景完全可用

     - 记录所有更改以便追踪

  3. 常见问题：

     - 如果映射面板中缺少项目，请参考映射文档

     - 确保仔细检查触发器模块的设置

     - 注意保持数据完整性和一致性


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/1ab438c564149808d76bc325f85bb7f1.png)