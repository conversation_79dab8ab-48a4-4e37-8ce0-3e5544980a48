---
title: "Make中文教程：Make开发工具（Make DevTool）"
created_at: "2024-09-21T15:31:03.000000Z"
uuid: "8221e7d2-0991-44f8-8b09-e56cf2be289a"
tags: ['Make基础教程']
---

# Make中文教程：Make开发工具（Make DevTool）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于创建开发工具的教程。

  1. **教程简介** ：本教程介绍如何使用Make DevTool来调试和优化在Make平台上的自动化场景（Scenario）。通过Chrome开发者工具中的额外面板，用户可以查看和分析场景的操作细节，识别和解决问题。

  2. **学习目标** ：学习如何安装和使用Make DevTool，通过Chrome开发者工具调试Make场景，监控实时流和查看历史日志，优化场景设置和模块连接，提高自动化工作的效率和准确性。

  3. **学习内容** ：

     - 安装和设置Make DevTool

     - 使用实时流（Live Stream）查看场景操作

     - 使用场景调试器（Scenario Debugger）浏览历史日志

     - 使用工具集来管理和优化场景模块

     - 处理和分析API请求与响应

  4. **概念理解** ：

     - **场景（Scenario）** ：在Make中定义的自动化流程，由多个模块组成。

     - **实时流（Live Stream）** ：显示场景运行时的各个请求和响应的详细信息。

     - **场景调试器（Scenario Debugger）** ：提供场景历史日志，帮助用户分析和搜索模块操作。

     - **模块（Module）** ：场景中的单个操作单元，可执行特定任务或功能。

  5. **任务练习** ：

     - 练习在Chrome开发者工具中安装Make DevTool，并打开一个Make场景来调试。

     - 使用实时流功能查看一次场景运行的详细信息，包括请求头、请求体、响应头和响应体。

     - 在场景调试器中搜索某个模块，并查看其相关的请求和响应详情。

     - 使用工具集，比如“复制过滤器”，将一个模块的过滤器设置复制到另一个模块。


## **轻松检查和调整您的 Make 场景\!**

使用 Make DevTool 可以在 Chrome 开发者工具中添加一个额外的面板来调试你的 Make 场景。使用这个新的调试器面板,你可以检查你场景中所有的手动运行,查看所有执行的操作,并查看每个 API 调用的详细信息。此外,使用 Make DevTools 你还可以看到哪个模块、操作,甚至哪个单一响应会在你的场景中引发错误。这有助于你调试你的场景,并使你的场景重新运行。


### **  安装 Make DevTool**

  1. 打开 Chrome 网上应用店,搜索"Make DevTool"。你也可以使用这个直接链接来安装扩展程序。

  2. 单击"添加到 Chrome"按钮。在弹出的窗口中确认您的决定。

  3. 开发人员工具\(Make DevTool\)安装在 Chrome 开发者工具面板中。

要开始使用 Make DevTool,请在 Make 中打开场景,然后按 Control+Shift+I 或F12\(Windows\)或 Command+Option+I\(Mac\)打开 Chrome 开发者工具并切换到 Make 选项卡。


### **  注意**

我们建议将 Chrome 开发者控制台停靠在底部,以保持更好的模块视图。

![](https://static.xiaobot.net/file/2024-11-23/720345/7f2df87bc8d02d693660bca84b282d8b.png)


### **使用实时流（直播）**

直播显示在您点击"执行一次"按钮时场景背景中发生的情况。

它允许您查看您场景中每个模块的以下信息:

  - 请求头\(API 端点 URL、HTTP 方法、请求调用的时间和日期、请求头和查询字符串\)

  -  请求正文

  -  响应头

  -  响应正文

在运行场景后,从左侧面板选择"实时流",然后点击 Make DevTool 右侧面板中的一个选项卡以查看信息。

![](https://static.xiaobot.net/file/2024-11-23/720345/5d04b7c93edbab099604bdaa66590649.png)

**搜索日志**

在 Make DevTool 左侧面板的搜索字段中输入搜索词,只显示包含该搜索词的请求和响应。

![](https://static.xiaobot.net/file/2024-11-23/720345/f1e78e18ae53d1dc6484f53d8f787f93.png)

**清除已记录的请求列表**

要清除 Make DevTool 记录的请求列表,请点击搜索框旁边的垃圾桶图标。

**  启用控制台日志记录**

要启用控制台登录功能,请点击搜索框旁边的电脑图标。

当记录功能启用时,计算机图标的颜色会切换为绿色。

![](https://static.xiaobot.net/file/2024-11-23/720345/a6919f96e04012c1dd21613943fbf6bb.png)

如果您想要禁用日志记录,请再次单击相同的图标。该功能禁用时,图标会变为灰色。

**以原始 JSON 格式或 cURL 检索请求**

要获取请求的原始 JSON 内容,请点击 DevTool 面板右上角的"复制原始"按钮。

![](https://static.xiaobot.net/file/2024-11-23/720345/4ca8974dc72f85675f7d8f9c96d94777.png)

同样地，您可以使用"Make DevTool"面板右上角"Copy RAW"旁边的"Copy cURL"按钮检索 cURL。


### **场景调试器**

场景调试器向您展示了场景的历史日志。场景调试器显示了场景运行的历史记录,并使您能够根据模块的名称或 ID 进行搜索。

**  搜索模块**

要通过模块的名称或 ID 号搜索该模块，请在 DevTool 左侧面板的搜索栏中输入搜索词。

![](https://static.xiaobot.net/file/2024-11-23/720345/1e6543fe04a3406f08a1fddb23a9367a.png)

**  打开模块设置**

在场景编辑器中双击模块名称打开其设置。

**查看请求或响应详情**

您可以通过点击列表中的操作来查看请求详情。

![](https://static.xiaobot.net/file/2024-11-23/720345/c31e58a833d250b1316947d948f92779.png)


### **工具**

以下工具集使您的场景设置更加轻松。

**  专注一个模块**

打开所选模块的设置。

![](https://static.xiaobot.net/file/2024-11-23/720345/c5e197b0a1b09e07588578cb5d5f39e4.png)![](https://static.xiaobot.net/file/2024-11-23/720345/14d4d4fe939f63ebe7bd2f4683d5aea1.png)

**通过映射查找模块**

允许您搜索指定术语的模块值。

![](https://static.xiaobot.net/file/2024-11-23/720345/aab100ab8e85bf4bf68a269b0234db87.png)

**  获取应用元数据**

通过应用程序的模块名称或 ID 检索应用程序的元数据。这很有用,例如当您需要检索用于技术支持或应用程序开发人员的应用程序版本时。

![](https://static.xiaobot.net/file/2024-11-23/720345/42def214f0c8ebee91536a6677b122e6.png)

**  复制映射**

将源模块中的值复制到目标模块中。

![](https://static.xiaobot.net/file/2024-11-23/720345/71210c04462bf62e273d0711529a822c.png)

当源模块和目标模块指定后,单击运行按钮即可执行该操作。

**  复制筛选器**

从源模块复制过滤器设置到目标模块。

![](https://static.xiaobot.net/file/2024-11-23/720345/2faa6fe35d5b8594af9de864c1ce6e14.png)![](https://static.xiaobot.net/file/2024-11-23/720345/0d27a6bf788284203db79e4ba1b4bd97.png)![](https://static.xiaobot.net/file/2024-11-23/720345/1062eb53f1183d3cdfa19bfc98c3601a.png)

当指定源模块和目标模块时，单击"运行"按钮即可复制筛选器。

**交换连接**

将连接从源模块复制到同一场景中该应用程序的每个模块。

![](https://static.xiaobot.net/file/2024-11-23/720345/3b0a22493b7661666782a9463ae7a84a.png)

**  交换变量**

在方案中搜索指定的变量并用新变量替换它们。

![](https://static.xiaobot.net/file/2024-11-23/720345/7dd0ae70e89278f96abfc277da4919a4.png)![](https://static.xiaobot.net/file/2024-11-23/720345/4ffcc3901e7b2664c24c5c11920a877a.png)

当一切就绪后,点击运行按钮执行操作。

**  交换应用程序**

将您场景中选定的应用程序版本替换为另一个应用程序版本。

这个工具可以用来升级 Gmail 和电子邮件应用程序的模块到最新版本。欲了解更多详情,请参阅《使用 Make DevTool 升级 Gmail 和电子邮件应用程序版本》一文。

![](https://static.xiaobot.net/file/2024-11-23/720345/31d07f0125902d87174baf1a8c9e9026.png)

**64 进制**

允许您将输入的数据编码为 Base64 或解码 Base64。当您想在编码的请求中搜索特定数据时,此工具可能很有用。

![](https://static.xiaobot.net/file/2024-11-23/720345/6bca584b7ddf751b6da54c5802988865.png)

当输入被指定时,点击"运行"按钮执行所选操作。

**  复制模块名称**

将所选模块的名称复制到剪贴板。

![](https://static.xiaobot.net/file/2024-11-23/720345/0c18627d042bb95ed48b25ec687c0e84.png)

当模块被选择时，单击运行按钮复制模块的名称。

**  重新映射源**

允许您将映射源从一个模块更改为另一个模块。您也可以对场景中已经存在的模块进行更改,并添加新的模块。

![](https://static.xiaobot.net/file/2024-11-23/720345/4a21851aff4f4d00a0d113ffc8a5ca2a.png)

当一切就绪后,点击运行按钮执行操作。

**  高亮应用程序**

强调您方案中指定应用程序的模块。

![](https://static.xiaobot.net/file/2024-11-23/720345/929c587e2a2eefe2d610b0a43d8a797d.png)![](https://static.xiaobot.net/file/2024-11-23/720345/0141b57172bdda1add7b9522308eb6d2.png)

**迁移 GS**

这个工具专门设计用于将 Google 表格（旧版）模块升级到最新版本的 Google 表格。它会在场景路径中的旧版模块后添加一个新版本的模块。

本工具自动执行《以新模块替换遗留模块》一文中描述的第 3、4、5 步。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-21/720345/2226c8461becc502077a9bd4e6b1075e.png)