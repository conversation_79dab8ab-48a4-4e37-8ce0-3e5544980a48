---
title: "视频 30 工作流：RAG 按时间订阅账号管理系统"
created_at: "2025-04-23T15:27:57.000000Z"
uuid: "e8f3487b-f769-456d-9bc4-ffd4725b5031"
tags: ['会员独家']
---

# 视频 30 工作流：RAG 按时间订阅账号管理系统

**标签**: 会员独家

#
# 一 简介：

在视频 29 之中，翔宇给大家介绍过通过密码进行校验的方式搭建副业收入系统。但是很多情况下需要根据时间进行订阅，所以翔宇设计了一种支持日、月、年订阅形式的工作流，该工作流支持密码账号自动化生成，可以嵌入对话系统提供服务，希望今天的工作流能够给大家进行参考。

  - **安全校验** ：首次请求校验密码是否存在，不存在则直接拒绝；

  - **一次性 + 时效性** ：密码首次使用后，设置状态并计算 1 个月过期；

  - **二次及以后使用** ：在过期前可重复获取账号信息，过期后需重新购买；

  - **全程无感切换** ：用表单节点统一对用户界面友好地展示结果，并自动跳转到下一环节。

**工作流逻辑：**

整个工作流的设计逻辑在30.终极RAG副业系统上线！0代码自动处理任意文档系统的对话中，设置基础验证，这个验证的密码根据自然月的数字生成一串数字，这串数字在同一个自然月之中是相同的，然后把这套生成数字的逻辑也给到30.RAG 按时间订阅账号管理系统中 Notion 模块，该模块可以把密码交付给用户。该逻辑支持日、月、年的的订阅形式，可以根据自己需求进行开发。


## 二 工作流解析：

下面按逻辑顺序，对这个 n8n 工作流做一个分步解读：


## **1\. 整体概览**

该工作流名为“30.RAG 按时间订阅账号管理系统”，主要功能是：

  1. 接收用户提交的“密码”验证码；

  2. 在 Notion 数据库中校验该验证码是否存在；

  3. 根据该验证码的“使用状态”与“过期日期”，决定是首次激活、正常重用，还是提示已过期／错误；

  4. 最终通过表单将“账号”+“动态密码”返给用户，或提示错误并跳回首页。

整个流程可以分为以下几大部分：

  - 表单触发 → 密码校验 → 分支判断

  - “未使用”分支：首次激活，设置过期日期，返回账号密码

  - “已使用”分支：检查是否过期 → 未过期则返回账号密码，已过期则提示过期


## **2\. 触发器：**


## **表格提交**

  - **节点类型** ：Form Trigger

  - **功能** ：在前端展示一个表单，标题“翔宇工作流 RAG 系统”，仅包含一个必填字段“密码”。

  - **输出** ：用户提交的 密码 字段，通过 webhook 推送到后续节点。


## **3\. 密码校验与分支判断**

  1. **密码校验（密码校验）**

     - **类型** ：Notion → databasePage:getAll

     - **操作** ：在指定数据库中，用 title == 用户提交的密码 过滤，查看是否有对应的页面。

     - **alwaysOutputData** ：打开，确保后续节点能接收到任何结果（空或列表）。

  2. **分支-密码判断（分支-密码判断）**

     - **条件** ：

       - 若 $json.id 存在 → 输出到“存在”分支；

       - 否则 → 输出到“不存在”分支。

     - **后续** ：

       - “存在” → 继续请求该页面详情；

       - “不存在” → 走到“返回表格-密码错误”节点，给用户提示“验证码次数用完或者不正确”，并在“密码错误-回到首页”中重定向回工作流首页。


## **4\. 获取密码详情 & 使用状态分支**

  1. **密码详情（密码详情）**

     - **类型** ：Notion → databasePage:get

     - **输入** ：上一节点筛选出的页面 id。

     - **输出** ：该页面的全部属性（包括“状态”、“过期日期”等）。

  2. **分支-使用情况（分支-使用情况）**

     - **条件** ：检查属性 状态.select.name 是否等于“已使用”。

     - **分支** ：

       - 若 **不等于** “已使用” → “未使用”分支；

       - 若 **等于** “已使用” → “已使用”分支。


## **5\. 「未使用」分支：首次激活**

  1. **更新-过期日期（更新-过期日期）**

     - **类型** ：Notion → databasePage:update

     - **操作** ：

       - 将该密码页面的 状态.select 更新为 “已使用”；

       - 将 过期日期.date 设为 **当前日期** \+ 1 个月（PRC 时区，不含具体时分）。

  2. **返回账号密码-未使用（返回账号密码-未使用）**

     - **类型** ：Form

     - **展示** ：

       - 固定 “账号” 字段：xiangyugongzuoliu；

       - 动态 “密码” 字段：用前端 JS 计算 year² + month²（例如 2025² + 4² → “4100629”？），每次输出最新数值；

       - 表单描述中提醒“请在无痕窗口中使用”。

  3. **跳转对话-未使用（跳转对话-未使用）**

     - **类型** ：Form \(completion\) → redirect

     - **操作** ：重定向到聊天页面，继续后续交互。


## **6\. 「已使用」分支：检查过期**

  1. **日期校验（日期校验）**

     - **类型** ：Notion → databasePage:getAll

     - **过滤条件** ：

       - 过期日期.date **after** 今天；

       - 验证密码|title 等于用户输入的密码；

     - **功能** ：如果返回非空，说明还未过期；否则说明已过期。

  2. **分支-过期日期（分支-过期日期）**

     - **分支** ：

       - “未过期” → 返回账号密码；

       - “已过期” → 返回“密码已过期，请重新购买密码”。

  3. **未过期输出**

     - **节点** ：返回账号密码-已使用 → 类似“未使用”表单，但不含“无痕窗口”提示；

     - **跳转** ：跳转对话-已使用 重定向到聊天页面。

  4. **已过期输出**

     - **节点** ：返回表格-密码过期 → 表单提示“密码已过期，请重新购买密码”，按钮“点击回到首页”；

     - **跳转** ：回到首页-密码过期 重定向回工作流首页。


## **7\. 总结**

这种设计利用 Notion 作为轻量化数据库，在 n8n 中结合多重 switch 分支，高效地实现了动态订阅账号管理、权限校验、时效管控及用户交互。

![](https://static.xiaobot.net/file/2025-04-23/720345/d812461c377f38aaa73e681efb1feace.jpeg)

![](https://static.xiaobot.net/file/2025-04-23/720345/59bdda5b1ba9d6843a0be220a6e7c215.jpeg)

密码的代码为：

![](https://static.xiaobot.net/file/2025-04-23/720345/002333d532edcd286683120fc097d3ca.jpeg)


## **三 n8n 工作流模版文件下载：**

该工作流包含订阅时间的密码校验工作流，可以根据自己需求决定是否使用。


### [**下载链接（点击）**](<https://pan.quark.cn/s/6ec45cdf1447>)

提取码：87BP


## **四 数据库模板：**

点击下方链接查看数据库模板：

[Notion 模板 （点击）](<https://xiaoyuzaici.notion.site/1db017a905fd806ca819cc34a159516d?v=1db017a905fd81b08b2f000c512c0ffd&pvs=4>)

![](https://static.xiaobot.net/file/2025-04-23/720345/5b914dab76075572996340d38c51c298.png)