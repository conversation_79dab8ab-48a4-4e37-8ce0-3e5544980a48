---
title: "Make23.提示词生成新方法！用Make自动化生成，只有今天能学到的独家技巧！"
created_at: "2025-01-15T14:55:48.000000Z"
uuid: "8c7cc322-dff4-4d66-bdd4-1a85b570d146"
tags: ['资源包']
---

# Make23.提示词生成新方法！用Make自动化生成，只有今天能学到的独家技巧！

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/23-prompt-automation-with-make-exclusive-tips/>


# **视频资源**

**1\. 使用教程和简要过程：**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

  - 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

  - 配置各模块的账户和API密钥

  - 链接和配置Notion数据库

  - 保存并运行工作流

**2\. Make 工作流模版文件下载：**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载超链接](<https://pan.quark.cn/s/929174e3923b>)

提取码：r8cK

注意如果提示词采集工作流只输出一个数据组时可参考如下图设置：

![](https://static.xiaobot.net/file/2025-02-12/720345/ab6ba9332919e5bc93e414953ba6c63e.jpeg)

**3.数据库模板：**

点击下方链接查看数据库模板：

[https://xiaoyuzaici.notion.site/161017a905fd800c91ded97b5ae1c5dc?v=161017a905fd8119ba16000c3ef7b831&pvs;=4](<https://xiaoyuzaici.notion.site/161017a905fd800c91ded97b5ae1c5dc?v=161017a905fd8119ba16000c3ef7b831&pvs=4>)

**4.PPT：**

<https://gamma.app/docs/23Make-cnj5wihfez7oev7>

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！

**5.提示词框架：**

[框架文档](<https://kdocs.cn/l/cdTRUubmiswN>)

![](https://static.xiaobot.net/file/2025-01-15/720345/448062c05ef5626cb8bd64732b349e82.webp)