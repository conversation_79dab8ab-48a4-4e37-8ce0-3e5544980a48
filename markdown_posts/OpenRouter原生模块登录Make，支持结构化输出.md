---
title: "OpenRouter原生模块登录Make，支持结构化输出"
created_at: "2024-12-13T11:52:33.000000Z"
uuid: "cfed3d6b-5795-4bec-9fd9-57c4096d988d"
tags: ['API']
---

# OpenRouter原生模块登录Make，支持结构化输出

**标签**: API

![](https://static.xiaobot.net/file/2024-12-13/720345/ea729b070666a0c2f376a60aaf986808.png)

Make 平台近日迎来了 **OpenRouter 原生模块** 的正式上线，为用户提供了更强大的对接能力和灵活性。通过 OpenRouter 模块，用户可以直接调用 OpenRouter 的功能，将其集成到自动化工作流中，实现自然语言处理、AI 辅助以及 JSON 结构化输出等高阶功能，**使用 OpenRouter，您可以轻松调用海量主流 AI 模型，将其无缝集成到自动化流程中，满足多样化场景需求。**

以下是 OpenRouter 模块在 Make 中的一些关键功能介绍：


## **1\. 模块核心功能**

• **选择模型 \(Model\)**

OpenRouter 模块支持调用多种模型，包括实验性和主流 AI 模型。用户可以根据需求选择最合适的模型，例如 **Google Gemini 2.0 Flash** ，以获得高性能的文本生成能力。

• **自动回退功能 \(Enable Automatic Fallback\)**

如果启用此功能，当主要模型无法正常运行时，系统会自动选择性能接近、价格更低的备选开源模型，保证任务流程不中断。这为用户节省了手动切换模型的时间。

• **JSON 模式也就是结构化输出 \(Enable JSON Mode\)**

JSON 模式是一项 Beta 功能，确保生成的文本输出符合 JSON 结构格式。在设计流程时，可以通过提示描述所需的 JSON 输出结构，适用于需要结构化输出的场景。

• **高级参数配置**

  1. **最大 Token \(Max Tokens\)**

设置生成文本的最大长度，控制输出内容的详细程度。这个参数为用户提供了灵活性，根据场景需求调整生成结果的粒度。

  2. **生成选项数量 \(Number of Choices\)**

支持一次生成多种选项，方便用户对比不同的生成结果。例如，设置为 2 时，OpenRouter 将返回两种不同版本的内容，供用户筛选。

  3. **Temperature**

**作用** ：控制生成内容的创意性和随机性。**取值范围** ：0 至 2。值越高（如 1.5），输出更加多样化和随机。值越低（如 0.2），输出更加保守和确定性。**默认值** ：1，适合平衡逻辑与创意的需求。

  4. **Top P**

**作用** ：使用核采样法，限制模型生成内容时的概率分布。**取值范围** ：0 至 1。值越低，生成内容限制越严格。值为 1 时，输出不受此约束。**默认值** ：1，适合开放性需求。

  5. **Frequency Penalty**

**作用** ：控制生成内容中重复词汇的惩罚力度。**取值范围** ：-2 至 2。值越高，越倾向于减少重复词汇。值为负时，可能增加词汇重复的倾向。

  6. **Presence Penalty**

**作用** ：控制模型生成时引入新词汇的概率。**取值范围** ：大于或等于 1。值越高，模型更倾向于引入未提及的新词汇。值越低，可能更倾向于重复已出现的词汇。

  7. **Seed**

**作用** ：设置随机数种子，保证生成内容的可复现性。**用途** ：输入相同的 Seed 和其他参数时，生成内容将保持一致。

  8. **Stop Sequences**

**作用** ：设置生成内容时的停止条件。**用途** ：输入特定的字符串作为停止信号，模型生成内容遇到该字符串时会终止。

  9. **Top K**

**作用** ：从前 K 个最可能的词中选择一个，限制候选词数量。**取值范围** ：整数值。较低的值会限制生成结果的随机性。较高的值允许更多可能性，输出更加丰富。


## **优势总结**

• **无缝集成** ：OpenRouter 模块与 Make 平台深度融合，无需复杂设置即可使用。

• **灵活配置** ：支持多模型选择和参数自定义，满足不同应用需求。

• **结构化输出** ：JSON 模式显著提升了应用范围。

• **稳定性保障** ：自动回退功能确保任务流程连续运行。

通过 OpenRouter 模块，Make 用户可以轻松引入 AI 功能，赋能自动化场景。无论是文本生成、数据结构化，还是智能交互，这一模块都提供了高效且可靠的解决方案。未来，随着更多模型和功能的支持，OpenRouter 模块在 Make 平台中的表现将更加值得期待！


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，[让您在**Make.com**](<http://让您在Make.com>)上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-13/720345/ebc685ec773020695d6aeb23e35d7cf3.png)