---
title: "Make中文教程：数组映射（Mapping arrays）"
created_at: "2024-09-01T10:01:50.000000Z"
uuid: "e79d9707-4a10-439a-8df9-2c5dc712f361"
tags: ['Make基础教程']
---

# Make中文教程：数组映射（Mapping arrays）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于数组映射的教程。

  1. **教程简介**  ：本教程讲解如何在Make.com中处理和映射数组（包括简单数组和复杂数组），涵盖访问数组的特定元素和使用函数操作数组中的数据。

  2. **学习目标**  ：学习如何通过映射和函数在Make.com中操作和处理简单及复杂数组，理解在自动化流程中提取和转换数据的基本方法。

  3. **学习内容**  ：

     - 简单数组和复杂数组的基础知识。

     - 如何映射数组的第一个和特定元素。

     - 使用 map\(\) 和 get\(\) 函数操作带有键值的数组。

     - 将数组转换为数据包系列的方法。

  4. **概念理解**  ：

     - 数组：包括简单数组和包含多个集合的复杂数组。

     - 映射（Mapping）：指通过索引或键值访问数组中的元素。

     - map\(\)：函数，用于过滤和获取复杂数组中的特定项。

     - get\(\)：函数，用于从映射的结果中提取特定值。

  5. **任务练习**  ：

     - 练习映射一个包含三个文本值的简单数组的第二个元素。

     - 使用 map\(\) 函数过滤一个包含键值对的复杂数组，提取特定键值所对应的值。

     - 利用迭代器模块将一个数组转换为独立的数据包系列。


## **数组映射基础**


### **什么是数组？**

在 Make.com 中，数组是一种数据结构，用于存储和管理多个相关项目的集合。具体特点包括：

  - 数组是按顺序排列的数据集合，可以在自动化场景中轻松处理和转换

  - 可以使用迭代器\(Iterator\)和数组聚合器\(Array Aggregator\)模块处理数组

  - 支持通过 Parse JSON、Get 和 Map 函数提取和操作数组中的单个项目

  - 在数据集成和自动化工作流中广泛使用，帮助在不同应用程序间传输和转换数据。

具体以例子讲解数组就像购物清单：

  - 简单数组：一串名字列表

  - 复杂数组：带详细信息的订单列表（每个订单包含商品名、价格、数量等）


### **基本操作**

**1\. 获取第一个元素**

    场景：获取收件人列表中的第一个人
    操作：直接映射 [1] 或留空（默认第一个）

![](https://static.xiaobot.net/file/2024-11-18/720345/beb77e04af7ef42d31ae228224574daf.png)

**2\. 获取指定位置元素**

    场景：获取第二个收件人
    操作：使用 [2] 指定位置

![](https://static.xiaobot.net/file/2024-11-18/720345/7ff1e647db9bec0e2c4c6cdd9292e60d.png)


## **实战应用**


### **1\. 电商订单处理**

    场景：从WooCommerce获取订单元数据
    目标：找到特定ID的订单信息

![](https://static.xiaobot.net/file/2024-11-18/720345/bc1eca2c52279d6f5409f9ae4ae1a338.png)


### **2\. 使用Iterator处理批量数据**

    场景：批量处理邮件附件
    方法：用Iterator逐个处理每个附件

![](https://static.xiaobot.net/file/2024-11-18/720345/b1d72b29fbaccd63b75509ca98c0762e.png)

小贴士：处理数组就像整理购物清单，知道位置和名称，就能快速找到想要的东西！


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/4f90b29f252eeb011b4de96731dc9a85.png)