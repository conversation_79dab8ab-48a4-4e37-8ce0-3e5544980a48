---
title: "Make使用指南5：映射教程"
created_at: "2024-08-27T10:50:28.000000Z"
uuid: "23f0e896-127b-4023-b646-a3af86be9387"
tags: []
---

# Make使用指南5：映射教程

**翔宇工作流** 今天为大家带来关于映射教程的教程。 

**教程简介** ： 

本教程介绍了Make.com中“映射”的概念及其应用，详细讲解了如何在不同模块间传输和转换数据，以及如何针对邮件、Slack、Jira等具体应用进行信息映射。

**学习目标** ： 

学习如何利用Make.com的映射功能，将不同应用程序之间的数据进行转换和传输，从而实现自动化工作流程的创建和管理，提高工作效率。

**学习内容** ：

  1. 了解映射的定义及其重要性。

  2. 学习如何获取和查看信息包。

  3. 了解数组和集合的概念及应用。

  4. 掌握具体应用场景下的数据映射方法。

  5. 学习解决映射过程中常见的问题。

**概念理解** ： 

映射（Mapping）使Make.com能够识别从一个应用获取的数据并将其发送到另一个应用。信息包（Bundle）是数据的集合，可能是数组（同类信息）或集合（多类型信息）。了解信息包的内容有助于准确映射所需数据。

**任务练习** ：

  1. 创建一个新场景，添加**Email > Watch emails** 模块，设置连接并运行模块获取信息包。

  2. 添加**Slack > Create a message** 模块，设置连接，将电子邮件内容映射到Slack消息内容中。

  3. 提交一次Airtable表单响应，右键运行**Airtable > Watch responses** 模块，查看获取的响应信息包。


# **映射（Mapping）**


## **什么是映射？**

映射是Make用来知道从一个应用获取什么信息并将其发送到另一个应用的方法。你可以使用映射告诉Make如何在场景（Scenario）中转换数据。例如：

  - 你想要自动将电子邮件中的信息发送给Slack中的同事。在这种情况下，可以将电子邮件的主题或正文映射到Slack。

  - 当客户提交表单请求支持时，你想要自动创建一个Jira问题。这里，你可以将表单中的信息映射到Jira。

  - 你的企业从Facebook获取有价值的潜在客户，因此你想要创建一个包含这些信息的数据库，并将相同的信息输入到Pipedrive中。只需将Facebook潜在客户的数据映射到Airtable和Pipedrive即可。

就像地图引导你从起点到达目的地一样，Make中的映射在场景中告诉你的数据要去哪儿。


## **我可以映射什么？**

你可以映射在一个模块中获取到的任何信息包。信息包（bundle）就像一个装有信息的盒子。有些盒子很大，有些很小，有的里面只有一件东西，有的可能里面还有更小的盒子。信息包类似。有时一个信息包只是一个信息，例如一个数字或电子邮件地址。其他时候，一个信息包包含各种信息。

当信息包有大量信息时，你可能会看到这些数据被组织成一个数组（array）或集合（collection）。数组就像菜单，按不同的部分组织，例如汤、开胃菜、甜点等。虽然有很多信息，但都是同类信息：食物。数组只有一种类型的信息。例如，它可能包含多个日期或名称，但不会同时包含两者。集合可以包含不同种类的信息，像一个搬家的箱子。当你搬家时，你可能会将各种东西放入一个箱子：书籍、小工具、信件盒等。集合包含类似种类的多种信息，例如日期、数字和文本描述。


### **获取信息包**

要知道可以从一个模块中映射什么，你只需运行该模块并查看其信息包。一旦知道可以映射什么，就可以开始映射。让我们用上面的第一个例子来探索如何操作。我们想要获取电子邮件中的信息并发送给同事。在进行任何映射之前，我们先找到可以从电子邮件模块获取什么信息。因为电子邮件模块提供了我们需要的信息，所以我们称它为源模块（source module）。

  1. 前往**场景编辑器（Scenario editor）并添加模块Email > Watch emails** 。

  2. 创建一个[连接](<https://www.make.com/document/preview/2268259#UUID-a868116f-4725-be96-d97e-965fe270f3f9>)。

  3. 选择一个文件夹和条件。

  4. 点击**确定** 。

![](https://static.xiaobot.net/file/2024-08-27/720345/0e023a53674bcf9c4330c45cac9e3ad9.png)
  5. 选择要让Make开始获取电子邮件的位置并点击**确定** 。

  6. 右键点击模块。

  7. 选择**仅运行此模块** 。

  8. 模块旁边会出现一个小圆圈，里面有1。点击小圆圈。

一个窗口将打开，显示电子邮件模块获取的所有信息。你可以映射任何出现的内容。点击\*\*+\*\*可以展开项目。


### **信息包里有什么？**

让我们更仔细地看看信息包窗口中的信息。在这个例子中，我们看到的是**Slack > Create a message** 模块中的信息包。

![](https://static.xiaobot.net/file/2024-08-27/720345/35beb343ec2b298f3fb64d69c89c0950.png)

  1. 输入 - Make发送到第三方应用的信息。在这个例子中，你可以看到：

     - 文本 - 发送的消息内容。点击\*\*+\*\*可以展开查看更多详情。

     - 用户 - 接收你Slack消息的人的用户ID。

  2. 输出 - Make从第三方应用获取回来的信息。在这个例子中，你可以看到：

     - 消息 - 发送消息的文本内容，类似于输入中的文本。点击\*\*+\*\*可以展开查看更多详情。

     - 频道 - 发送消息的频道ID。在这个例子中，它和输入中的用户ID相同，因为我们发送的是给特定用户的直接消息。


## **映射到目标**

现在我们知道可以发送哪些信息到其他模块。接下来，我们将这些信息映射到目标模块。为此，我们首先添加接收信息的模块。在我们的例子中，这是**Slack > Create a message** 。如果你仍在**场景编辑器** 中，请继续以下步骤：

  1. 添加模块**Slack > Create a message** 。

  2. 创建一个连接。

  3. 在**输入频道ID或名称** 下，选择**从列表中选择** 。

  4. 在**频道类型** 下，选择**IM频道** 。

  5. 在**用户** 下，搜索或滚动找到目标接收者。

  6. 在**文本** 下，点击空字段。一个新窗口会出现。

  7. 在新窗口中，点击**文本内容** 。一个名为**文本内容** 的红框会出现在**文本** 字段。

  8. 点击**确定** 。

![](https://static.xiaobot.net/file/2024-08-27/720345/f8be0ce2be43a2140095f2cbf0f9aa66.png)

你的数据现在映射完成，Slack会自动接收电子邮件内容并发送消息。


## **提示和解决方案**


### **找到源模块**

在映射场景时，你可以通过悬停在映射数据上找到源模块。当你将鼠标悬停在映射项目上时，源模块会脉冲闪烁。


### **当即时触发器没有信息时**

当你点击“运行一次”或“仅运行此模块”时，即时触发器会等待接收信息。即时触发器需要接收传入的信息才能运行。因此触发器会一直等待，直到收到传入的数据。要运行该模块，你需要提供传入的信息。让我们用一个例子来探索：**Airtable > Watch responses** 。

  1. 右键点击**Airtable > Watch responses** 并选择**仅运行此模块** 。模块会等待新的响应。

![](https://static.xiaobot.net/file/2024-08-27/720345/53018362554b0e8e7bb2a632d618221f.png)
  2. 前往你的Airtable表单并提交一个响应。

提交的信息将进入你的**Airtable > Watch responses** 模块。你可以检查信息包以确认配置是否正常工作。


### **当轮询触发器没有信息时**

你可以使用上述过程为大多数模块生成信息包，包括轮询触发器。对于即时模块，请参阅[即时触发器没有信息时的解决方案](<https://www.make.com/en/help/mapping/mapping.html#when-there-s-no-info-for-an-instant-trigger>)。以下是获取信息包的一般步骤。

  1. 前往**场景编辑器** 并找到你想要获取信息包的模块。

  2. 右键点击该模块。

  3. 选择**选择开始位置** 。

  4. 要获取信息包，选择以下选项之一并点击**确定** ：

     1. **从特定日期开始** 选项：例如从指定日期之后的电子邮件。输入一个你知道有数据的日期。例如：输入你知道收到电子邮件的日期。

     2. **选择第一个** 选项：当可用时，这个选项允许你从列表中选择。

  5. 右键点击该模块。

  6. 选择**仅运行此模块** 。

模块旁边会出现一个小圆圈，里面有1。点击小圆圈。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！

![](https://static.xiaobot.net/file/2024-08-27/720345/a27515b75c7169fafb7c2a080ed680ff.png)