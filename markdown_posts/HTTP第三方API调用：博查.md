---
title: "HTTP第三方API调用：博查"
created_at: "2024-09-10T03:43:30.000000Z"
uuid: "3eca3296-f550-4773-a55a-2ab40651f816"
tags: ['快捷工具']
---

# HTTP第三方API调用：博查

**标签**: 快捷工具

**使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 Make.com 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 <https://jsonformatter.org/> 点击“Format / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下文档**

[HTTP第三方API调用：博查](<https://kdocs.cn/l/cueewbpm98XP>)

博查Web Search：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://open.bochaai.com/api/v1/search","serializeUrl":false,"method":"post","headers":[{"name":"Content-Type","value":"application/json"},{"name":"Authorization","value":"Bearer YOUR_API_KEY"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"query\":\"2024年阿里巴巴的esg报告\",\"freshness\":\"oneYear\",\"summary\":false,\"count\":8}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"Bocha"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}

博查AI Search：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://api.bochaai.com/v1/ai-search","serializeUrl":false,"method":"post","headers":[{"name":"Content-Type","value":"application/json"},{"name":"Authorization","value":"Bearer sk-xxxxxxxx"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"query\":\"你好2333\",\"stream\":false}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"Bocha"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}


## **翔宇工作流制作**

翔宇工作流致力于持续优化和更新便捷化工具，以提升 Make.com 工作流的创建与使用效率。通过内置的常用 API 调用模块代码，减少了重复配置的时间，并增强了操作的一致性和可靠性。

![](https://static.xiaobot.net/file/2024-09-10/720345/6692c027f6dd18ffdad3e226cd666798.png)