---
title: "Make中文教程：HTML/CSS 转图像（HTML/CSS to Image）"
created_at: "2024-09-01T03:17:54.000000Z"
uuid: "6e2e1cb8-2d9a-472a-a659-4667fd0c6bec"
tags: ['Make基础教程']
---

# Make中文教程：HTML/CSS 转图像（HTML/CSS to Image）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于将HTML/CSS转为图片的教程。

  1. **教程简介** ： 本教程介绍如何使用Make.com的HTML/CSS to Image模块将HTML和CSS代码转换成图片，包括账户连接、模块设置和API调用示例。

  2. **学习目标** ： 学习将HTML/CSS转换为图片的具体方法，掌握在Make.com中配置HTML/CSS to Image模块的技能，并了解如何使用API进行图片处理。

  3. **学习内容** ：

  - 创建和连接HTML/CSS to Image账户

  - 配置并使用HTML/CSS to Image模块

  - 通过API调用获取转换后的图片

  4. **概念理解** ： **HTML/CSS to Image模块** ：这是一个在Make.com中使用的工具，允许用户将HTML和CSS代码转化为图片格式（如jpg, png, webp），并支持使用Google字体。

  5. **任务练习** ： 创建一个将简单HTML和CSS代码转换为图片的Make.com自动化场景，并通过API调用下载生成的图片进行验证。


# **HTML/CSS 转图像**

HTML/CSS 转图像模块使您可以在 HTML/CSS 到图像帐户中将 HTML/CSS 转换为图像。


## **开始使用 HTML/CSS 转换为图像**

 先决条件

  - HTML/CSS 到图像账户

要使用 HTML/CSS to Image with Make,需要有一个 HTML/CSS to Image 账号。如果你还没有,可以在 htmlcsstoimage.com 上创建一个。

![](https://static.xiaobot.net/file/2024-11-26/720345/bdc8330b13aee0be118691667b82b99c.png)


## **将 HTML/CSS 与图像联系起来制作**

要将您的 HTML/CSS 连接到 Image 帐户,您需要获取您的用户 ID 和 API 密钥。

1\. 登录您的 HTML/CSS 到图像帐户。

2\. 前往仪表盘,您可以在此找到您的用户 ID 和 API 密钥。

![](https://static.xiaobot.net/file/2024-11-26/720345/5bc742dca9e1c6fceb108f41a23b98f8.png)

3\. 进入"制作"并打开"HTML/CSS 到图像"模块的"创建连接"对话框。

4\. 在步骤 2 中提供的用户 ID 和 API 密钥输入到相应的字段中,然后点击继续按钮建立连接。


## **行动**


### **使用 HTML/CSS 创建图像**

将 HTML/CSS 转换为图像\(jpg、png、webp\)。

![](https://static.xiaobot.net/file/2024-11-26/720345/f563261b22bd3a2b92a63c3f78ab1102.png)![](https://static.xiaobot.net/file/2024-11-26/720345/ccf36462745469d1f58a5f5b6aba4405.png)

另请参阅 HTML/CSS to Image 网站上的文档。


### **  发起 API 请求**

允许您执行自定义 API 调用。

![](https://static.xiaobot.net/file/2024-11-26/720345/f1ff30726fe7b5056f12a3c7e3b2d5b8.png)


### **使用示例 - 获取图像**

以下 API 调用下载创建的图像:

  /image/\{\{image\_id\}\}

 方法: GET

![](https://static.xiaobot.net/file/2024-11-26/720345/c034c49c6da72832d188bef07477b3aa.png)

结果可以在模块的输出下的 Bundle > Body 中找到。图像数据:

![](https://static.xiaobot.net/file/2024-11-26/720345/1a73126b6e416c958513971d241959ac.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-01/720345/8ebf449502d261a1d86b6cfc2f72d5e9.png)