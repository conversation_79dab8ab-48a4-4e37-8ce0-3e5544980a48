---
title: "Make中文教程：使用自定义OAuth客户端连接到Google服务（Connecting to Google services using custom OAuth client）"
created_at: "2024-12-04T08:39:23.000000Z"
uuid: "2ac9bd72-cecf-4b03-b9c2-879091cf494f"
tags: ['Make基础教程']
---

# Make中文教程：使用自定义OAuth客户端连接到Google服务（Connecting to Google services using custom OAuth client）

**标签**: Make基础教程

本文将向您展示如何在 Google Cloud 控制台中创建自己的项目以及自定义 OAuth 客户端。这对于连接受限的 Google 服务\(如 Google Drive 或 Gmail\)到 Make 很有用。

要连接,您必须拥有一个谷歌账户。您可以在 accounts.google.com/signin 创建一个账户。

以下程序适用于:

  - 个人使用\(@gmail 和@googlemail.com 用户\)

  - 内部使用（喜欢使用自定义 OAuth 客户端的 Google 工作区用户）


## **配置 Google Cloud 控制台项目**

配置 Google 云控制台项目有 5 个步骤。

  1. 创建一个 Google Cloud Console 项目

  2.  开启 API

  3. 配置您的 OAuth 同意屏幕

  4. 创建您的客户凭据

  5. 使用自定义凭据连接到 Make


### **创建一个 Google Cloud Console 项目**

  1. 使用您的谷歌账号登录谷歌云控制台。

  2. 在顶部菜单上,单击"创建"或选择"新项目"。

  3. 输入项目名称并选择项目位置。

  4.  点击创建。

  5. 在顶部菜单中,检查您新创建的项目是否在"选择项目"下拉列表中被选中。如果没有,请选择您刚刚创建的项目。

![](https://static.xiaobot.net/file/2024-12-04/720345/3b5f88edc14c87875d38e3b87c73c846.png)


### **  启用 APIs**

  1. 打开左侧导航菜单，并转到 APIs & Services > 库。

  2. 为您的项目搜索所需的 API。

有关特定 API 的信息,请参阅您正在使用的应用程序的 Make 帮助中心。

  3. 点击所需的 API，然后点击启用。


### **配置您的 OAuth 同意屏幕**

  1. 前往 APIs & Services > OAuth 同意屏幕。

  2. 在"用户类型"下选择"外部"。

有关用户类型的更多信息,请参阅 Google 的"验证要求例外"文档。

  3.  点击创建。

  4. 在应用程序信息部分，输入"Make"作为应用程序名称,并输入您的 Gmail 地址。

  5. 在"授权域"部分，添加 make.com 和 integromat.com 。

  6. 在开发者联系信息部分,输入您的 Gmail 地址。

  7.  点击保存并继续。

  8. 在 Scopes 页面,单击添加或移除 Scopes,添加任何所需的 Scopes,然后单击更新。

以下是您可能需要用于 Gmail 和 Google 云端硬盘的一些作用域示例。要了解更多作用域信息,请参考您正在使用的应用程序的 Make 帮助中心。

![](https://static.xiaobot.net/file/2024-12-04/720345/7805d84d1ce9a5ce24d9a4c7bf28864c.png)
  9.  点击保存并继续。

  10. 如果您的项目将保持测试发布状态,请在"测试用户"页面添加测试用户电子邮件,然后单击"保存并继续"。

![](https://static.xiaobot.net/file/2024-12-04/720345/9066b5b95a56a4a11d201a478f966529.png)


### **创建您的客户凭证**

  1. 在左侧边栏中,单击凭证。

  2. 点击+创建凭据>OAuth 客户端 ID。

![](https://static.xiaobot.net/file/2024-12-04/720345/1ad0cb4c4b549a5f2571b6e5407f1f4b.png)
  3. 在应用程序类型下拉菜单中,选择 Web 应用程序。

  4. 更新您的 OAuth 客户端的名称。这将帮助您在控制台中识别它。

  5. 在"授权重定向 URI"部分,单击"+ 添加 URI"并输入重定向 URI。

以下是您可能需要的一些重定向 URI 示例。有关其他重定向 URI，请参考您使用的应用程序的 Make 帮助中心。

     - https://www.integromat.com/oauth/cb/google-restricted - 用于 Gmail 或 Google Drive

     - https://www.integromat.com/oauth/cb/google-custom - 用于 Google Cloud 文本转语音

     - https://www.integromat.com/oauth/cb/google-cloud-speech - 用于 Google Cloud 语音识别

     - https://www.integromat.com/oauth/cb/youtube - 适用于 YouTube

     - https://www.integromat.com/oauth/cb/app - 针对通过 Make Developer Platform 创建的任何应用程序

     - https://www.integromat.com/oauth/cb/google - 用于其他 Google 应用程序

  6.  点击创建。

  7. 请复制您的客户端 ID 和客户端密钥值,并将它们存储在一个安全的地方。

您将在 Make 中的"客户端 ID"和"客户端密钥"字段中使用这些值。


### **在制作中建立连接**

建立在 Make 中的连接

  1. 登录您的 Make 账户,添加一个 Google 应用程序模块到您的场景中,然后点击创建连接。

  2. 在"连接名称"字段中输入连接的名称。

  3. 打开"显示高级设置"开关,然后输入您的 Google Cloud Console 项目客户端凭据。

  4. 点击使用谷歌登录。

  5. 如果提示,请验证您的账户并确认访问权限。

您已成功建立连接。您现在可以编辑场景并添加更多模块。如果您的连接在任何时候需要重新授权,请按照此处的连接续订步骤进行操作。


## **  常见问题**


### **每日登录限制已达到 100 次**

这种情况很少发生,但当它发生时,我们建议创建另一个 OAuth 客户端。


### **\[403\] 未配置访问权限**

如果出现此错误消息,您需要在 Google 云平台中启用相应的 API。

![](https://static.xiaobot.net/file/2024-12-04/720345/e67d79c4bb6599c2dd9f0e196e9ae649.png)


### **授权错误 - 错误 403：access\_denied**

![](https://static.xiaobot.net/file/2024-12-04/720345/e7d25891aa068f4fe223038208af23ce.png)

谷歌已添加了所需的同意屏幕设置。您需要添加您想要连接的谷歌帐户的关联电子邮件地址,并将其设置为测试用户。

  1. 使用您的 Google 凭证登录 Google Cloud Platform。

  2. 前往 APIs & Services > OAuth 同意屏幕。

  3. 在"测试用户"部分,单击"添加用户"添加测试用户。输入您要连接的 Google 帐户的关联电子邮件地址,然后单击"保存"。

![](https://static.xiaobot.net/file/2024-12-04/720345/3b739b198826b81a126849ef89fa8479.png)
  4. 现在,转到"制作"并连接到所需的 Google 服务。


### **无法验证连接'My Google Restricted connection'。状态代码错误：400**

您的连接已过期,不再有效。您需要重新授权连接。

此错误影响了非 Google Workspace 账户。欲了解更多详情,请参考 Google OAuth 文档。

由于谷歌更新的安全政策,未发布的应用程序只能有 7 天的授权期。在 OAuth 安全令牌到期后,连接将不再被授权,任何依赖它的模块都将失败。

**  解决方案**

**  选项 1：**

为避免每周重新授权,您可以更新项目的发布状态。

如果您将您的项目更新到已投产状态,您将不需要每周重新授权连接。

按照以下步骤改变您项目的状态:

  1. 登录 Google Cloud 控制台。

  2. 导航到 OAuth 同意屏幕。

  3. 点击您应用旁边的发布应用程序按钮。

  4. 如果您看到"需要验证"的通知,您可以选择是否通过 Google 验证流程来验证该应用程序,或者连接到未经验证的应用程序。目前在 Make 中可以连接到未经验证的应用程序,但我们无法保证 Google 将永远允许连接到未经验证的应用程序。

有关发布状态的更多信息,请参阅 Google 的 OAuth 同意屏幕设置帮助和我们社区页面中的"发布状态"部分。

**  选项 2**

如果您将项目保持在测试状态,您将需要每周在 Make 中重新授权您的连接。

按照以下步骤重新授权您的 Google 帐户连接:

  1.  登录 Make.

  2.  转到连接。

  3. 找到您的 Google 连接并点击重新授权按钮。

![](https://static.xiaobot.net/file/2024-12-04/720345/0350f2f4f3a1542ff3c695fb1f233a35.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-04/720345/a8c48c2a304ae44b4cbf7ec6199da338.png)