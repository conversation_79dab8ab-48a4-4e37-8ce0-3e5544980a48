---
title: "免费推文自动化采集Make工作流"
created_at: "2024-11-19T08:51:32.000000Z"
uuid: "7397f304-2a96-409d-8dd9-99552e8d834e"
tags: ['会员独家']
---

# 免费推文自动化采集Make工作流

**标签**: 会员独家

![](https://static.xiaobot.net/file/2024-11-19/720345/d61fe45d60666401e97deb7f47e54668.png)


## **前言**

今天翔宇要分享的是一个关于推文自动化采集的工作流。该工作流主要利用make工具进行数据采集，然后将采集到的数据存储到Notion数据库中。


### 采集字段说明

本工作流主要采集以下推文数据：

  - ID：推文唯一标识

  - 时间：推文发布时间

  - 作者：发布者信息

  - 链接：推文URL

  - 观看次数：推文浏览量

  - 转发数量：推文被转发次数

  - 喜欢数量：获赞数

  - 评论数量：评论总数

  - 引用数量：被引用次数

  - 简介：推文正文内容


## **Apify 简介**

Apify 是一个强大的网络爬虫和自动化平台，它提供了丰富的预构建 actors（爬虫脚本）来满足各种数据采集需求。对于推文采集，虽然平台上有多个付费的专业 actors 可供选择，但今天我要分享的是一个可以在免费额度内使用的方案。

> 提示：Apify 为每个用户提供每月5美元免费使用额度，对于个人用户来说完全够用。


## **工作流程详解**


### **1\. 定义采集参数**

  - 模块名称：Set multiple variables

  - 功能说明：设置推文采集的关键参数

  - 配置要点：可以设置目标账号网址、采集时间范围、采集数量等


### **2\. Apify 开始采集**

  - 模块名称：Run an Actor

  - 功能说明：启动 Apify 采集推文

  - 配置要点：

    - 选择合适的 Actor

    - 正确传入采集参数

    - 确保任务启动成功


### **3\. 等待采集完成**

  - 模块名称：Sleep

  - 功能说明：设置合理的等待时间

  - 配置要点：根据采集量调整等待时间


### **4\. 获取采集数据**

  - 模块名称：Get Dataset Items

  - 功能说明：获取采集到的推文数据

  - 配置要点：配置正确的数据获取格式


### **5\. 数据查重检查**

  - 模块名称：Search Objects

  - 功能说明：避免重复采集同一推文

  - 配置要点：设置合适的查重规则


### **6\. 创建数据库记录**

  - 模块名称：Create a Database Item

  - 功能说明：存储新采集的推文

  - 配置要点：确保数据正确写入


## **重要提示**

  1. Apify 平台上有多个可以采集推文的 actors，但大多需要付费，这个工作流使用的是如下actor：<https://console.apify.com/actors/2dZb9qNraqcbL8CXP/input>

![](https://static.xiaobot.net/file/2024-11-19/720345/3d94761cefb108b8b88bec4f05354fa3.png)

  2. 根据自己的需求进行参数的设置，然后插入到make的apify模块之中。

  3. 相关过程可参考视频教程：[观看教程](<https://xiangyugongzuoliu.notion.site/2-Apify-Notion-make-Youtube-d2e2f51fc27f4a88ac80765cf95f4634?pvs=4>)


## **资源下载**

  - 工作流下载：[点击下载](<https://pan.quark.cn/s/997508ad9334>)

提取码：9QK9

  - Notion 模板：[查看模板](<https://xiaoyuzaici.notion.site/143017a905fd80d98330ec9e749c43e2?v=143017a905fd81e7aa5a000c1b44eee3&pvs=4>)


## **使用建议**

  1. 首次使用建议先看完视频教程

  2. 测试时可以先采集少量数据

  3. 注意监控 Apify 的使用额度


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-19/720345/4db63a3b924ed4c7844f498e83efa3f3.png)