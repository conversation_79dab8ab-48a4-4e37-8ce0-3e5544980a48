---
title: "Make中文教程：指数退避（Exponential Backoff）"
created_at: "2024-08-30T01:57:25.000000Z"
uuid: "92158494-8d2f-4c2b-afc8-4804b47d40e6"
tags: ['Make基础教程']
---

# Make中文教程：指数退避（Exponential Backoff）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于指数退避的教程。

  1. **教程简介** ：教程介绍了如何使用“指数退避”方法自动安排Make.com场景重新运行的时间，并在发生特定错误时逐步增加延迟时间。

  2. **学习目标** ：了解并掌握Make.com中“指数退避”的基本原理及其应用，确保当场景出错时能有效地自动安排重新运行，提高场景的稳定性和可靠性。

  3. **学习内容** ：教程涵盖了“指数退避”方法的定义及其在Make.com中的应用，详细说明了发生错误时的具体时间间隔，并讨论了场景重新运行的处理机制及相关设置。

  4. **概念理解** ：指数退避是一种用于自动增加场景重新运行时间间隔的方法，避免因连续错误导致的系统过载。包括特定错误类型及其对应的时间延迟计划。

  5. **任务练习** ：设置一个包含可能出现ConnectionError的场景，并启用顺序处理和允许存储未完成执行选项，模拟场景错误并观察指数退避的重新运行机制。


# **指数退避**

使用指数退避自动安排具有不断增加时间延迟的场景重新运行。当场景中的模块输出时,会触发场景重新运行:

  - ConnectionError

  - ModuleTimeoutError

情景重复运行之间的时间延迟为：

  -  1 分钟

  -  2 分钟

  -  5 分钟

  -  10 分钟

  -  1 小时

  -  3 小时

  -  12 小时

  -  24 小时

这意味着当您场景中的模块输出 ConnectionError 时,因为第三方服务不可用,Make 会在错误发生一分钟后重新运行该场景。

如果在重新运行期间模块输出了 ConnectionError ，Make 会在 2 分钟后安排另一次重新运行,如此循环。如果第 8 次尝试失败,Make 会禁用该场景的调度。

![](https://static.xiaobot.net/file/2024-11-29/720345/94191741b0a5624b375a8c819fd38553.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-08-30/720345/c93e0b149649c323ba67867ba0b89271.png)