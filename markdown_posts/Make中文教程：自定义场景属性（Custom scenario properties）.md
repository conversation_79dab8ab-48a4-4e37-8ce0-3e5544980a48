---
title: "Make中文教程：自定义场景属性（Custom scenario properties）"
created_at: "2024-09-21T12:22:06.000000Z"
uuid: "e68ad2a5-0a4b-4787-a078-8417fff9dc10"
tags: ['Make基础教程']
---

# Make中文教程：自定义场景属性（Custom scenario properties）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于自定义场景属性的教程。

**教程简介** ：本教程介绍如何在Make.com中创建和使用自定义场景属性。该功能允许企业用户添加、编辑和管理场景的可定制元数据，以提升组织和排序场景的效率。

**学习目标** ：学习如何通过创建和管理自定义场景属性，优化Make.com中的场景处理流程，提升工作效率，并掌握为场景添加可定制元数据的方法。

**学习内容** ：

  1. 创建、自定义和管理场景属性。

  2. 应用和编辑自定义属性。

  3. 使用自定义属性来过滤和排序场景列表。

  4. 了解自定义属性的字段类型及其应用场景。

**概念理解** ：

  - 自定义场景属性：用户为场景添加的可定制元数据，帮助组织和排序场景。

  - 字段类型：属性的具体类型，如短文本、长文本、数字、布尔值、日期、下拉框和多选。

  - 过滤与排序：利用自定义属性在场景列表中进行搜索、过滤和排序操作。

**任务练习** ：

  1. 登录组织仪表板，创建一个新的自定义场景属性，设置名称、标签，并选择字段类型为“短文本”。

  2. 应用该自定义属性到一个具体的场景中，并尝试编辑它。

  3. 通过自定义属性过滤场景列表，确认过滤结果是否准确。

  4. 按不同自定义属性对场景列表进行排序。


# **自定义场景属性**

![](https://static.xiaobot.net/file/2024-11-24/720345/bcd12c44d0c7c0b507a89eb3654d1114.png)

要帮助组织和排序场景,您可以使用自定义场景属性来添加可自定义的元数据到您的场景中。您创建的属性将出现在场景详细信息页面上,也会出现在场景表格视图的可排序列中。您可以添加任意数量的自定义属性,并编辑哪些属性出现在您的表格视图中。

所有组织成员都可以查看和使用自定义场景属性。这包括使用自定义属性来过滤场景的表格视图。

对于所有属性,您可以定义以下内容:

  - 名称 - 一种用作唯一标识符的描述性名称,仅在管理自定义属性时出现

  - 标签 - 在表视图和详细页面上显示的名称

  - 提示文本 - 您添加的一条简短消息,用于帮助您的组织成员使用您的自定义属性

  - 字段类型 - 属性的类型,例如,下拉菜单或布尔值

  - 必需的 - 选择"是"以将属性设置为在应用自定义属性至场景时必须包含

自定义场景属性支持以下字段类型:

  - 简短文本 - 最多 200 个字符; 适用于电子邮件地址、网址

  - 长期以来,人类一直对自然界的运作方式感到好奇。从古老的文化中我们可以看到,人类通过观察和思考,试图找到解释自然现象的方法。科学的发展为人类提供了更深入的理解。科学家们通过实验和观察,发现了自然界运转的规律,并将其概括为各种理论和定律。 这些理论和定律不仅解释了自然现象,也为人类的发展提供了重要的工具。通过应用科学知识,人类创造了各种技术,改变了生活方式,提高了生活质量。从农业到医疗,从交通到通讯,科学技术的进步使我们的生活更加便利和富足。 然而,科学发展的同时也带来了一些问题。人类活动对环境的影响日益严重,许多自然资源正面临枯竭的威胁。我们必须以更加负责任的态度对待自然,采取行动保护环境,维持可持续发展。只有这样,人类才能与自然和谐相处,共同走向美好的未来

  - 数字 - 支持整数

  - 布尔型 - 出现为单选按钮的是/否值

  - 根据 ISO 8601 标准的日期和时间

  - 下拉列表 - 用户只能从中选择一项的项目列表

  - 多项选择 - 用户可以选择多个值的项目列表


## **  创建自定义属性**

您的组织仪表板有一个场景属性选项卡,您可以在此处创建和管理您的自定义属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/2ff2c7b9fb88e628a878d2099ca4a68f.png)

  1. 在左边边栏中，点击组织。

  2. 点击"场景属性"。

  3.  点击添加属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/057085ea35775a25ed2d151a1c0b0bdd.png)
  4. 在添加自定义属性对话框中输入并选择您的信息。有关每个字段的详细信息,请参见上方的说明。单击保存以创建您的自定义属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/84635999d54684744e3f555ea4797f2b.png)

您的自定义属性出现在"方案属性"选项卡的列表中。


## **使用自定义场景属性**

所有组织成员可以:

  - 对场景应用并编辑自定义属性。

  - 使用自定义属性来过滤和排序场景列表。


### **应用和编辑自定义场景属性**

  1. 在左侧侧边栏中,单击"场景"。

  2. 单击

![](https://static.xiaobot.net/file/2024-11-24/720345/2d3234665cd2d602361d4212af454d78.png)

在您要编辑自定义属性的方案旁边。

  3. 单击编辑自定义属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/e0fed0384dee1cf3d2034c68c1185aa8.png)
  4. 在对话框中编辑您的场景属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/5178d12283c8c66ed618f739cea6ed50.png)
  5.  点击保存。

对话框关闭,您的属性会出现在场景表视图中。

![](https://static.xiaobot.net/file/2024-11-24/720345/336b288aa91661ffcc1ee1c90fc047af.png)


### **  过滤场景列表**

  1. 在左侧侧边栏中,单击"场景"。

  2. 点击切换进入表格视图。

![](https://static.xiaobot.net/file/2024-11-24/720345/b4ad9687e9e054f718356b0fe6f4bcb9.png)
  3.  点击。

![](https://static.xiaobot.net/file/2024-11-24/720345/9e7995573591be54beddaa4284e82905.png)

  4. 在对话框中，选择您想在表格中查看的属性。

![](https://static.xiaobot.net/file/2024-11-24/720345/2ac44c8c178b7e98b6c33b610ecb3e48.png)
  5.  点击应用。

  6. 使用

![](https://static.xiaobot.net/file/2024-11-24/720345/05bcfc6d604caa95c805fd7176653765.png)

可以根据该属性筛选你的场景。

只有符合您过滤条件的场景会出现在列表中。


### **  排序场景列表**

默认情况下，方案按名称排序，活动方案位于列表顶部。但是，可以在列表视图和表格视图中更改排序。

 从列表中:

  1. 在左侧边栏中选择「场景」。

  2. 点击排序按钮。

  3. 选择一个排序选项。您可以根据名称或创建日期对场景进行排序。

![](https://static.xiaobot.net/file/2024-11-24/720345/dac1b947f54039f12e85b7940e614fd0.png)

根据您选择的选项，这些情况现在将被排序。

 从桌子上:

  1. 在左侧侧边栏中,单击"场景"。

  2. 点击切换进入表格视图。

![](https://static.xiaobot.net/file/2024-11-24/720345/26a08c1c738b556e2361c9d6049bfd06.png)
  3. 单击列的标题可按该属性对列表进行排序。再次单击可查看降序排列的列表。

根据您点击的属性,表格会显示您的场景排序。


## **  管理自定义属性**

您可以从组织仪表板的方案属性中编辑或删除您的自定义方案属性。


### **  编辑自定义属性**

  1. 在左边边栏中，点击组织。

  2. 点击"场景属性"。

  3. 找到您要编辑的自定义属性并单击编辑。

  4. 在对话中根据需要更改信息。

  5.  点击保存。

屏幕底部出现确认消息，您的更改会在表格中显示。


### **  删除自定义属性**

  1. 在左边边栏中，点击组织。

  2. 点击"场景属性"。

  3. 找到你想要删除的自定义属性,然后点击。

![](https://static.xiaobot.net/file/2024-11-24/720345/8205ba7b960a4333077d91e277e9255d.png)
  4.  点击删除。

该物品从桌子上消失了。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/7aa3422480bf4a3d80bb888b14258f91.png)