---
title: "Make中文教程：场景执行历史（Scenario execution history）"
created_at: "2024-09-21T15:03:42.000000Z"
uuid: "6c271a10-03a8-4b52-a926-34c12c9bd085"
tags: ['Make基础教程']
---

# Make中文教程：场景执行历史（Scenario execution history）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于场景执行历史的教程。

  1. **教程简介** ：教程介绍如何通过Make.com的场景执行历史功能查看场景运行信息，包括运行日期、状态、运行时长、操作次数和数据传输大小等详细记录。

  2. **学习目标** ：掌握如何使用Make.com查看场景的执行历史和详细信息，了解场景运行中的关键指标以及调试工具的使用方法。

  3. **学习内容** ：

     - 访问场景详情中的历史按钮

     - 查看每次场景运行的详细信息

     - 理解运行日志的保存时间与定价计划的关系

     - 使用Make开发工具检查HTTP请求和响应

  4. **概念理解** ：

     - **场景 \(Scenario\)** ：在Make.com中定义的一系列自动化操作。

     - **操作次数** ：指每次场景运行中执行的具体步骤总数。

     - **Make开发工具 \(Make DevTool\)** ：用于检查HTTP请求和响应，有助于调试和找出错误详情。

  5. **任务练习** ：

     - 登录Make.com并创建一个简单的场景。

     - 运行场景并使用历史按钮查看运行日期、状态、运行时长等详细信息。

     - 使用Make开发工具检查一个操作的HTTP请求和响应，记录并分析结果。


# **场景执行历史**

在场景详细信息中单击"历史记录"按钮会显示所有场景运行的信息。

![](https://static.xiaobot.net/file/2024-11-23/720345/fb946681064e0c93c75fdf7acda02979.png)

以下是每次运行显示的详细信息：

  -  运行日期

  - 状态\(成功或失败\)

  -  运行时长

  -  操作数量

  -  传输数据的大小

  - 详细信息的链接

![](https://static.xiaobot.net/file/2024-11-23/720345/71093eeefc75483d6e69b359b3004dd1.png)


## **  场景细节**

要查看所选场景运行的详细信息,包括已处理的捆绑包,请点击"详细信息"链接。

![](https://static.xiaobot.net/file/2024-11-23/720345/f19eeb75058c1a73be53ee5c8e6b1462.png)

使用 Make DevTool，您还可以检查模块操作期间进行的 HTTP 请求和响应。这些信息都已记录下来:

  - 所有模块的手动场景执行

  - 如果在执行抛出错误的模块时发生错误，则计划执行


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-23/720345/794691a2a6d4dbf3aad8dbd0d01a1ec0.png)