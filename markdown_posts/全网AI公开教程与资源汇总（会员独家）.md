---
title: "全网AI公开教程与资源汇总（会员独家）"
created_at: "2024-10-01T06:34:05.000000Z"
uuid: "c76a17f9-b768-44e5-a8aa-df207fa7a6b4"
tags: ['AI教程与资源']
---

# 全网AI公开教程与资源汇总（会员独家）

**标签**: AI教程与资源

该教程由翔宇整理，汇集了AI提示词、原理讲解及常用工具的网站资源，旨在为会员提供便捷的定向学习路径。通过这份汇总，大家可以根据自己的兴趣和需求，高效开展AI相关学习。AI是一个广泛且复杂的领域，保持持续学习的心态，按需学习能够提高效率。此教程将持续更新，任何优质且有价值的内容都会及时补充。

![](https://static.xiaobot.net/file/2024-10-01/720345/6048ac4529f0a98b6beb27805adef371.png)


# 1\. AI知识库


### 通往 AGI 之路

<https://waytoagi.feishu.cn/wiki/QPe5w5g7UisbEkkow8XcDmOpn8e>

这里旨在提供一个全面系统的 AI学习路径， 帮助您了解从 AI常见名词到 AI 应用等各方面知识。

![](https://static.xiaobot.net/file/2024-10-01/720345/7e5863dca3e3a9c3f7c0f9e8be2eb5cf.png)


### **AGI 掘金知识库**

<https://agijuejin.feishu.cn/wiki/UvJPwhfkiitMzhkhEfycUnS9nAm>

AGI掘金知识库是一个专注于通用人工智能（AGI）研究与应用的创新平台。我们的使命是推动AGI技术落地，帮助各行业创造价值。网站提供最新资讯、实用解决方案和有趣服务，旨在让AI技术更易获取。我们希望为爱好者提供个性化的知识工具，帮助他们掌握相关知识并探索新的创业机会。

![](https://static.xiaobot.net/file/2024-10-01/720345/3d6f1081ea46180d1a14442965eba758.png)


### 顺众AI知识库

<https://shunzhong.feishu.cn/wiki/RvO4wxV4aiIEaGk5f4HcfX9znMd>

3000+AI文章和资料，200+AI变现案例。

![](https://static.xiaobot.net/file/2024-10-01/720345/7526c2a653dd2d7e945000840c86486e.png)


### 一堂•AI未来实验室

<https://yitanger.feishu.cn/wiki/HGBBwSm6diFI9AkW0G0cQpWknug>

该网站为用户提供一个全面的人工智能学习框架。通过了解AI行业动态和掌握实用工具，用户可以提升自身的实战能力，找到在业务中降本增效的应用场景。此外，网站还鼓励用户借鉴他人的经验，沉淀知识技能，并探索AI的商业化机会。这是一个帮助您在变革时代掌握AI相关知识、开拓创业思路的重要平台。

![](https://static.xiaobot.net/file/2024-10-01/720345/af875adbe89515f8133dee206e80e08d.png)


### 艾文AIGC知识库

<https://tauacgr5lqv.feishu.cn/docx/ZnPedHE3loaQ5MxwE7TctC6PnNh>

超1000+AI文章教程，500+行业报告，300+变现案例。

![](https://static.xiaobot.net/file/2024-10-01/720345/58061ae89a641d91aedce8b26105ca90.png)


### AI研习社

<https://aiyanxishe.feishu.cn/wiki/wikcnu3kSXr75E50w8PFefhJMYf>

该网站汇聚了丰富的人工智能工具和教程，包括ChatGPT、MidJourney绘画、Stable Diffusion绘图、Sora视频生成等，旨在帮助用户掌握AI技术，提升实战能力。用户可以探索AI落地解决方案，如虚拟数字人和数字交互人，以实现业务降本增效。此外，网站还提供AI行业的商业模式研究、大厂实战案例及副业变现案例库等专题内容，为用户提供全面的学习和应用资源，助力他们在AI领域开拓新的机会。

![](https://static.xiaobot.net/file/2024-10-01/720345/2011812e94c26c4eea6b6a98351a1a9a.png)


### **奔赴 AGI**

<https://aigcreative.feishu.cn/wiki/DQGiwCAwji9ZoAkwZ7Tcvqhvngf>

奔赴AGI知识库是一个完全免费、开放的平台，专注于收集和分享最新的AIGC学习资源。我们相信知识分享能促进进步和激发创新。平台的使命是提供全面的AIGC学习材料，愿景是成为该领域的资源共享中心。我们的价值观包括开放分享、追求卓越、持续学习和互相尊重。欢迎加入我们，共同探索AI的无限可能！

![](https://static.xiaobot.net/file/2024-10-01/720345/f7b687a950369a914a6834f01498a0fb.png)


### **AI 洞察者中心**

<https://bluefocus.feishu.cn/wiki/XcIiwWooAi6OgyksHcYc5X3knde>

AI洞察者中心知识库专为AI产品创作者、想了解AI使用者、学习AI变现的人群以及希望提升软实力的学生而设。平台提供友好的教学体系、每日更新的AI日报、每周更新的AI Agent智能体榜单、精选网站和变现方式。此外，活跃的社群汇聚了众多高手，为初学者提供支持，帮助大家更好地利用AI技术。

![](https://static.xiaobot.net/file/2024-10-01/720345/097d02e1e1a064f29c56bc61730e4e2f.png)


### **AI 鹊桥 · 智库**

<https://g7c5xfa0cz.feishu.cn/wiki/CEFJwoogJiIRG7kUISbc6JctnJg>

AI鹊桥·智库是一个专注于AI实践的免费知识库，旨在为用户提供AI商业化应用、行业动态、实用工具分享、研报阅读和教程技巧教学。平台汇聚了众多志同道合者，促进交流与分享。在这里，用户可以了解行业动态、掌握实用工具、沉淀知识技能，并探索实践之路，是学习和了解AI知识的理想选择。

![](https://static.xiaobot.net/file/2024-10-01/720345/aa9ef375337694e1471d228a2e535aff.png)


### **水木 AI 知识库**

<https://sqesbpzxe8w.feishu.cn/wiki/QeuwwiVL8izdx2kYznTcsTzMn1f>

水木AI知识库由水木AI社区发起，专注于收集高质量的AI行业报告和学习资料，供从业者学习使用。该平台还定期组织线上线下的讲座、沙龙和名企参观等丰富的社区活动，欢迎大家积极参与。

![](https://static.xiaobot.net/file/2024-10-01/720345/2d6e4d5f35c07babab447b2216863963.png)


### **AI +X | Datawhale 学习指南**

<https://datawhaler.feishu.cn/wiki/X9AVwtmvyi87bIkYpi2cNGlIn3v>

“AI +X | Datawhale学习指南”采用开源学习模式，旨在帮助AI学习者与知识、人与人、场景之间建立连接，推动AI人才的培养。该学习指南完全开源，用户可以下载和复制，引用时请注明来源，并遵循CC-BY-NC-SA协议。它提供了丰富的学习路径、线上活动以及AI与各学科和行业的结合，适合所有希望深入了解AI的学习者。

![](https://static.xiaobot.net/file/2024-10-01/720345/21eacee2b3d05e1db9054bb4bed3fc6c.png)


### ChatGPT入门指南

<https://mi2zo429cdh.feishu.cn/docx/IGd1d8UQuo9DAkxfHqAcnx5OnFd>

这里为小白们汇总与GPT相关的资源和教程，专注于打造一部最好的GPT入门指南。

![](https://static.xiaobot.net/file/2024-10-01/720345/9305253bee3e7c8491b107a6faed8f4d.png)


### ChatGPT 学习手册

<https://nujuo8y1qx.feishu.cn/docx/AdqEdlT52oBiawx6Vv2cc89DnLb>

GPT 的相关资讯动态，研究报告，赚钱案例，实用场景等12个板块内容。

![](https://static.xiaobot.net/file/2024-10-01/720345/b062c2d772ca7773af2ddfe64773fe6d.png)


### **Awesome-ChatGPT**

<https://github.com/ai919/Awesome-ChatGPT>

该网站是一个名为 **Awesome ChatGPT 中文全指南** 的GitHub项目，旨在提供有关ChatGPT及其应用的持续更新知识库。它汇集了大量与人工智能相关的资源，包括教程、提示词和实用工具，帮助用户更好地理解和使用ChatGPT。该项目内容主要基于互联网公开信息，适合对AI领域感兴趣的学习者和开发者。用户可以在此找到丰富的参考资料和链接，促进个人学习和技术提升。

![](https://static.xiaobot.net/file/2024-10-01/720345/a922d61eb65567320f5114b3557c7bad.png)


### **Awesome AIGC Tutorials**

<https://github.com/luban-agi/Awesome-AIGC-Tutorials>

该网站是 **Awesome AIGC Tutorials** 的GitHub项目，旨在提供关于生成式人工智能内容（AIGC）的丰富教程和资源。项目汇集了多个领域的学习材料，包括文本生成、图像生成和音频处理等，适合希望深入了解AIGC技术的开发者和研究者。用户可以在此找到实用的示例、工具和最佳实践，以帮助他们更有效地应用生成式AI技术。该项目的内容持续更新，具有较高的实用价值。

![](https://static.xiaobot.net/file/2024-10-01/720345/e9cf56116479d74d4038ca38de5aafcb.png)


### Awesome-ChatGPT

<https://github.com/dalinvip/Awesome-ChatGPT>

**Awesome-ChatGPT** 是一个在GitHub上汇总和学习ChatGPT及相关人工智能技术的资源库。该项目旨在持续更新与AIGC（生成式人工智能内容）相关的动态、研究报告和商业应用，涵盖了ChatGPT、AI绘画、大模型研究等多个领域。用户可以获取最新的技术解读、论文资料以及代码实现，促进对AI技术的深入理解与应用。此外，网站还鼓励用户参与交流与共享，推动社区合作与知识传播。

![](https://static.xiaobot.net/file/2024-10-01/720345/d49829fece494dbd819e838e8899a5a9.png)


### Awesome-ChatGPT-resource-zh

<https://github.com/DeepTecher/awesome-ChatGPT-resource-zh>

该网站是一个中文的ChatGPT资源汇总平台，旨在为用户提供精选的OpenAI ChatGPT及相关技术资源。内容涵盖多个方面，包括通用资源、社区讨论、学术论文、提示示例以及各类工具和应用。用户可以在这里找到最新的研究成果、API工具、Chrome插件和其他平台使用ChatGPT的方式。此外，网站还关注国内外AI大模型的动态，帮助用户了解行业趋势和竞争情况。整体上，这是一个促进学习与交流的重要资源库。

![](https://static.xiaobot.net/file/2024-10-01/720345/872fcf716b8df0777e23e1e176c40ed9.png)


### awesome-ChatGPT-repositories

<https://github.com/taishi-i/awesome-ChatGPT-repositories>

该网站是一个精心策划的资源库，专注于与ChatGPT相关的开源GitHub项目。它提供了丰富的资源，包括各种工具、提示、聊天机器人和浏览器扩展等，旨在帮助用户更好地利用ChatGPT。用户可以通过Hugging Face Spaces搜索这些资源，方便查找所需的项目和工具。此外，网站还鼓励社区贡献，保持内容的更新和多样性。截至2024年9月26日，该库已列出1770个相关项目，是AI开发者和爱好者的重要参考。

![](https://static.xiaobot.net/file/2024-10-01/720345/c05c8fe043a0bbf81a6a5fea03ad28a3.png)


### Al知识库

<https://ssw9noe1h6.feishu.cn/wiki/wikcn59pPXfAiF1ONajdPzvhA9d>

该网站致力于为用户提供全面的人工智能资源，帮助他们轻松获取最新的AI行业动态和信息。用户可以掌握各种实用工具和技巧，提升实战能力，并借鉴他人的经验，沉淀自己的知识技能。此外，网站还探索AI的商业化路径，从实战角度分析如何更好地实现盈利。无论您是AI初学者还是行业专家，这里都是您获取知识、提升技能和拓展视野的理想平台。

![](https://static.xiaobot.net/file/2024-10-01/720345/25b224fda6e535437f2034285630f7d9.png)


# 2\. 提示词免费中文教程


### **Prompting Guide**

[**https://www.promptingguide.ai/zh**](<https://www.promptingguide.ai/zh>)

这个网站是一个全面的提示工程指南，旨在帮助用户理解如何与大型语言模型（LLM）进行有效互动。

\- 提示设计技巧：提供高效提示设计的方法。

\- 应用场景：讨论不同领域如何利用提示工程来提升工作效率。

\- 安全性提升：介绍如何通过提示工程增强模型的安全性。

![](https://static.xiaobot.net/file/2024-10-01/720345/676c9e8290ee9bc4e90b8962649ca08e.png)


### **Learn Prompting**

[**https://learnprompting.org/zh-Hans/docs/introduction**](<https://learnprompting.org/zh-Hans/docs/introduction>)

这是一个为初学者和有经验的用户提供的开源提示工程课程。课程涵盖从基础到高级的多种技术。

![](https://static.xiaobot.net/file/2024-10-01/720345/8b0dde975c430c5faf5e448e45d5ebe9.png)


### **Learning Prompt Wiki**

[**https://learningprompt.wiki/zh-Hans/docs**](<https://learningprompt.wiki/zh-Hans/docs>)

该维基网站提供了丰富的提示工程资料，涵盖各种主题和技术，适合不同级别的学习者。

![](https://static.xiaobot.net/file/2024-10-01/720345/c69b9ea6075ae059137233b9969a700c.png)


### **Fresns Prompts Guide**

[**https://prompts.fresns.cn/guide/**](<https://prompts.fresns.cn/guide/>)

此网站专注于提供实用的提示指南，帮助用户在不同场景中高效利用提示工程。

![](https://static.xiaobot.net/file/2024-10-01/720345/f568fb3e84147d62d00c901dd1235b2c.png)


### 提示词工程指南

<https://github.com/yunwei37/Prompt-Engineering-Guide-zh-CN?tab=readme-ov-file>

该网站是 **Prompt Engineering Guide 中文版** 的GitHub项目，旨在为用户提供关于提示工程的全面指南。项目包含了提示工程的基本概念、最佳实践和应用示例，帮助用户有效地与AI模型进行交互。内容结构清晰，适合希望深入了解如何优化提示以提高AI响应质量的开发者和研究人员。该指南为中文用户提供了易于理解的资源，促进了相关知识的传播与应用。

![](https://static.xiaobot.net/file/2024-10-01/720345/6860c0c5a5b668d705784ccfa1340d68.png)


### LangGPT

<https://github.com/langgptai/LangGPT>

该网站是 **LangGPT** 的GitHub项目，旨在帮助用户轻松创建高质量的ChatGPT提示。通过结构化的模板方法，LangGPT将提示设计过程简化为类似编程的直观体验。项目提供多种功能，包括自动化提示生成、角色模板和示例，适合希望提升与大型语言模型交互效率的开发者。用户可以快速上手，利用该工具创建个性化的AI交互体验。

![](https://static.xiaobot.net/file/2024-10-01/720345/4a90f65bd27dc15b9a34622ae5e42f77.png)


# 3\. 提示词工具


### **PromptPerfect**

[**https://promptperfect.jina.ai/**](<https://promptperfect.jina.ai/>)

概述

PromptPerfect 是一个在线工具，旨在优化用户的提示输入，以提高生成内容的质量。

主要功能

\- 自动优化提示词：根据用户输入自动生成更有效的提示。

\- 多种模式选择：支持不同类型的生成任务，包括文本与图像等。


### **AI提示词优化**

[**https://ai.fuckjava.mom/**](<https://ai.fuckjava.mom/>)

该网站是一个专注于优化提示词的在线平台，旨在帮助用户提高与AI模型（如ChatGPT）互动的效果。


# 4\. AI资源导航


### **AI 产品数据榜**

<https://zw73xyquvv.feishu.cn/wiki/UH5QwtUWtis1gTk4R6rcnWK2nZc>

AI产品数据榜由郎瀚威主导，精心筛选并分析了超过10,000个Web端AI产品，涵盖访问量、增长速度等关键数据，并对这些产品进行了分类，涉及50多个AI细分领域。最终，该榜单为用户呈现了内容丰富且观点独到的成果，是了解AI产品市场的重要参考。

![](https://static.xiaobot.net/file/2024-10-01/720345/13138aad2b4bdfd16cc2bc65480b2c97.png)


### **AI 产品榜**

<https://dnipkggqxh.feishu.cn/wiki/YTIUwM6Vmij4IQkSm9PctPWunIb>

AI产品榜单由李榜主主导，联合30多位AI领域公众号主理人发布，是被众多权威媒体和专家引用的榜单。该榜单经过精心筛选，分析了超过10,000款AI产品，涵盖关键数据并进行分类，涉及100个细分领域，为用户呈现内容丰富、观点独到的AI产品排名。

![](https://static.xiaobot.net/file/2024-10-01/720345/5193f29c9605d7d86cc9ee1251250119.png)


### **AI Collection**

<https://github.com/ai-collection/ai-collection?tab=readme-ov-file>

该网站是 **AI Collection** 的GitHub项目，旨在展示生成式人工智能（Generative AI）应用的广泛景观。项目汇集了众多优秀的生成式AI应用，涵盖文本、图像、音频等多个领域，提供了丰富的示例和资源。用户可以通过该项目了解最新的AI技术和应用趋势，适合对生成式AI感兴趣的开发者和研究人员。该项目内容持续更新，致力于促进AI技术的学习和应用。

![](https://static.xiaobot.net/file/2024-10-01/720345/47c401228fdedd97ee7e6d3b54a62f58.png)


### Awesome AGI Resources

<https://github.com/EmbraceAGI/Awesome-AGI>

**Awesome-AGI** 是由EmbraceAGI团队在GitHub上创建的一个资源库，专注于人工通用智能（AGI）研究。该仓库汇集了多种工具、框架、论文和项目，旨在为研究人员和开发者提供全面的信息支持。用户不仅可以浏览现有资源，还可以通过贡献新内容来参与社区建设。凭借其丰富的资料和活跃的互动，Awesome-AGI成为了AGI领域的重要参考，助力推动相关技术的发展与创新。

![](https://static.xiaobot.net/file/2024-10-01/720345/8e22cb6896a0b6d7f92b100d892d053f.png)


### Awesome AI Tools

<https://github.com/ikaijua/Awesome-AITools?tab=readme-ov-file>

**Awesome-AITools** 是由ikaijua创建的GitHub仓库，汇集了与人工智能相关的各种实用工具和资源。该项目涵盖多个类别，包括聊天机器人、AI搜索引擎、开源语言模型、图像生成、编程开发等，旨在为开发者和研究人员提供全面的支持。用户可以浏览并贡献新的工具和资源，促进AI领域的合作与创新。该仓库的内容丰富，适合希望深入了解AI技术的用户。

![](https://static.xiaobot.net/file/2024-10-01/720345/e5743ee6562863a58fb1c9c3b83979db.png)


### Awesome-AISourceHub

<https://github.com/AmbroseX/Awesome-AISourceHub>

该网站是一个专注于人工智能领域高质量信息源的资源库，旨在帮助用户获取最新的AI科技动态，避免信息孤岛和信息差。内容涵盖了多个平台，包括推特、公众号、播客、博客等，提供了丰富的AI相关资源和资讯。用户可以通过参与贡献，分享自己的信息源，从而共同建立一个更为全面的信息网络。此外，网站还提供了如何筛选优质信息流的技巧和建议，帮助用户在海量信息中找到有价值的内容。

![](https://static.xiaobot.net/file/2024-10-01/720345/e8deecc72ef13980c1f3244254495b6e.png)

通过以上网站和工具，您可以系统地学习和掌握提示工程的基本知识与应用技巧。无论您是初学者还是有经验的开发者，这些资源都能够为您的学习旅程提供有力的支持。建议您逐步探索每个网站，并根据自己的需求选择合适的提示词学习材料。