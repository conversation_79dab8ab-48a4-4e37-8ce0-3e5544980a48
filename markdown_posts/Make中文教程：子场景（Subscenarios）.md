---
title: "Make中文教程：子场景（Subscenarios）"
created_at: "2024-09-21T12:04:17.000000Z"
uuid: "026bcba6-6940-4ad9-a0ab-bdcfe7d1656b"
tags: ['Make基础教程']
---

# Make中文教程：子场景（Subscenarios）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于子场景的教程。

  1. **教程简介** ：本教程介绍如何通过Make.com的子场景功能，将多个场景连接在一起形成场景链，以实现顺序执行、数据传输和模块化管理。教程包括创建父场景、子场景以及连接它们的方法。

  2. **学习目标** ：学习如何通过Make.com的子场景功能构建场景链，以实现自动化任务的顺序执行、数据共享和模块化管理，从而提高工作流程的效率和可维护性。

  3. **学习内容** ：主要知识点包括场景链的基本概念、场景链中不同类型场景的作用、创建场景链的步骤及细节、场景链的调度设置以及如何从其他模块迁移至场景链。

  4. **概念理解** ：子场景是场景链的一部分，通过父场景或桥接场景触发，子场景自身不包含“运行场景”模块。场景链允许数据在不同场景之间传输并确保顺序执行，适合需要多步骤处理的复杂工作流程。

  5. **任务练习** ：创建一个简单的场景链，包括一个获取天气数据的父场景和两个通知场景（通过Gmail发送邮件及通过Slack发送消息），确保在父场景完成后正确触发子场景并传输数据。


# 子场景

子场景允许您将多个场景链接在一起以形成场景链。当场景链中的第一个场景完成时,它会触发下一个场景。这在具有不同阶段或需要按特定顺序运行的工作流程中很有用。创建包含子场景的场景链带来以下特性:

  - 按顺序运行:当第一个场景完成时,它会自动触发下一个场景的执行。这确保任务按所需顺序执行,无需手动干预。

  - 数据传输：第一个场景可以将数据发送到第二个场景。这样可以将第一个场景的结果用作第二个场景的输入。

例如,您可以将订单号发送到更新仓库库存的方案,也可以发送到设置订单交付跟踪的另一个方案。

  - 模块化:您可以将复杂的场景分割成多个串联的场景。通过将复杂的工作流程分解为较小、可管理的场景,设计、调试和维护变得更加容易。此外,您可以将场景与未来的其他场景相链接,避免构建相同的逻辑。

例如,您可以设置多个场景来给客户发送通知电子邮件和订单发票。每个场景都从几个模块开始,用于获取用户数据。通过场景链接,您可以将重复的模块提取到一个单独的场景,并将其放在场景链的开始位置。


## **情景链中的情景类型**

在情景链中有三种类型的情景:

  - **  家长场景:**

    - 一个父场景启动了场景链。

    - 一个父级场景会触发桥接或子级场景。

    - 一个父场景可以有多个运行场景模块。

  - **  桥梁场景:**

    - 桥梁场景将子场景或其他桥梁场景与场景链中的父场景连接起来。

    - 桥接场景由父场景或另一个桥接场景触发。此外,桥接场景还会触发另一个桥接场景或子场景。

    - 一个桥梁方案可以有多个运行方案模块。

  - **  儿童情景：**

    - 一个儿童场景结束了场景链。

    - 儿童场景由父场景或桥接场景触发。

    - 儿童场景没有任何运行场景模块。

以下图表显示了父、桥和子场景之间的关系:

![](https://static.xiaobot.net/file/2024-11-24/720345/2c83bf964d13962dad1f9335eb922366.png)

场景链甚至可以创造分支,就像这样:

![](https://static.xiaobot.net/file/2024-11-24/720345/69b922073605da2e942bbdd55bc2190a.png)


## **  创建一个场景链**

创建一个场景链，您需要创建多个场景。以下步骤重点介绍一个快速简单的演示,您无需构建复杂的场景即可尝试场景链接。请记住,当您想将复杂的场景分成多个部分时,场景链接会带来最大的收益。

创建情境链的步骤概述:

  1. 创建父场景。

  2. 创建子场景。

  3. 将场景与运行场景模块相连,以形成从父场景和子场景的链条。

  4. 设置场景链中场景的调度。

情景链可以像这样可视化:

![](https://static.xiaobot.net/file/2024-11-24/720345/922dbeac9dd8513afa2beac60a5d1b06.png)


### **创建父场景**

在父场景中，我们将使用 OpenWeather 应用程序获取天气数据。我们将使用"设置多个变量"模块设置我们将发送到子场景的数据。

  1. 在"创建"中，点击"创建新场景"。

  2. 在场景编辑器中,点击大加号图标并搜索 OpenWeather 应用程序。

在搜索结果中，选择"获取当前天气"模块。

将获取当前天气模块配置为获取您最喜欢城市的天气数据,并运行该模块以获取用于绘制地图的数据包。

  3. 将"获取当前天气"模块与"设置多个变量"模块连接起来。在"设置多个变量"模块中,创建新变量并映射要发送到子场景的数据。

例如，将获取当前天气模块中的温度和压力药丸映射到 london\_temperature 和 london\_pressure 变量:

![](https://static.xiaobot.net/file/2024-11-24/720345/eaf25e2da7d7f89f1583e1c3549880d7.png)

您创建了场景链中父场景的起始片段。

你的场景应该是这样的:

![](https://static.xiaobot.net/file/2024-11-24/720345/a269e6285b6f048a288577cf506380fd.png)


### **创建子场景**

在以下步骤中,我们将创建两个子场景。这两个子场景都将使用场景输入从父场景接收数据。

  1. 点击创建新场景。

  2. 在场景编辑器中,点击大加号图标并搜索 Gmail 应用程序。

在搜索结果中,选择发送电子邮件模块。

  3. 设置场景输入。该场景将需要 london\_temperature 和 london\_pressure 变量作为输入。

![](https://static.xiaobot.net/file/2024-11-24/720345/15bab6cb8f9a5362568224a2aa534876.png)
  4. 配置发送电子邮件模块。在电子邮件消息中使用场景输入。

 例如：

![](https://static.xiaobot.net/file/2024-11-24/720345/09c9ea8a800743b75ab19fcfb49f0a39.png)
  5. 确认模块设置并保存场景。

  6. 创建另一个子场景。

例如，如果您已经为它创建了连接，比如 Slack 应用程序，您可以使用另一个消息应用程序。

您的 Make 团队中有三个新的场景,但它们还未连接。让我们来解决这个问题。

![](https://static.xiaobot.net/file/2024-11-24/720345/e2aacbeff0d923800820631c7fc316bb.png)


### **将父场景与子场景相连**

以下步骤描述如何使用"情景 > 运行情景"模块将父情景连接到子情景。

  1. 转到带有"获取当前天气"和"设置多个变量"模块的第一个场景。

在方案中添加"运行方案"模块。

  2. 在"情景"字段中，选择您希望使用该模块触发的情景。如果您正在遵循示例，请选择发送电子邮件通知的情景。

请注意,这些情景都被组织在情景文件夹中。

  3. 为选定的方案检索场景输入。将 london\_temperature 和 london\_pressure 变量映射到场景输入字段。

![](https://static.xiaobot.net/file/2024-11-24/720345/d90796175eb27184ffc3d47b3fa3cbff.png)

在模块设置的顶部,有一个"非活动"标签告诉您子方案处于非活动状态。还有子方案 ID、方案文件夹和名称,以及一个打开新标签页并在方案编辑器中打开子方案的预览链接。

  4. 单击"确定"确认设置并保存父方案。

  5. 再次打开"运行场景"模块设置,点击"预览"链接进入子场景。

将儿童场景计划设置为按需执行。启用该场景并保存该场景。

  6. 对于第二个孩子的情况重复相同的步骤。

当您将这两个场景与父场景连接起来时，您的场景列表应该如下所示:

![](https://static.xiaobot.net/file/2024-11-24/720345/e29ae7069ebbfb70ca1e8c1eb757401a.png)![](https://static.xiaobot.net/file/2024-11-24/720345/6652bead78abe59405d812a163b7f6f2.png)


### **设置场景链中各场景的时间表**

在上一个部分中,我们将场景调度设置为按需,使场景可以在不定期运行的情况下启用进行测试。

![](https://static.xiaobot.net/file/2024-11-24/720345/e08814d629e07edb6dfc382dadfedae0.png)

在示例中,设置父场景的计划,但让子场景按需运行。

子方案使用父方案的方案输入,而无默认值。如果子方案自行运行时间表,它们将不包含任何方案输入值。

![](https://static.xiaobot.net/file/2024-11-24/720345/ea60d3bddc58075db9d36c0cc2447c46.png)


## **从使用"运行场景"模块迁移**

从"制作"应用程序的"运行场景"模块中,您可以触发从另一个场景运行场景。从"场景"应用程序的"运行场景"模块工作方式类似。

场景链接相比于使用 Make 应用触发场景的主要优势是场景链接具有管理实用性和可审查性。您可以在关系树选项卡中查看互连的场景,并可在场景列表和历史记录中查看链接的场景。

要全面比较从一个场景触发另一个场景的各种方法,请查看比较部分。

开始使用场景链接而不是从 Make app 运行场景模块:

  1. 前往使用"创建 > 运行场景"模块的场景。

  2. 将"制作 > 运行场景"模块替换为"场景 > 运行场景"模块。

  3. 在"场景"栏中，选择您希望触发的场景。

  4. 可选：如果您在场景之间传输数据,请将场景输入映射到"运行场景"模块。

  5. 查看子场景的时间安排。子场景除了可以从场景链触发外,还可以拥有自己的时间安排。

您可以从"运行场景"模块设置中的预览链接访问子场景。

在任何场景的详细信息中查看关系树选项卡。关系树选项卡现在显示了两个场景。

父场景是包含"运行场景"模块的场景,子场景是由该模块触发的场景。


## **使用 HTTP 和自定义 Webhook 应用程序的迁移**

使用 HTTP > 发送请求模块,您可以向 Webhooks > 自定义网络挂钩模块发送数据。Scenarios > 运行场景模块的工作方式不同。运行场景模块通过场景输入发送数据到场景,而不是使用 HTTP 应用程序。

情景链接的主要优势在于其管理效用和可审核性。您可以在关系树选项卡中查看已连接的情景,并且可以在情景列表和历史记录中查看链接的情景。

你也可以使用场景输入来验证你发送到子场景的数据,而不是在 webhook 接收到数据后检查数据。

要全面比较从一个场景触发另一个场景的各种方法,请查看比较部分。

开始使用场景链接而不是 HTTP 和自定义网络钩子模块:

  1. 转到使用 HTTP > 发起请求模块的场景。

  2. 将 HTTP >发出请求模块交换到场景 >运行场景模块。

  3. 在"场景"栏中，选择您希望触发的场景。

  4. 可选：设置场景输入以将数据从父场景转移到子场景。Make 会在运行场景之前评估输入的结构,从而提高场景链的可靠性。

![](https://static.xiaobot.net/file/2024-11-24/720345/ec33f90444e197e60873b2c8d0fa6f51.png)
     1. 转到孩子场景。您可以在运行场景模块设置中使用预览按钮。

     2. 设置场景输入的结构。您可以手动设置输入结构,也可以使用集合数据类型并从数据样本生成输入结构。

查看场景输入文档以了解更多信息。

     3. 前往父场景。

     4. 将场景输入的数据映射到运行场景模块。

     5. 进入子场景并将自定义 webhook 的映射替换为场景输入。

  5. 从子场景中移除自定义 Webhook 模块。

![](https://static.xiaobot.net/file/2024-11-24/720345/d425513a2fe16e203228941b170c0b3d.png)
  6. 审查子场景的计划安排。子场景可以在从父场景触发时按需运行,或者可以在被场景链触发的同时拥有自己的计划安排。

在任何场景的详细信息中查看关系树选项卡。关系树选项卡现在显示了两个场景。

父场景是包含"运行场景"模块的场景,子场景是由该模块触发的场景。


## **  管理场景链**

场景链接简化了场景序列的管理,其中一个场景触发另一个场景。这种方法消除了需要命名约定或在文件夹中组织序列的需求。相反,您可以轻松地从 Make 中的多个位置监控和管理场景链中的场景。

![](https://static.xiaobot.net/file/2024-11-24/720345/fb11e5aabcfc7eb15957de3d1460ef1e.png)

  - 运行场景模块:当您在"场景">"运行场景模块"中选择一个场景时,您可以在模块设置的顶部看到有关该模块触发的场景的信息:

![](https://static.xiaobot.net/file/2024-11-24/720345/d742b0c0010781e82ef9d1b005764145.png)

 您可以看到:

    - 无论场景是启用\(激活\)还是禁用\(不活动\)

    -  情景 ID

    -  情景文件夹名称

    - 预览链接。点击预览链接会在新的浏览器标签页中打开链接的场景,并在场景编辑器中打开。

  - 关系树选项卡:属于场景链一部分的场景有关系树选项卡:

![](https://static.xiaobot.net/file/2024-11-24/720345/d3024444f421170d96f280df7398c810.png)
    1. 关系树选项卡显示与当前打开的场景直接相关的场景。

    2. 开放场景在场景关系树中缩进。

    3. 关系树中的一列显示了场景链中场景的关系。

  - 情景列表还显示了情景链关系:

![](https://static.xiaobot.net/file/2024-11-24/720345/80956f209a7027e4410ee8ab07d89040.png)
  - 运行场景模块输出的包包含对触发的场景运行的链接。

![](https://static.xiaobot.net/file/2024-11-24/720345/b2def18c3b233854c0a814ef84295a63.png)
  - 情景历史包含了触发情景运行的情景链接。

![](https://static.xiaobot.net/file/2024-11-24/720345/b0e3a0536a08d64b7dafc4c4d5f1b4bf.png)![](https://static.xiaobot.net/file/2024-11-24/720345/60b349973941b2954f9335de3ef59dfc.png)


### **添加场景到场景链中**

在现有情景链中添加一个场景:

  1. 创建您想添加到链中的场景。

  2. 转到您希望触发新场景的场景。

  3. 在父场景中,添加"场景 > 运行场景"模块。

  4. 在场景字段中,选择您想要触发的场景\(在步骤 1 中创建的场景\)。

您已将场景连接到场景链。请查看添加的场景的关系树选项卡,查看与父场景连接的场景。

![](https://static.xiaobot.net/file/2024-11-24/720345/e67f62f5cdb77c428310034613ea0751.png)


### **从场景链中删除场景**

从情景链中删除一个情景:

  1. 进入触发您要删除的场景的父场景。

  2. 在父场景中,删除"场景 > 运行场景"模块。

你已从场景链中移除了该场景。该被移除的场景不再拥有关系树选项卡,而 Make 也不会高亮显示该场景是场景链的一部分了。


## **情景链对比与其他连接情景的选项**

如果你是一个 Make 的重度用户,你可能知道有其他选择将场景连接成序列。下表比较了其他方法与场景链接的差异:** **

![](https://static.xiaobot.net/file/2024-11-24/720345/627bd9f4687e38ba9620777986632866.png)


## **情景串联的局限性**

场景链接有某些功能限制,以帮助您避免创建复杂或难以管理的链条,从而导致无限循环。这些限制如下：

  - 您无法使用"运行场景"模块来触发当前场景本身。

  - 您无法形成循环场景链。如果场景已经是场景链的一部分,您无法再次将其添加到同一链中。

  - 您无法连接来自不同团队的场景。您只能从同一团队内的场景创建场景链。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/fe791fd320250512d87316012301e9ef.png)