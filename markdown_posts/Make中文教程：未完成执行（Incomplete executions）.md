---
title: "Make中文教程：未完成执行（Incomplete executions）"
created_at: "2024-09-21T12:32:58.000000Z"
uuid: "da9d39c4-0005-4f06-b05e-b1764ac467dd"
tags: ['Make基础教程']
---

# Make中文教程：未完成执行（Incomplete executions）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于未完成的执行的教程。

  1. **教程简介** ： 未完成的执行讲述了如何处理Make.com中的因错误未完成的场景执行，包含未完成执行的存储、查看及解决方法，帮助用户有效管理和处理这些失败的执行。

  2. **学习目标** ： 学习如何启用并管理Make.com中的未完成执行文件夹，掌握查看和解决未完成执行的方法，以及了解导致未完成执行的常见错误类型及其处理方式。

  3. **学习内容** ：

     1. 启用未完成执行存储的设置。

     2. 分析和解决未完成执行的方法。

     3. 了解与未完成执行相关的选项设置。

     4. 掌握常见导致未完成执行的错误类型及其处理策略。

  4. **概念理解** ： 未完成执行：因错误未完成的场景执行，临时存储在未完成执行文件夹中，便于手动或自动解决。 允许存储未完成执行：场景高级设置选项，启用后可以记录未完成的执行。

  5. **任务练习** ：

     1. 启用某个场景的“允许存储未完成执行”选项。

     2. 模拟一个场景执行错误，并在未完成执行文件夹中查找失败记录。

     3. 根据错误日志，修复错误并重新运行场景，确保成功完成。


# 未完成执行

不完整的执行是存储无法成功完成的场景执行的文件夹,由于发生错误而无法最终完成。每个存储的不完整执行可以手动或自动解决。

![](https://static.xiaobot.net/file/2024-11-24/720345/7b5854b6f697b3946edb30d3ea93f868.png)

如果模块在其操作过程中遇到错误\(参见导致执行不完整的错误\),则会在"不完整执行"文件夹中添加一个新的不完整执行。每个不完整执行都包含了场景蓝图和可以映射到失败模块的所有捆绑包。您可以在"不完整执行"选项卡中查看不完整执行的列表。

![](https://static.xiaobot.net/file/2024-11-24/720345/e0398602e0db30464ad1ca5e496dabd5.png)![](https://static.xiaobot.net/file/2024-11-24/720345/a43664a06620dbe40e29bf7bf51721b6.png)


## **解决未完成的执行**

当一个新的未完成的执行被存储时,您可以按照以下方式解决它:

  1. 前往"未完成执行"标签。

  2. 转到确切的不完整执行。

     - 如果您要先查看所有模块操作的日志:

       1. 转到历史标签页。

       2. 定位失败执行日志并单击详细信息。

![](https://static.xiaobot.net/file/2024-11-24/720345/90e40f5d0d0b83f03210c3735f836a85.png)
       3. 点击有错误的失败模块。

![](https://static.xiaobot.net/file/2024-11-24/720345/0a8418389a150572e324dd4df131d9f1.png)
       4. 点击解决。重定向会给您带来非常不完整的执行效果。

![](https://static.xiaobot.net/file/2024-11-24/720345/dd64c18f4ad25813c210b4d50c3131cd.png)
     - 如果您想直接前往未完成的执行:

       1. 前往"未完成执行"标签。

       2. 找到失败的执行并点击详细信息。

![](https://static.xiaobot.net/file/2024-11-24/720345/2db0287e91355d8e849306e551801e3f.png)
  3. 点击故障模块。场景画布打开。

  4. 检查模块设置并解决错误原因。

  5.  点击保存。

  6.  点击一次运行。


## **有关未完成执行的选择**

在 Make 场景设置中以下选项决定未完成执行是否以及如何存储:

  - 允许存储不完整的执行

  -  顺序处理

  -  启用数据丢失


## **导致执行不完整的错误**

导致存储不完整执行的错误可以分为几个类别,包括: 因不完整或错误的数据引起的验证错误,主要是由于缺失模块处理所有数据所需的项目; 或由于临时或长期连接故障\(如连接到电子邮件或远程 FTP 服务器\)导致最终目标无法访问而产生的错误。

  1. 如果在场景中的第一个模块上发生错误,执行会立即停止,并且不会保存任何未完成的执行。

  2. 如果在任何其他模块中发生错误且没有附加任何错误处理路径，那么:

     - 如果错误类型是 ConnectionError、RateLimitError、OutOfSpaceError 或 ModuleTimeoutError，将存储一个带有自动重试的不完整执行记录。

     - 如果错误类型为 DataError、InvalidConfigurationError、InvalidAccessTokenError、UnexpectedError、MaxFileSizeExceededError 或 MaxResultsExceededError,将存储一条不完整的执行记录,而不会自动重试。

     - 如果错误类型不是上述任何一种,执行将失败。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/84cb72329e362af6783fffd84eb3008b.png)