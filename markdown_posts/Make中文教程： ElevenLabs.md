---
title: "Make中文教程： ElevenLabs"
created_at: "2024-12-05T13:38:29.000000Z"
uuid: "46e40c58-7a50-4201-a728-e446a231b0d2"
tags: ['Make基础教程']
---

# Make中文教程： ElevenLabs

**标签**: Make基础教程

使用 ElevenLabs 模块在 Make 中,你可以创建语音合成。

要开始使用 ElevenLabs,请在 elevenlabs.io 创建一个账户。

请参考 ElevenLabs API 文档了解可用的端点列表。


## **连接 ElevenLabs 进行制作**

要将 ElevenLabs 连接到 Make,您必须先从您的 ElevenLabs 账户获取 API 密钥。

  1. 登录您的 ElevenLabs 账户。

  2. 在右上角,点击您的个人资料图标 > 个人资料。

  3. 在 API 密钥字段旁边,点击眼睛图标查看并复制您的 API 密钥,并将其存储在安全的地方。

  4. 登录您的 Make 帐户,将 ElevenLabs 模块添加到您的场景中,然后单击创建连接。

  5. 在"连接名称"字段中,输入连接的名称。

  6. 在 API 密钥字段中粘贴在步骤 3 中复制的 API 密钥。

  7.  点击保存。

  8. 如果提示,请验证您的帐户并确认访问权限。

您已成功建立连接。您现在可以编辑您的方案并添加更多 ElevenLabs 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接续期步骤操作。


## **构建 ElevenLabs 场景**

连接应用程序后,您可以执行以下操作:

**  行为**

  - 创建语音合成

  -  发起 API 调用


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-05/720345/87350ca96d6d1469bea6c0844ebb81c4.png)