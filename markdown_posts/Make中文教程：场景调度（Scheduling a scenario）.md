---
title: "Make中文教程：场景调度（Scheduling a scenario）"
created_at: "2024-08-27T10:30:50.000000Z"
uuid: "40e20b1e-b69e-4a1a-b739-cbf06d7a3e5c"
tags: ['Make基础教程']
---

# Make中文教程：场景调度（Scheduling a scenario）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于计划场景的教程。

  1. **教程简介** ：本教程介绍如何在Make.com上调度一个场景，调整其运行计划和间隔时间，灵活控制场景的执行频率。

  2. **学习目标** ：了解如何使用Make.com的调度设置面板，配置场景的运行时间和频率，以便根据需求自动化执行任务。

  3. **学习内容** ：学习进入场景编辑器，使用调度设置面板自定义场景运行计划，包括立即、定期、一次性、每天、每周、每月、指定日期和按需等选项。

  4. **概念理解** ：调度设置面板用于调整场景运行计划，使其按照预定的时间和频率执行。按需调度让场景仅在手动启动或API调用时运行。

  5. **任务练习** ：进入场景编辑器，尝试配置一个场景，使其在每周的特定日期运行。设置开始日期和结束日期，并观察调度的效果。


# **场景调度**

Make 允许您定义何时以及执行已激活的场景的频率。

  1. 进入场景编辑器。

  2. 点击时针图标。

  3. 时间表设置面板显示。使用此面板来自定义您的情景时间表。


## **  日程安排面板**

预设时间设置面板允许您调整方案的运行时间表。默认情况下,方案每 15 分钟运行一次,但在此面板中,您可以自定义运行时间表和间隔。

![](/img/error.png)


### **  运行场景**

您可以选择以下时间安排选项:

  -  立即

  -  定期地

  -  一次

  -  每天

  -  一周中的日子

  -  月份的日子

  -  指定日期

  -  按需

当选择这些选项时，您可能需要进一步定义运行的频率。例如，如果选择"定期间隔"选项，您必须定义两次连续场景运行之间的时间间隔\(以分钟为单位\)。

间隔的最小长度取决于您的订阅计划。

仅某些触发器可用"立即"选项。有关触发器的更多信息,请参见模块类型帮助指南。

![](https://static.xiaobot.net/file/2024-12-02/720345/295fa7f6381214edaa8bd52f81f4d18e.png)

**  按需排班**

使用按需调度选项,您的方案将等待被 API 调用或"一次性运行"按钮触发。该方案没有任何安排,除非您手动启动,否则它不会运行。


### **  开始和结束日期**

要定义激活场景运行的时间范围,请勾选"显示高级设置"复选框,然后填写开始日期和结束日期。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/11f754b8bc9896ff6e7f392065ffa844.png)