---
title: "Make中文教程：模块设置（Module settings）"
created_at: "2024-08-27T11:25:25.000000Z"
uuid: "6d3b37f4-7b16-4536-b216-f56796456c5b"
tags: ['Make基础教程']
---

# Make中文教程：模块设置（Module settings）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于模块设置的教程。

**教程简介**  ：本教程介绍了在Make.com的场景中如何设置模块，包括必填和可选字段、连接设置、数据类型等，确保用户理解如何配置每个模块以实现特定功能。

**学习目标**  ：掌握如何在Make.com中配置不同模块的设置，理解必填字段和可选字段的区别，学会设置连接和使用高级字段，以便构建功能强大的自动化场景。

**学习内容**  ：教程重点包括模块设置的基本步骤、连接设置、必填和可选字段、标准字段和高级字段的应用，以及如何选择和使用合适的数据类型。

**概念理解**  ：

  1. 模块设置：定义模块接收或发送的数据及其具体行动。

  2. 连接设置：创建到应用程序账户的连接。

  3. 必填字段：必须填写的字段，以粗体显示。

  4. 高级字段：包含复杂项目的字段，需要特定技术知识。

**任务练习**  ：配置一个Dropbox模块和一个Email模块，确保在Dropbox模块中指定上传目标文件夹，并在Email模块中设置好发送电子邮件的地址；尝试利用映射功能将一个模块中的数据传递到另一个模块。


## **1\. 模块设置基础**

每个模块都需要适当的配置来实现特定功能。模块设置是自动化流程中的"控制面板"，决定了数据如何流转和处理。

![](https://static.xiaobot.net/file/2024-11-18/720345/9efa4dfc7837a3f50fbd20027795686e.png)


### **1.1 如何访问设置**

  - 添加新模块时自动打开设置

  - 点击现有模块重新打开设置

  - 在场景编辑器中随时修改设置


### **1.2 设置示例**

不同模块有不同的设置要求：

    Dropbox模块：
    - 指定上传文件夹
    - 设置文件权限
    - 配置文件处理选项
    邮件模块：
    - 设置收件人地址
    - 配置邮件主题
    - 定义邮件内容


## **2\. 连接设置（Connection Settings）**


### **2.1 基本概念**

连接设置是与外部服务建立联系的"通行证"，可能包含：

  - 用户名/密码

  - API密钥

  - 客户端凭证


### **2.2 连接配置步骤**

  1. 选择要连接的服务

  2. 提供认证信息

  3. 授权访问权限

  4. 测试连接可用性


## **3\. 字段类型与要求**


### **3.1 必填字段**

  - 以粗体显示字段名

  - 保存时必须填写

  - 缺少将导致错误

  - 场景无法运行


### **3.2 可选字段**

  - 普通字体显示

  - 根据需要填写

  - 提供额外功能

  - 可以留空


### **3.3 数据填写方式**

两种主要方法：

  1. 直接输入：

     - 手动输入文本

     - 选择预设选项

     - 设置具体值

  2. 映射填充：

     - 从其他模块获取数据

     - 使用动态值

     - 实现数据传递


## **4\. 字段类别详解**


### **4.1 标准字段（Standard Fields）**

特点：

  - 最常用的配置项

  - 包含基本功能设置

  - 默认显示在界面上

  - 可能是必填或可选


### **4.2 高级字段（Advanced Fields）**

特点：

  - 需要开启"显示高级设置"

  - 包含复杂配置选项

  - 可能需要技术知识

  - 提供更多控制选项


## **5\. 数据类型系统**


### **5.1 常见数据类型**

    1. 文本（Text）
       - 字符串
       - 描述信息
       - 标识符
    2. 数字（Number）
       - 整数
       - 小数
       - 计数值
    3. 日期（Date）
       - 日期
       - 时间
       - 时间戳
    4. 布尔值（Boolean）
       - 是/否
       - 开/关
       - 真/假


### **5.2 数据类型识别**

  - 悬停在字段名上查看类型

  - 查看字段说明

  - 参考文档说明


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/7dfc124f96974df200daca99c0b9ca60.png)