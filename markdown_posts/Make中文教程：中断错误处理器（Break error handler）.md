---
title: "Make中文教程：中断错误处理器（Break error handler）"
created_at: "2024-08-30T05:20:25.000000Z"
uuid: "7f3fc119-c059-4149-8c2e-0493ef63a72e"
tags: ['Make基础教程']
---

# Make中文教程：中断错误处理器（Break error handler）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于错误处理中断的教程。

  1. **教程简介** ：本教程介绍了如何在Make.com中使用“中断（Break）”错误处理器，处理数据包错误，使流程继续运行或将错误数据包存储为未完成执行以便手动处理。

  2. **学习目标** ：通过学习本教程，用户将能够设置和使用中断错误处理器，从而有效地处理场景中的错误，确保流程的稳定性和可控性。

  3. **学习内容** ：主要内容包括添加中断错误处理器的方法、中断处理器的配置、未完成执行的设置和示例场景演示。

  4. **概念理解** ：中断错误处理器用于从场景中移除发生错误的数据包，并将错误信息存储为未完成执行，以自动或手动解决，类似于重试机制，但更灵活。

  5. **任务练习** ：为您的场景添加一个中断错误处理器，当数据存储模块中出现错误时，配置错误处理器将未完成执行储存起来并设置为自动完成尝试。


# **中断错误处理器**

故障处理程序"中断"从方案流中删除出现错误的捆包。Make 存储错误消息、映射和剩余的方案流作为不完整的执行。根据"中断"故障处理程序的设置,Make 会自动完成未完成的方案运行,或者将它们存储起来,直到您自己解决它们。

![](https://static.xiaobot.net/file/2024-11-28/720345/3e56cf14ca8f0c3814c583e4b3603b89.png)

处理场景流程中其余的捆绑包。

要在您的场景中使用 Break 错误处理程序,您必须在场景设置中启用不完整的执行。

例如:这个演示场景包含五个模块。该场景对于测试和展示错误处理程序的效果很有用:

  1. JSON - 解析 JSON 数据可提供由三个记录 ID 组成的测试数据数组。

  2. 迭代器将数组拆分为单独的捆绑包。

  3. 数据存储 - 更新记录：更新数据存储中的数据。

  4. 数据存储 - 更新记录：本模块再次更新数据。这次模块的工作方式有所不同。在模块映射中，有一个映射明确地创建了一个错误：

![](https://static.xiaobot.net/file/2024-11-28/720345/cd82b529a4450b8b6979c2f0908fc274.png)

映射将 null 值插入必需的密钥字段中,这总是会创建 BundleValidationError 。

  5. Slack - 发送消息：发送一条消息到私密测试频道。

这就是示例场景的样子:

![](https://static.xiaobot.net/file/2024-11-28/720345/cfedd599a5f3875e47daf649a36e90ed.png)

当我们运行示例场景时,我们将得到 BundleValidationError :

![](https://static.xiaobot.net/file/2024-11-28/720345/c83dfd959e49c9d04db1aa8beb13a8cc.png)![](https://static.xiaobot.net/file/2024-11-28/720345/d5ed0df4c0c43262461297685c5729f7.png)

如果我们将 Break 错误处理程序添加到更新记录模块中,Break 错误处理程序将会从场景流程中删除该捆绑包。导致错误的捆绑包不会继续通过其余场景流程。相反,Make 会创建一个不完整的执行来存储错误类型、映射和剩余的场景流程。

然后就会在情景流程中处理剩余的捆绑包。

![](https://static.xiaobot.net/file/2024-11-28/720345/7d484674eb1a5a511433a70f8ea7f9f2.png)

关于错误处理策略的更多信息请查看错误处理概述。


## **错误发生时存储场景**

使用 Break 错误处理程序时,当发生错误时,您可以将剩余的场景流存储在场景未完成执行中。它还存储错误消息、场景映射和数据。

您可以手动完成"未完成的执行"选项卡中的情景运行,也可以让 Make 自动完成该运行。

例如,以下场景在 Data Store 应用程序模块中输出错误:

![](https://static.xiaobot.net/file/2024-11-28/720345/6b9a43be4c26fc322e4f188bd0846e61.png)![](https://static.xiaobot.net/file/2024-11-28/720345/6fe9a2a77bb1c59078c3ccd5a2902d55.png)

将方案存储为未完成的执行，请按以下步骤操作:

  1. 右键单击导致错误的模块。在菜单中选择添加错误处理程序。

  2. 选择"错误处理器中断"。

  3. 在错误处理程序设置中,选择是否要自动完成未完成的执行。对于自动完成,您可以设置尝试次数和每次尝试之间的时间延迟。

  4. 使用确认按钮确认中断处理程序设置。

  5. 在场景设置中启用存储未完成的执行。

  6.  保存您的场景。

您已将 Break 错误处理程序添加到您的方案中。当数据存储模块发生错误时,Make 会创建一个不完整的执行。该不完整的执行包含错误消息、方案映射和数据。

![](https://static.xiaobot.net/file/2024-11-28/720345/773a18dd1ed3b30c6c56cfe22187957d.png)

如果您使用自动情景完成功能,请尝试自动完成该情景。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/e9f40a9d53259c48dc7da9f078a0c07b.png)