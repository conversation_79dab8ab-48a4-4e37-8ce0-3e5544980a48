---
title: "Make中文教程：文本解析器（Text Parser）"
created_at: "2024-09-19T13:44:02.000000Z"
uuid: "528c34b5-2e20-432d-911f-f5fc26ac1ed3"
tags: ['Make基础教程']
---

# Make中文教程：文本解析器（Text Parser）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于文本解析的教程。

  - **教程简介** ：本教程介绍如何在Make.com中使用文本解析器模块。它涵盖从HTML中提取元素、匹配模式、替换文本和数据抓取的具体操作步骤及注意事项。

  - **学习目标** ：学习如何利用Make.com的文本解析器模块，从不同类型的文本中提取、替换所需信息，以及使用数据抓取工具从网站获取数据。

  - **学习内容** ：学习从HTML中提取元素的操作；理解并应用正则表达式进行匹配和替换；了解数据抓取的定义及常用工具。

  - **概念理解** ：

    - **文本解析器** ：从文本中提取或替换指定的信息。

    - **正则表达式** ：用于匹配字符串的搜索模式。

    - **数据抓取** ：从网站收集数据的过程。

  - **任务练习** ：创建一个Make.com场景，使用文本解析器模块从HTML中提取链接元素。然后使用匹配模式模块提取特定格式的文本，并使用替换模块修改文本中的某些部分。


### **从 HTML 中提取元素**

从 HTML 代码中提取所需的元素。

![](https://static.xiaobot.net/file/2024-11-18/720345/22606ad5e04cc1a1eb7af81faf76e69b.png)

**匹配模式**

匹配模式模块使您能够从给定文本中查找和提取与搜索模式匹配的字符串元素。搜索模式是正则表达式（也称为 regex 或 regexp），它是一个字符序列，其中每个字符都是元字符，具有特殊含义，或者是具有字面含义的普通字符。

    - 在 MDN 网站上可以找到完整的元字符列表。

    - 推荐访问 RegexOne 网站学习如何创建正则表达式。

    - 为了一个简单快速的正则表达式生成器，请尝试使用正则表达式生成器。

    - 为了尝试正则表达式，我们建议使用正则表达式 101 网站。请确保在左侧面板中选择 ECMAScript（JavaScript）FLAVOR：

![](https://static.xiaobot.net/file/2024-11-18/720345/5376746a8d02e8195ed6d1fc55452547.png)
![](https://static.xiaobot.net/file/2024-11-18/720345/2927b6af60a105bcce853bb9c54a58e1.png)


### **替换**

搜索输入的文本以查找指定值或正则表达式，并用新值替换结果。

![](https://static.xiaobot.net/file/2024-11-18/720345/2b9ab4a7e4baab0d018c477a38b24680.png)


## **数据提取**

数据抓取，有时被称为网页抓取、数据提取或网络收割，简单来说就是从网站收集数据并将其存储在本地数据库或电子表格中。如果您希望从网站抓取数据但不熟悉正则表达式，可以使用数据抓取工具。

    - Apify 是一个非常好用的工具，我们已经将其集成进来

    - [**2021 年最佳数据抓取工具**](<https://www.octoparse.com/blog/best-data-scraping-tools-for-2019-top-10-reviews>)

    - [**  2022 年网络数据提取工具**](<http://whitepapers.virtualprivatelibrary.net/Web%20Data%20Extractors.pdf>)

如果数据抓取工具提供 REST API，您可以通过我们的通用 HTTP 和 Webhooks 模块连接到它。您还可以使用 Make App SDK 自行开发应用程序。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/a77aa4d8c15f5b10478918606b0eb718.png)