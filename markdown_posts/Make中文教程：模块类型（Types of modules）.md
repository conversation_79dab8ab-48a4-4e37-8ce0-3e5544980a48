---
title: "Make中文教程：模块类型（Types of modules）"
created_at: "2024-08-27T11:23:26.000000Z"
uuid: "a60dc728-6404-4ad3-b117-96a607de6a3a"
tags: ['Make基础教程']
---

# Make中文教程：模块类型（Types of modules）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于模块类型的教程。

**教程简介**  ：本教程介绍Make.com的五种模块类型：操作模块、搜索模块、触发器模块、聚合器模块和迭代器模块。特别将聚合器和迭代器模块应用在高级场景中。

**学习目标**  ：学习如何使用和区分Make.com的五种模块类型，了解每种模块的功能、使用场景和操作方式，提升在Make.com上的自动化工作流程设计能力。

**学习内容**  ：包括操作模块的基本使用、搜索模块的功能、触发器模块的触发机制、聚合器模块的数据聚合功能和迭代器模块的数据拆分功能。

**概念理解**  ：

  1. 操作模块（Actions）：执行单一步骤并传递数据给下一个模块。

  2. 搜索模块（Searches）：搜索数据并传递搜索结果。

  3. 触发器模块（Triggers）：检测某事件发生并触发后续操作，分为轮询触发器和即时触发器。

  4. 聚合器模块（Aggregators）：将多个数据包聚合为一个数据包。

  5. 迭代器模块（Iterators）：将一个数据包拆分成多个数据包处理。

**任务练习**  ：

  1. 使用操作模块创建一个自动化任务，如在Dropbox中上传文件。

  2. 创建一个包含搜索模块的场景，列出所有Monday.com板中的脉冲。

  3. 设置一个触发器模块，当Twitter新增推文时触发其他操作。

  4. 使用聚合器模块将多个文本数据包聚合为一个单一字符串。

  5. 通过迭代器模块将邮件的附件数组拆分成各个独立的数据包进行处理。


## **1\. 模块的定义与基本概念**

模块是Make平台中的基本构建单元，就像搭积木一样，通过组合不同的模块来构建自动化流程。

![](https://static.xiaobot.net/file/2024-11-18/720345/132f4de627825f76c84db5dac2533b27.png)


### **模块的基本功能**

  - 从服务中获取数据

  - 创建或更新记录

  - 下载文件

  - 按条件搜索数据

示例：一个简单的自动化流程可能包含三个模块：

  1. 监控CRM中的新客户数据

  2. 将数据转换为其他格式

  3. 将信息发送到另一个服务


## **2\. 四大核心模块类型详解**


### **2.1 触发器（Triggers）**

触发器是场景的起点，负责监控服务中的变化。

**A. 轮询触发器（Polling Triggers）**

特点：

  - 定期检查服务变化

  - 名称通常以"Watch"开头

  - 根据场景计划运行

  - 可以设置检查起始点

**B. 即时触发器（Instant Triggers）**

![](https://static.xiaobot.net/file/2024-11-18/720345/d6212c7b21e9b94d33d1af2b8dc17035.png)![](https://static.xiaobot.net/file/2024-11-18/720345/d366c4200173f499b70a1f84f8896449.png)

特点：

  - 通过Webhook实时接收通知

  - 自动设置为"数据到达时立即执行"

  - 需要创建Webhook连接


### **2.2 搜索模块（Searches）**

提供两种搜索方式：

**A. 搜索模块（Search）**

  - 支持过滤和搜索查询

  - 可以精确定位特定数据

  - 例如：搜索记录、搜索行等

**B. 列表模块（List）**

  - 获取服务中的所有数据

  - 无过滤功能

  - 例如：列出记录、列出表格等


### **2.3 操作模块（Actions）**

最常用的模块类型，包括：

  - Get（获取）：获取特定信息

  - Create（创建）：新建数据

  - Update（更新）：修改现有数据

  - Delete（删除）：移除数据

某些服务可能有特殊操作，如：

  - pin（固定）

  - save（保存）

  - download（下载）


### **2.4 通用模块（Universal）**

用于Make没有预设模块的API调用：

  - 支持自定义API调用

  - 适用于大多数服务

  - 需要参考服务API文档


## **3\. 使用限制和注意事项**


### **3.1 数据限制**

  - 搜索模块最多返回3,200个对象

  - 单次运行数据上限5MB

  - 可能受第三方API限制


### **3.2 基本使用建议**

  1. 触发器建议：

     - 优先使用Watch模块作为触发器

     - 合理设置检查间隔

     - 注意选择合适的起始点

  2. 搜索模块使用：

     - 需要特定数据时使用Search

     - 需要完整数据时使用List

     - 注意数据量限制


## **4\. 模块分类：Apps与Tools**


### **4.1 应用模块（Apps）**

需要与外部服务建立连接的模块。

连接设置界面：

![](https://static.xiaobot.net/file/2024-11-18/720345/d75fb18fc1da039651f58d683e49f93e.png)![](https://static.xiaobot.net/file/2024-11-18/720345/81551dfbae628adbd678b57992fd55cf.png)

特点：

  - 需要创建服务连接

  - 执行特定服务操作

  - 可能受API限制


### **4.2 工具模块（Tools）**

Make内置工具模块：

![](https://static.xiaobot.net/file/2024-11-18/720345/83662eaa96b8420085433dbbe67cd8c0.png)![](https://static.xiaobot.net/file/2024-11-18/720345/ff5a66dd6e9593869bde7008dfd1871e.png)

特点：

  - 无需外部连接

  - 即插即用

  - 可能需要简单配置


## **5\. 高级功能与限制**


### **5.1 模块限制控制**

![](https://static.xiaobot.net/file/2024-11-18/720345/ea3b39b9bd7e4499b05e66c9acaaf652.png)

限制设置建议：

  - 设置合理的数据获取限制

  - 考虑场景运行时间（最长40分钟）

  - 注意数据处理延迟


### **5.2 ACID模块特性**

![](https://static.xiaobot.net/file/2024-11-18/720345/f1556b453ff5bf005d68f2301d18d840.png)

特点：

  - 支持操作回滚

  - 确保数据一致性

  - 错误时自动撤销


## **6\. 高级使用技巧**


### **6.1 更新模块的三种操作**

![](https://static.xiaobot.net/file/2024-11-18/720345/076fa7e832b26e25bf1c18473293099e.png)

  1. 擦除内容：

     - 使用erase关键字

     - 适用于PUT请求

  2. 保持不变：

     - 留空内容字段

     - 保留原有数据

  3. 覆盖内容：

     - 输入新数据

     - 完全替换原内容


### **6.2 触发器起始点设置**

![](https://static.xiaobot.net/file/2024-11-18/720345/ed3f5243b5098c6c577b3cbde3d3e47f.png)

可选起始点：

  1. 当前时刻

  2. 特定日期

  3. 特定ID/记录/邮件

  4. 第一条记录

注意：起始点设置只影响首次运行。


## **7\. 最佳实践建议**


### **7.1 模块选择**

  1. 数据获取：

     - 特定数据用Search

     - 完整数据用List

     - 单条数据用Get

  2. 性能优化：

     - 合理使用限制

     - 避免过度获取

     - 注意执行时间


### **7.2 错误处理**

  1. ACID模块：

     - 关键操作使用

     - 确保数据一致性

     - 支持错误回滚

  2. 执行监控：

     - 定期检查日志

     - 监控执行状态

     - 及时处理错误


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/7b5f58d55cb4b58002ce67d85f1c4a62.png)