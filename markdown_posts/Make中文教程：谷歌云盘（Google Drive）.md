---
title: "Make中文教程：谷歌云盘（Google Drive）"
created_at: "2024-09-20T09:10:08.000000Z"
uuid: "d7558027-6ab2-4060-b0e5-0b6c1a580cd1"
tags: ['Make基础教程']
---

# Make中文教程：谷歌云盘（Google Drive）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于谷歌云端硬盘的教程。

  1. **教程简介** ：本教程介绍如何使用Make平台中的Google Drive模块管理Google Drive账户中的文件、文件夹和共享驱动器，包括如何建立连接、监视和操作文件及文件夹等。

  2. **学习目标** ：通过本教程的学习，用户将掌握如何在Make平台上连接并管理Google Drive账户中的各种文件和文件夹，实现自动化文件管理。

  3. **学习内容** ：主要涵盖连接Google Drive账户、创建和配置Google Cloud Console项目、监视和操作文件和文件夹、使用API访问等内容。

  4. **概念理解** ：Make中的Google Drive模块允许用户通过Google API管理Google Drive文件和文件夹。连接账号需通过OAuth认证，某些操作需配置Google Cloud Console项目尤其是使用@gmail或@googlemail域时。

  5. **任务练习** ：

     1. 在Make平台上创建一个Google Drive连接。

     2. 配置Google Cloud Console项目并启用Google Drive API。

     3. 创建一个监视文件夹中的文件的场景，设置定期检查并处理新文件。


# **谷歌云盘**

使用 Make 中的 Google Drive 模块，您可以管理您 Google Drive 账户中的文件、文件夹或共享驱动器。

要使用 Google 应用程序模块,您必须拥有 Google 账户。您可以在 http://account.google.com 创建一个账户。

请参考 Google Drive API 文档了解可用的 endpoint。

![](https://static.xiaobot.net/file/2024-11-26/720345/407f058a6b3c8ebbf09568ca781b895b.png)


## **连接 Google Drive 以制作**

![](https://static.xiaobot.net/file/2024-11-26/720345/dfca600757647958aaef39be9c89caa4.png)

建立在 Make 中的连接

  1. 登录您的 Make 账户,将 Google Drive 模块添加到您的方案中,然后单击创建连接。

  2. 在"连接名称"字段中输入连接的名称。

  3. 非 @gmail 或 @googlemail 域的 Google Drive 用户可选: 打开"显示高级设置"开关,输入您的 Google Cloud Console 项目客户端凭据。更多信息,请参见下面的"为 Google Drive 创建和配置 Google Cloud Console 项目"部分。

  4. 点击使用谷歌登录。

  5. 如果提示,请验证您的账户,授予所有请求的权限,并确认访问。

您已成功建立连接。您现在可以编辑您的场景并添加更多 Google Drive 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接续期步骤操作。

![](https://static.xiaobot.net/file/2024-11-26/720345/696e2b1ff76f0ae29cf7edb9e7bfdb3b.png)


### **创建并配置用于 Google Drive 的 Google 云控制台项目**

要使用您自己的客户端凭据连接到 Make,您可以在 Google Cloud 控制台中创建和配置一个项目。

![](https://static.xiaobot.net/file/2024-11-26/720345/448515d984ba1df1a82a6e3b98474e1c.png)

**为 Google Drive 创建 Google 云控制台项目**

创建一个 Google Cloud 控制台项目:

  1. 使用您的谷歌账号登录谷歌云控制台。

  2. 在顶部菜单上,单击"创建"或选择"新项目"。

  3. 输入项目名称并选择项目位置。

  4.  点击创建。

  5. 在顶部菜单中,检查您新创建的项目是否在"选择项目"下拉列表中被选中。如果没有,请选择您刚刚创建的项目。

![](https://static.xiaobot.net/file/2024-11-26/720345/9ff7ef07ffabb8d7c8ad5d8f49f1bc27.png)

**为 Google Drive 启用 API**

启用所需的 API:

  1. 打开左侧导航菜单，并转到 APIs & Services > 库。

  2. 搜索以下 API：Google Drive API。

![](https://static.xiaobot.net/file/2024-11-26/720345/5900d530f13fb19cc5de564fd186be0a.png)
  3. 点击 Google Drive API，然后点击启用。

**为 Google Drive 配置您的 OAuth 同意屏幕**

配置您的 OAuth 同意屏幕:

  1. 在左侧边栏中,单击 OAuth 同意屏幕。

  2. 在"用户类型"下选择"外部"。

有关用户类型的更多信息,请参阅 Google 的"验证要求例外"文档。

  3.  点击创建。

  4. 在应用程序信息部分，输入"Make"作为应用程序名称,并输入您的 Gmail 地址。

  5. 在认证域部分中添加:

[make.com](<http://make.com>) integromat.com

  6. 在开发者联系信息部分,输入您的 Gmail 地址。

  7.  点击保存并继续。

  8. 在 Scopes 页面中,点击添加或删除 Scopes,添加以下 Scopes,然后点击更新。

     - https://www.googleapis.com/auth/drive

     - https://www.googleapis.com/auth/drive.readonly

  9.  点击保存并继续。

  10. 如果您的项目将保持在测试发布状态,请在"测试用户"页面添加您的 Gmail 地址,然后点击保存并继续。

![](https://static.xiaobot.net/file/2024-11-26/720345/ba7d4ce33ea16f4b2f2f08e32b37628b.png)

**创建您的 Google Drive 客户端凭据**

创建您的客户凭证:

  1. 在左侧边栏中,单击凭证。

  2. 点击+创建凭据>OAuth 客户端 ID。

![](https://static.xiaobot.net/file/2024-11-26/720345/43d86b4b4898d36fac1a7eb3c2c0efe3.png)
  3. 在应用程序类型下拉菜单中,选择 Web 应用程序。

  4. 更新您的 OAuth 客户端的名称。这将帮助您在控制台中识别它。

  5. 在授权重定向 URI 部分,单击 + 添加 URI,输入以下重定向 URI: https://www.integromat.com/oauth/cb/google-restricted 。

  6. 请复制您的客户端 ID 和客户端密钥值,并将它们存储在一个安全的地方。

您将在 Make 中的"客户端 ID"和"客户端密钥"字段中使用这些值。


## **  文件/文件夹**


### **监视文件夹中的文件**

当文件添加或修改在指定文件夹时检索文件详细信息。

![](https://static.xiaobot.net/file/2024-11-26/720345/462d6713cf644c5db9d27c58fd81cee5.png)


### **  观看所有文件**

当您的 Google 云端硬盘中的文件添加或修改时获取文件详细信息。

![](https://static.xiaobot.net/file/2024-11-26/720345/393a3008571e494a1c489c9b2ba6bcd7.png)


### **  监视文件夹**

在创建新文件夹或修改现有文件夹时获取文件夹详细信息。

![](https://static.xiaobot.net/file/2024-11-26/720345/d11040621650bb3432188fbbcac77478.png)


### **  搜索文件/文件夹**

根据搜索条件查找文件或文件夹。

![](https://static.xiaobot.net/file/2024-11-26/720345/6946fbfd55664f24c3a36f9189447d3c.png)


### **  下载文件**

从您的 Google 云盘下载文件。

![](https://static.xiaobot.net/file/2024-11-26/720345/91a49c42acba8fd068d25f891dcddaa0.png)


### **从文本创建文件**

在所选的 Google Drive 文件夹中创建一个文件。

![](https://static.xiaobot.net/file/2024-11-26/720345/485eec7ccd4544f20c34ddff8cc21af7.png)


### **  创建文件夹**

在指定位置创建一个文件夹。

![](https://static.xiaobot.net/file/2024-11-26/720345/02dbe9793ffe375a5e15bebfea2912a5.png)


### **创建文件/文件夹快捷方式**

创建新文件或文件夹快捷方式。

![](https://static.xiaobot.net/file/2024-11-26/720345/23a80aab191069abe0398b999f7795e2.png)


### **  上传文件**

上传文件到您的谷歌云端硬盘。

![](https://static.xiaobot.net/file/2024-11-26/720345/594f84e398959d2211f8dbabd4448b00.png)


### **  更新文件**

更新文件的元数据或内容。

![](https://static.xiaobot.net/file/2024-11-26/720345/59c1928258fc96d790d7c4ec7774b60a.png)


### **  重命名文件夹**

重命名现有文件夹。

![](https://static.xiaobot.net/file/2024-11-26/720345/91271d596c6a0b00d17696953afa8cee.png)


### **将文件/文件夹移动到回收站**

将文件或文件夹移动到垃圾箱。

![](https://static.xiaobot.net/file/2024-11-26/720345/8d000be747dc961ebd3ad0a0eeb907c8.png)


### **  删除文件或文件夹。**

永久删除文件或文件夹。

![](https://static.xiaobot.net/file/2024-11-26/720345/014cfc72fe498050fdee0f974d6df3d2.png)


### **  复制文件**

将文件复制到新位置。

![](https://static.xiaobot.net/file/2024-11-26/720345/2d1398bde40f493584efbe8421798de9.png)


### **移动文件/文件夹**

将文件或文件夹移动到新的目标位置。

![](https://static.xiaobot.net/file/2024-11-26/720345/30de6d5731c21ae2ba4bcb75a464ea5c.png)


## **文件/文件夹访问**


### **  获取分享链接**

检索和设置权限并发送文件或文件夹的共享链接。

![](https://static.xiaobot.net/file/2024-11-26/720345/fa1215e273b1d6a08968cdeb8b7d8521.png)


### **更新文件/文件夹访问权限**

更新现有文件或文件夹的访问权限。

![](https://static.xiaobot.net/file/2024-11-26/720345/30bce5a5eb0e812b5c0cbf84ddf8e024.png)


### **撤销文件/文件夹访问权限**

撤销文件或文件夹访问权限。

![](https://static.xiaobot.net/file/2024-11-26/720345/df489415399a43473fdf162e2ef7d025.png)


## **谷歌共享驱动器\(仅限 Google Workspace 用户\)**


### **  观看共享驱动器**

当共享驱动器被创建时触发。

![](https://static.xiaobot.net/file/2024-11-26/720345/61d3f441570465b6518e1edf60b8276e.png)


### **  搜索共享驱动器**

查找 Google 共享驱动器。

![](https://static.xiaobot.net/file/2024-11-26/720345/4efb81bdbb6987fb66c55150079f6f7e.png)


### **创建共享驱动器**

创建新的共享驱动器。

![](https://static.xiaobot.net/file/2024-11-26/720345/9f110cb628f4b84bafb859030cec1511.png)


### **获取共享驱动器**

获取共享驱动器详情。

![](https://static.xiaobot.net/file/2024-11-26/720345/0b7540c7aa555bfdf01b1ec3eca4511b.png)


### **  更新共享驱动器**

更新现有驱动器的名称和/或限制。

![](https://static.xiaobot.net/file/2024-11-26/720345/c489dc6e5d1d517826a7a57852e37add.png)


### **  删除共享驱动器**

永久删除用户是组织者的共享驱动器。共享驱动器不能包含任何未删除的项目。

![](https://static.xiaobot.net/file/2024-11-26/720345/648e8b61a239ca569a6fcf1fcb64c8b9.png)


## **文件修订**


### **  文件修订列表**

检索文件的修订列表。

![](https://static.xiaobot.net/file/2024-11-26/720345/1c1b943b2181002ac238949e454b9eaa.png)


### **  获取文件修订版**

获取指定文件的修订版。

![](https://static.xiaobot.net/file/2024-11-26/720345/e15d05ab9b682d39ccd9e7032b67c564.png)


### **  更新文件版本**

更新现有文件的版本。

![](https://static.xiaobot.net/file/2024-11-26/720345/a1921eaf983b8ce61a6cfc872bf0d76d.png)


### **删除文件修订**

删除文件的修订版。

![](https://static.xiaobot.net/file/2024-11-26/720345/d03964b4014641be18e93afb86ba0ffe.png)


## **其他**


### **  查看评论**

当在所选文件上添加或修改注释时触发。

![](https://static.xiaobot.net/file/2024-11-26/720345/6af8ad5759eda4414aed2b88711ec55e.png)


### **获取路径的文件夹 ID**

获取文件夹路径的文件夹 ID

![](https://static.xiaobot.net/file/2024-11-26/720345/53f19ba24d5cd4d6d3de529d33666b3c.png)


### **根据 ID 获取文件/文件夹路径**

根据 ID 获取文件或文件夹路径。

![](https://static.xiaobot.net/file/2024-11-26/720345/c2e346bd98fcce99e2b74b5bf029693e.png)


### **  发起 API 请求**

允许您执行自定义 API 调用。

![](https://static.xiaobot.net/file/2024-11-26/720345/520e5ef7392354c46b140126a331431c.png)

**使用示例 - 列出所有文件**

以下 API 调用将返回您 Google Drive 中的所有文件:

 https://www.example.com/product-page.html 网址： https://www.example.com/product-page.html

/v3/files

 方法: GET

![](https://static.xiaobot.net/file/2024-11-26/720345/1b53609b70901a1f1f07f2816658e762.png)

结果可以在模块的输出下的 Bundle > Body > files 部分找到。在我们的示例中,返回了 30 个文件:

![](https://static.xiaobot.net/file/2024-11-26/720345/29013eb8b550f98d679f11fa8cce7c69.png)


## **  查找文件**

在"搜索文件/文件夹"模块中，您可以使用由以下部分组成的自定义查询:

字段 - 正在搜索的文件的属性,例如文件的 name 属性。

操作员 - 对数据执行的测试以提供匹配项,例如 contains 。

值 - 被测试的属性的内容,例如文件的名称 My cool document 。

使用连词 and 、 or 组合从句,并使用 not 查询。


## **  领域**

![](https://static.xiaobot.net/file/2024-11-26/720345/f492768165d1405f2dc8583fbb43e2d0.png)

\[1\] contains 操作符只对 name 执行前缀匹配。例如，名称"HelloWorld"将匹配 name contains 'Hello' 但不匹配 name contains 'World' 。

\[2\] contains 运算符仅对整个字符串令牌执行匹配 fullText 。例如,如果一个文档的全文包含字符串"HelloWorld",仅 fullText contains 'HelloWorld' 查询会返回结果。在这种场景下,诸如 fullText contains 'Hello' 的查询不会返回结果。

\[3\] contains 运算符匹配被双引号包围的精确字母数字短语。例如,如果一个 doc 的 fullText 包含字符串"Hello there world",那么查询 fullText contains '"Hello there"' 会返回结果,但查询 fullText contains '"Hello world"' 不会。此外,由于是字母数字搜索,如果 doc 的 fullText 包含字符串"Hello\_world",那么查询 fullText contains '"Hello world"' 会返回结果。

\[4\] owners 、 writers 和 readers 属性间接反映在列表中,并涉及权限角色。


## **值类型**

![](https://static.xiaobot.net/file/2024-11-26/720345/3424ee7bc00b47c4f5ea929ab54fd430.png)


## **操作员**

![](https://static.xiaobot.net/file/2024-11-26/720345/72b76dc01c619caaf554d2d6a5e9d064.png)

对于复合从句，你可以使用括号将从句组合在一起。例如:

modifiedTime > '2019-06-04T12:00:00' and \(mimeType contains 'image/' or mimeType contains 'video/'\)

此搜索返回的所有文件的图像或视频 MIME 类型,其最后修改时间在 2019 年 6 月 4 日之后。因为 and 和 or 运算符从左到右进行计算,没有括号,上述示例将只返回在 2019 年 6 月 4 日之后修改的图像,但会返回所有视频,包括 2019 年 6 月 4 日之前的视频。


## **例子**

![](https://static.xiaobot.net/file/2024-11-26/720345/e690f558dd1252f0dd5dd52c9f017626.png)

搜索文件和文件夹 搜索查询条件


## **  可能的问题**


### **无法上传或更新文件**

以下几种情况下上传或更新文件可能会失败:

  - 上传的文件太大,超过了您的 Google 云端硬盘计划所允许的最大文件大小限制,或者您已经超出了 Google 云端硬盘的存储限额。您可以升级存储计划,或者从 Google 云端硬盘服务中删除现有的文件。

文件上传的目标文件夹已不存在。该场景已终止,需要重新选择目标文件夹。


### **无法验证连接'My Google Restricted connection'。状态代码错误：400**

您的连接已过期,不再有效。您需要重新授权连接。

此错误影响了非 Google Workspace 账户。欲了解更多详情,请参考 Google OAuth 文档。

由于谷歌更新的安全政策,未发布的应用程序只能有 7 天的授权期。在 OAuth 安全令牌到期后,连接将不再被授权,任何依赖它的模块都将失败。

**  解决方案**

**  选项 1：**

为避免每周重新授权,您可以更新项目的发布状态。

如果您将您的项目更新到已投产状态,您将不需要每周重新授权连接。

按照以下步骤改变您项目的状态:

  1. 登录 Google Cloud 控制台。

  2. 导航到 OAuth 同意屏幕。

  3. 点击您应用旁边的发布应用程序按钮。

  4. 如果您看到"需要验证"的通知,您可以选择是否通过 Google 验证流程来验证该应用程序,或者连接到未经验证的应用程序。目前在 Make 中可以连接到未经验证的应用程序,但我们无法保证 Google 将永远允许连接到未经验证的应用程序。

有关发布状态的更多信息,请参阅 Google 的 OAuth 同意屏幕设置帮助和我们社区页面中的"发布状态"部分。

**  选择 2:**

如果您将项目保持在测试状态,您将需要每周在 Make 中重新授权您的连接。

按照以下步骤重新授权您的 Google 帐户连接:

1\. 登录 Make。

2\. 前往联系人。

![](https://static.xiaobot.net/file/2024-11-26/720345/e4e274fccc74e8032fa196183a3dcf6a.png)

3\. 找到您的 Google 连接并点击重新授权。

![](https://static.xiaobot.net/file/2024-11-26/720345/92b761575c3dae3a131268aa307aa0e6.png)

为防止您的 Google 连接过期,我们建议您每周重新授权连接或根据以下信息更新项目发布状态。

![](https://static.xiaobot.net/file/2024-11-26/720345/ad87b007471ad6dfbcb29872f859b357.png)


### **Google Drive 应用程序更新日志**

 最后更新：2020-06-08

  - 所有模块现在都支持所有实例 - 我的云端硬盘、与我共享、Google 共享云盘\(Google 工作空间\)。Google 团队云盘应用程序不可用,因为 Google 云盘应用程序现在支持相同的方法。新增了 Google 共享云盘\(适用于 Google 工作空间用户\)模块组：

    - **  观看共享驱动器**

    - **  搜索共享驱动器**

    - **  获取共享驱动器**

    - **  更新共享驱动器**

    - **  删除共享驱动器**

    - **  创建共享驱动器**

  - 监控文件夹中的文件 - 该模块帮助用户通过不下载文件来控制数据和操作的使用。如有需要,可使用单独的"下载文件"模块下载文件。

  - 监视所有文件 - 此模块帮助用户通过不下载文件来控制已使用的数据和操作。如有需要,可以使用单独的"下载文件"模块下载文件。

  - 上传文件/更新文件-高级功能 转换文件-使用 MIME 类型的解决方案。

  - 所有模块 - 使用独立的"识别文件夹路径"模块来检索文件夹 ID。

  - 获取共享链接 - 支持对共享链接的权限进行高级修改。

  - 下载文件 - 原来是获取文件。现在您可以手动选择要下载的文件。

  - 复制文件/移动文件 - 支持所有实例,例如您可以将文件从"我的云盘"复制/移动到"G 共享盘"。

  - 创建文件夹 - 共享文件夹功能 - 支持对共享链接权限的高级修改。

  - 根据文本创建文件 - 一个新模块

  - 将文件/文件夹移到回收站 - 旧模块只能将文件放入回收站。新模块还支持删除空文件夹。

  - 删除文件/文件夹- 旧模块仅可删除文件。新模块也支持删除空文件夹。

  - 发起 API 调用 - 通用模块

  - 查看共享文件 - 已弃用,现在可以使用模块"监视所有文件"或"监视文件夹中的文件"。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-26/720345/9f914bf827e3380c56854c4a4f96fc11.png)