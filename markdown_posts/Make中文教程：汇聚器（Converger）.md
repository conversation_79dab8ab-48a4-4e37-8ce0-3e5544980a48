---
title: "Make中文教程：汇聚器（Converger）"
created_at: "2024-08-27T15:21:31.000000Z"
uuid: "94801611-12fd-4249-bdfd-a5e13acae829"
tags: ['Make基础教程']
---

# Make中文教程：汇聚器（Converger）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于聚合的教程。

**教程简介**  ：本教程介绍了在Make.com中实现Converger模块概念的方法，即如何通过不同变通方案避免常见序列的重复操作，重点介绍了数据存储、JSON、设置变量和使用单独新场景四种方案。

**学习目标**  ：学习如何通过不同的模块和技术手段实现Make.com中没有的Converger模块功能，解决客户需求中常见的操作序列重复问题，提高流程自动化的效率。

**学习内容**  ：主要学习内容包括数据存储模块、JSON模块、设置变量模块、新场景的HTTP与Webhook模块的使用方法，以及如何通过这些模块实现通用序列调用。

**概念理解**  ：Converger模块是一个概念，用来减少不同路线上模块的重复。具体实现方法可以通过数据存储、JSON、设置变量和单独的新场景等变通方案来完成。

**任务练习**  ：尝试在Make.com中创建一个使用Router模块的流程，使用Data store模块在不同路由的末尾存储数据，并在通用序列的开头获取这些数据，验证数据存储和调用的完整性。


## **什么是Converger？**

Converger是Router的反向操作，它的作用是将多个路由分支重新合并到一个公共的处理流程中。虽然Make平台目前没有直接提供Converger模块，但我们有多种替代方案。


## **四种实用的替代方案**


### **1\. 使用Data Store（数据存储）**

最可靠的解决方案，适合处理复杂数据：

![](https://static.xiaobot.net/file/2024-11-18/720345/f2994f048f693f9021292d1ca8f5a5b8.png)

实现步骤：

  - 在Router中添加一个无过滤器的额外路由

  - 在每个分支末尾添加Data Store存储数据

  - 在公共序列开始处用Get Record获取数据


### **2\. 使用JSON方法**

适合需要临时存储结构化数据的场景：

![](https://static.xiaobot.net/file/2024-11-18/720345/8ee1aa8fa617a9d6d01ba73f313e0efd.png)

关键步骤：

  - 用JSON Create + Set Variable存储数据

  - 用Get Variable + Parse JSON读取数据

错误处理提示：

![](https://static.xiaobot.net/file/2024-11-18/720345/affaae216319c31634e5965a05d43284.png)

使用ifempty\(\)函数避免报错：

![](https://static.xiaobot.net/file/2024-11-18/720345/aebe9d32fa0827642e35abc5b84dae71.png)


### **3\. 使用变量**

最简单的方案，适合传递单个值：

  - 使用Set Variable存储

  - 使用Get Variable获取

  - 支持Set Multiple Variables


### **4\. 使用独立场景**

适合复杂的工作流程：

  - 使用HTTP请求传递数据

  - 使用Webhook接收数据


## **选择建议**

  - 简单数据传递 → 使用变量

  - 结构化数据 → 使用JSON

  - 重要数据需持久化 → 使用Data Store

  - 复杂工作流 → 使用独立场景


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/e21277764c7f4b87ad272914d5bf845c.png)