---
title: "Make中文教程：X推特模块授权"
created_at: "2024-11-22T12:12:55.000000Z"
uuid: "ccd27632-3a2c-4838-b165-8a622bae00bd"
tags: ['Make基础教程']
---

# Make中文教程：X推特模块授权

**标签**: Make基础教程

#
# **功能概述**

通过 Make 平台，您可以实现对 X 账户的全面自动化管理。主要功能包括：

  - 帖子管理（发布、监控、搜索）

  - 私信处理

  - 互动管理（点赞、转发）

  - 用户关系管理（关注者、列表）


# 具体配置步骤：

由于X的接口存在两个版本，所以该步骤包含V5和V6版本的配置方法，方便各位读者一次性完成配置。

  1. 访问开发者网站点击登陆输入自己的X账号进行登陆：

<https://developer.x.com/en>

![](https://static.xiaobot.net/file/2024-11-22/720345/3638bb1af41b9e144459d726b5ea47ac.png)
  2. 登陆之后点击右上角的 **Developer Portal**

![](https://static.xiaobot.net/file/2024-11-22/720345/d57973e4d9e17d9c5672797487bf8697.png)
  3. **配置V5和V6版本的连接**

     1. **点击"添加项目"**

![](https://static.xiaobot.net/file/2024-11-22/720345/cddd7ec1c66cf9640989364700fc6d60.png)
     2. **输入项目名称make（名称任选），点击下一步继续**

![](https://static.xiaobot.net/file/2024-11-22/720345/6263697fbda21ec6b8a3b05d38566c9b.png)
     3. **用途根据自己用途进行选择，点击下一步继续**

![](https://static.xiaobot.net/file/2024-11-22/720345/985edc8d24f61512ea5e69e9aa8b85af.png)
     4. **输入一段项目描述，点击下一步继续**

![](https://static.xiaobot.net/file/2024-11-22/720345/5c0cc6338d9c5a00cd6c3ec06116082a.png)
     5. **输入APP名称为project-make（任选），点击下一步继续**

![](https://static.xiaobot.net/file/2024-11-22/720345/b3f3f9ba960b6038f473ae5847c3adeb.png)
     6. **出现API的key相关页面**

![](https://static.xiaobot.net/file/2024-11-22/720345/990ec371f9c746f65ee95106fc4c2abe.png)
     7. **来到Make场景中新建一个X模块，选择V5版本**

![](https://static.xiaobot.net/file/2024-11-22/720345/84a338eff6e177a07ed358156841c901.png)
     8. **点击添加**

![](https://static.xiaobot.net/file/2024-11-22/720345/2155280ed90fd8aab5c7a3468e63b14a.png)
     9. **点击高级设置，输入第6步的API Key和API Key Secret，点击保存一路授权完成V5版本的授权。如果出现报错属于正常现象，由于没有授予权限造成的可以先继续后面V6的步骤进行授权，然后回到这里重新输入即可解决报错！**

![](https://static.xiaobot.net/file/2024-11-22/720345/48c32ddc486ca59dc9de6ac56fa0794c.png)

     10. **回到项目浏览界面，点击设置。**

![](https://static.xiaobot.net/file/2024-11-22/720345/1e1f97538b40e82cc71130be1f5f6d2e.png)
     11. **点击授权设置键**

![](https://static.xiaobot.net/file/2024-11-22/720345/ecfd1d0d37d061dbf8be502e5a18c028.png)
     12. **根据图片选择两个项目**

![](https://static.xiaobot.net/file/2024-11-22/720345/d351e84894bf0354ec90a7882d255c22.png)

     13. **根据图片填写两个网址，点击保存，点击是进行保存。**

**Callback URI / Redirect URL** ：
            
            https://www.integromat.com/oauth/cb/twitter2
            https://www.integromat.com/oauth/cb/twitter

**Website URL** ：
            
            https://www.integromat.com/oauth/cb/twitter

![](https://static.xiaobot.net/file/2024-11-22/720345/850d785ef3a664ca3aaca1b8bf8d9007.png)
     14. **复制两个项目，回到Make进行粘贴**

![](https://static.xiaobot.net/file/2024-11-22/720345/fd949bb2e546b12f24e5b1515ac82a55.png)
     15. **来到Make场景中新建一个X模块，选择V6版本**

![](https://static.xiaobot.net/file/2024-11-22/720345/40b42e314c361861c2e3f9887b8764d4.png)

     16. **填写连接信息，输入第14步中的Client ID和Client Secret一路授权完成V6版本的连接。**

![](https://static.xiaobot.net/file/2024-11-22/720345/c8fbd34bdc781eedbac14e34e4a720b7.png)

注意，要创建包含媒体的帖子，您可以将应用程序版本切换到 v5，使用上传媒体模块完成图片的上传。


## **X支持的动作如下：**

帖子

  - 监视帖子

  - 监视列表中的帖子

  - 监视提及

  - 搜索帖子

  - 列出用户帖子

  - 列出转发者

  - 列出提及

  - 创建帖子

  - 获取帖子

  - 删除帖子

私信

  - 监视私信

  - 列出私信

点赞

  - 列出点赞

列表

  - 将成员添加到列表

  - 列出列表

用户

  - 列出关注者

  - 获取用户


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在[**Make.com**](<https://www.make.com/en>)上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-22/720345/cdef040a6925271d05035ade4254b3e6.png)