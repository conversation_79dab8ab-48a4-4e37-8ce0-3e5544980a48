---
title: "Make中文教程：HTTP"
created_at: "2024-08-30T09:30:51.000000Z"
uuid: "724ee1e8-49bd-4e2a-9a80-8340f77b0424"
tags: ['Make基础教程']
---

# Make中文教程：HTTP

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于HTTP入门的教程。

  1. **教程简介** ：本教程介绍如何使用HTTP模块在Make.com上进行通信，涵盖几种不同的认证方式，包括基本认证、API密钥认证、OAuth 2.0等，讲解如何发送HTTP请求并处理响应。

  2. **学习目标** ：掌握使用Make.com的HTTP模块进行Web通信的基本技能，了解如何配置和发送各种认证类型的HTTP请求，以及如何处理和解析HTTP响应数据。

  3. **学习内容** ：介绍HTTP模块的几种主要功能，包括发送不需要认证的普通请求、基本认证请求、API密钥认证请求，以及下载文件、解析URL和检索HTTP头信息的方法。

  4. **概念理解** ：HTTP是Web数据传输的基础协议，HTTP模块通过发送HTTP请求和处理响应来进行Web通信，不同的认证方式（如基本认证、API密钥认证等）确保请求的安全性和正确性。

  5. **任务练习** ：尝试使用“Make a request”模块发送一个GET请求获取某个网页的内容，并在“Headers”添加 Accept: \*/\* 头来确保接收预期的响应。在执行请求前先手动运行模块测试。


# **HTTP**

这个 HTTP 应用程序提供了基于超文本传输协议\(HTTP\)的通信模块。HTTP 是万维网数据传输的基础组件。作为网络服务器和客户端之间信息交换的骨干,HTTP 允许您下载网页、访问文件、发起 API 请求和触发 webhooks。


## **HTTP 模块概述**

根据您想要使用的资源的身份验证要求,选择 HTTP 应用程序的模块。要使用需要身份验证的模块,您必须先创建连接。

  - 发出请求:通用模块,最适合用于不需要身份验证的资源。

  - 发起基本身份验证请求:访问需要基本身份验证的资源。

  - 使用 API 密钥进行身份验证请求:针对需要 API 密钥验证的资源。

  - 发起一个 OAuth 2.0 请求：对于需要 OAuth 2.0 授权的资源。

  - 发起客户端证书认证请求:对于需要客户端证书认证的资源。

  - 获取文件：从 URL 下载文件。

  - 解析目标 URL：从一系列 HTTP 重定向中检索目标 URL。

  - 从 HTTP 请求模块中分别获取报头。

![](https://static.xiaobot.net/file/2024-11-25/720345/d605d8aa21d87c5fd4de7c318807d57d.png)


## **  提出申请**

发送请求模块允许您创建 HTTP 请求并将其发送到服务器。输出包包含 HTTP 响应。

![](https://static.xiaobot.net/file/2024-11-25/720345/f2fe4e28bf457334e861b91f4ae1406f.png)


### **示例 HTTP 请求与"发送请求"模块**

查看以下截图,了解如何设置"发送请求"模块以发送 POST 请求,并以 JSON 数据格式发送 body :

![](https://static.xiaobot.net/file/2024-11-25/720345/d148cec075354e0be29dad449736a6b7.png)

要确保您的 JSON 有效,请使用 JSON 验证器\(例如:<https://jsonlint.com/)或使用> Create JSON 模块创建 JSON。

![](https://static.xiaobot.net/file/2024-11-25/720345/e44ae3193eaee2472dfb1d25e5f73af3.png)![](https://static.xiaobot.net/file/2024-11-25/720345/2c7184d8c861239749f4db79507e775a.png)


## **进行基本身份验证请求**

发送基本身份验证的 HTTP 请求模块允许您发送具有基本身份验证的 HTTP 请求。输出包包含 HTTP 响应。

![](https://static.xiaobot.net/file/2024-11-25/720345/fae3c5e082e19806d7d0f5ed706ebe54.png)


## **生成 API 密钥验证请求**

制作 API 密钥认证请求模块允许您向需要 API 密钥授权的应用程序发送 API 调用。该模块还支持"Bearer"授权。

输出包包含 HTTP 响应。


### **为 Make an API key Auth request 模块创建 API 密钥连接**

如果您想为"创建 API 密钥验证请求"模块设置连接,请查看以下示例:

  1. 在"创建 API 密钥验证请求"模块设置中,单击"添加"按钮。"添加一个新的密钥链"窗口弹出。

  2.  填写:

     1. 名称：您 API 密钥连接的标签。

     2. 密钥: 用于授权 HTTP 调用的 API 密钥。如果 API 使用 "Bearer" 或 "Token" 授权方式,请在 API 密钥前添加相应的词语。

 示例: Bearer 1234-5678-abcd-efgh

![](https://static.xiaobot.net/file/2024-11-25/720345/ae7718b10b4b239ac9e31cec17e25be3.png)
     3. API 密钥放置:选择是将授权信息放在请求头还是查询字符串中。

     4. API 密钥参数名称：包含 API 密钥的参数的名称。

  3. 点击创建以建立连接。

您已创建了一个 API 密钥连接。您现在可以在"发送 API 密钥身份验证请求"模块中使用该连接。

![](https://static.xiaobot.net/file/2024-11-25/720345/53f48f795bbbb3c8e6b1d4b25fbd46fd.png)


## **发送 OAuth 2.0 请求**

要发送需要 OAuth 2.0 授权的 HTTP\(S\) 请求,您首先需要创建 OAuth 连接。


### **创建 OAuth 2.0 连接**

要在 Make 中配置应用程序,您需要在应用程序开发者门户或设置中创建 OAuth 2.0 凭证,并将它们输入到场景中的 HTTP 模块。

![](https://static.xiaobot.net/file/2024-11-25/720345/a03b593a196633bead6464514101e9e1.png)

**  先决条件**

  -  应用程序账户

  - 开发者门户或设置的访问权限

  - 重定向 URL（有时称为回调 URL）。

**在应用程序中获取凭证**

  1. 在您想要连接的 Make 中创建一个 OAuth 客户端。为此,请进入开发者门户或设置。

     1. 指定重定向 URL: <https://www.integromat.com/oauth/cb/oauth2>

     2. 获取客户端 ID 和客户端密码。有时应用程序称之为应用程序密钥和应用程序密码。

     3. 妥善保管客户端 ID 和客户端密钥。在 Make 中构建场景时您将需要用到它们。

  2. 在应用程序 API 文档中找到 Authorize URL 和 Token URL。这些是 Make 与应用程序通信的地址。

**建立"制作"中的连接**

一旦你从 app 获得了你的凭证,你就可以在 Make 的 app 配置中输入它们

  1. 前往一个创作方案。

  2. 添加 HTTP - 制作 OAuth 2.0 请求模块。

  3. 点击创建连接。

  4. 在"连接名称"字段中输入连接的名称。

  5. 在 Flow 类型中,选择流。

  6. 在作用域中添加 API 作用域。请参阅应用程序 API 文档以获取作用域。

  7. 在"客户端 ID"和"客户端密钥"中输入您之前保存的凭据。

  8. 单击"保存"以创建 OAuth 2.0 连接。

您已创建了一个 OAuth 2.0 连接。现在您可以在"发起 OAuth 2.0 请求"模块中使用该连接。

查看创建连接的高级设置:

![](https://static.xiaobot.net/file/2024-11-25/720345/d8cc0d86cda103193ebabefa7513018e.png)


### **  模块设置**

![](https://static.xiaobot.net/file/2024-11-25/720345/7eefa1369338a31f168452ae08c8f514.png)


## **提出客户端证书认证请求**

向需要客户端证书授权的应用程序发送 HTTP\(S\) 请求。

![](https://static.xiaobot.net/file/2024-11-25/720345/f00a565303adfe08c1a76120452b7176.png)


## **  获取文件**

从 URL 下载文件。

![](https://static.xiaobot.net/file/2024-11-25/720345/2fc006105e8c59ce1b7687c64104222f.png)


## **  解决目标 URL**

输入您想要解析的 URL。输出包包含原始 URL 在 location 响应头中重定向的链接。

该模块可以帮助您获取资源的直接 URL,而不是重定向 URL。例如,在存储应用程序\(如 Dropbox\)中共享文件的链接会先重定向您,然后才能到达目标文件。该模块可以导航重定向链并返回目标 URL。

![](https://static.xiaobot.net/file/2024-11-25/720345/ed184393089a2c6b2437f2a278f473d1.png)


## **  检索消息头**

从指定的 HTTP 模块中返回每个标头（名称和值）的单独捆包。

![](https://static.xiaobot.net/file/2024-11-25/720345/929bf2220f557807485845cbdca2ff95.png)


## **如何生成 JSON Web 令牌（JWT）**

如果您需要连接到 API 或发送需要 JWT 身份验证的消息,您可以使用 HS256 或 HS512 算法通过 HTTP 模块创建 JWT。 Make 允许使用自定义或内置函数创建 JWT。


### **通过自定义函数生成 JWT**

您可以使用自定义函数创建 HS256 或 HS512 JWT。

![](https://static.xiaobot.net/file/2024-11-25/720345/d980875cbe8df8cfb7f7bb00704012ba.png)

按照以下步骤创建 JWT:

  1. 前往"功能"并使用以下主体创建自定义函数:
         
         function readableJWTencode(payload, secret, header = '{"alg":"HS256","typ":"JWT"}') {
             let formattedHeader = iml.replace(iml.replace(iml.replace(iml.base64(header), "=", ""), "+", "-"), "/", "_")
             let formattedPayload = iml.replace(iml.replace(iml.replace(iml.base64(payload), "=", ""), "+", "-"), "/", "_")
             let signature = iml.sha256(formattedHeader + "." + formattedPayload, "base64", secret)
             let formattedSignature = iml.replace(iml.replace(iml.replace(signature, "=", ""), "+", "-"), "/", "_")
             let jwt = formattedHeader + "." + formattedPayload + "." + formattedSignature
             return jwt
         }

![](https://static.xiaobot.net/file/2024-11-25/720345/ab0c39ae73751bfe46959115e2a56ada.png)
  2. 使用"设置多个变量"模块开始您的场景，并添加三项内容:

     1.  物品 1:

        1. 在变量名中，输入 header 。

        2. 在变量值中,输入以下函数:

\{"alg":"HS256","typ":"JWT"\}

![](https://static.xiaobot.net/file/2024-11-25/720345/60809b44527f8f342eed675a84af759f.png)
     2.  项目 2：

        1. 在变量名中，输入 payload 。

        2. 在变量值中,输入以下函数:

\{"var1":1,"var2":"text"\}

     3.  项目三：

        1. 在变量名中，输入 secret 。

        2. 在"变量值"中输入您的 256 位或 512 位密钥。

  3. 添加另一个设置多个变量模块来生成 JWT。

  4. 可选：在变量名称中输入令牌的名称。

  5. 在变量值中,输入以下函数:

\{\{jwtEncode\(11.payload; 11.secret\)\}\}

![](https://static.xiaobot.net/file/2024-11-25/720345/3368bbf4a3a3ca9fc9874da25835c4c5.png)

如果您运行该场景,您将在第二个模块的输出中接收到 HS256 或 HS512 JWT。

![](https://static.xiaobot.net/file/2024-11-25/720345/8a3fbdfdefc4892451d1efa6c2548347.png)

您可以连接到需要 JWT 身份验证的 API 或发送消息。


### **通过内置函数生成 JWT**

使用内置函数生成 JWT 时,请按以下步骤操作:

  1. 使用"设置多个变量"模块开始您的场景，并添加三项内容:

     1.  物品 1:

        1. 在变量名中，输入 header 。

        2. 在变量值中,输入以下函数:

\{\{replace\(replace\(replace\(base64\("\{""alg"":""HS256"",""typ"":""JWT""\}"\); "="; emptystring\); "+"; "-"\); "/"; "\_"\)\}\}

![](https://static.xiaobot.net/file/2024-11-25/720345/624f1405f6b57fba1fcfe666a88a52c1.png)
     2.  项目 2：

        1. 在变量名中，输入 payload 。

        2. 在变量值中,输入以下函数:

\{\{replace\(replace\(replace\(base64\("\{""var1"":1,""var2"":""text""\}"\); "="; emptystring\); "+"; "-"\); "/"; "\_"\)\}\}

     3.  项目三：

        1. 在变量名中，输入 secret 。

        2. 在"变量值"中输入您的 256 位或 512 位密钥。

  2. 添加另一个设置多个变量模块来生成 HS256 或 HS512 JWT。

  3. 可选：在变量名称中输入令牌的名称。

  4. 在变量值中,输入以下函数:

\{\{5.header\}\}.\{\{5.payload\}\}.\{\{replace\(replace\(replace\(sha256\(5.header + "." + 5.payload; "base64"; 5.secret\); "="; emptystring\); "+"; "-"\); "/"; "\_"\)\}\}

![](https://static.xiaobot.net/file/2024-11-25/720345/8a5accc9e5b70ba5255d789d94d27b5d.png)

如果您运行该场景,您将在第二个模块的输出中接收到 HS256 或 HS512 JWT。

![](https://static.xiaobot.net/file/2024-11-25/720345/fb870697aa439d560bacb9c0cc700f27.png)

您可以连接到需要 JWT 身份验证的 API 或发送消息。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-25/720345/583b4e1d742cb96aea2da3d708dae088.png)


###