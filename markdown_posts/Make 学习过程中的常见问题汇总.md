---
title: "Make 学习过程中的常见问题汇总"
created_at: "2024-10-04T01:46:49.000000Z"
uuid: "fa3c50e7-7488-4843-b51b-c248f2de60c3"
tags: ['Make高级应用']
---

# Make 学习过程中的常见问题汇总

**标签**: Make高级应用

#
## 1\. 没有AI基础也可以学习使用Make吗？

翔宇建立工作流的初衷是为了帮助没有接触过AI的小伙伴，通过AI工具提升工作效率。虽然工作流相比基础的大模型对话稍有难度，但只要认真观看教程视频，积极进行资料检索，并保持耐心，遇到的问题都能得到解决。需要特别提醒的是，使用Make工作流时，必须保持持续学习的心态，勇于实践和探索，不要只停留在提出问题的阶段，而忽视了自身的主动性。当然，翔宇也在不断更新相关基础教程，帮助初学者解决各种问题。


### 2\. 不会代码能正常学习使用Make工作流吗？

不需要编程基础也可以正常学习和使用Make工作流。Make的设计初衷就是为了让没有技术背景的用户也能轻松创建和管理自动化流程。通过其直观的拖拽界面和预设的模块，用户可以轻松搭建各种工作流，无需编写任何代码。此外，翔宇提供了详细且易于理解的教程视频，涵盖从基础操作到高级应用的各个方面，帮助初学者逐步掌握Make的使用方法。


### 3\. 学习工作流花费很多吗？

学习和使用Make工作流的成本非常低，基本可以通过以下方式控制费用：

• **免费额度** ：Make提供每月1000次免费操作，满足大多数日常需求。如果超出，可以通过注册额外账号获取额外的1000次操作。

• **付费方案** ：只需70元即可购买1万次操作数，性价比高，适合需要频繁使用的用户。

• **API费用** ：使用ChatGPT-4 API，每百万token输入费用为0.15美元，输出为0.6美元，日常使用成本可控。如果使用国产模型，智谱AI的**GLM-4-Flash** 现在是免费调用，基础应用没有问题。

• **其他模块** ：翔宇分享的模块大多有免费额度，足以覆盖基础使用需求。

总体来说，通过合理利用免费资源和选择合适的付费方案，学习和使用Make工作流的费用是非常经济的，适合各种预算的用户。


### 4\. 为什么要学习工作流？

随着时代的快速发展，尤其是AI的快速进步，掌握工作流变得越来越重要。像GPT这样的AI，已经能处理接近人类水平的问题，使用AI的需求也越来越强烈。未来，人与人之间的差距更多体现在谁能更好地利用工具。

最近发布的ChatGPT 1o模型，已经达到博士级别的推理能力。换句话说，未来的竞争对手不仅是人类，还有这种高水平的AI。它们可以写作、计算、绘画、制作视频，甚至进行创新。只有学会使用AI，才能在竞争中保持优势。

比如，现在的AI在生成文本时有字数限制，通常在2000字左右。如果未来突破这一限制，写作领域的许多工作将会被AI替代。所以，现在花时间和精力学习AI，不仅成本不高，而且性价比很高。

而Make工作流就属于最好的选择，各个工作流面向的是不同应用场景，将AI工具串起来，解决的是一个领域具体的问题，避免陷入只学习工具，而忽略应用的本末倒置的问题，所以工作流解决的是应用的问题、是效率的问题，而不是学习工具的问题。


### 5\. Make工作流达到的效果怎么样？

翔宇分享的工作流是根据不同工作场景有针对性地搭建的，具有相当的竞争力（这需要大家自己判断，以避免有广告嫌疑）。未来，大家可以结合自己的领域进行专业化调整，从而提升工作流的质量。其实，目前的效果中有60%左右仍受限于大模型本身的能力，但随着模型不断进步，可出现完全替代人类具体工作的情况。


### 6\. 什么是RSS？

RSS（Really Simple Syndication）可以比作一个信息快递服务。通常，如果你想获取某个网站的最新内容，可能需要频繁访问它。而通过RSS，你只需订阅一次，之后就像安排了快递员，网站有更新时，内容会自动送到你的“收件箱”——也就是RSS阅读器。

RSS阅读器（如Feedly、Inoreader）能帮你管理这些更新。你只需添加网站的RSS订阅源，它们会自动收集并汇总最新内容，省去手动访问网站的麻烦。

在Make中，也可以使用RSS、Feedly、Inoreader模块，自动获取订阅源中的信息，并集成到你的工作流中。比如，自动提取文章用于内容创作，实现高效信息追踪和整合，提升创作效率。RSS不仅方便获取信息，结合Make的自动化功能，还能让信息高效转化为创作素材。


### 7\. 为什么Make工作流一直使用notion作为数据库？有哪些其他的工具可替代吗？

这是因为Notion具有如下特点：

灵活性：Notion提供了极大的灵活性，可以创建各种类型的数据库结构。

易用性：Notion有直观的用户界面，非技术用户也能轻松使用。

协作功能：Notion允许多人实时协作，适合团队工作。

API支持：Notion提供了强大的API，便于与Make工具集成使用。

数据可视化：Notion可以将数据以多种视图呈现，如表格、看板、日历等。

文档与数据库结合：Notion允许在同一空间内混合使用文档和数据库。

前后一体的数据库及页面，方便发布进行外部访问。

有如下工具可以进行替代：

Airtable：提供灵活的数据库功能和友好的用户界面，可通过代码实时触发。

Google Sheets：广泛使用的电子表格工具，适合简单的数据管理。

Trello：适合用作简单的任务管理和工作流程跟踪。

ClickUp：项目管理和协作工具，可以用作工作流数据库。

Smartsuit：可支持机翻中文的数据库工具，Make原生模块支持实时触发。


### 8\. 为什么自动化工具选择Make，而不是Coze和Dify等其他平台？

Make 之所以是许多用户选择的自动化工具，主要原因在于以下几点：

  1. **广泛的模块支持** ：Make 支持1900多个原生模块，无需编码即可连接各种应用程序，并通过HTTP模块扩展到数千个应用。每个模块都支持多种动作，可满足各方面自动化需求。相比之下，其他平台在应用支持数量和灵活性方面可能不足。

  2. **强大的可视化设计界面** ：Make 提供立体化的可视化界面，支持线性、分支设计路径和数据映射，便于用户直观地构建复杂的自动化流程。相比之下，其他平台的可视化功能可能不够全面，缺乏立体化的数据控制和设计灵活性。

  3. **高性价比** ：以较低的成本提供大量的自动化运行次数，尤其适合个人用户。

  4. **成熟的生态系统** ：Make 社区丰富，用户可以轻松获取教程和示例，降低学习成本，并在模块扩展和数据处理方面提供良好支持。而其他平台的生态系统相对薄弱，难以达到Make那样的广泛支持和教程积累。

  5. **盈利模式成熟** ：Make 的盈利模式可持续，这确保用户的技能和投入不会因平台策略变动而浪费，学习后的技能具备长期适用性，降低了对平台不稳定性的担忧。

总结来看，Make 在模块支持、可视化操作、成本效益、生态系统与稳定性方面均表现出色，使其成为构建复杂自动化流程的优质选择。