---
title: "抖音自动化采集 Make工作流"
created_at: "2024-10-23T07:40:21.000000Z"
uuid: "03d8dedf-c0ca-42ca-9f52-93e613e604a8"
tags: ['会员独家']
---

# 抖音自动化采集 Make工作流

**标签**: 会员独家

![](https://static.xiaobot.net/file/2024-10-23/720345/cfff3bee6381fe9276f3d17c9747c377.png)

抖音视频采集工作流旨在帮助会员高效采集抖音视频相关信息。采集的内容广泛，包括但不限于以下信息：

**标题**

**昵称**

**头像**

**用户URL**

**音乐URL**

**用户id**

**视频id**

**视频URL**

**笔记内容**

**视频下载URL**

**封面URL**

**收藏数量**

**评论数量**

**喜欢数量**

该工作流对视频的各个方面进行了全面覆盖，会员可根据需求进行深度分析和内容再创作。请注意，工作流仅涵盖数据采集部分，具体的内容创作及赛道分析将在后续的视频教程中发布，敬请期待。本工作流基于TikHub.io提供的丰富接口支持，确保采集过程的稳定性和高效性。


# **关于 TikHub.io：**

[TikHub.io](<https://beta-web.tikhub.io/users/signup?referral_code=kHHZHSGN>)是一个功能强大的集成工具及服务平台，支持主流社交媒体及视频平台的数据获取。它的集成工具适用于多种平台，包括但不限于：

\- **Douyin（抖音）**

\- **TikTok**

\- **Xiaohongshu（小红书）**

\- **Kuaishou（快手）**

\- **Weibo（微博）**

\- **Instagram**

\- **YouTube（油管）**

\- **Twitter（X）**

此外，TikHub.io 还提供了验证码解决方案和临时邮箱 API，帮助用户自动化获取和管理数据。


# **TikHub.io 的核心功能：**

1\. **API 接口**

提供多种 RESTful API 接口，支持各大平台数据的访问，包括获取用户作品、视频信息、互动数据等。

2\. **功能定制**

用户可根据业务需求定制特定功能。平台允许社区成员接入自定义功能或接口，以灵活满足个性化需求。

3\. **数据爬取**

平台支持通过 API 获取各大社交媒体平台的用户信息、视频及互动数据，适合用于市场调研及数据分析。

4\. **用户管理**

TikHub.io 提供后台管理功能，用户可生成并管理 API Token，设定权限和有效期。

TikHub.io 的设计目标是帮助用户高效展开业务，同时提供灵活的工具，满足各类业务需求。不论是营销人员、数据分析师，还是内容创作者，都可以借助 TikHub.io 迅速获取社交媒体数据，实现业务转化。

**价格：**

![](https://static.xiaobot.net/file/2024-10-23/720345/7740b80b08666dde6268564f2c337128.png)


# **注册与使用步骤：**

1\. **注册账户：**

请访问以下链接完成注册：

[TikHub.io 注册](<https://beta-web.tikhub.io/users/signup?referral_code=l5FI5n2b>)

2\. **获取 API Key：**

注册后，登录控制台，进行账户充值并获取您的 API Key。

3\. **权限设置：**

进入接口管理页面，配置所需接口的使用权限。

![](https://static.xiaobot.net/file/2024-10-23/720345/ae0dd64238c2b2b8b2b4b98f0447c919.png)![](https://static.xiaobot.net/file/2024-10-23/720345/03f0fdecdf598514ae16ba7ab44fd98c.png)

如果希望测试需要进行授权查看调用示例：

![](https://static.xiaobot.net/file/2024-10-23/720345/e18ee07404c516165f563fd9f5f7e09e.png)

完成上述步骤后，您便可开始在小红书平台进行高效的数据采集工作。

有关具体的使用教程视频，您可以访问以下链接：

[链接](<https://tikhub.io/>)

![](https://static.xiaobot.net/file/2024-10-23/720345/1aae41d3ddb40ace4ac2c5c45860aed5.png)


# **工作流介绍：**

![](https://static.xiaobot.net/file/2024-10-23/720345/7d95add64021307516439e27fab3dfe2.png)

1\. **通过基础触发器触发**

在基础触发器中输入需要采集的用户id，以及采集的视频数量。

其中用户id具体获取方法如下：

在电脑端打开抖音，搜索需要采集的用户，其中地址栏中user后面的一串数字就是用户id。

[https://www.douyin.com/user/MS4wLjABAAAApAf1TgonjhWrLMzRSpVFrbcCrOGH5FHoxTZotQdGTyM/search/%E5%9B%BD%E5%A4%96%E9%A3%8E%E6%99%AF?aid=7bb9754c-630f-4ed4-87dc-c9fd324c34b2&type;=general](<https://www.douyin.com/user/MS4wLjABAAAApAf1TgonjhWrLMzRSpVFrbcCrOGH5FHoxTZotQdGTyM/search/%E5%9B%BD%E5%A4%96%E9%A3%8E%E6%99%AF?aid=7bb9754c-630f-4ed4-87dc-c9fd324c34b2&type=general>)

以上示例中userid为：[MS4wLjABAAAApAf1TgonjhWrLMzRSpVFrbcCrOGH5FHoxTZotQdGTyM](<https://www.douyin.com/user/MS4wLjABAAAApAf1TgonjhWrLMzRSpVFrbcCrOGH5FHoxTZotQdGTyM/search/%E5%9B%BD%E5%A4%96%E9%A3%8E%E6%99%AF?aid=7bb9754c-630f-4ed4-87dc-c9fd324c34b2&type=general>)

2\. **获取抖音具体用户的视频信息**

\- 使用“Make a Request”模块发送API请求。

\- 配置请求URL，并填写具体的请求内容获取视频数据。

\- 注意将自己的api key进行替换填写正确。

3\. **遍历笔记ID**

\- 处理API响应，将视频ID进行遍历，获取每个检索到的视频。

4\. **检索重复保存项**

\- 基于视频ID，在Notion数据库中进行检索以验证是否存在重复项目。

5\. **获取笔记详情**

\- 基于视频ID，使用“Make a Request”模块获取详细的视频信息。

  6. **保存Notion数据库**

\- 更新notion数据库视频条目，将获取到的视频信息进行正确映射。


# **资源链接：**

抖音用户自动化采集库：

[https://xiaoyuzaici.notion.site/128017a905fd8016aef1ea8d31fed73b?v=128017a905fd81f68a06000c9204b863&pvs;=4](<https://xiaoyuzaici.notion.site/128017a905fd8016aef1ea8d31fed73b?v=128017a905fd81f68a06000c9204b863&pvs=4>)

工作流地址：

[抖音视频批量采集工作流（单用户版）](<https://pan.quark.cn/s/cdb7cc1109d7>)

提取码：EbPt

**注意事项** ：

视频获取信息非常复制，需要详细检查获取到的数据是否正确映射。

由于种种限制，采集接口变动频繁，如出现问题可登陆接口网站查询最新接口调用指南。

由于第三方服务可能存在不稳定性，可能随时失效，建议会员采取小额充值、按需使用的策略，避免大额充值带来的资金浪费。此外，务必注意保护个人信用卡信息，防范网络诈骗或数据泄露风险。

另外，由于属于第三方服务，出现的任何使用问题与不稳定情况，可到相关网址进行解决方案的获取。

本文分享的所有数据获取方法和工具推荐仅供学习、交流和了解数据获取途径之用，禁止用于任何商业用途。会员在使用这些工具时应严格遵守相关法律法规，确保使用行为的合法性。