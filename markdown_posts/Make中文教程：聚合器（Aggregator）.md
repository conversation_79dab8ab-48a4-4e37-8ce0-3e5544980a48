---
title: "Make中文教程：聚合器（Aggregator）"
created_at: "2024-08-27T11:34:18.000000Z"
uuid: "dfc0b5be-6f38-4d27-84ee-7d1e28b3ceca"
tags: ['Make基础教程']
---

# Make中文教程：聚合器（Aggregator）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于聚合器的教程。

  1. **教程简介**  ：本教程介绍了Make.com中的聚合器模块，重点讲解如何使用该模块将多个数据包合并成一个单一的数据包，并通过实际示例展示了聚合器的应用场景。

  2. **学习目标**  ：了解并掌握聚合器模块的基本用法，学会在工作流中通过聚合多个数据包来简化数据处理流程，能够在实际项目中应用聚合器模块进行数据整合。

  3. **学习内容**  ：

     - 概述聚合器模块的功能和操作机制

     - 聚合器模块主要字段的配置

     - 聚合器的实际应用示例

     - 注意事项及最佳实践

  4. **概念理解**  ：聚合器是一个模块，用于将多个数据包合并成一个含有数组的单一数据包。通过定义来源模块和分组依据，可以灵活地控制数据包的聚合方式。启用 "在空聚合后停止处理" 选项可以避免无数据包时的输出。

  5. **任务练习**  ：设计一个工作流，监控一个邮箱中的新邮件附件，使用迭代器逐一处理附件，利用聚合器模块将所有附件整合成一个ZIP文件，并最终将其上传至Dropbox。


# 聚合器

聚合器是一种旨在将多个数据包合并为单个数据包的模块类型。

当执行聚合器时，它:

  1. 收集它收到的所有捆绑包\(在单个源模块的操作期间\)

  2. 输出一个包含一个数组的单个 bundle,数组中的每个项目都是一个累积的 bundle。数组项目的内容取决于特定的聚合器模块及其设置。

数组聚合模块是聚合模块的一个典型示例。聚合器通常包含以下字段:

![](https://static.xiaobot.net/file/2024-11-28/720345/05d79cee77c42f45b6061f8b41eafdbe.png)![](https://static.xiaobot.net/file/2024-11-28/720345/0147c667c9044bcef9cf5d9f20259458.png)

如果您需要从源模块和聚合器模块之间的任何其他模块输出的捆绑包中访问项目,请确保将它们包括在聚合器的设置中,例如在 Array 聚合器模块的设置中的"聚合字段"字段中。


## **  示例**


### **用例：将所有电子邮件附件压缩为 ZIP 文件,并将其上传至 Dropbox**

以下场景演示如何:

  1. 监视邮箱以接收传入的电子邮件:电子邮件>监视电子邮件触发器将输出一个包含项目 Attachments\[\] 的包,这是一个包含所有电子邮件附件的数组。

  2. 遍历电子邮件的附件：从电子邮件的 Attachments\[\] 数组中逐个取出项目,并将它们作为单独的包发送。

  3. 将电子邮件>迭代附件模块输出的束聚合到:存档>创建存档聚合器,它会累积所有接收到的束并输出一个包含 ZIP 文件的单一束

  4. 将生成的 ZIP 文件上传到 Dropbox：Dropbox > 上传文件从归档中获取 ZIP 文件 > 创建一个归档模块并将其上传到 Dropbox。

![](https://static.xiaobot.net/file/2024-11-28/720345/2bfe78f6bc462709e240bc539c8a3d1e.png)

下面是设置存档>创建存档聚合器的示例：

![](https://static.xiaobot.net/file/2024-11-28/720345/09db59c79a676d375f37717af5c7004a.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/b54eb06ab07c3d124c39b416efe8de0d.png)