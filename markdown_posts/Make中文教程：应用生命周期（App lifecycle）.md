---
title: "Make中文教程：应用生命周期（App lifecycle）"
created_at: "2024-08-27T11:19:57.000000Z"
uuid: "7937d6b3-c1ff-41f7-8c55-ddb2964f2a39"
tags: ['Make基础教程']
---

# Make中文教程：应用生命周期（App lifecycle）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于应用生命周期的教程。

  1. **教程简介**  ：本教程介绍了Make.com应用的生命周期，其中包含Beta、Stable、Deprecated和Shutdown四个阶段的详细描述及其标识和管理方式。

  2. **学习目标**  ：了解Make.com应用在其生命周期中各个阶段的特征和标识，掌握如何识别和处理不同阶段的应用，确保应用的稳定性和持续性。

  3. **学习内容**  ：

     - 应用的Beta阶段特征及标识

     - 应用的Stable阶段特征及标识

     - 应用的Deprecated阶段特征及标识及处理方法

     - 应用的Shutdown阶段特征及应对措施

  4. **概念理解**  ：

     - **Beta阶段**  ：标志为Beta标签，表示应用依赖的API处于测试版。此阶段应用已全面测试，但可能有小问题。

     - **Stable阶段**  ：没有特殊标签，表示应用稳定且支持频率低。

     - **Deprecated阶段**  ：标志为绿色图标和白色箭头，表示旧应用已弃用且不再接受支持。

     - **Shutdown阶段**  ：应用不再运行，并会提前通知用户用新版本替代旧应用。

  5. **任务练习**  ：

     - 在Make.com中找到一个处于Beta阶段的应用，并记录其特征。

     - 识别并记录一个Stable阶段应用的标识。

     - 找到一个Deprecated应用，尝试将其替换为最新版本，记录替代过程。

     - 模拟一个应用进入Shutdown阶段的场景，制定应对策略并记录步骤。

在Make平台中，大部分应用都依赖第三方服务的API（应用程序接口）。随着这些服务的发展，其API也在不断更新迭代。新版本API会定期发布，旧版本则会逐步废弃并最终关闭。Make团队会密切关注新API版本的发布，并基于最新API开发新的应用。


## **应用的四个生命周期阶段**


### **1\. Beta阶段**

在场景编辑器的应用列表中，Beta阶段的应用会显示特殊标记：

![](https://static.xiaobot.net/file/2024-11-18/720345/682a5e2ef23b9abb426a3e06e555e520.png)

应用列表展示：

![](https://static.xiaobot.net/file/2024-11-18/720345/67210d3271b893aebcd54f6062153a36.png)

重要说明：

  - Beta标记表明该应用基于第三方服务的测试版API

  - 虽然经过QA团队的严格测试，但可能存在小问题

  - 欢迎用户反馈发现的问题

  - 具体细节请参考应用文档


### **2\. 稳定阶段**

当问题报告频率降低后，应用进入稳定阶段：

![](https://static.xiaobot.net/file/2024-11-18/720345/9d70968faf46dc480f06dadc90be8867.png)

特点：

  - 不显示特殊标记

  - 功能稳定可靠

  - 适合生产环境使用


### **3\. 废弃阶段**

当新版本或替代应用可用时，旧应用进入废弃阶段：

![](https://static.xiaobot.net/file/2024-11-18/720345/9a80329e754c821036e08f7bc4310f3e.png)

在场景编辑器中的表现：

![](https://static.xiaobot.net/file/2024-11-18/720345/857d7ff99e5d0507bc4d5bf846be5f0f.png)![](https://static.xiaobot.net/file/2024-11-18/720345/64bf557079fe48a063c4f0f37db1d1f0.png)

废弃阶段的特点：

  - 不再提供技术支持

  - 可能从应用列表中隐藏

  - 将在预先通知后停止运行

  - 应用名称后缀标注"\(legacy\)"


### **4\. 关闭阶段**

这是应用的最终阶段：

  - 应用将停止按照规范运行

  - Make会提前通知使用该应用的客户

  - 建议用户及时升级到新版本


## **重要建议**

  1. 升级时机

     - 发现废弃标记时应立即计划升级

     - 不要等到收到关闭通知才行动

     - 提前规划可避免紧急情况

  2. 监控建议

     - 定期检查应用状态

     - 关注Make的更新通知

     - 测试替代方案

  3. 迁移策略

     - 创建新版本的备份场景

     - 逐步迁移重要流程

     - 确保新旧版本平稳过渡

通过了解这些生命周期阶段，您可以更好地规划和管理您的自动化场景，确保业务流程的稳定运行。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/575d04240f3885a0bcfaeee69a0a1219.png)