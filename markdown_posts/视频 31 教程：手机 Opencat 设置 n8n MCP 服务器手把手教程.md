---
title: "视频 31 教程：手机 Opencat 设置 n8n MCP 服务器手把手教程"
created_at: "2025-05-04T07:56:07.000000Z"
uuid: "353a96f4-cb67-48a6-9101-c4d3b5f739cd"
tags: ['n8n 基础教程']
---

# 视频 31 教程：手机 Opencat 设置 n8n MCP 服务器手把手教程

**标签**: n8n 基础教程

![](https://static.xiaobot.net/file/2025-05-04/720345/941774df37ebd95bf0397f1f515d2a53.png)

大家好，我是翔宇，专注于自动化和 AI 。今天要分享的是如何利用手机 APP Opencat 设置 MCP 服务器，实现方便快捷的大模型调用。通过本文的详细介绍，即便你是第一次接触 MCP 和 Opencat，也能轻松掌握操作方法，快速落地自己的 AI 自动化服务。


### **什么是 MCP 服务器？**

MCP（Model Communication Protocol）即模型通信协议，是专门为大语言模型与外部服务交互设计的标准接口协议。简单来说，MCP 就像是大语言模型与外部软件之间沟通的桥梁。

以往，我们使用大语言模型时往往只能单一调用，无法灵活扩展。现在有了 MCP，只需一次配置，就能让大语言模型轻松与任何外部工作流、API 或应用进行交互，实现复杂业务场景的自动化执行。

例如，你想要搭建一个 AI 图片自动生成流程，通过 MCP 可以非常方便地将你的大模型与 n8n 工作流连接，让模型直接从 n8n 中获取参数并返回生成结果。


### **什么是 OpenCat？**

OpenCat 是一款适用于 iOS 和 macOS 的原生 ChatGPT 客户端，专为希望在 Apple 设备上高效使用 OpenAI API 的用户设计。该应用由 Early Moon, LLC 开发，支持 iPhone、iPad 和 Mac，用户可通过 App Store 免费下载。

**主要功能包括：**

  - **原生键盘扩展** ：在任何文本输入场景中快速调用 AI，提升输入效率。

  - **语音聊天与朗读** ：集成微软 TTS（文本转语音）技术，支持多种语言和语音选项，实现自然流畅的语音交互。

  - **多模型支持与提示词库** ：可自定义提示词，支持多轮对话，适用于翻译、写作、代码辅助等多种场景。

  - **iCloud 同步与 Siri 集成** ：在 iOS 和 macOS 设备间同步聊天记录，并可通过 Siri 快速调用特定 AI 角色。

**MCP 支持：**

OpenCat 支持 Model Context Protocol（MCP），允许用户连接和使用 MCP 服务器提供的工具和功能。例如，OpenCat 内置了 Apple 地图 MCP，无需任何配置即可使用，支持将地址转换为坐标、搜索景点、搜索美食、规划路线等功能，类似于高德地图的功能。

**使用说明：**

  - 提供免费版和付费版，Pro 版本一次性购买，解锁键盘扩展、iCloud 同步、Siri 集成等高级功能。

  - 首次使用可能需要订阅 Cloud AI 订阅服务（有免费试用根据自己需求选择），支持 GPT、语音对话、图像生成等功能，适合对 AI 有更高需求的用户。

**下载地址：**

  - <https://opencat.app/zh-Hans/>


### **MCP + OpenCat可以实现哪些价值？**

当 MCP 服务器与 OpenCat 相结合，可以实现以下价值：

  - ​**降低门槛** ​：无需编写复杂的代码，图形界面配置即可完成高效的大模型调用。

  - ​**快速集成** ​：支持任意工作流和第三方服务的快速接入，减少重复开发。

  - ​**灵活扩展** ​：一次设置，可无限次重复调用，极大提高业务灵活性。

  - ​**成本降低** ​：免去复杂服务部署和维护成本，更专注于业务逻辑本身。

接下来，我将手把手教你如何在 OpenCat 中配置 MCP 服务器。


### **在 n8n 中使用哪种MCP模式？**

MCP（Model Communication Protocol）支持两种主要的通信模式：**SSE（Server-Sent Events）模式** 和**stdio（标准输入输出）模式** 。

n8n 里的 MCP Server Trigger 节点当前只持 Server‑Sent Events \(SSE\)，明确标注“暂不支持 stdio 传输” 。

**原因** ：SSE 需要长连接才能把模型生成的流式数据实时推回 n8n，而 n8n 的触发节点本身就是对外暴露 HTTP URL；stdio 属于本地进程输入/输出，n8n 云端环境无法直接把它映射成公网 URL，所以官方干脆没做。所以下面的方法针对 SSE 进行设置，具体过程如下：

* * *


## **一、** 获取 MCP 网址和设置密码：

视频中会完整展示过程，如果不会设置推荐首先完整观看视频了解概念、理解。

  - **然后导入运行视频 31 的工作流之后，点击 MCP server trigger节点，获取网址和设置密钥，如图所示：**

![](https://static.xiaobot.net/file/2025-05-04/720345/0c632e19a5abfaf76dc2e124c220ccf7.png)

  - **上图获取网址**

![](https://static.xiaobot.net/file/2025-05-04/720345/a638fe4b03d2c6a6a66d4e2a9e3a0844.png)![](https://static.xiaobot.net/file/2025-05-04/720345/9afc6cda64495cf9b79f21a270fbbce4.png)

  - **上图设置密码**

  - **在测试第三步开始前需要激活MCP工作流。**

![](https://static.xiaobot.net/file/2025-05-26/720345/d63eeaca0215404445cf6daf51268aae.jpeg)


## **三、如何在 iOS 端启用 MCP 地图功能**

  1. **升级至最新版本**

确保您的 OpenCat 已更新至最新版本。

![](https://static.xiaobot.net/file/2025-05-04/720345/236326444cff4cf1410c625b73004e3a.x-www-form-urlencoded)
  2. **进入设置打开 MCP 服务**

     - 打开 OpenCat，点击左上角“设置”（齿轮图标）。

![](https://static.xiaobot.net/file/2025-05-04/720345/530a09d897a9103c42b48093177e0109.x-www-form-urlencoded)
     - 选择“MCP 服务”一栏，点击右上角加号手动配置

![](https://static.xiaobot.net/file/2025-05-04/720345/9bab609a0d879e967795f0d0974812be.x-www-form-urlencoded)

![](https://static.xiaobot.net/file/2025-05-04/720345/d8d5b850c42bbe4ecc35e4f918f7d440.x-www-form-urlencoded)
     - 在进入的界面内 URL 栏目填写在第一步获取到的网址，其中类型选择 sse，运行确认选择自动运行选，启用这个工具开启。在HEADER 栏目填写如下代码，其中 Bearer 空格后面是你自己在第一步设置的密码，我这里就使用我设置的 xiangyugongzuoliu 密码。

Authorization=Bearer xiangyugongzuoliu。

  3. **测试配置**

     - 完成测试配置退回到对话界面，点击锤子图片，然后选择n8n 即刚才设置的 MCP 即可正常对话进行使用。

![](https://static.xiaobot.net/file/2025-05-04/720345/0eb8639a78f59e1bcfcdb6f711ff1ca5.x-www-form-urlencoded)

![](https://static.xiaobot.net/file/2025-05-04/720345/ccea576abd9986220e186fa791133531.x-www-form-urlencoded)

这样，你不再需要频繁修改代码或重复进行技术部署，只需专注于业务流程本身，即可轻松实现从想法到落地的高效工作流自动化。

今天翔宇就分享到这里，赶快动手试试吧，享受低门槛、快速落地的大模型服务新体验！

![](https://static.xiaobot.net/file/2025-05-04/720345/06946921c04fe1466410af0e0ba5da53.x-www-form-urlencoded)