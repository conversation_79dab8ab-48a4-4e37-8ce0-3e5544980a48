---
title: "Make中文教程：活跃和非活跃场景（Active and inactive scenarios）"
created_at: "2024-09-21T09:09:11.000000Z"
uuid: "fe700235-d409-4147-8595-fc81f53f5364"
tags: ['Make基础教程']
---

# Make中文教程：活跃和非活跃场景（Active and inactive scenarios）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于活跃与非活跃场景的教程。

**教程简介** ：本教程介绍了在Make.com中如何管理场景的活动状态和非活动状态，以及在不同情况下如何选择合适的场景状态来实现自动化任务。

**学习目标** ：了解如何在Make.com中激活或停用场景，以及在何种情况下选择活动或非活动场景，以便更高效地管理自动化流程。

**学习内容** ：

  1. 场景的定义和两种状态（活动和非活动）。

  2. 活动场景的定期运行机制和应用场景。

  3. 非活动场景的单次运行机制和使用案例。

  4. 激活和停用场景的具体操作步骤。

**概念理解** ：活动场景是指根据预设计划定期运行的场景，而非活动场景只在手动触发时执行一次。用户可以根据需求随时在两种状态间切换，以便更高效地实现自动化任务管理。

**任务练习** ：创建一个连接Instagram和Dropbox的场景。先设置为非活动，手动执行一次上传照片任务。然后激活场景，观察其在预设间隔时间内自动上传新照片。


# 活跃和非活跃场景

在 Make 中,您可以通过两种方式使用场景。根据您希望如何使用场景,您将激活或停用场景。

  - 按照既定的时间表，活跃场景会定期运行

  - 非活动场景会被执行一次,并且随时可以运行它们

您可以随时在这两种状态之间切换\(活动场景可以变为非活动状态,反之亦然\)。


## **为什么激活？为什么不激活？**

![](https://static.xiaobot.net/file/2024-11-24/720345/57b2fa92e1fad2ec691a1f620ac8acc7.png)

激活你的情景有特定的目的,同时也有其他原因使你的情景保持非激活状态。

以连接 Instagram 到 Dropbox 为例,当您希望长期持续关注您的帐户时,您希望激活此情景。在这种情况下,每当您添加一张照片,它都会自动将该照片上传到 Dropbox。它将根据其计划执行此操作。

另一方面，非活动场景非常适合仅从特定时间间隔中选择照片的情况。在场景编辑器中单击"一次运行"图标将启动场景的一次性执行。


## **  如何激活**

默认情况下,新创建的场景是非激活状态。您可以通过单击场景详情页面上的开/关按钮来激活它。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-21/720345/1562ab4757015cdb2d6f1f7dcd74cf78.png)