---
title: "Make中文教程：Make中的警告类型（Types of warnings in Make）"
created_at: "2024-08-30T01:55:26.000000Z"
uuid: "167ee104-0aa3-4e8b-945d-0cf97b001c27"
tags: ['Make基础教程']
---

# Make中文教程：Make中的警告类型（Types of warnings in Make）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Make中的警告类型的教程。

  1. **教程简介** ：本教程介绍Make.com中可能遇到的各种警告类型及其处理方法，重点讲解了ExecutionInterruptedError和OutOfSpaceError的成因及解决策略。

  2. **学习目标** ：了解Make.com中的警告类型及其与错误的区别，掌握针对ExecutionInterruptedError和OutOfSpaceError的具体处理方法，以提升自动化任务的稳定性和效率。

  3. **学习内容** ：教程涵盖Make.com中情境模块触发的警告类型、警告与错误的区别、ExecutionInterruptedError和OutOfSpaceError的成因、解决策略及具体操作步骤。

  4. **概念理解** ：警告是情境运行中需要注意的事件，但不会中断情境运行或计入连续错误数。ExecutionInterruptedError是情境运行时间超限的结果，OutOfSpaceError则是数据存储空间耗尽导致。

  5. **任务练习** ：创建一个包含多个模块的情境，以实验ExecutionInterruptedError和OutOfSpaceError。调整模块数量和数据存储策略，应用错误处理器，以解决警告并优化情境运行。


# **Make 警告类型**

在一个场景中,您可以拥有众多模块和项目映射。它们共同创造了您的场景。这些元素中的每一个都可能创造一个需要您注意的事件--一个警告。

警告也可能是由于使用 Break 错误处理程序处理错误而产生的结果。使用警告 Make 通知您 Break 错误处理程序创建了您的方案的不完整执行。

警告和错误之间的主要区别是,警告不会禁用您的方案的调度,而连续错误的数量不包括警告。

当模块返回警告时，Make 会在模块上添加"警告"标志。要检查错误类型，请单击模块上方的思维泡泡。

![](https://static.xiaobot.net/file/2024-12-02/720345/53744e30cfc13e9f1f6efd250ca8f3e7.png)

当您使用错误处理时,错误处理程序启动时会显示警告。在示例中,Break 错误处理程序处理了 BundleValidationError 。错误处理程序的激活将原始错误转换为警告。


## **ExecutionInterruptedError**

当场景运行超过 40 分钟\(免费订阅为 5 分钟\)时,模块输出 ExecutionInterruptedError 。当前正在处理数据的模块输出警告。

当模块输出 ExecutionInterruptedError 时，该场景以警告结束。该场景不会处理剩余的包。Make 将继续安排后续的场景运行。

要修复 ExecutionInterruptedError ，请考虑优化场景的策略:

  - 拆分你的场景。你可以使用以下模块的组合:

    - 生成 > 运行一个场景模块并将您的数据作为场景输入发送到下一个场景。

![](https://static.xiaobot.net/file/2024-12-02/720345/5bd78f652178a6d20dba17e5f71b92bc.png)
    - 向下一个场景中的 webhook 发送相关数据的 HTTP 应用程序。

例如,如果您正在处理一个场景中的大量捆绑包,该场景包含 20 个模块,并且该场景以 ExecutionInterruptedError 结束,您可以将该场景分为两个各有 10 个模块的场景。

  - 将搜索模块的限制设置为较低的数字,以减少场景处理的数据量。

  - 查看您在该场景中使用的应用程序的 API 文档。如果该应用程序的 API 具有支持批量请求的合适端点,您可以使用 JSON 聚合器和 HTTP 应用程序来批量发送您的数据。此外,您还可以节省操作消耗。


## **OutOfSpaceError**

当 Make 无法再在数据存储中存储更多数据时,数据存储模块会输出 OutOfSpaceError 。当您填满不完整的执行存储时,您也会获得 OutOfSpaceError 。

如果模块输出了 OutOfSpaceError 而没有错误处理,该场景以警告结束。该场景不会处理剩余的包。make 将继续安排后续的场景运行。

要修复一个场景中的 OutOfSpaceError ，您可以使用带有 Resume 错误处理程序的备份数据存储。您还应该检查数据存储或您的不完整执行存储中的数据。您还可以查看 Make 定价。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/4c8e1fcc6875a78687db9a9c6db08b0f.png)