---
title: "会员必看：Make与 n8n 小报童使用指南"
created_at: "2024-09-19T06:19:55.000000Z"
uuid: "7c407ce2-2043-40fe-a72d-2c08d97a4c6d"
tags: ['会员独家']
---

# 会员必看：Make与 n8n 小报童使用指南

**标签**: 会员独家

衷心感谢各位会员订阅小报童！为帮助您快速入门，翔宇精心准备了这份万字长文教程。建议您在加入小报童后，首先完整、仔细地阅读本教程，这将为您的自动化之旅打下坚实基础。

**微信号：xiangyugzl**


## **教程变化说明**

**2024 年 09 月 19 日 第一版发布**

翔宇自动化教程正式上线，聚焦 Make 平台的核心功能应用，为零基础用户提供循序渐进的学习路径。内容覆盖基础模块使用、自动化场景搭建以及实战案例解析，帮助初学者迅速入门。

**2024 年 12 月 10 日 第二版更新**

Make 平台功能持续迭代，翔宇同步更新教程内容以保持技术前沿性。需要说明的是，为了保证视频教学内容的一致性，早期课程所用的工作流文件将保持不变，而新发布的工作流则会采用当前最优技术方案。这种“旧例保持 + 新例进化”的并行策略，既便于初学者跟学，也能让进阶用户体验技术演进的全过程。

**2025 年 01 月 21 日 第三版更新**

本次更新新增翔宇官网与系列内容导航，进一步完善教程体系的入口与可访问性。用户可通过网站便捷查找各类工作流、实用工具与进阶教学，提升整体学习效率。

**2025 年 03 月 15 日 第四版更新**

随着 Make 平台和 AI 大模型技术快速发展，翔宇对教程内容进行了全面升级。

  - **大模型调用方面** ：从最初推荐使用 OpenAI 官方模块，转向支持 OpenRouter 多模型中转平台，具备更高性价比与结构化输出能力。

  - **HTTP 数据处理方面** ：从早期依赖文本解析器手动提取 JSON 字段，进化为采用 Create JSON 模块快速构建标准结构，极大提升开发效率与可读性。

此次更新不仅紧跟技术趋势，也重新梳理了推荐的学习路径，帮助用户更系统地掌握自动化与 AI 的融合应用。

**2025 年 04 月 25 日 第五版更新**

翔宇全新推出 **n8n 自动化工作流系列** ，正式将开源自动化平台 n8n 纳入教程体系。

本系列以实际应用场景为核心，涵盖自媒体运营、跨境电商、科研自动化、知识服务与副业变现等方向，提供高质量、可复用的工作流模板。所有内容均基于 n8n 的灵活特性（如流程控制、代码节点、多源数据整合）进行开发，最大限度发挥其高性能与自定义优势。

同时，翔宇也将陆续推出 **n8n 中文系统教程** ，帮助用户从零构建复杂自动化系统，真正做到“掌握一套工具，解决多个问题”。


## **1\. 订阅小报童后的重要资源位置：**

由于翔宇发布的内容越来越多,为了方便会员获取和使用各类独家资源，翔宇设立了以下几个主要平台：


### **翔宇工作流专栏介绍：**

**小报童资源包分栏：**

点击 <https://xiaobot.net/p/xygzl2024> 微信扫码登录后，点击**顶部切换资源包标签页。**

作为小报童平台的核心板块之一，这里精心汇集Make 和 n8n 工作流视频教程配套的工作流模板文件。会员可通过一键下载并导入这些模板，轻松复现并即刻运行视频中展示的所有自动化工作流。不仅如此，我们的资源包还包含视频配套的PPT与Notion数据库模板，让您能够无缝衔接、快速部署。

![](https://static.xiaobot.net/file/2025-04-25/720345/c95175e852a8b163eaf50fb6471d0837.png)![](https://static.xiaobot.net/file/2025-04-25/720345/b2047828fb12e2f8eb1cbf6fd7c532d8.png)

**小报童会员独家分栏：**

会员专区收录了翔宇为会员量身打造的高价值自动化工作流，涵盖自媒体变现等多个应用场景。每个工作流均配有对应的工作流源文件，帮助会员快速上手，实现从学习到实战、从掌握自动化工具到高效变现的一站式转变。

**小报童微信公众号：**

订阅微信公众号，方便随时在微信中获取阅读最新内容：

![](https://static.xiaobot.net/file/2025-04-25/720345/cca1a7028a9f2888f8e684ee03ef3b01.png)

**小报童 AI 教程与资源分栏：**

本板块为会员专属资源中心，汇集了丰富多元的资源，涵盖精选提示词库、AI资源汇总、高效RSS订阅指南。这里的每一项资源都经过精心筛选与优化，旨在全方位提升您的工作效率与创作能力。

**小报童自动化赚钱分栏：**

手把手教你如何结合Make与 n8n 自动化技术，高效拓展副业收入。提升技能的同时，实现学习与收益双赢。深入解析自动化工作流与创收平台，帮你打造可持续的赚钱体系，让副业更轻松、更智能！

**小报童 Make 基础教程分栏：**

这是一个全面且实用的Make平台使用指南,主要包含三大核心内容:

  - 详尽的模块使用教程 - 系统讲解Make平台内置及第三方模块的功能介绍、配置方法

  - 结构化的错误排查指南 - 按模块类型和错误类别整理常见问题,提供明确的解决方案

**小报童快捷工具分栏：**

为提升工作流搭建效率，翔宇精心打造了一系列实用工具。包括HTTP模块快捷调用工具方便直接复制数据结构创建 JSON 以及复制代码创建 HTTP 模块。

**小报童API资源分栏：**

翔宇精选整理了大量优质API资源，既包含完全免费的接口，也囊括了具有免费额度的高性能服务。会员可以根据具体需求灵活选择最适合的API。更重要的是该板块持续更新 AI 优惠信息让会员能够领取各种福利。


## **翔宇工作流官网介绍：**

网址： [https://xiangyugongzuoliu.com/ ](<https://xiangyugongzuoliu.com/>)包含如下内容：

**AI 自动化工作流：**

专注于为自媒体、跨境电商、科研、知识学习、办公等多个领域提供实用的自动化解决方案。每个工作流不仅能帮助您解决具体的业务需求（明线），同时还会深入介绍Make平台的一种新颖使用方法，为您的未来进行工作流的魔改提供丰富的参考案例（暗线）。明线和暗线相结合，让会员可以学习到更多的知识。每次看完视频可以思考一下：例如这个视频明线是制作万字长文，而暗线是解决图文混排文章创建的方法。

访问网址：<https://xiangyugongzuoliu.com/category/ai-automation-workflow/>

**AI 自动化赚钱：**

手把手教你如何结合 Make 与 n8n 自动化技术，高效拓展副业收入。提升技能的同时，实现学习与收益双赢。深入解析自动化工作流与创收平台，帮你打造可持续的赚钱体系，让副业更轻松、更智能！

访问网址：<https://xiangyugongzuoliu.com/category/ai-automation-money-making/>

**Make 中文教程：**

专为零基础会员打造的系统化学习课程。通过循序渐进的方式，带领您掌握Make平台的核心概念\(如基础模块、路由器、数据映射等\)，让您能够轻松上手并熟练运用Make平台进行自动化操作。每个视频都经过精心设计，确保学习过程清晰明了,学以致用，视频会定期更新。

访问网址：<https://xiangyugongzuoliu.com/category/make-automation-tutorial/>

**n8n 中文教程：**

专为零基础会员打造的系统化学习课程。通过循序渐进的方式，带领您掌握Make平台的核心概念\(如基础模块、路由器、数据映射等\)，让您能够轻松上手并熟练运用Make平台进行自动化操作。每个视频都经过精心设计，确保学习过程清晰明了,学以致用，视频会定期更新。

访问网址：<https://xiangyugongzuoliu.com/category/n8n-chinese-tutorial/>


## **2\. Make基础学习路径指南**

本部分将为您介绍系统化的Make学习步骤，帮助您从零开始掌握Make自动化平台。

会员朋友们请特别关注这一部分。掌握Make平台首先需要建立扎实的基础概念和知识体系，这样才能更好地进行后续学习。每位学习者应根据自身情况选择合适的学习路径：

\- 如果您具备代码编写经验并了解数据结构，可以直接导入模板进行实操，因为您已具备必要的计算机基础知识

\- 如果您没有相关经验，根据翔宇的实践经验，建议先学习基础概念，例如什么是映射、模块、连接等核心概念，下方提供的视频教程将帮助您全面了解这些内容

请记住，学习过程中切勿急于求成。有时候"欲速则不达"，建议您先系统学习以下基础概念板块，打好坚实基础，为后续的高级应用做好准备。

**2.1 基础概念学习**

以下视频教程是Make平台入门的必备基础资源，强烈建议您首先观看翔宇精心制作的Make中文系列教程。这些教程内容丰富、讲解清晰，确保您始终掌握最新的Make平台知识：

**Make自动化基础入门**

网址：<https://xiangyugongzuoliu.com/make-automation-tutorial-1-basics/>

该教程以深入浅出的方式全面讲解Make平台的核心概念、基础功能与运作原理，帮助您构建对Make自动化的系统性认知框架。这是掌握Make平台的关键第一步。观看完视频后，建议您自我检测：能否清晰解释Make中"映射"的含义？理解模块的本质功能是什么？掌握Make的核心配置流程了吗？通过这些自问自答，您可以有效评估自己对平台核心原理的理解程度，为后续实践打下坚实基础。

**5分钟快速上手教程**

网址：<https://xiangyugongzuoliu.com/make-automation-tutorial-2-5min-workflow/> 这是一个实操性质的视频教程，详细演示了如何导入和运行翔宇精心设计的模板工作流。通过这个快速入门指南，您可以在最短时间内亲身体验Make平台的强大功能。教程中重点解析了初学者最常遇到的结构化输出问题、JSON数据处理技巧，以及模板导入的完整步骤。观看后，强烈建议您进行多次实际操作练习，因为这是Make平台最基础也是最常用的操作流程。熟练掌握后，您应当能够不依赖视频指导，独立完成模板的导入与运行，为后续更复杂的自动化工作流搭建奠定坚实基础。

**会员必看：Make工作流模板导入教程（会员独家）**

该教程详细讲解了模板的导入流程与技巧，特别强调了一个关键环节：每当在工作流中添加新的 Notion 页面后，必须前往连接设置界面重新进行授权操作，以确保系统能够正确识别并访问这些新增页面。这一步骤对解决常见的 Notion 授权问题至关重要，是确保工作流顺畅运行的必备知识点。

网址：<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039>

**Make平台价格方案2025最新详解：免费版vs付费版完整对比**

该文章全面剖析了Make平台免费版与各级付费版本之间的功能差异、操作限制及性价比对比，帮助用户根据自身需求和预算做出最合理的选择。无论您是个人创作者还是企业用户，都能从中找到最适合自己的订阅方案。

网址：<https://xiangyugongzuoliu.com/make-pricing-plan-comparison-guide-2025/>

**Make平台常见错误与解决方案**

该教程全面梳理了Make平台使用过程中最常遇到的各类错误及其对应解决方案，包括连接授权失败、数据映射异常、模块配置错误等典型问题。

网址：<https://xiangyugongzuoliu.com/make-common-errors-and-solutions-guide/>

**免费用Make的方法来啦！（会员独家）**

该内容详细解析了一种巧妙的资源优化策略，通过创建多个独立账号并灵活配置工作区授权机制，实现Make平台功能的免费长期使用。

网址：<https://xiaobot.net/post/face36c3-4310-459f-824a-************>

**Make平台使用过程中的常见问题与解决方案**

该教程全面解析了初学者在使用Make平台时最常遇到的疑问与挑战，包括"没有AI或编程基础能否轻松上手Make"、"学习自动化工作流需要投入多少时间与精力"等核心问题，为零基础小白提供了清晰的入门指引，消除了技术门槛的顾虑。

网址: <https://xiaobot.net/post/fa3c50e7-7488-4843-b51b-c248f2de60c3>

**2.2 入门级工作流实践**

掌握基础概念后,建议按以下顺序尝试简单工作流:

**小红书自动化：如何利用Make制作个人自媒体中心，批量生成爆款笔记**

网址：<https://xiangyugongzuoliu.com/4-xiaohongshu-make-content-automation/>

通过简单模块组合,实现小红书爆款笔记的批量生成,并自动保存到Notion数据库。这个案例非常适合初学者上手。

**微信公众号图文混排文章自动化实战：利用Make 批量制作**

网址：<https://xiangyugongzuoliu.com/9-wechat-make-batch-article-automation/>

学习如何自动生成微信公众号文章并保存到Notion,是一个实用性很强的入门案例。

**从 Apify到 Notion：如何利用make实现自动化Youtube视频采集**

在本期视频中，深入探讨如何利用Make工具实现从Apify到Notion的自动化Youtube视频采集。通过整合Apify、Make.com、JSON格式化工具等模块，我们构建了一套自动更新的视频库系统。

网址：<https://xiangyugongzuoliu.com/2-apify-to-notion-make-youtube-automation/>

**Jina Reader api实操：如何利用make自动采集OpenAI官网新闻**

在本期视频中，我们将深入探讨如何利用make.com来创建一个网页数据采集的自动化工作流。我们将以OpenAI官网的新闻页面为例，演示如何自动采集全文内容并将其保存到Notion知识库中。通过整合RSS、ChatGPT、HTTP模块、文本解析、Notion等模块，我们构建了一套自动更新的新闻采集库。

网址：<https://xiangyugongzuoliu.com/3-jina-reader-make-chatgpt-openai-news-automation/>

**2.3 进阶工作流实战**

在熟悉基础操作后,可以尝试更复杂的工作流:

**提示词生成新方法！用Make自动化生成，只有今天能学到的独家技巧！**

网址：<https://xiangyugongzuoliu.com/23-prompt-automation-with-make-exclusive-tips/>

这是一个综合性较强的案例,涉及多个复杂模块的配合使用,能帮助您深入理解Make的高级功能。在本视频中，我们将探讨如何通过Make平台构建一个高效的提示词生成工作流。您将了解如何利用自动化工具，快速生成高质量、结构化的提示词，并将其存储到Notion知识库中，实现随时调用。

**AI写作革命！DeepSeek自动生成10万+爆文工作流教程**

本视频将为您展示如何利用Make平台和DeepSeek的 R1 模型，自动化生成高质量的爆文。只需输入主题和风格要求，系统即可全程自动化地从素材抓取到文章创作，再到图片生成，快速产出一篇图文并茂的爆款文章。

网址：<https://xiangyugongzuoliu.com/24-ai-writing-revolution-deepseek-workflow-wechat-office/>

**3分钟速成：利用Make和Flux打造图文并茂的儿童绘本**

在本期视频中，搭建一个基础的图片绘制工作流，实现图文并茂儿童绘本的制作。

网址：<https://xiangyugongzuoliu.com/19-make-flux-children-picture-book/>

**小红书图文笔记批量制作，Make帮你实现图片创作自动化**

本期视频将带您了解如何利用Make实现小红书图文笔记的批量化制作和自动化创作。我们将通过网页数据结构化提取、大模型内容生成和网页转图片工具，批量制作财经词汇知识卡片，实现百张图片的一键生成，极大提高创作效率。

网址：<https://xiangyugongzuoliu.com/xiaohongshu-bulk-content-creation-make/>

**其他工作流可以根据自己的需求进行选择实战。**

网址：<https://xiangyugongzuoliu.com/category/make-automation-workflow/>

**2.4 数据采集工作流实战（由于该部分没有视频教程，推荐完全熟悉Make之后在进行实践）**

小红书自动化采集工作流（会员独家）

网址：<https://xiaobot.net/post/02648d80-5a88-4c73-9fd6-6d3680a30e96>

特点：通过Make自动化采集小红书笔记数据,并保存到Notion数据库。

抖音自动化采集工作流（会员独家）

网址：<https://xiaobot.net/post/03d8dedf-c0ca-42ca-9f52-93e613e604a8>

特点：通过Make自动化采集抖音视频数据,并保存到Notion数据库。

微信公众号采集工作流-方案1（会员独家）

网址：<https://xiaobot.net/post/36df6fc9-c215-499c-b371-c571026f5663>

特点：通过Make自动化采集微信公众号文章,并保存到Notion数据库。

微信公众号采集工作流-方案2（会员独家）

网址：<https://xiaobot.net/post/a1c777f7-4098-4849-9934-6793ce3a2241>

特点：通过Make自动化采集微信公众号文章,并保存到Notion数据库。

免费推文自动化采集工作流（会员专享）

网址：<https://xiaobot.net/post/7397f304-2a96-409d-8dd9-99552e8d834e>

特点：通过Make自动化采集推文数据,并保存到Notion数据库。

免费Instagram自动化采集工作流（会员独家）

网址：<https://xiaobot.net/post/bbbc4483-31ca-4308-91ab-010e9cbb2b13>

特点：通过Make自动化采集Instagram数据,并保存到Notion数据库。

完成以上学习后，您可以根据个人需求和兴趣，有选择地探索其他工作流案例，持续提升您的Make平台应用能力。不过，初学阶段建议避免直接挑战过于复杂的工作流，以免因经验不足而频繁遇到挫折，影响学习热情。随着您对平台理解的逐步深入，您将能够掌握更多高级功能，最终打造出完美契合自身需求的专属自动化工作流方案。


## **3\. n8n基础学习路径指南**

**n8n常见问题16问：新手入门指南** 本篇翔宇原创教程，围绕 n8n 新手常见的 16 个核心问题，系统解答“什么是 n8n、如何搭建、常见报错如何调试、与 Make 有何不同”等关键疑问。适合零基础入门，也适合有 Make 经验的用户进阶过渡，帮你从小白到上手实战，掌握自动化副业核心技能！

<https://xiangyugongzuoliu.com/n8n-faq-for-beginners-guide/>

**n8n 新手指南：开源工作流自动化平台全面介绍**

本篇系统讲解了开源自动化平台 **n8n** 的核心概念、应用场景、发展历程、适用人群与未来趋势。翔宇不仅深入拆解了工作流、节点、触发器等基础知识，还结合多个真实案例（如内容创作、营销自动化、AI集成等），带你全面掌握 n8n 如何在个人副业与企业应用中高效落地。

<https://xiangyugongzuoliu.com/n8n-beginner-guide-open-source-workflow-automation/>

**新手必读！n8n教程与资源指南：从入门到实战全面掌握工作流自动化**

本期翔宇工作流整理了一份超实用的《n8n 入门指南与资源地图》，涵盖：官方文档、中文教程、模板库、插件扩展、活跃社区入口……一步步带你从新手入门到实战上手，快速掌握 n8n 的自动化能力，构建属于自己的副业工作流系统！

<https://xiangyugongzuoliu.com/n8n-resources-guide-for-beginners/>


## **4\. 大模型调用选择**


### **4.1 大模型API概述**

在开始使用Make工作流之前,我们需要先了解并选择合适的大模型API。这一步骤对于工作流的顺利运行至关重要。 在导入工作流运行的过程中,其中最关键的是选择合适的大模型平台。目前市面上主要有以下几种选择方式：

  1. OpenAI官网API - 最推荐的综合性选择

  2. OpenRouter中转API - 灵活方便的方案

  3. DeepSeek官方 API - 最具性价比的方案

  4. Mistral AI - 免费且便捷的选择

  5. 其他API - 包括国内外各类替代方案


### **4.2 API类型说明**

在使用大模型服务时,我们会遇到两种主要的API类型:官方API和中转API。让我们来了解它们的区别:

什么是API? API\(应用程序接口\)是一种允许不同软件系统之间进行通信的技术接口。在大模型场景下,API让我们能够通过编程方式调用AI模型的能力。

**官方API vs 中转API：**

官方API\(如OpenAI原生API\)：

直接由模型开发商\(如OpenAI\)提供的原生接口

稳定性好,响应速度快

通常只能访问该公司的模型

**中转API\(如OpenRouter、302AI\)：**

由第三方平台提供的统一接口

支持多种不同公司的模型

可以灵活切换不同模型

可能会有轻微的延迟

偶尔会出现报错等问题

下面我们将介绍几个常用的API选项。为了帮助大家更好理解,我们先来说明一下它们的类型:

**• OpenRouter是中转API**

\- 这意味着它相当于一个"中间商",可以帮我们连接多个AI模型,使用起来更灵活方便

**• OpenAI是官方API**

\- 这是直接由OpenAI公司提供的接口,只能使用OpenAI自家的模型\(如GPT-3.5、GPT-4等\),但稳定性更好

**• DeepSeek是官方API**

\- 这是直接由DeepSeek公司提供的接口,支持 DeepSeek V3 和 R1 两个版本。

**• Mistral AI也是官方API**

\- 同样是由Mistral公司直接提供的接口,专门用于调用他们自己开发的AI模型

对新手来说,建议先从OpenRouter开始尝试,因为它既容易上手,又能让你体验多种不同的AI模型。等熟悉了之后,再根据具体需求选择是否要使用官方API。


### **4.3 各API平台详细介绍**

**4.3.1 OpenRouter API\(灵活之选\)**

OpenRouter是一个强大的AI模型中转平台,通过提供统一的API接口,让用户可以便捷地访问和调用各类主流大语言模型。作为中转平台,它不仅简化了API的调用流程,还大幅降低了使用成本。**由于OpenRouter使用量大，针对特定模型偶尔会出现报错问题，只需重新运行即可。**

如果您追求性价比和灵活性,OpenRouter是一个非常理想的选择。它支持包括OpenAI的GPT-4和GPT-3.5、Anthropic的Claude系列、Google的Gemini Pro、Mistral的开源模型以及Meta的Llama2在内的数百种主流大模型。通过OpenRouter,您可以根据具体需求灵活切换不同的模型,充分利用各个模型的独特优势。

平台的其他优势还包括:支持信用卡直接充值、完整的结构化输出\(JSON\)支持、即将推出的联网搜索功能,以及与Make平台的原生集成支持。特别值得一提的是,OpenRouter提供了统一的API调用接口,这意味着您只需要配置一次,就可以轻松在不同模型之间切换,大大简化了开发和使用流程。

官方网站：<https://openrouter.ai/>

OpenRouter支持的模型列表可以在 <https://openrouter.ai/models> 查看。

这个页面非常友好,特别适合初学者:

• 提供多维度筛选功能:您可以根据价格、使用场景、模型系列、热门程度等多个维度进行筛选,快速找到最适合您需求的模型

• 免费模型筛选:只需将价格滑块调至"免费"档位,即可一键筛选出所有免费可用的模型,对预算有限的用户特别友好

• 详细的模型信息:每个模型都标注了关键参数,包括:

  - 上下文长度\(决定模型能处理多长的文本\)

  - 输入价格\(发送给模型的文本收费\)

  - 输出价格\(模型生成的回复收费\) 这些信息可以帮助您更好地评估使用成本

强烈建议大家花些时间浏览这个页面,了解不同模型的特点,这对后续选择合适的模型非常重要。

**4.3.2 OpenAI API\(新手推荐\)**

作为行业标杆,OpenAI API提供:

• 稳定可靠的服务

• Make平台原生支持

• 完整的结构化输出

• 成熟的技术支持

重要说明:OpenAI API与ChatGPT Plus会员是完全独立的系统,需要单独购买和充值。不要混淆两者 - ChatGPT Plus是面向个人用户的订阅服务,而API是面向开发者的付费接口。购买ChatGPT Plus会员并不能使用API功能,反之亦然。要使用API,您需要在OpenAI官网\(https://platform.openai.com\)注册并充值。请务必确认您是在正确的平台\(platform.openai.com\)上注册API账户,而不是在chat.openai.com上订阅ChatGPT Plus会员。

  1. 在OpenAI官方平台完成账户注册

  2. 添加有效的付款方式\(通常需要国外信用卡\)

  3. 完成账户充值

如果您没有国外信用卡,可以考虑以下方案:

使用Wildcard虚拟信用卡服务\(需支付开卡费用\)

注册地址: <https://bit.ly/400lC4T>

详细配置教程: <https://www.xiaobot.net/post/d4199829-cc22-41b2-aee9-7658cd378d06>

![](https://static.xiaobot.net/file/2025-04-25/720345/bc22db984335cbc0e57526ef231a1c04.png)

**充值页面选择 OpenAI API 进行充值**

建议:首次仅进行小额充值测试,避免资金风险

完成API账户设置后,您可以参考此教程将OpenAI API与Make平台进行对接:

网址：<https://xiaobot.net/post/2fe22e2e-cb71-49d2-8a70-e910d20287d6>

**4.3.3 DeepSeek \(简单便捷\)**

DeepSeek是一款功能强大且极具性价比的AI模型，凭借其卓越的性能和便捷的使用体验在市场上脱颖而出。它已经成为Make平台的原生模块，用户只需在Make平台上直接选择DeepSeek模块，无需任何额外配置即可使用。在数据处理方面，DeepSeek提供了完善的结构化输出支持，能够轻松应对JSON等各类格式化数据的处理需求，为开发者提供了极大便利。

其最新的R1推理模型在性能表现上更是令人瞩目，不仅能够从容应对各类复杂任务，其推理能力更是可以与ChatGPT O1相媲美。对于那些希望在有限预算内获得顶级AI体验的用户而言，DeepSeek无疑是一个明智之选。它完美平衡了性能与成本，为用户提供了一个经济实惠且功能强大的AI解决方案。

使用方法：<https://xiangyugongzuoliu.com/deepseek-ai-guide-make-automation-2025/>

**4.3.4 Mistral AI\(免费方案\)**

对于预算有限或刚开始接触AI开发的用户来说,Mistral AI是一个非常理想的选择。作为一个新兴的AI模型提供商,Mistral AI以其慷慨的免费政策和出色的性能表现脱颖而出。

这个平台最大的优势在于它完全免费提供服务,让用户可以在零成本的情况下开始AI开发之旅。同时,它与Make平台的原生集成支持,意味着您无需复杂的技术配置就能快速上手使用。平台支持结构化输出功能,能够轻松处理需要格式化返回的场景,比如JSON数据处理等。

对于新手来说,Mistral AI的另一大亮点是其简单直观的配置流程。您只需要按照平台提供的指南进行简单设置,就能开始使用其强大的AI功能。如果您在配置过程中需要帮助,可以参考这些详细的配置教程：

文字版教程：

<https://xiaobot.net/post/17cb50a4-b256-46cf-90aa-c874f34b980a>

视频教程：

<https://xiangyugongzuoliu.com/22-xiaohongshu-instagram-make-image-video-automation/>

**4.3.5 其他AI模型选择**

让我们来看看还有哪些优秀的AI模型可供选择:

  - Claude系列\(由Anthropic公司开发\)： Claude是一个非常强大的AI助手,它的主要优势包括:

优秀的多语言处理能力\(可以轻松处理中英文等多种语言\)

支持图像处理\(可以理解和分析图片内容\)

新版Claude 3.5 Haiku性能已经非常接近高端版本Sonnet

价格非常合理\(处理输入文本每百万字符只需1美元,生成回复每百万字符5美元\) 想深入了解Claude,可以访问:

详细介绍：<https://www.anthropic.com/api>

价格方案：<https://www.anthropic.com/pricing>

  - Gemini Pro\(谷歌最新推出的AI模型\)： Gemini是谷歌的王牌AI产品,它的特点是:

超强的长文本处理能力\(一次可以处理200万个字符,相当于一本小说的长度\)

在复杂思维和推理方面表现出色,特别适合处理需要深度思考的任务 想了解更多细节,可以查看:

文档地址：<https://ai.google.dev/gemini-api/docs?hl=zh-cn>

价格信息：<https://ai.google.dev/pricing?hl=zh-cn>

国产AI选项：

Kimi\(特别擅长生成长篇文章和内容创作\)

智谱AI\(价格实惠,性价比很高\)，这些国产平台虽然在接入时需要多花一些时间配置,但使用成本较低,非常适合预算有限的用户。


### **4.4 新手入门指南**

如果您刚开始接触AI API,我们建议您按照下面的顺序尝试:

  1. OpenRouter - 性价比高,支持多个模型,价格实惠

  2. DeepSeek - Make原生支持,配置简单,性能强大，性价比最高

  3. **OpenAI API - 最稳定可靠，新手推荐使用**

  4. Mistral AI - 完全免费,非常适合初学者练手和测试

  5. 其他API - 等您熟悉了基本操作后,可以根据具体项目需求选择合适的API

值得一提的是，翔宇工作流中大量采用了OpenAI的大语言模型。这主要是因为ChatGPT作为行业先驱，拥有最为成熟和完善的功能生态。若您需要提取或复用工作流中的提示词，只需创建一个OpenAI的API密钥，无需充值即可将其连接至Make平台。这样不仅能方便地复制和替换提示词，还可以根据个人需求灵活切换至其他您偏好的模型，实现工作流的个性化定制。


### **5\. Notion在Make中的使用方法：**

Notion是一个强大的笔记和知识管理工具,要实现自动化操作,我们需要先将它与Make平台连接起来。本章将为您详细介绍如何完成这个过程。


### **5.1 Notion中Make的配置方法：**

在Make与Notion之间建立连接时，我们强烈推荐选择Public连接方式。这种方式不仅操作简单，而且稳定性好，特别适合新手使用。

**具体操作步骤：**

  1. 首先确保您已经注册了Notion账号并成功登录。如果还没有账号，可以到Notion官网免费注册。

  2. 在Notion中，选择Public连接方式。这种方式相比其他连接方式更加直观和便捷。

  3. 打开Make平台，在添加新连接时选择Notion模块，按照提示完成Notion账户授权。这个步骤很重要，它确保Make可以安全地访问您的Notion内容。

想要查看详细的图文教程，请访问：

**网址：**

<https://xiaobot.net/post/59e0ee29-fbfb-4547-9c69-8d49b2e8cf95?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

**特别注意事项：**

• 每当您在Notion中新建页面或数据库时，都需要在Make的Connection设置中重新授权，让新建的内容对Make可见。这一点经常被新手忽略，如果遇到访问权限问题，请优先检查这里。

• 建议在操作时参考上述教程中的"将数据库添加到 Make 中可见"部分，这能帮助您避免很多常见的权限问题。


### **5.2 使用过程中可能遇到的问题：**

在使用Notion与Make连接的过程中，难免会遇到一些技术问题。我们已经整理了最常见的错误及其解决方案，您可以通过以下链接查看：

网址：<https://xiaobot.net/post/5dcf39c4-a0c2-4cc5-9391-77a91ceac939>

这份错误解决指南涵盖了绝大多数新手可能遇到的问题，建议您先收藏起来，以备不时之需。


### **5.3 和Notion相关的工作流有：**

**5.3.1 从RSS到Notion：如何利用make实现自动更新知识库**

介绍如何将RSS订阅源的内容自动同步到Notion知识库

实现信息的自动化采集和分类整理

链接：<https://www.xiaobot.net/post/2e535cdf-3142-444c-98ea-792bc69dfa9e>

**5.3.2 高效Notion自动化实操：数据库自动关联分类整理**

详细讲解如何使用Make实现Notion数据库的自动化管理

包含数据库间的关联、内容分类和智能整理功能

帮助用户摆脱手动整理的繁琐工作

链接：<https://xiaobot.net/post/86e6a93a-3920-4663-bbe9-adb86aa8b134>

**5.3.3 零基础教程：使用Notion和Make搭建自动化电子图书馆**

面向新手的完整教程，从零开始搭建个人电子图书馆

结合Notion和Make实现图书资源的自动化管理

包含图书信息采集、分类和检索等功能的实现方法

链接：<https://xiaobot.net/post/086cfd74-e51a-40e8-9c0b-107ecdce6726>


## **6\. Make中的RSS资源汇总**

RSS是一个强大的信息获取工具，结合Make自动化平台可以实现内容的智能聚合、过滤和分发。以下是RSS学习的推荐路径和重要资源：


### **6.1 RSS基础入门**

RSS 是什么：高效获取信息的利器

网址：<https://xiangyugongzuoliu.com/rss-make-automation-guide/>

介绍RSS的基础概念，方便理解RSS.


### **6.2 RSS实用资源**

2025年RSS订阅源汇总

网址：<https://xiangyugongzuoliu.com/2025-rss-resource-guide/>

2025年RSS订阅源汇总，方便快速查找具体领域的RSS订阅源.


### **6.3 进阶应用（会员专享）**

微信公众号RSS订阅指南 （会员独家）

网址：<https://xiaobot.net/post/d7e45c3b-c381-429b-be3e-6738b28849f5>

介绍微信公众号RSS订阅的方法和注意事项.

RSS订阅工具汇总（会员独家）

网址：<https://xiaobot.net/post/3fb1a892-cc7b-4775-943c-9aa1d414897b>

介绍RSS订阅工具的使用方法和注意事项.

为什么自动化工作流需要Follow？（会员独家）

网址：<https://xiaobot.net/post/80797a10-**************-eac9fe120276>

介绍为什么自动化工作流需要Follow，以及如何使用Follow.


## **7\. Make中的API使用方法：**


### **7.1 什么是API？**

API（Application Programming Interface，应用程序编程接口）是一些预先定义的函数，或指软件系统不同组成部分衔接的约定。

具体介绍可以参见： 深入解读API：Make打造自动化工作流的关键

网址：<https://xiangyugongzuoliu.com/make-api-integration-guide-2025/>

2025年Make平台Google API指南

网址：<https://xiangyugongzuoliu.com/slug-make-google-apis-integration-guide-2025/>


### **7.2 HTTP调用快捷方法：Make中调用第三方API的利器**

在Make中,有些第三方服务可能没有现成的原生模块可以直接使用。这时我们就需要通过HTTP请求来调用这些服务的API接口。为了帮助会员更好地使用这些服务，翔宇花费了三个月的时间，精心整理了一份HTTP调用快捷工具集。

**7.2.1 HTTP快捷工具包含哪些内容？**

这份工具集涵盖了20多种常用的第三方服务的HTTP调用方法，包括:

  - AI对话类：KIMI、Claude、智谱AI、Google Gemini等

  - 搜索工具类：博查、Exa、Firecrawl等

  - AI创作类：Deepbricks、Replicate、Fal.ai等

  - 其他实用工具：OpenRouter、Jina、302.AI等

**7.2.2 为什么需要这份工具？**

省时省力：免去自己摸索API调用方式的时间

避免错误：特别是在使用Create JSON模块时，可以有效避免HTTP request content的常见错误

经过验证：每个调用方法都经过了细致的测试，确保可用性

快速上手：整理成表格形式，方便查找和使用

**7.2.3 如何使用这些工具？**

详细的使用教程请参见：

网址：<https://xiaobot.net/post/008e7fa9-1cd4-4769-b2c1-4597f846afe6>

通过这份教程，即使是编程小白也能轻松地在Make中调用各种第三方服务的API，大大提升自动化工作流的能力。


## **8\. Make中的微信公众号使用方法：**


### **8.1 微信公众号配置Make教程：**

网址：<https://xiaobot.net/post/2d77f7a6-ce1d-447e-ad63-0df63b660190>


## **9\. 提问及问题解决步骤**

遇到问题时,我们建议按照以下步骤逐步解决:

**自助排查:**

遇到错误时,首先复制错误代码在Google中搜索，仔细阅读搜索结果中的解决方案，大部分常见问题都能通过这种方式快速解决，建议同时查看英文和中文的搜索结果,以获得更全面的参考

**平台搜索:**

如果Google搜索未能解决,请使用小报童平台的搜索功能

根据错误类型或涉及的具体模块进行精准检索

平台已收录并整理了大量实用的解决方案

这些方案都经过实践验证,可以直接参考使用

**寻求帮助:**

当自助排查无法解决时,可以通过以下渠道获取支持:

翔宇交流群

翔宇微信，翔宇会在看到留言后回复

提问建议:

• 优先咨询教程中标准工作流相关的问题

• 提供清晰的问题描述和复现步骤

• 说明已经尝试过的解决方法

• 建议新手阶段先专注于掌握基础工作流的搭建和使用


## 10.AI福利（部分福利可能会随时下线，及时领取）

**Mistral 免费25美元 Firecrawl 免费 100 美元领取方法手把手教程**

<https://xiaobot.net/post/1280c602-7ae5-43c7-82ba-f6bd0a9b22f2>

想免费领取 Mistral AI 的 $25 额度和 Firecrawl 的 $100 额度？翔宇带来详细教程，手把手教你如何兑换这些 AI 福利，助你探索高性能 AI 模型和自动化工具。详细步骤已整理，抓住这波机会，轻松获取免费额度！快来看看吧！

**Make工作流必备搜索 API：EXA 免费 80刀额度领取手把手教程**

想要免费获取 EXA 的 $80 额度，用于 AI 搜索和自动化工作流？翔宇带来详细教程，教你如何轻松领取，让你在 Make 生态中更高效地获取素材。简单几步完成兑换，搜索次数超 16,000 次，长期免费使用！

<https://xiaobot.net/post/9d334e30-590c-4084-879a-41b86aa6823a>

**Make调用xAI Gork手把手教程，充值 5 美元，每月 150 美元免费额度！**

想体验 xAI 的 Grok 模型？现在只需充值 $5，就能每月领取 $150 的 API 调用额度！翔宇带来详细教程，手把手教你如何充值、获取 API Key，并在 Make 里轻松调用 Grok-2 模型。抓住这个机会，开启你的 AI 体验之旅吧！

<https://xiaobot.net/post/0004527c-2a6c-412b-b623-a7e7ede12736>

**每月25美元免费额度的大模型API来了！**

xAI 正式开放 Grok API 公测，限时每月免费领取 $25 额度，体验 128k 上下文、函数调用等强大功能！如果你有预付额度，还能额外叠加，最高每月 $75！翔宇带来详细教程，教你如何快速注册、获取 API 并在 Make 中调用。

<https://xiaobot.net/post/ce9457a5-9ce9-4c7f-8afb-9e00eb0084dd>


## **11.注意事项：**

**11.1 安全充值建议：**

在使用Make相关付费服务时，由于服务稳定性可能存在波动，建议采用"小额多次"的充值策略。这样不仅可以更好地控制成本,也能避免因服务中断带来的资金损失。同时,在进行支付操作时,请务必:

• 使用可靠的支付渠道

• 确保网络环境安全

• 妥善保护个人支付信息

• 警惕各类诈骗风险

**11.2 免责声明：**

本文中分享的所有 Make 自动化工作流、数据获取方法及工具推荐，均以教学目的为出发点，旨在帮助会员学习和掌握 Make 平台的基础操作与应用技巧。这些内容仅作为学习 Make 自动化工具的示例和参考，不构成任何商业建议或指导。

会员在学习过程中，应当将这些工作流视为技术能力培养的练习材料，而非直接可用于商业目的的解决方案。任何将本教程中的工作流用于商业活动、数据采集或可能涉及侵犯第三方权益的行为，均与本教程的初衷相违背。

请会员在实践学习过程中，严格遵守国家相关法律法规、平台使用条款及数据伦理规范，确保自己的学习行为合法合规。