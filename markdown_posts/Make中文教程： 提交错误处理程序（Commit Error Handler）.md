---
title: "Make中文教程： 提交错误处理程序（Commit Error Handler）"
created_at: "2024-08-30T05:30:35.000000Z"
uuid: "76e48380-16a9-4389-bfbd-757f8a367d24"
tags: ['Make基础教程']
---

# Make中文教程： 提交错误处理程序（Commit Error Handler）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于提交错误处理的教程。

  1. **教程简介** ：本教程介绍了如何使用Commit错误处理器在Make.com中处理场景中的错误，通过实例展示如何保存数据库中的更改并停止场景的运行。

  2. **学习目标** ：掌握Commit错误处理器的设置和应用，理解如何在发生错误时停止场景并保存数据更改，提高错误处理效率。

  3. **学习内容** ：教程涵盖如何设置Commit错误处理器、示例场景的创建与测试、处理场景中的错误及确保数据在出错情况下的保存等关键点。

  4. **概念理解** ：Commit错误处理器：用于在发生错误时停止场景的运行，并提交数据库中的更改。适用于支持事务的数据库应用，如MySQL和数据存储。

  5. **任务练习** ：创建一个包含多模块的场景，故意引入一个会导致错误的映射，设置Commit错误处理器，运行场景并验证数据在错误发生后的保存情况。


# **提交错误处理程序**

提交错误处理程序会停止场景运行并提交数据库应用程序中的更改。如果您的场景没有使用支持事务的应用程序,如 MySQL 或数据存储,则提交错误处理程序只会停止该场景。

![](https://static.xiaobot.net/file/2024-11-28/720345/0a49544c3e9e2d1f5e18e53ed3e29540.png)

导致错误的软件包不会进入其他场景流程。Make 不会处理其余软件包。

例如:这个演示场景包含五个模块。该场景对于测试和展示错误处理程序的效果很有用:

  1. JSON - 解析 JSON 数据可提供由三个记录 ID 组成的测试数据数组。

  2. 迭代器将数组拆分为单独的捆绑包。

  3. 数据存储 - 更新记录：更新数据存储中的数据。

  4. 数据存储 - 更新记录：本模块再次更新数据。这次模块的工作方式有所不同。在模块映射中，有一个映射明确地创建了一个错误：

![](https://static.xiaobot.net/file/2024-11-28/720345/ed6ba33f986439ccd1fbe355dc4313f7.png)

映射将 null 值插入必需的密钥字段中,这总是会创建 BundleValidationError 。

有两个数据存储模块执行相同的操作,但其中一个模块失败,这将成为提交和回滚错误处理程序的良好示例。

  5. Slack - 发送消息：发送一条消息到私密测试频道。

这就是示例场景的样子:

![](https://static.xiaobot.net/file/2024-11-28/720345/6d5d3058e8cde205c9773099b3790a29.png)

当我们运行示例场景时,我们将得到 BundleValidationError :

![](https://static.xiaobot.net/file/2024-11-28/720345/f982c45c639274d3f43ac36bb0d7c81c.png)![](https://static.xiaobot.net/file/2024-11-28/720345/f273d6a044e9f8891f6958e9947f64df.png)

如果我们将提交错误处理程序添加到更新记录模块中,提交错误处理程序将在该情况下停止处理捆绑包,并将更改保存到数据库应用程序中的数据。处理程序不会处理剩余的捆绑包。

![](https://static.xiaobot.net/file/2024-11-28/720345/99966b8370479dc932f0a674c7ead5f2.png)

让我们也检查一下数据存储中的数据。

在运行该方案之前,数据存储中包含以下数据:

![](https://static.xiaobot.net/file/2024-11-28/720345/0f4316027c66d42c970f2a11c76e7e03.png)![](https://static.xiaobot.net/file/2024-11-28/720345/0fbe9ef66587f3b2a403bf267b22bba3.png)![](https://static.xiaobot.net/file/2024-11-28/720345/31a95d0f6e33e7d2507d69f9ba7de515.png)

更新记录模块的映射。第一个模块将 ID 列更新为数字 4 ，将 Name 列更新为文本 Test 4。

第二个模块将 ID 列更新为数字 5 ，并将 Name 列更新为文本 Test 5 。

跑完场景后,Make 会更新数据存储中的数据

![](https://static.xiaobot.net/file/2024-11-28/720345/7d7b3db24cf388ef0258ea9d8c99a639.png)

  1. 第一个数据包成功通过了方案流,并在数据存储中更新了第一行数据,两次都如此。第一行包含来自第二个"更新记录"模块的更新: ID = 5, Name = Test 5 。

  2. 第二个包成功到达第一个更新记录模块,但在第二个模块中导致错误。

提交错误处理程序将更新保存在第一个模块中,但防止第二个模块中的更新并停止该方案。第二行只包含第一个模块的更新: ID = 4, Name = Test 4 。

  3. 制作不会更新第三行,因为提交错误处理程序已经停止了该场景运行。第三行的数据保持不变: ID = 3, Name = Test 3 。

关于错误处理策略的更多信息请查看错误处理概述。


## **当发生错误时停止该场景**

使用提交错误处理程序,您可以在发生错误时停止该场景。将场景中的数据库应用程序更改保存下来,而不会继续处理场景流中的其余包。

![](https://static.xiaobot.net/file/2024-11-28/720345/1fbe94ee904ad56d67e3f3f42189a7fb.png)

例如,以下场景在 Data Store 应用程序模块中输出错误:

![](https://static.xiaobot.net/file/2024-11-28/720345/8375387c95f2b9d27daa31705cf04078.png)![](https://static.xiaobot.net/file/2024-11-28/720345/630d3a71b56d15a41d8512a9c7d4dcd9.png)

要停止此方案并保存更改,请按照以下步骤操作:

  1. 右键单击导致错误的模块。在菜单中选择添加错误处理程序。

  2. 选择提交错误处理程序。

  3.  保存您的场景。

您已将提交错误处理程序添加到您的场景中。当数据存储模块发生错误时,该场景将停止并保存在支持事务的模块中进行更改。

![](https://static.xiaobot.net/file/2024-11-28/720345/c56bcadcfc0f713654f0dd9402162ddb.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/17e3285813ed1edcfce3f026aeed10ea.png)