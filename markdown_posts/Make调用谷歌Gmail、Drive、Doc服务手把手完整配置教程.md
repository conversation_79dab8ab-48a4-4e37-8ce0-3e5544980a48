---
title: "Make调用谷歌Gmail、Drive、Doc服务手把手完整配置教程"
created_at: "2025-02-24T07:41:21.000000Z"
uuid: "674c3437-68b3-4fa7-8557-9e0bd4771a9b"
tags: ['API']
---

# Make调用谷歌Gmail、Drive、Doc服务手把手完整配置教程

**标签**: API

谷歌作为全球领先的互联网基础设施提供商,为用户提供了丰富的在线服务,包括Gmail邮件、Google Drive云盘、Google Sheets表格、Google Docs文档等核心应用。然而,这些服务的API接入流程相对复杂,对开发新手来说不太友好。为此,翔宇将重新梳理完整的接入流程,并配以详细的截图说明,帮助新手开发者能够轻松地在Make平台中调用这些服务。本教程将分步骤介绍各项服务的接入方法,推荐按照步骤都配置完成，以发挥 Make 的最大价值。

注意 谷歌的授权分为两种方式：

1.直接登录即可：Google Docs文档

2.配置客户端 id 和密钥：Google Drive云盘和Gmail邮件

具体各个服务的教程如下：


# Google Docs文档：

**来到 Make 的场景编辑页面，搜索 google doc，添加模块**

![](https://static.xiaobot.net/file/2025-02-24/720345/37d6c98c882892b1d622a576fdddf291.png)

点击添加账户

![](https://static.xiaobot.net/file/2025-02-24/720345/7bad0503cfcaf199f03cbb9a8b2c937a.png)

再弹出的页面点击登录

![](https://static.xiaobot.net/file/2025-02-24/720345/a12631fc26df487d7c99321ed66423cb.png)

选择自己的邮件，点击

![](https://static.xiaobot.net/file/2025-02-24/720345/efa76f4a61ed2accd42b82f0771eaa63.png)

点击继续

![](https://static.xiaobot.net/file/2025-02-24/720345/841135942452827f2c4d9227ad8e1fd1.png)

点击允许

![](https://static.xiaobot.net/file/2025-02-24/720345/738283cf0665118faaae3e2a3c0de654.png)

完成谷歌文档的授权，至此可以正常使用谷歌云文档。

![](https://static.xiaobot.net/file/2025-02-24/720345/1c3b94a6cef791fb228e34e5163fcae0.png)


# 谷歌 Gmail

谷歌 Gmail需要配置客户端 id 和客户端密钥，具体操作步骤稍复杂，可参考入下过程。

首先访问谷歌云控制台：<https://console.cloud.google.com/>，点击右上角免费开始使用吧。

在 Make.com 中，您可以使用 Gmail 模块来管理 Gmail 帐户中的电子邮件、标签和附件。以下是如何连接和配置 Gmail 模块的完整步骤。

**1\. 创建并配置 Google Cloud Platform 项目**

如果您使用的是 @gmail 或 @googlemail 结尾的邮箱，按照以下步骤在 Google Cloud Platform 创建并配置项目。

**创建项目**

登录 [Google Cloud Platform](<https://console.cloud.google.com/>)（使用您的 Google 账户）。点击右上角免费开始使用吧。

![](https://static.xiaobot.net/file/2025-02-24/720345/5e9528e874e88d43ae81f3e1c375364e.png)

**跳转到免费使用 Google Cloud 页面，选择美国点击同意并继续。**

![](https://static.xiaobot.net/file/2025-02-24/720345/700bf80fd3e92b1b024e2f3ba329d170.png)

**来到验证身份页面，点击加号添加地址，具体地址使用美国地址生成器进行生成。**

具体网址：<https://www.meiguodizhi.com/>

![](https://static.xiaobot.net/file/2025-02-24/720345/a46e23def95638e1f41f12323857f645.png)

**将如下美国地址输入到正确的位置。**

![](https://static.xiaobot.net/file/2025-02-24/720345/b324d7d064b8c3db19e8f9785a9a8175.png)

![](https://static.xiaobot.net/file/2025-02-24/720345/f37e8f24c63ffbd05a41301e321351bb.png)

**填写完成之后点击添加付款方式，选择添加信用卡或借记卡**

![](https://static.xiaobot.net/file/2025-02-24/720345/eaadb5beccc7bc78b7f5f5eceed6b26e.png)![](https://static.xiaobot.net/file/2025-02-24/720345/b044b1b033d2a23878f9834268c42269.png)![](https://static.xiaobot.net/file/2025-02-24/720345/6c6d87b4c34fb066b8bb4d70b860d3a9.png)

**注意信用卡不可使用虚拟卡，可以使用国内招商银行的双币信用卡，支持美元扣款的信用卡。**

成功与否与网络环境相关：

1.确保美国网络环境。

2.IP 地址干净，避免使用免费环境

3.开启全局选项，同时使用无痕标签页进行申请。

其他问题如果还是不成功可自行搜索网络教程寻求解决方案。

**通过之后，自行选择调查问卷，点击完成进入谷歌云控制台。**

![](https://static.xiaobot.net/file/2025-02-24/720345/a0d61b7af49493912aadcb25c7df5207.png)

**在谷歌云控制台点击左上角，弹出的页面点击新建项目。**

![](https://static.xiaobot.net/file/2025-02-24/720345/98681a0e994654dcf814c69d653c034a.png)![](https://static.xiaobot.net/file/2025-02-24/720345/37f9526f83c1c17c7859eb6cc92c02c0.png)

输出英文字母的项目名称，等待创建项目完成

![](https://static.xiaobot.net/file/2025-02-24/720345/ab2d1ed8b2ae859f884b2eb2281ceb03.png)

**创建完成点击右上角选择项目**

![](https://static.xiaobot.net/file/2025-02-24/720345/ced157da89104d88af17e18146ae4ace.png)

**点击 API 和服务，而后点击启用 API 和服务**

![](https://static.xiaobot.net/file/2025-02-24/720345/37c0e6004bf93a6db93e8a343e22c2e6.png)![](https://static.xiaobot.net/file/2025-02-24/720345/44d38a81860527f67d01d43eef377ee8.png)

输入 Gmail 搜索定位

![](https://static.xiaobot.net/file/2025-02-24/720345/90484b0dccea82b95e0e082001513206.png)

点击 Gmail API进入启用页面

![](https://static.xiaobot.net/file/2025-02-24/720345/8c5ddcf64830574a9e069de9884ec5f3.png)

点击启用

![](https://static.xiaobot.net/file/2025-02-24/720345/75eb3681642b13f502f644c528410059.png)

点击OAuth 权限请求页面，注意谷歌最近更新了页面，所以如果提示是否选择使用新页面，请允许，该教程针对新页面进行教学

![](https://static.xiaobot.net/file/2025-02-24/720345/d8ea4cfe3ff78b154e80c2d63a62822d.png)

**点击开始**

![](https://static.xiaobot.net/file/2025-02-24/720345/1bb710a5826fd6dcfd833f1dfb43c9d3.png)

根据下面一步步填写内容，应用名称，用户支持电子邮件，点击下一步

![](https://static.xiaobot.net/file/2025-02-24/720345/6b5d43cdfd21260fdb4e9b43175d449b.png)

**选择外部受众群体，点击下一步**

![](https://static.xiaobot.net/file/2025-02-24/720345/ede9ed7433bb654dab046ac989e1fb99.png)

**输入自己要管理的电子邮件，该过程使用的所有电子邮件都选择同一个**

![](https://static.xiaobot.net/file/2025-02-24/720345/ae468c618cc8cecb28deb67c21f13930.png)

**点击我同意完成**

![](https://static.xiaobot.net/file/2025-02-24/720345/e44dddce95fc4b2cc12d3e8d2f77b7b0.png)

**完成后进入主页面，点击目标对象，点击添加测试用户**

![](https://static.xiaobot.net/file/2025-02-24/720345/139c985164d95927b3a127565542a995.png)

**输入一样的邮件地址，点击保存**

![](https://static.xiaobot.net/file/2025-02-24/720345/ddc0d4d747a78c10eb9c1a41a0237b17.png)

完成后点击数据访问，点击添加或移除范围

![](https://static.xiaobot.net/file/2025-02-24/720345/47dde9c554b96787816b1a5624f6ecde.png)

输入<https://mail.google.com/>搜索勾选后点击更新

![](https://static.xiaobot.net/file/2025-02-24/720345/42af3ac1cda7957bc0d41dfb165fe017.png)

输入<https://www.googleapis.com/auth/userinfo.email>搜索勾选后更新

![](https://static.xiaobot.net/file/2025-02-24/720345/217b4021c39b2fb28cad9895d548e2d0.png)

**回到概览页面，点击创建客户端**

![](https://static.xiaobot.net/file/2025-02-24/720345/249356c0cb44dc608dc7206af44e0d44.png)

**如下图填写和选择，其中 URI 为**

<https://www.integromat.com/oauth/cb/google-restricted>

![](https://static.xiaobot.net/file/2025-02-24/720345/4b74d0efb1fb58c81c2a16ccd8905f3e.png)

**创建完成后来到客户端复制客户端 id**

![](https://static.xiaobot.net/file/2025-02-24/720345/f1d2213205d65cb37ae92b3118c1cbd1.png)

**点击名称出，进入内部，复制密钥**

![](https://static.xiaobot.net/file/2025-02-24/720345/c85b51e1177ab7febd163767c32b3198.png)

**2\. Make 中配置 Gmail 过程：**

**来到 Make 场景编辑页面搜索 Gmail，添加模块**

![](https://static.xiaobot.net/file/2025-02-24/720345/ef352d328f7658f06ed65fa723dbc41e.png)

**点击 add 添加账号**

![](https://static.xiaobot.net/file/2025-02-24/720345/0c6bff53d47e984132fbd3b88a135af1.png)

**如图添加 id 和密钥，注意需要打开高级选项才能看到，而后点击登入**

![](https://static.xiaobot.net/file/2025-02-24/720345/57604a8252cf00f4a1ae263aaf839bc2.png)

选择自己的邮件

![](https://static.xiaobot.net/file/2025-02-24/720345/f15cfcd1a2f5ac6544476686ffa0ba5b.png)

**注意一定要选择继续**

![](https://static.xiaobot.net/file/2025-02-24/720345/433454032ae655d1fe91ed7258c1b225.png)

**点击继续**

![](https://static.xiaobot.net/file/2025-02-24/720345/78ad0f5c375e1932113c0feff184bfb4.png)

**确保勾选**

![](https://static.xiaobot.net/file/2025-02-24/720345/22b74a8bffc8f5c9e0f7e3b41567f2c9.png)

通过完成上述步骤，您可以顺利地将 Gmail 连接到 Make，并使用各种 Gmail 模块构建自动化流程。希望今天翔宇撰写的手把手教程，能够帮助到各位希望使用谷歌邮件服务的用户。


# 谷歌云盘：

完成了之后，我们在此基础上来配置谷歌云盘的过程。

**在页面顶部的搜索框中输入 google drive 点击第一个**

![](https://static.xiaobot.net/file/2025-02-24/720345/7b31075d2657ed271fc91bc3eeb0fbdd.png)

**点击启用**

![](https://static.xiaobot.net/file/2025-02-24/720345/2e441922729f0f2bbc0fd58f93a229e6.png)

来到项目页面，点击客户端，复制客户端 id 和密钥

![](https://static.xiaobot.net/file/2025-02-24/720345/9d188f8d58d7cda82aeede644569585f.png)

点击项目名称处进入复制密钥

![](https://static.xiaobot.net/file/2025-02-24/720345/0e267af821e752e39a9ce4b101046a3d.png)

来到 Make 场景编辑页面，点有添加账户

![](https://static.xiaobot.net/file/2025-02-24/720345/b88cc21c68a9edb6d9b813d74dfd0109.png)

将 id 和密钥粘贴

![](https://static.xiaobot.net/file/2025-02-24/720345/2bfb0e4531652a835f0b67772985adf9.png)

出现的页面选择自己的有限，然后点击继续

![](https://static.xiaobot.net/file/2025-02-24/720345/588bdbab717136deb068da09b25741cb.png)

点击继续

![](https://static.xiaobot.net/file/2025-02-24/720345/b5012563a8daa4aa255dc2d8de0560d0.png)

通过完成上述步骤，您可以顺利地将 Gmail 连接到 Make，并使用各种 Gmail 模块构建自动化流程。希望今天翔宇撰写的手把手教程，能够帮助到各位希望使用谷歌邮件服务的用户。


# 谷歌文档