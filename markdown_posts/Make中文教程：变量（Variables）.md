---
title: "Make中文教程：变量（Variables）"
created_at: "2024-09-19T09:47:45.000000Z"
uuid: "38103885-d879-4e38-97e7-37d915b268c4"
tags: ['Make基础教程']
---

# Make中文教程：变量（Variables）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于变量的教程。

**教程简介** ：本教程介绍了在Make.com中使用“变量”的概念，包括系统变量和自定义变量，以及如何在场景中应用它们来管理和存储数据。教程还涵盖了变量的创建、编辑和删除等操作。

**学习目标** ：学习如何在Make.com中创建和使用系统变量和自定义变量，以便在自动化工作流中高效管理和使用数据。同时，掌握变量的基本操作，提高工作流设计的灵活性和准确性。

**学习内容** ：

  - 变量概念及其作用

  - 系统变量及其使用场景

  - 自定义变量的创建、编辑和删除

  - 不同用户的变量操作权限

  - 在场景中应用变量的实际示例

**概念理解** ：变量是数据存储的容器，可用于Make.com场景中，分为系统变量和自定义变量。系统变量是Make.com预设的，适用于所有用户。自定义变量是用户定义的，适用于付费计划，可以根据需要创建和管理。

**任务练习** ：

  1. 创建一个新场景，添加系统变量并将其映射到模块的输入字段中。

  2. 创建一个自定义变量，在场景中使用工具模块设置该变量，然后通过邮件模块通知变量值变化。

  3. 编辑一个自定义变量的值，观察更新对场景的影响。

  4. 删除一个使用中的自定义变量，理解其在场景中的影响。


# 变量


## **什么是变量？**

变量是存储数据的容器。你可以把变量想象成一个贴有独特标签的"数据盒子"。

想象你正在搬家,需要把家里的东西打包到不同的箱子里。你可能需要一个箱子装衣服,一个装书本,还有一个装餐具。打包时,给每个箱子贴上标签是个好主意 - 这样你就知道每个特定箱子里装的是什么。

变量就像这样的盒子 - 它就是一种存储数据的方式。

![](https://static.xiaobot.net/file/2024-11-19/720345/4e916d7b7edb79e1aa339fcfe5d48a3b.png)

变量存储数据就像盒子存储物品

与实物盒子不同,一个变量一次只能存储一个内容。变量有固定的名称但其值可以改变。你可以在场景中引用这个名称,该名称会被变量当前的值所替代。

在使用Make平台时,你可能需要保存某些信息并给这些信息贴上标签,以便之后可以检索这些信息。用户名、星期几或者电商剩余库存 - 这些都是可以存储在变量中的数据示例。

变量是可重复使用的元素,可以映射到任何模块的输入字段中。


## **变量类型**

Make平台提供两种类型的变量:

  - 系统变量

  - 自定义变量

系统变量和自定义变量都可以在场景中使用。系统变量对所有Make用户开放。

自定义变量仅适用于Pro、Teams和Enterprise版本计划。详见[Make定价](<https://www.make.com/en/pricing>)。

你可以为你的组织和团队定义自定义变量。


## **系统变量**


### **观看视频教程**

系统变量是Make平台提供的默认选项。你不能修改或删除系统变量。你可以在以下位置使用系统变量:

  - 带有输入字段的模块

  - 场景编辑器中的过滤器

  - 模板

系统变量对所有Make用户和所有定价计划开放。

系统变量包含以下信息:

  - 场景本身的信息\(场景ID、场景名称、场景URL\)

  - 特定场景执行的信息\(执行ID、消耗的操作数、消耗的数据量、执行开始日期/时间\)

  - 场景所在团队的信息\(团队ID、团队名称\)

  - 场景所在组织的信息\(组织ID、组织名称、剩余操作数、剩余数据量、域名\)

通过系统变量,你可以看到更多关于你的场景、当前执行、团队和组织的信息。变量在错误处理和场景活动跟踪方面特别有用。例如,当场景中出现错误时,你可以使用场景ID或组织名称变量来精确定位错误发生的具体场景执行和组织。

系统变量的其他使用场景示例:

  - 创建一个包含创建该消息的场景链接的Slack消息、邮件或支持工单

  - 设置邮件通知,在剩余操作数达到特定数值时通知你

你还可以重复使用变量,并将它们映射到任何模块的输入字段中。

下面的截图显示了在包1中使用系统变量作为模块输出获取的组织、团队和场景信息。你可以在后续模块中使用这个输出。

![](https://static.xiaobot.net/file/2024-11-19/720345/ca38cb8d80770a44a9e02702af58193b.png)

示例:使用系统变量的包1输出

你可以在映射对话框的Variables标签下找到系统变量。

![](https://static.xiaobot.net/file/2024-11-19/720345/c9f32814f299695bc4d30860e6054df2.png)


## **自定义变量**


### **观看视频教程**

自定义变量仅适用于Pro、Teams和Enterprise定价计划。自定义变量在[旧版计划](<https://www.make.com/en/help/general/pricing-parameters.html#legacy-plan>)中不可用。更多信息请参见[Make定价](<https://www.make.com/en/pricing>)。

你可以创建、编辑和删除自定义变量。

当你需要多次重复使用相同信息时,自定义变量特别有用。与其每次都从头开始重写,不如直接使用自定义变量。

使用自定义变量的一些场景:

  - 发送带有统一邮件签名的邮件

  - 设置在停止联系潜在客户前的外联尝试次数

  - 电子邮件地址

  - 公司名称或地址

使用自定义变量的好处包括:

  - **节省时间**  \- 当关键信息发生变化时,你不必在许多不同的场景中手动更改。

  - **准确性**  \- 你不必每次都仔细检查信息是否因人为错误而不正确。

  - **标准化**  – 更多人可以使用相同的变量,使你的业务更加一致。

自定义变量使场景更易于管理和维护。

你可以在组织级别和团队级别定义自定义变量。对于每个变量,你需要确定以下内容:

  - **名称**\(永久变量名\)

  - **数据类型**\(文本、数字、布尔值或日期\)

  - **值**\(变量的实际值\)


### **创建自定义变量**

你可以在组织级别和团队级别创建自定义变量。

  1. 决定要创建组织变量还是团队变量。

     - 对于组织变量，进入组织仪表板并点击 **Variables > Add organization variable** 。

     - 对于团队变量，进入团队仪表板并点击 **Variables > Add team variable** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/2405090fab5cf8632ebf503e5013dbe5.png)

  2. 填写信息。 名称

     - 此字段必填，保存后无法更改。

     - 名称是变量的标识符。

     - 名称只能包含字母、数字或符号 $ 和 \_。

     - 名称不能以数字开头。

     - 名称最多包含128个字符。

数据类型

从下拉菜单中选择变量数据类型

     - 数字\(Number\)

     - 文本\(Text\)

     - 布尔值\(Boolean\)

     - 日期\(Date\)

值\(Value\)

     - 输入变量的值。

     - 可以使用字母、数字、空格和特殊字符。

     - 值不能为空。


### **警告**

变量不适合存储机密信息！不要用它们存储任何敏感数据！

变量值以明文形式存储，未加密。不要使用变量存储密码或任何其他敏感数据。请注意，其他团队成员和组织管理员可以访问变量。

![](https://static.xiaobot.net/file/2024-11-19/720345/2bc6cce1f35030ec41dc6988275e35f4.png)

  3. 点击**保存** 以保存你的自定义变量。

你的新变量将出现在变量列表中。

要预览变量值，将鼠标悬停在个别值字段上。

![](https://static.xiaobot.net/file/2024-11-19/720345/87018e0d8d2f2377f6d9ce040238cc1a.png)


### **编辑自定义变量**


### **警告**

如果你的其他团队成员或组织管理员在其场景中使用相同的变量，更改也会影响到他们。

  1. 转到要编辑的变量。

     - 对于组织变量，进入组织仪表板并点击 **Variables** 。

     - 对于团队变量，进入团队仪表板并点击 **Variables** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/14a0668ccb3870e970958e6a6b633bc4.png)

  2. 在自定义变量列表中找到要编辑的变量，点击旁边的**编辑** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/a5fba4c9210136f88ebe1738e54276a8.png)

  3. 根据需要编辑变量。你可以编辑变量数据类型和值。

![](https://static.xiaobot.net/file/2024-11-19/720345/b28e35dcde3405f12d3485b165091bf4.png)

  4. 点击**保存** 以保存更改。

你的更改已保存，更新后的变量将出现在变量列表中。将鼠标悬停在个别值字段上可预览变量值。

更改会自动更新到已经使用该变量的场景中。


### **删除自定义变量**


### **警告**

删除已在一个或多个场景中使用的变量可能会影响在其场景中使用相同变量的其他用户。删除变量后，该变量在所有使用它的场景中变为非活动状态，并停止返回预期值。

  1. 决定要编辑组织变量还是团队变量。

     - 对于组织变量，进入组织仪表板并点击 **Variables** 。

     - 对于团队变量，进入团队仪表板并点击 **Variables** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/69a6cf66a5b854cd74e89f68c900e158.png)

  2. 在自定义变量列表中找到要删除的变量，点击旁边的**删除** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/f4c0619292f7d006f6409483248bd5ed.png)

如果要删除的变量已在场景中使用，会弹出一个模态框要求你确认删除变量。点击**确定** 以删除变量。

![](https://static.xiaobot.net/file/2024-11-19/720345/abd4b00a65132db9265ff91468d8d049.png)

变量已删除，从自定义变量列表中消失。


### **检查自定义变量历史记录**

你可以查看谁更改了自定义变量，何时更改，以及更改内容。

  1. 转到要检查其历史记录的自定义变量。

     - 对于组织变量，进入组织仪表板并点击 **Variables** 。

     - 对于团队变量，进入团队仪表板并点击 **Variables** 。

  2. 点击**编辑** 旁边的下拉菜单。选择**显示历史记录** 。

![](https://static.xiaobot.net/file/2024-11-19/720345/1001754e48af196175bb6862b82cdfb9.png)

打开一个新窗口，你可以在其中查看变量历史记录。最近的更改显示在顶部。

![](https://static.xiaobot.net/file/2024-11-19/720345/b7ed7be8fdc071f0fdc1d2108e69b4c6.png)


## **自定义变量的用户权限**


### **组织变量**

表1. 组织自定义变量用户权限

![](https://static.xiaobot.net/file/2024-11-19/720345/73abc1a25e41453de8bd62f5c69e6fb6.png)


### **团队变量**

表2. 团队自定义变量用户权限

![](https://static.xiaobot.net/file/2024-11-19/720345/3654dba894cfad11f9ba017e0b444301.png)


# **Make平台变量实战**


## **变量的使用范围和权限**

在Make平台中，变量的使用非常灵活，就像是给你的自动化流程配备了一个智能记事本：

  - 系统变量：所有用户都可以使用，相当于平台内置的"公共笔记"

  - 自定义变量：需要专业版及以上会员才能使用，就像是你的"私人笔记本"


## **实战案例：操作次数监控预警系统**

今天我们要学习一个非常实用的案例：如何在操作次数即将用完时收到提醒？这就像是给你的账户设置了一个"余额不足预警"。


### **第一步：创建提醒器（Set Variable模块）**

  1. 进入场景创建界面：**Scenarios > Create a new scenario**

  2. 添加工具模块：**Tools > Set variable**

  3. 设置三个关键信息：

     - 变量名称："Operations left"（剩余操作数）

     - 生命周期："one execution"（单次执行）

     - 值："Ops limit"（操作限制）

> 小贴士：这就像是设置一个计数器，随时记录还剩多少次操作机会

![](https://static.xiaobot.net/file/2024-11-19/720345/9456763003d0c2edc7475c0a915070fb.png)


### **第二步：配置邮件通知（Email模块）**

  1. 添加邮件发送模块：**Email > Send an email**

  2. 配置邮箱连接

  3. 关键设置：

     - 收件人：可添加多个（点击 **Add a recipient** ）

     - 邮件主题：建议使用醒目的提示语，如"警告：操作次数即将用完"

     - 内容格式：可选HTML或纯文本

![](https://static.xiaobot.net/file/2024-11-19/720345/7b702037e35639a3e31a0fb6861fa6b0.png)

  4. 编写邮件内容：

     - 从变量选项卡中选择系统变量

     - 可以插入以下信息：

       - 剩余操作次数

       - 组织名称

       - 组织ID

![](https://static.xiaobot.net/file/2024-11-19/720345/cbe128c9817263843e70251e23eaf3bd.png)

> 专业提示：使用HTML格式可以让邮件更美观，但记得要用HTML标签来排版


### **第三步：设置触发条件（Filter）**

这一步最关键，决定了在什么时候发出预警：

  1. 点击模块连接线上的过滤器图标

![](https://static.xiaobot.net/file/2024-11-19/720345/7cc4183829ad5f9ba1276bd1ce3229bc.png)

  2. 设置触发条件：

     - 选择"Operations left"变量

     - 设置具体的预警阈值

![](https://static.xiaobot.net/file/2024-11-19/720345/15ede7abf296ef6df426de12bdeef853.png)

> 实战建议：建议设置一个合理的预警值，比如剩余1000次操作时提醒，这样能给你足够的处理时间

完整设置示例：

![](https://static.xiaobot.net/file/2024-11-19/720345/eb14857f4fdefc862310f0b64565d0d7.png)


### **最终效果展示**

成功设置后，你的自动化流程应该是这样的：

![](https://static.xiaobot.net/file/2024-11-19/720345/40877131abb5be213614cbc452f4862b.png)


### **测试和使用**

  1. 点击"Run once"进行测试

  2. 检查是否收到预警邮件

  3. 根据实际需求调整预警阈值

![](https://static.xiaobot.net/file/2024-11-19/720345/7eed7ad30b183ff51484ee23dc66c349.png)


## **使用建议**

  1. **阈值设置** ：根据你的使用频率设置合理的预警值

  2. **邮件内容** ：尽可能包含详细信息，便于快速判断和处理

  3. **定期检查** ：建议定期测试确保预警系统正常运行


## **常见问题解答**

  1. 没收到邮件？检查邮箱设置和垃圾邮件文件夹

  2. 预警太频繁？适当调高阈值

  3. 需要多人收到提醒？可以添加多个收件人

这个自动化预警系统设置好后，就像给你的Make账户配了个贴心的管家，会在适当的时候提醒你注意额度使用情况！


# **实战案例：打造专业的自动邮件回复系统**

想象一下，你每天要处理大量的客服邮件，而且每封回复都需要保持统一的格式和签名。这就像需要一个永不疲倦的助手，24小时帮你处理邮件！今天我们就来创建这样一个智能助手。

> ⚠️ 温馨提示：此功能仅适用于Pro、Teams和Enterprise版本的用户。


## **第一步：准备你的"签名印章"（创建自定义变量）**

就像准备一个专业的印章，我们首先要设置统一的回复模板和签名。


### **创建变量的选择**

  1. 组织级变量：适合整个公司统一使用

  2. 团队级变量：适合特定团队使用（比如客服团队）

在我们的例子中，选择团队级变量更合适：

![](https://static.xiaobot.net/file/2024-11-19/720345/2dbdccff222c887d3a8c820a1a674142.png)


### **设置签名模板**

填写三要素：

  - 变量名称（你的"印章"名字）

  - 数据类型（文本格式）

  - 具体内容（签名内容）

![](https://static.xiaobot.net/file/2024-11-19/720345/8a244376feb4c51302feae4138d8bba8.png)![](https://static.xiaobot.net/file/2024-11-19/720345/6b082b4ca3f1b9e9c2e46f686ba2eac1.png)

> 小贴士：保存后，可以随时将鼠标悬停在值字段上预览内容


## **第二步：设置邮件监控（邮件监听模块）**


### **创建"邮件接收员"**

  1. 创建新场景：**Scenarios > Create a new scenario**

  2. 添加邮件监控：**Email > Watch email**

  3. 连接你的邮箱账号

  4. 选择需要监控的文件夹

  5. 设置监控条件（可以理解为设置"过滤规则"）

![](https://static.xiaobot.net/file/2024-11-19/720345/6e3aff4c3e6ea87e07da235ec6be2bce.png)


### **设置起始时间**

选择从什么时候开始监控邮件：

![](https://static.xiaobot.net/file/2024-11-19/720345/faee9903abff2c1032a07b583110ba0d.png)


## **第三步：配置自动回复（发送邮件模块）**


### **基础设置**

  1. 添加发送模块：**Email > Send an email**

  2. 连接邮箱

  3. 设置是否保存已发送邮件

  4. 添加收件人（支持多个收件人）

  5. 设置邮件主题（可以引用收到的邮件主题）

![](https://static.xiaobot.net/file/2024-11-19/720345/7000c3c7be148d9bf69db0f5375a0044.png)


### **内容配置**

  1. 选择内容格式（HTML或纯文本）

  2. 插入自定义变量（我们之前设置的签名和回复模板）

![](https://static.xiaobot.net/file/2024-11-19/720345/ad4a5889fd64b5a758c65afb65c69bf2.png)

> 专业提示：使用 SupportEmailSignature 和 SupportAutomaticResponse 变量确保回复的一致性


## **系统测试**


### **测试步骤**

  1. 点击 **Run once**  进行测试

  2. 给自己发送一封测试邮件

  3. 检查收件箱是否收到预期的自动回复

测试成功后的效果应该如下：

![](https://static.xiaobot.net/file/2024-11-19/720345/88dd9a46329c3e3a4c96700ebe7ebe4f.png)


## **使用建议**


### **1\. 回复模板设计**

  - 保持专业性和友好度的平衡

  - 包含必要的问题解决时间预期

  - 添加其他联系方式（如在线客服）


### **2\. 监控规则设置**

  - 合理设置邮件过滤条件

  - 避免误触发自动回复

  - 定期检查规则是否需要更新


### **3\. 维护建议**

  - 定期更新签名信息

  - 根据客户反馈调整回复模板

  - 监控自动回复的效果


## **常见问题解答**

  1. Q: 邮件没有自动回复？ A: 检查邮件监控条件是否正确设置

  2. Q: 想要针对不同情况回复不同内容？ A: 可以设置多个回复模板，通过条件判断使用

  3. Q: 担心重复发送自动回复？ A: 可以添加判断条件，避免对同一主题重复回复

这样，你就拥有了一个24小时不间断工作的专业邮件助理！它会帮你维持统一的专业形象，同时大大提升工作效率。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！

![](https://static.xiaobot.net/file/2024-11-19/720345/c62a9b45441261ae9e3b1ceae196eb3c.png)