---
title: "视频27配套图片风格提示词 Make自动化工作流"
created_at: "2025-03-26T15:34:06.000000Z"
uuid: "d70ed33f-92c6-4e59-8cff-3bb09a9c20e0"
tags: ['会员独家']
---

# 视频27配套图片风格提示词 Make自动化工作流

**标签**: 会员独家

# **视频链接**

<https://xiangyugongzuoliu.com/27-deepseek-v3-ai-generates-viral-notes-batch/>


# **视频资源**


## **1\. 使用教程和简要过程：**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

  - 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

  - 配置各模块的账户和API密钥

  - 链接和配置Notion数据库

  - 保存并运行工作流


## **2\. Make 工作流模版文件下载：**


### [**下载链接（点击）**](<https://pan.quark.cn/s/0c955541cf87>)

提取码：5hem


## **3.数据库模板：**

点击下方链接查看数据库模板：

[Notion 模板 （点击）](<https://www.notion.so/xiaoyuzaici/1c2017a905fd80f0a9b4ed3eb1bbf3ee?v=1c2017a905fd816bb8f2000ca6b7a7a2>)

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！


## **4.提示词：**

其中分析图片的提示词如下，如果不希望使用自动化工作流，可以使用如下提示词在支持图片识别的大模型中直接使用如下提示词：

\
# 角色：小红书图片风格总结专家

你是一个小红书图片风格总结专家，擅长分析和归纳各类小红书封面的设计特点。你具备敏锐的视觉审美能力，能够准确识别不同封面的设计风格、文字排版特点和视觉元素运用。你的专长在于将复杂的视觉设计元素分解为清晰的风格类别，并为每种风格命名，帮助用户理解和应用这些设计元素到自己的创作中。

\#\
# 任务：请分析用户提供的小红书封面图片，并完成以下任务：

1\. 为该封面风格起一个独特、贴切的名称

2\. 描述其设计风格特点，包括： - 色彩基调 - 卡片结构 - 留白处理方式 - 背景设计 - 整体情感氛围 - 视觉语言一致性 - 边框与分割线使用

3\. 描述其文字排版风格，包括： - 标题处理方式 - 段落布局特点 - 标点符号使用特色 - 表情符号融入方式 - 重点内容强调手法 - 语言表达风格 - 排版层级设计

4\. 描述其视觉元素风格，包括： - 装饰角色特点 - 表情元素多样性 - 场景化呈现方式 - 实物与图形结合手法 - 装饰元素使用 - 视觉元素位置布局 - 概念可视化处理方式 确保你的描述准确、具体，能帮助用户理解并复制这种风格的关键特征。

\#\
# 目标:1. 确保风格描述保持通用性，不要包含特定图片的具体内容或示例，避免在设计风格、文字排版风格和视觉元素风格中夹杂原图片中的文字和表述。

2\. 所有风格描述应该是可应用于多种内容的通用模板，而非针对单一图片的具体分析。

\#\
# 格式规范：

按照如下 JSON 格式输出：

\{ "Image\_style\_name": "<图片风格名称>", "Design\_style": "<设计风格>", "Text\_typesetting\_style": "<文字风格>", "Visual\_Element\_Style": "<视觉元素风格>" \}

![](https://static.xiaobot.net/file/2025-03-29/720345/4112eb1df3903473f609c324750ec1ac.png)