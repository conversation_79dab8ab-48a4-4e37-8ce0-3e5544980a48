---
title: "Make中文教程：场景执行流程（Scenario execution flow）"
created_at: "2024-09-21T15:00:06.000000Z"
uuid: "4e0d02d3-31a6-43c4-8f25-24a16988ceba"
tags: ['Make基础教程']
---

# Make中文教程：场景执行流程（Scenario execution flow）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于场景执行流程的教程。

**教程简介** ：本教程介绍Make.com中场景的执行流程，包括数据在场景中的流经方式，如何找到已处理数据的信息，以及如何读取这些信息。此外，还提供了将Facebook图片下载到Dropbox的示例。

**学习目标** ：了解Make.com中场景的执行机制，掌握数据包在场景中的传递与处理过程，学会读取已处理数据的信息，并能够处理执行过程中可能遇到的错误。

**学习内容** ：1. 场景的设置和激活。2. 数据包在模块间的传递。3. 读取和理解已处理数据包信息。4. 示例操作：将Facebook照片上传至Dropbox。5. 处理场景执行中的错误。

**概念理解** ：场景：Make.com工作流，包括多个模块。模块：执行特定任务的组件。数据包：模块之间传递的数据单元。事务处理：数据包在模块中处理的四个步骤（初始化、操作、提交/回滚及完成）。

**任务练习** ：创建一个简单场景，连接Twitter和Google Drive，监视Twitter上的新推文，并将推文内容保存到Google Drive文档中。完整流程包括监视数据、新推文处理和文件保存。


# **场景执行流程**

在本节中，您将了解场景如何执行以及数据如何在场景中流动。它还将向您展示在哪里可以找到有关已处理数据的信息以及如何阅读它。

一旦场景设置正确并激活，它将根据其定义的时间表执行。

场景从第一个模块响应其设置要监视的事件开始。如果它返回任何捆绑包（数据），则将捆绑包传递给下一个模块，场景继续进行。如果它不返回任何模块，则场景不会继续，并在第一个模块之后结束。在第一个模块之后返回捆绑包的情况下，捆绑包将逐个通过每个后续模块。如果在所有模块中正确处理捆绑包，则将场景标记为成功。


## **示例：将 Facebook 连接到 Dropbox**

下面的示例显示了如何在一个场景中连接三个模块。它解释了如何从 Facebook 下载照片，将它们转换为另一种格式，然后将它们发送到选定的 Dropbox 文件夹。

![](https://static.xiaobot.net/file/2024-11-24/720345/60e5b346f0f4f1456d8c7d4f746dc8e1.png)

当场景开始时，第一步是观察捆绑包。在这种情况下，是观察 Facebook 上的照片。如果没有返回捆绑包（一张照片），则场景的处理不会继续，而是在第一个模块之后结束。

如果一个捆绑包被退回，那么捆绑包将通过剩余的场景。捆绑包首先通过“查看照片”模块接收，然后通过“转换格式”模块进行图像应用程序，最后通过“上传文件”模块到达其最终目的地，即 Dropbox 文件夹。

值得注意的是，如果 Facebook 返回多个捆绑包，例如两个捆绑包，后一个捆绑包的处理将在第一个捆绑包被转换并上传到 Dropbox 之后才会开始。


## **已处理捆绑包的信息**

![](https://static.xiaobot.net/file/2024-11-24/720345/cd954640f3d02b7c44f8a97f5ef8d029.png)

对于每个模块，在继续到下一个模块或到达最终目的地之前，捆绑包会经过 4 个步骤的过程。这 4 个步骤是初始化、操作、提交/回滚和完成。这被称为事务处理，有助于解释模块中数据是如何被处理的。

一旦场景运行完成，每个模块都会显示一个图标，显示执行的操作数量。单击此图标将显示有关已处理捆绑包的详细信息，格式如上所述。您可以查看使用了哪些模块设置以及哪些捆绑包由哪个模块返回。

旁边的图片说明了上述场景中使用的最后一个模块的处理过程，即 Dropbox 模块，上传文件。

模块收到了以下输入信息：

  1.  转换后的图像

  2. 选择要上传图像的文件夹

  3. Facebook 图像的原始名称

处理后，模块返回了以下输出信息：

  - 由 Dropbox 分配的图像 ID

  - 在 Dropbox 中上传文件的完整路径

以上信息是针对每个捆绑包单独捕获的，如图中下拉框操作 1 和操作 2 所示。


## **执行场景时发生错误**

在执行场景时可能会出现错误。例如，如果您删除了在模块设置中设置为目标文件夹的 Dropbox 文件夹，则场景将以错误消息终止。有关更多信息以及如何处理错误的信息，请参阅错误处理帮助指南。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/2ac05d08371aa9129a053ad3d051b6ec.png)