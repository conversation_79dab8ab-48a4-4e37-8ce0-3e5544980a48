---
title: "视频 31 教程：电脑 Cherry Studio 设置 n8n MCP 服务器手把手教程"
created_at: "2025-05-04T07:56:37.000000Z"
uuid: "6458ee4a-08c1-4093-8e5b-a163b1c8e491"
tags: ['n8n 基础教程']
---

# 视频 31 教程：电脑 Cherry Studio 设置 n8n MCP 服务器手把手教程

**标签**: n8n 基础教程

![](https://static.xiaobot.net/file/2025-05-04/720345/59296373fad882df4437c764767ad1d5.png)

大家好，我是翔宇，专注于自动化和 AI 。今天要分享的是如何利用 Cherry Studio 设置 MCP 服务器，实现方便快捷的大模型调用。通过本文的详细介绍，即便你是第一次接触 MCP 和 Cherry Studio，也能轻松掌握操作方法，快速落地自己的 AI 自动化服务。


### **什么是 MCP 服务器？**

MCP（Model Communication Protocol）即模型通信协议，是专门为大语言模型与外部服务交互设计的标准接口协议。简单来说，MCP 就像是大语言模型与外部软件之间沟通的桥梁。

以往，我们使用大语言模型时往往只能单一调用，无法灵活扩展。现在有了 MCP，只需一次配置，就能让大语言模型轻松与任何外部工作流、API 或应用进行交互，实现复杂业务场景的自动化执行。

例如，你想要搭建一个 AI 图片自动生成流程，通过 MCP 可以非常方便地将你的大模型与 n8n 工作流连接，让模型直接从 n8n 中获取参数并返回生成结果。


### **什么是 Cherry Studio？**

Cherry Studio 是一款极简、强大的大模型交互平台，它允许用户无需编程，通过简单的图形化界面就能配置并调用各种 AI 模型服务。

它内置了 MCP 支持，这意味着你不仅可以快速调用各种 AI 服务，还能便捷地集成自己的 MCP 服务器，实现更高级的自动化流程。

Cherry Studio 让复杂的技术实现变得简单易懂，降低了大模型技术应用的门槛，即使你不会写代码，也能快速构建个性化的 AI 服务。


### **MCP + Cherry Studio 可以实现哪些价值？**

当 MCP 服务器与 Cherry Studio 相结合，可以实现以下价值：

  - ​**降低门槛** ​：无需编写复杂的代码，图形界面配置即可完成高效的大模型调用。

  - ​**快速集成** ​：支持任意工作流和第三方服务的快速接入，减少重复开发。

  - ​**灵活扩展** ​：一次设置，可无限次重复调用，极大提高业务灵活性。

  - ​**成本降低** ​：免去复杂服务部署和维护成本，更专注于业务逻辑本身。

接下来，我将手把手教你如何在 Cherry Studio 中配置 MCP 服务器。


### **在 n8n 中使用哪种MCP模式？**

MCP（Model Communication Protocol）支持两种主要的通信模式：**SSE（Server-Sent Events）模式** 和**stdio（标准输入输出）模式** 。

n8n 里的 MCP Server Trigger 节点当前只持 Server‑Sent Events \(SSE\)，明确标注“暂不支持 stdio 传输” 。

**原因** ：SSE 需要长连接才能把模型生成的流式数据实时推回 n8n，而 n8n 的触发节点本身就是对外暴露 HTTP URL；stdio 属于本地进程输入/输出，n8n 云端环境无法直接把它映射成公网 URL，所以官方干脆没做。所以下面的方法针对 SSE 进行设置，具体过程如下：

* * *


## **一、** 安装 Cherry Stuido 及简单设置：

<https://www.cherry-ai.com/>

进入官网点击顶部下载，进入下载页面根据自己的系统下载安装软件。

![](https://static.xiaobot.net/file/2025-05-04/720345/227dd3ef4ecfaad7c5f0b14dd53e7848.png)![](https://static.xiaobot.net/file/2025-05-04/720345/ce3e549f9b6695a0320373ea730436b9.png)

安装完成，点击左下角设置图标，选择模型服务，而后根据自己购买的 API，点击对应的模型服务商，输入 API 密钥，保存完成模型的配置。

![](https://static.xiaobot.net/file/2025-05-04/720345/a232fb77524ad7c582bd2b87771b85e9.png)

这样就可以在对话界面进行对话交互了。

![](https://static.xiaobot.net/file/2025-05-04/720345/a2f638e919f76cc87616f4ac5ba2a3d9.png)![](https://static.xiaobot.net/file/2025-05-26/720345/ef88ce53e0f7643a325b9c800382bdb4.jpeg)

在第二步设置前，需要激活该工作流，如上图所示。


## **二、** 获取 MCP 网址和设置密码：

视频中会完整展示过程，如果不会设置推荐首先完整观看视频了解概念、理解。

  - **然后导入运行视频 31 的工作流之后，点击 MCP server trigger节点，获取网址和设置密钥，如图所示：**

![](https://static.xiaobot.net/file/2025-05-04/720345/b3dde728a09378da1860645e9350f95e.png)

  - **上图获取网址**

![](https://static.xiaobot.net/file/2025-05-04/720345/0c022924e7f31121c7d84ac32f1486ef.png)![](https://static.xiaobot.net/file/2025-05-04/720345/7de65308b190adc1489926c7b0e31029.png)

  - **上图设置密码**


## **三、配置 SSE 模式 MCP 服务器**

**新建 MCP 服务器，打开 Cherry Studio 后台管理界面，进入「MCP 服务器」管理页面，点击「新增」。**

![](https://static.xiaobot.net/file/2025-05-04/720345/cbed38ecfa6624055614520954ef710c.png)

  - **逐项填写 MCP 配置**

![](https://static.xiaobot.net/file/2025-05-04/720345/f1543b4df004e994df5a35e9c4ca1175.png)

**1\. 名称**

    n8n

**2\. 描述（可选）**

    n8n MCP 图片生成

如图所示：

![](https://static.xiaobot.net/file/2025-05-04/720345/67d3bebdc8936cdcfcfb874aee928f0b.png)

**3\. 类型**

选择「服务器发送事件（sse）」。

**4\. URL**

填写 MCP 服务接口地址，该地址就是 n8n的 MCP trigger 的 URL 地址上面已经获取到：

    https://xiangyugongzuoliu-n8n.zeabur.app/mcp/n8n-image/sse

​**5\. 请求头** ​（每行一条）

这个格式是标准的，把Bearer 空格后面的密钥修改为自己的密钥，也就是把xiangyugongzuoliu 一段字符修改为自己的密钥，粘贴进去，形如下图：

    Content-Type=application/json
    Authorization=Bearer xiangyugongzuoliu

**6\. 超时（秒）**

  - 设为300，适合SSE场景：

    300

  - 检查无误后，点击保存。

  - 配置完成保存，完整配置如图所示：

![](https://static.xiaobot.net/file/2025-05-04/720345/f42bafefc1d50f3a27731b63380ec807.png)

* * *


## **配置完成后如何调用？**

完成配置后，你即可通过 Cherry Studio 的工作流或界面，选择刚配置好的 MCP 服务器直接调用大模型，并与各种外部服务进行实时数据交互。

![](https://static.xiaobot.net/file/2025-05-04/720345/2af376ea64de954a5c6b361e6cf4c0a6.png)

这样，你不再需要频繁修改代码或重复进行技术部署，只需专注于业务流程本身，即可轻松实现从想法到落地的高效工作流自动化。

今天翔宇就分享到这里，赶快动手试试吧，享受低门槛、快速落地的大模型服务新体验！