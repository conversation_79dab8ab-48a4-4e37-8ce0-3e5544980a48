---
title: "Make中文教程：数组函数（Array functions）"
created_at: "2024-09-19T08:03:26.000000Z"
uuid: "95a668ff-b73c-4b7b-b018-ff6386474c3a"
tags: ['Make基础教程']
---

# Make中文教程：数组函数（Array functions）

**标签**: Make基础教程

# **Make平台数组函数完全指南**


## **教程简介**

本教程旨在帮助大家全面掌握 **Make.com**  平台中数组函数的使用方法。内容涵盖数组元素的添加、删除、验证、去重、排序、合并等操作，帮助用户轻松应对实际数据处理场景，提升工作效率。


## **学习目标**

  - 熟悉 **Make.com**  数组函数的基本用法。

  - 掌握数组操作的核心技巧，如数据转换、过滤、排序等。

  - 能够独立解决实际业务场景中的数组数据处理问题。


## **学习内容**

  1. **添加或移除数组元素**

  2. **验证和获取数组元素**

  3. **去重和排序数组**

  4. **合并和转化数组**

  5. **使用键访问复杂对象的属性**


### 该教程的函数不能直接粘贴到 make 中使用仅为讲解用


### **1\. add - 数组添加元素**

**功能** ：向数组添加新元素，就像往购物车添加商品

    {{add([1, 2, 3], 4, 5)}}

结果：\[1, 2, 3, 4, 5\]

> 🌟 实用场景：
> 
>   - 追加新订单到订单列表
> 
>   - 添加新用户到用户池
> 
>   - 更新商品目录
> 
> 


### **2\. contains - 元素检查器**

**功能** ：检查数组中是否存在特定元素

    {{contains([1, 2, 3], 2)}}

结果：true

> 💡 应用示例：
> 
>   - 检查邮箱是否已注册
> 
>   - 验证产品是否在库存中
> 
>   - 确认用户权限
> 
> 


### **3\. deduplicate - 重复项清理器**

**功能** ：移除数组中的重复元素

    {{deduplicate([1, 2, 2, 3])}}

结果：\[1, 2, 3\]

> 🔍 最佳实践：
> 
>   - 清理客户邮件列表
> 
>   - 整理产品目录
> 
>   - 优化标签系统
> 
> 


### **4\. distinct - 智能去重工具**

**功能** ：基于对象特定属性去重

    {{distinct([
      {"name": "Alice"},
      {"name": "Bob"},
      {"name": "Alice"}
    ], "name")}}

结果：\[\{"name": "Alice"\}, \{"name": "Bob"\}\]


### **5\. first/last - 首尾元素获取器**

    // 获取第一个元素
    {{first([1, 2, 3])}}

结果：1

    // 获取最后一个元素
    {{last([1, 2, 3])}}

结果：3


### **6\. flatten - 数组展平工具**

**功能** ：将嵌套数组转换为扁平结构

    {{flatten([1, [2, [3, 4]]])}}

结果：\[1, 2, \[3, 4\]\]

> 📝 使用技巧：
> 
>   - 处理复杂的API响应
> 
>   - 简化多层级数据
> 
>   - 优化数据结构
> 
> 


### **7\. join - 数组连接器**

**功能** ：将数组元素用指定分隔符连接成字符串

    {{join(["Alice", "Bob", "Charlie"], ", ")}}

结果："Alice, Bob, Charlie"

> 🎯 应用场景：
> 
>   - 生成格式化报告
> 
>   - 创建邮件收件人列表
> 
>   - 构建CSV数据
> 
> 


## **8\. map 函数详解**


### **基本概念**

map函数就像一个智能数据提取器，能从复杂的对象数组中提取你需要的特定属性，并可以按条件筛选。


### **语法结构**

    {{map(complex_array, key, [filter_key], [filter_values])}}


### **参数说明**

  - complex\_array: 要处理的对象数组

  - key: 要提取的属性名

  - filter\_key: \[可选\] 用于过滤的属性名

  - filter\_values: \[可选\] 过滤条件的值


### **实战案例解析**

**基础数据提取**

    {{map([
        {"email": "<EMAIL>", "role": "admin"},
        {"email": "<EMAIL>", "role": "user"},
        {"email": "<EMAIL>", "role": "user"}
    ], "email")}}

结果：

    [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]

> 🔍 使用场景：提取所有用户的邮箱地址

**带条件过滤的提取**

    {{map([
        {"email": "<EMAIL>", "role": "admin"},
        {"email": "<EMAIL>", "role": "user"},
        {"email": "<EMAIL>", "role": "user"}
    ], "email", "role", "admin")}}

结果：

    ["<EMAIL>"]

> 🎯 应用：只提取管理员用户的邮箱

**处理嵌套数据**

    {{map([
        {
            "user": {
                "name": "Alice",
                "contact": {"email": "<EMAIL>"}
            }
        },
        {
            "user": {
                "name": "Bob",
                "contact": {"email": "<EMAIL>"}
            }
        }
    ], "user.contact.email")}}

结果：

    [
        "<EMAIL>",
        "<EMAIL>"
    ]

> 💡 技巧：使用点符号访问嵌套属性

**多条件过滤**

    {{map([
        {"email": "<EMAIL>", "type": "work", "active": true},
        {"email": "<EMAIL>", "type": "home", "active": true},
        {"email": "<EMAIL>", "type": "work", "active": false}
    ], "email", "type,active", "work,true")}}

结果：

    ["<EMAIL>"]

> 🔥 高级用法：同时过滤多个条件


## **9\. keys 函数详解**


### **基本概念**

keys函数用于获取对象的所有属性名（键名），就像获取一本书的目录。


### **语法结构**

    {{keys(object)}}


### **参数说明**

  - object: 要获取键名的对象或数组


### **实战案例解析**

**基础对象属性提取**

    {{keys({
        "name": "Alice",
        "age": 25,
        "email": "<EMAIL>"
    })}}

结果：

    ["name", "age", "email"]

> 🌟 使用场景：了解对象的结构

**处理嵌套对象**

    {{keys({
        "user": {
            "profile": {
                "name": "Alice",
                "age": 25
            },
            "settings": {
                "theme": "dark"
            }
        }
    })}}

结果：

    ["user"]

> ⚠️ 注意：keys只返回顶层属性

**数组对象的键提取**

    {{keys([
        {
            "name": "Alice",
            "age": 25
        }
    ][0])}}

结果：

    ["name", "age"]

> 💡 技巧：通过\[0\]获取数组中第一个对象的键


### **10\. merge - 数组合并工具**

**功能** ：将多个数组合并成一个

    {{merge([1, 2], [3, 4], [5, 6])}}

结果：\[1, 2, 3, 4, 5, 6\]

> 💡 应用示例：
> 
>   - 合并多个数据源
> 
>   - 整合不同部门的报表
> 
>   - 汇总用户反馈
> 
> 


### **11\. sort - 智能排序器**

**功能** ：按指定规则对数组进行排序

**简单排序**

    {{sort([3, 1, 4, 1, 5], "asc")}}

结果：\[1, 1, 3, 4, 5\]

**对象数组排序**

    {{sort([
        {"name": "Bob", "age": 30},
        {"name": "Alice", "age": 25}
    ], "asc", "name")}}

结果：\[\{"name": "Alice", "age": 25\}, \{"name": "Bob", "age": 30\}\]


### **12\. slice - 数组切片工具**

**功能** ：提取数组的特定部分

    {{slice([1, 2, 3, 4, 5], 1, 3)}}

结果：\[2, 3\]

> 🎯 使用场景：
> 
>   - 分页显示数据
> 
>   - 提取特定范围的记录
> 
>   - 数据采样分析
> 
> 


### **13\. shuffle - 随机排序器**

**功能** ：随机打乱数组元素顺序

    {{shuffle([1, 2, 3, 4, 5])}}

结果：\[3, 1, 5, 2, 4\]（结果随机）

> 🎲 应用场景：
> 
>   - 随机抽奖
> 
>   - 问卷题目乱序
> 
>   - A/B测试样本分配
> 
> 


### **14\. 数据结构转换函数**

**toArray - 集合转数组**

    {{toArray({
        "id": 1,
        "name": "Alice"
    })}}

结果：\[\{"key": "id", "value": 1\}, \{"key": "name", "value": "Alice"\}\]

**toCollection - 数组转集合**

    {{toCollection([
        {"id": 1, "name": "Alice"},
        {"id": 2, "name": "Bob"}
    ], "id", "name")}}

结果：\{"1": "Alice", "2": "Bob"\}


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-19/720345/86a3071b61d37463c4764143e009b8f7.png)