---
title: "火山引擎调用 DeepSeek R1 大模型手把手教程"
created_at: "2025-02-15T03:52:17.000000Z"
uuid: "c706c6a9-4729-4c24-8f7f-96b8ebf2b14f"
tags: ['API']
---

# 火山引擎调用 DeepSeek R1 大模型手把手教程

**标签**: API

现在支持 DeepSeek R1 的平台越来越多，现在翔宇给大家介绍另外一个平台的调用方式，来自字节跳动的火山引擎，火山引擎和其他平台的区别，是每个大模型的调用需要新建一个推理点，这个推理点的 id 作为模型的名称，所以一定要注意获取这个 id。

火山引擎调用速度快，同时将思考过程和回复内容分开处理，方便在 Make 工作流中进行数据映射，同时翔宇测试调用成功率高，价格便宜，现在是非常好的DeepSeek R1服务平台，方便的朋友可以访问如下网址进行使用。

现在火山开战获得每位用户被成功注册的用户，可获得1张15元代金券，约可抵扣375万R1模型tokens。

注册链接如下：

[https://www.volcengine.com/experience/ark?utm\_term=202502dsinvite∾=DSASUQY5&rc;=14HN82R6](<https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=14HN82R6>)

1.首先访问[https://www.volcengine.com/](<https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=14HN82R6>)，点击右上角 API 接入，登陆后实名认证。

![](https://static.xiaobot.net/file/2025-02-22/720345/1e40c0015fcec90a1379566ed4eb15a6.png)

2.而后点击做成开通管理，选择需要开通的大模型，点击开通服务。

![](https://static.xiaobot.net/file/2025-02-22/720345/abc8b8b8d58bc8de4be3055cc32437d2.png)

3.选择全部需要访问大模型，点击立即开通。

![](https://static.xiaobot.net/file/2025-02-22/720345/66ad9ce0185899752bfc245981d16bd4.webp)

4.开通之后，主界面点击左侧API key管理，创建API Key，留意这个稍后会使用。

![](https://static.xiaobot.net/file/2025-02-22/720345/6b73faaa4ce750cb4482a6e40dd776e2.webp)

5.创建好之后，主界面点击在线推理，创新新的接入点

![](https://static.xiaobot.net/file/2025-02-22/720345/f9afa4189b63320af8022dcbf1f4ff3f.webp)

6.选择一个主力使用的大模型和版本确定新建推理点。

![](https://static.xiaobot.net/file/2025-02-22/720345/52b621a2af1f4f847677430d4ec85338.png)

7.输入基本信息，点击接入模型。

![](https://static.xiaobot.net/file/2025-02-22/720345/85624a84761d1910fd50de9ba3a36a9d.png)

8.接入之后可以获得接入点id，也就是一般使用的模型名称，留意这个稍后会使用。

![](https://static.xiaobot.net/file/2025-02-22/720345/aae19afdacb66feaf8e45b0b71549e62.webp)

9.进入金山文档点击这个链接[HTTP第三方API调用：火山引擎 DeepSeek R1](<https://kdocs.cn/l/cuz8iELndPP8>)复制HTTP 对话模块

![](https://static.xiaobot.net/file/2025-02-22/720345/836260b315ea5c87c0c4e8be007cc492.jpeg)

10.来到 Make 右键粘贴会出现 HTTP模块

**如果复制粘贴到 Make没有出现模块可能原因：**

**Make页面的权限是否打开了允许复制**

**复制粘贴过程中开始加入了一个点，可以首先粘贴在**[**https://jsonformatter.org/**](<https://jsonformatter.org/>)**中，然后把前面的点删除之后，再全部复制到Make中粘贴。**

![](https://static.xiaobot.net/file/2025-02-22/720345/d1f21ba20ca372bdd2ccbb65decc8de8.jpeg)

11.出现 HTTP 模块之后，修改 API KEY 为你自己的 KEY,找不到的根据下面这个图片找。

![](https://static.xiaobot.net/file/2025-02-22/720345/ad0d7ef19e0aecf846ebf0ddb98ba23b.jpeg)

![](https://static.xiaobot.net/file/2025-02-22/720345/325447b1588d898e02e7a854bd8fd07c.png)

12.然后去金山文档复制请求体的代码

![](https://static.xiaobot.net/file/2025-02-22/720345/b86fbccd0d39e4507c0345640ac0185c.jpeg)

13.来到 Make 中，新建 Create JSON 模块，依次点击 add 添加数据结构，然后点击 Generate，把刚复制的数据结构代码粘贴保存

![](https://static.xiaobot.net/file/2025-02-22/720345/8dbd84fdf44a2597635706c4b4ded081.jpeg)

14.上面复制接入点 id 粘贴，输入对话内容完成 JSON 的创建（接入点 id 在第 8 步）

![](https://static.xiaobot.net/file/2025-02-22/720345/0183b5b24579e91e282bfdb247a6203a.jpeg)

15.然后来到 HTTP 模块将这个 JSON 进行映射

![](https://static.xiaobot.net/file/2025-02-22/720345/ad938f1f39dc417d983e6c0dbb250dc3.jpeg)

16.完成调用！


## **翔宇工作流制作**

翔宇工作流致力于持续优化和更新便捷化工具，以提升 [Make.com](<http://make.com/>) 工作流的创建与使用效率。通过内置的常用 API 调用模块代码，减少了重复配置的时间，并增强了操作的一致性和可靠性。

![](https://static.xiaobot.net/file/2025-02-22/720345/369e9e481ba2b64d121d8582e682ec5f.webp)