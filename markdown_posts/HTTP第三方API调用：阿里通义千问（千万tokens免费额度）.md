---
title: "HTTP第三方API调用：阿里通义千问（千万tokens免费额度）"
created_at: "2024-11-25T03:42:20.000000Z"
uuid: "bc73f863-d636-4519-9ca4-b7c949d0567e"
tags: ['快捷工具']
---

# HTTP第三方API调用：阿里通义千问（千万tokens免费额度）

**标签**: 快捷工具

# 阿里通义千问API申请方法：

阿里通义千问的访问地址为：

<https://bailian.console.aliyun.com/#/home>

具体的免费额度可查看如下网址：

<https://help.aliyun.com/zh/model-studio/new-free-quota?spm=5176.28197581.d_index.4.16e829a4c0ODVL>

当您通过API/SDK调用大模型或应用时，需要获取API Key作为调用时的鉴权凭证。


## **操作步骤**

  1. 登录[阿里云百炼大模型服务平台](<https://bailian.console.aliyun.com/?spm=a2c4g.11186623.0.0.44134823ljWZ5j>)。

> 如果顶部显示如下消息，您需要开通百炼的模型服务，以获得免费额度。

![](https://static.xiaobot.net/file/2024-11-25/720345/234044a30f806f46570dd82197f533a7.png)
  2. 鼠标悬停于页面右上角的图标上，在下拉菜单中单击**API-KEY** 。

![](https://static.xiaobot.net/file/2024-11-25/720345/ac399df5462503b0ce3b84e6224ead00.png)
  3. 在左侧导航栏，选择**全部API-KEY** 或**我的API-KEY** ，然后创建或查看API Key。

**说明**

     - 仅主账号拥有查看**全部API-KEY** 的权限。主账号可以获取所有子账号的API-KEY，子账号仅能获取自己的API-KEY。

     - 请不要将API Key以任何方式公开，避免因未经授权的使用造成安全风险或资金损失。


# **快捷调用工具使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 [Make.com](<http://Make.com>) 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 https://jsonformatter.org/点击“Format / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下文档**

[HTTP第三方API调用：阿里通义千问](<https://kdocs.cn/l/cg8vCfbOuMC7>)

对话补全：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Authorization","value":"Bearer $DASHSCOPE_API_KEY"},{"name":"Content-Type","value":"application/json"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\n    \"model\": \"qwen-plus\",\n    \"messages\": [\n        {\n            \"role\": \"system\",\n            \"content\": \"You are a helpful assistant.\"\n        },\n        {\n            \"role\": \"user\",\n            \"content\": \"你是谁？\"\n        }\n    ]\n}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}

结构化输出对话：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Authorization","value":"Bearer $DASHSCOPE_API_KEY"},{"name":"Content-Type","value":"application/json"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\n    \"model\": \"qwen-plus\",\n    \"messages\": [\n        {\n            \"role\": \"system\",\n            \"content\": \"You are a helpful assistant.\"\n        },\n        {\n            \"role\": \"user\",\n            \"content\": \"请用json格式输出一个学生的信息，姓名是张三，学号是12345678\"\n        }\n    ],\n    \"response_format\": {\n        \"type\": \"json_object\"\n    }\n}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label


## **翔宇工作流制作**

翔宇工作流致力于持续优化和更新便捷化工具，以提升 [Make.com](<http://Make.com>) 工作流的创建与使用效率。通过内置的常用 API 调用模块代码，减少了重复配置的时间，并增强了操作的一致性和可靠性。

![](https://static.xiaobot.net/file/2024-11-25/720345/8091188a3f4022d825eef36784fd158f.png)