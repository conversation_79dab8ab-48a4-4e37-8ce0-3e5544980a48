---
title: "Make中文教程：证书和密钥(Certificates and keys)"
created_at: "2024-09-19T08:39:34.000000Z"
uuid: "aea31564-dcdc-4d39-9024-94d55ebbb772"
tags: ['Make基础教程']
---

# Make中文教程：证书和密钥(Certificates and keys)

**标签**: Make基础教程

#
## Make平台证书和密钥使用指南

**翔宇工作流** 今天为大家带来关于证书和密钥的教程。

  1. **教程简介** ： 本教程介绍如何在Make.com平台上插入密钥或证书，包括两种方法：直接插入和从文件中提取，并展示了具体操作步骤和注意事项。

  2. **学习目标** ： 学习如何在Make.com平台上插入或提取密钥和证书，以便于在平台上配置和连接各种服务，确保操作的安全性和方便性。

  3. **学习内容** ：

     - 直接插入RSA私钥的方法

     - 从P12、PFX或PEM格式文件中提取私钥和证书

     - 转换OpenSSH私钥为PEM格式的命令

     - 使用密钥提取功能的详细步骤

  4. **概念理解** ： 密钥和证书在连接和认证服务时至关重要。直接插入需要复制粘贴RSA私钥，而提取则更适合从已加密文件中获取密钥或证书，支持P12、PFX和PEM格式。


## **插入密钥的两种方式**

在Make平台中，你可以通过以下两种方式插入密钥或证书：

  1. 直接插入

  2. 从文件提取（支持 P12、PFX 或 PEM 格式）


## **1\. 直接插入方式**


### **操作步骤**

  1. 准备包含 **RSA PRIVATE KEY**  的文件

  2. 从文件中复制密钥，确保包含完整的头尾标记：
         
         -----BEGIN RSA PRIVATE KEY-----
         .....
         -----END RSA PRIVATE KEY-----

  3. 将密钥直接粘贴到指定字段：

![](https://static.xiaobot.net/file/2024-11-19/720345/9567d94ab978c556f716fa2e8eb51f11.png)

> ⚠️ 重要提示： 系统不支持 OPENSSH PRIVATE KEY 格式。如需使用，请先进行转换：
> 
>   - Mac/Linux用户：在终端中使用命令：
>         
>         ssh-keygen -p -m PEM -f <密钥路径>
> 
>   - Windows用户：请使用 PuTTy 密钥生成器
> 
> 


## **2\. 从文件提取**

为了简化操作流程，Make平台提供了密钥提取功能，支持：

  - 提取私钥或证书

  - 处理加密文件

  - 支持 **P12** 、**PFX**  和 **PEM**  格式


### **提取选项**

证书提取对话框提供两个选项：

  1. 私钥提取：

![](https://static.xiaobot.net/file/2024-11-19/720345/b623523873b281a2f43a824fa46aeece.png)

  2. 证书提取：

![](https://static.xiaobot.net/file/2024-11-19/720345/73882869fab44e960a9cd4283686a551.png)


### **实例教程：AWS EC2密钥提取**

在创建Linux版EC2实例时，AWS会提供PEM格式的登录凭证：

该文件包含用于连接实例的私钥：

**详细步骤**

  1. 打开模块的"Create a connection"对话框，在"Advanced Settings"中点击"Extract"按钮：

![](https://static.xiaobot.net/file/2024-11-19/720345/f7443e89960b91d3855e9d42a78acd32.png)

  2. 在提取对话框中选择"Private Key"选项

  3. 点击"Choose Files"按钮，选择目标文件：

![](https://static.xiaobot.net/file/2024-11-19/720345/190447b868d4a80f76950d152ecef774.png)

  4. 如有需要，输入密码

  5. 点击"Save"按钮完成设置

提取完成后，私钥将被导出并用于服务连接：

![](https://static.xiaobot.net/file/2024-11-19/720345/8032969e57627811eb1ff65028ee4e18.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转账！**

![](https://static.xiaobot.net/file/2024-11-19/720345/3c3b0235d10356ef53d9412b2b745a5f.png)