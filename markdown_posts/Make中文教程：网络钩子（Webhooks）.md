---
title: "Make中文教程：网络钩子（Webhooks）"
created_at: "2024-09-19T15:34:32.000000Z"
uuid: "874d0617-7c5f-4358-8778-3622422d0f05"
tags: ['Make基础教程']
---

# Make中文教程：网络钩子（Webhooks）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于网络钩子的教程。

  1. **教程简介** ：本教程介绍了如何在Make.com中使用Webhooks通过HTTP向场景发送数据。内容涵盖创建应用特定Webhooks和自定义Webhooks，设置及调度Webhooks处理，并讨论了数据处理和错误处理。

  2. **学习目标** ：学习如何在Make.com上创建和使用Webhooks，通过HTTP触发场景的执行，掌握应用特定Webhooks、自定义Webhooks的创建方法及其数据结构设置和调度。

  3. **学习内容** ：

     - Webhooks的概念及其类型

     - 创建应用特定Webhooks和自定义Webhooks

     - 设置Webhook的数据结构

     - 调度Webhooks处理

     - Webhooks的队列管理及日志查看

     - 错误处理和支持的传入数据格式

  4. **概念理解** ：

     - **Webhooks** ：一种HTTP回调机制，当特定事件发生时，通过HTTP POST请求将数据发送到预定义的URL。

     - **即时触发器** ：在Webhooks收到请求时立即触发场景执行的机制。

     - **自定义Webhooks** ：用户自定义的Webhooks，允许向特定URL发送任意数据。

     - **数据结构** ：Webhook请求的预期数据格式，用于验证和映射传入数据。

  5. **任务练习** ：

     - 创建一个自定义Webhook模块并将其插入到一个新场景中。

     - 设置Webhook的数据结构，尝试向生成的Webhook URL发送一个包含测试数据的HTTP请求。

  6. 调整场景设置，使场景每小时运行一次，并处理队列中的Webhooks请求。


# **Webhooks**

Webhooks 允许您通过 HTTP 将数据发送到 Make。Webhooks 创建一个 URL，您可以从外部应用程序或服务，或另一个 Make 场景中调用它。使用 webhooks 来触发场景的执行。

Webhooks 通常充当即时触发器。与定期访问特定服务以获取要处理的新数据的计划触发器相反，webhooks 在 webhook URL 接收请求后立即执行场景。

Make 支持以下类型的 webhooks：

  - 特定应用程序的网络钩子监听特定应用程序传出的数据，也称为即时触发器。

  - 自定义网络钩子允许您创建一个 URL，您可以向其发送任何数据。


## **创建特定于应用程序的 Webhook**

许多应用程序提供 Webhooks 来在应用程序中发生特定更改时执行场景。 这些称为即时触发器。 即时触发器在应用程序模块列表中标有标签“INSTANT”：

![](https://static.xiaobot.net/file/2024-11-18/720345/14352bf3475dc534f2941150866a6822.png)

如果应用程序没有提供 Webhooks，请使用轮询触发器定期轮询服务以获取新数据。


## **  创建自定义 Webhook**

创建 Webhook，您必须将自定义 Webhook 模块插入到场景中。


### **  注意**

每个场景必须使用自己的 webhook。不能在多个场景中使用同一个 webhook。


### **将 Webhook 插入到场景中**

  1. 将自定义 Webhook 模块从 Webhooks 应用程序中插入。

  2. 在模块的设置中，点击“添加”。

  3. 设置 Webhook 的名称和其他设置，然后点击保存。

Make 生成一个 URL 并开始监听对该 URL 的请求。发送一个请求到这个 URL，让 Make 自动确定这个 Webhook 的数据结构。

** ** 您可以在左侧菜单中的 Webhooks 部分访问 Webhook 的详细信息并更改 Webhook 的设置。


### **设置 Webhook 数据结构**

可选地，您可以让 Make 知道在 Webhook 请求负载中期望什么数据结构。Make 可以使用数据结构来验证传入数据。如果您没有设置数据结构，Make 将在不进行任何验证的情况下将传入数据传递给您场景中的后续模块。

要启用验证传入数据，请以以下一种方式设置 Webhook 的数据结构：

  - 在数据结构部分手动创建一个新的数据结构。

  - 使用现有的数据结构。

您还可以使用以下方法告诉 Make 应该期望什么样的数据结构。

  - 在创建 Webhook 后立即调用 Webhook URL 并在请求正文中使用示例数据创建数据结构。

  - 重新确定现有 Webhook 的数据结构，进入 Webhook 模块设置，点击重新确定数据结构，并在请求正文中使用示例数据调用 Webhook URL。

如果您调用 Webhook URL 来自动确定或重新确定数据结构，Make 不会在数据结构部分创建可重用的数据结构。以这种方式确定的数据结构会与特定的 Webhook 内部存储。在这种情况下，Make 不会验证传入的数据。


## **调度 Webhooks 处理**

默认情况下，当 Make 在 Webhook 上接收到数据时，您的方案会立即执行。如果您不希望在 Webhook 接收到数据后立即运行您的方案，您可以安排您的方案定期处理所有 Webhook 请求。

  1. 编辑由您的 Webhook 触发的场景。

  2. 编辑场景计划设置。

编辑 Webhook 模块的计划设置。

  3. 设置您期望的时间表。

当预定的 Webhook 收到数据时，Make 会将数据存储在 Webhook 的队列中。然后在满足调度条件时处理整个队列。


## **如何制作处理 Webhooks 的流程**

当 Webhook 收到请求时，系统会将请求存储在 Webhook 的队列中。每个 Webhook 都有自己的队列。转到左侧菜单中的 Webhooks 部分，查看所有 Webhooks 及其队列。


### **并行处理 vs. 顺序处理**

如果您正在使用即时 Webhook，Make 会在收到请求时立即开始处理每个请求。默认情况下，使用即时 Webhook 的场景会并行处理。即使之前的场景执行仍在处理中，Make 也不会等待其处理完成。

您可以检查方案详细信息中的所有运行执行。单击运行执行列表中的项目，以查看该特定执行的图形表示。当前显示的执行将标有眼睛图标。

![](https://static.xiaobot.net/file/2024-11-18/720345/a18e29f6e8389ab20266edc0e1dbd45d.png)

要关闭并行处理，请打开您的场景设置并选择顺序处理。启用顺序处理后，Make 会等待前一个执行完成后再开始下一个。当您需要按照请求顺序处理 webhook 请求时，也请使用顺序处理。


### **处理预定的网络钩子**

如果您正在使用预定的 Webhook，请求会在队列中累积，直到满足计划标准。当满足计划标准时，Make 会根据您为 Webhook 设置的最大结果数处理排队的请求。

例如，如果您的方案计划每小时运行一次，并且您的最大结果数设置为默认值 2，则每小时从队列中处理两个项目。如果您的 Webhook 队列正在积累请求，请增加最大结果数或调整计划以更频繁地执行方案。


### **  注意**

即时触发模块具有“最大循环次数”参数，而不是“最大结果数”参数。

将即时触发模块中的最大循环次数设置为与 Webhooks 和最大结果参数相同的数据处理行为。


### **  Webhook 队列详情**

当数据到达 Webhook 并且调用未立即处理时，Make 将其存储在 Webhook 处理队列中。

Webhook 队列项目数量的限制取决于您的使用配额，这是您订阅的一部分。每月获得许可的每 10,000 次操作，您可以在每个 Webhook 队列中最多拥有 667 个项目。Webhook 队列中的最大数量为 10,000 个项目。

当 Webhook 队列已满时，Make 拒绝所有超过限制的传入 Webhook 数据。

传入的 Webhook 数据始终存储在队列中，无论数据是否设置为保密选项。一旦数据在场景中被处理，它将被永久删除。

**  查看 Webhook 队列**

查看队列内容，请按照以下步骤操作。

  1. 转到左侧菜单中的 Webhooks 部分。

![](https://static.xiaobot.net/file/2024-11-18/720345/c9b635c014b15a7b3deee55f0e32d2c2.png)
  2. 找到您想查看队列的 Webhook。

  3. 单击列表中的特定 Webhook 以查看其详细信息。

![](https://static.xiaobot.net/file/2024-11-18/720345/2b7ac27d526d37961641d4b8920e7f04.png)

 您可以看到：

     -  Webhook 状态

     - Webhook URL 和 webhook UDID（唯一 webhook 标识符）

     -  您的方案状态

     - 场景 ID 和场景 URL

  4. 要查看 Webhook 的队列，请单击“队列”。

![](https://static.xiaobot.net/file/2024-11-18/720345/542aed6f5bdc9dc2c0fc65d02b88d183.png)

您还可以在 Webhooks 页面上点击带有卡车图标的按钮。

![](https://static.xiaobot.net/file/2024-11-18/720345/3c631e4fe6cdf47c4087d2962d0462d0.png)

Webhook 的队列显示。

  5. 单击要检查的 Webhook 旁边的“详细信息”。

![](https://static.xiaobot.net/file/2024-11-18/720345/2f188b38ebb343c49601697731c1c518.png)

您可以看到已解析的项目。

![](https://static.xiaobot.net/file/2024-11-18/720345/666bfd84fcd96c5b11d7d3fffe29232c.png)

**不活跃网络钩子的到期**

自动地禁用未连接到任何场景超过 5 天（120 小时）的网络钩子。该钩子返回 410 Gone 状态码。

![](https://static.xiaobot.net/file/2024-11-18/720345/fef951511aa1b09bee4aab28f0ae70f4.png)

**从队列中删除 Webhook 项目**

  1. 转到左侧菜单中的 Webhooks 部分。

![](https://static.xiaobot.net/file/2024-11-18/720345/864c5fe12718e98910a4f286df775f05.png)
  2. 单击带有卡车图标的按钮以查看 Webhook 的队列。

![](https://static.xiaobot.net/file/2024-11-18/720345/c73fcfd75b22651b69a5ef925605868a.png)

Webhook 的队列显示。

  3. 在您想要删除的条目前面的左侧方框中打勾。

  4. 单击“删除所选”以删除所选的 Webhook。

![](https://static.xiaobot.net/file/2024-11-18/720345/a3c75bff36d8c8067e1bf24fabb30313.png)

要删除全部内容，请勾选左侧的第一个框，然后点击“全部删除”。

![](https://static.xiaobot.net/file/2024-11-18/720345/e8a7cd7c5dec2478946ccdf10b5197fb.png)

 点击“确定”以确认。

![](https://static.xiaobot.net/file/2024-11-18/720345/98449edbdb705f47e3fbbcce1cda7fa5.png)

您已从队列中删除了传入的 Webhook 项目。

**  Webhook 日志**

存储店铺的 Webhook 日志 3 天。对于企业计划中的组织，Make 保留 Webhook 日志 30 天。Make 删除超过其他保留限制的日志。

查看 Webhook 日志，请按照以下步骤操作。

  1. 转到左侧菜单中的 Webhooks 部分。

![](https://static.xiaobot.net/file/2024-11-18/720345/4bffe2b69a464b87943a52b6b8b66bad.png)
  2. 单击列表中的特定 Webhook 以查看其详细信息。

![](https://static.xiaobot.net/file/2024-11-18/720345/4fe3df9d95886fdfe10807827d554748.png)
  3.  点击日志。

![](https://static.xiaobot.net/file/2024-11-18/720345/2d2e09495bd62fcdf3e8c3006e01c633.png)

 您可以看到：

     - Webhook 调用的状态（成功、警告、错误或全部）

要按状态筛选 Webhook 日志，请单击筛选图标。

![](https://static.xiaobot.net/file/2024-11-18/720345/d024b6293828172152efa80a6414316d.png)
     - 传入 Webhook 的日期和时间

要按日期和时间对 Webhook 日志进行排序，请单击箭头。

![](https://static.xiaobot.net/file/2024-11-18/720345/caa21f7f471be4849088e9d4be659f37.png)
     - Webhook 执行日志大小

  4. 要查看特定 Webhook 日志的详细信息，请单击“详细”。

![](https://static.xiaobot.net/file/2024-11-18/720345/b6388ba52cb51d8a3713531b8eb51d6c.png)

 您可以看到：

  - Webhook 请求（时间戳，URL，方法，标头，查询，正文）

  - Webhook 响应（状态，标头，正文）

  -  解析的项目

解析的项目将查询参数和 Webhook 请求的主体组合在一个捆绑包中。

![](https://static.xiaobot.net/file/2024-11-18/720345/46693305d0f1b48779bb6d834b61549b.png)


## **  Webhook 设置**

要调整 Webhook 的设置，请单击左侧菜单中的 Webhooks，然后编辑一个 Webhook。

![](https://static.xiaobot.net/file/2024-11-18/720345/6447df18188a183d954e7b611919c0ae.png)


## **  错误处理**

当您的场景中出现 Webhook 错误时，该场景：

  - 立即停止-当场景设置为立即运行时。

  - 在按计划运行的情况下，连续 3 次尝试失败后停止（3 个错误）。

如果您的场景包含 Webhook 响应模块，则错误将发送到 Webhook 响应。除非您在场景设置中启用自动提交，否则 Webhook 响应模块始终最后执行。


## **支持的输入数据格式**

Make 支持以下格式的输入数据：

  -  查询字符串

  -  表单数据

  - JSON

如果一个 Webhook 同时接收查询字符串和表单数据或 JSON 数据，系统会将这些数据合并成一个数据包。如果请求中包含不同格式的重复数据，查询字符串优先，并覆盖其他格式接收到的数据。我们建议您不要在查询字符串、表单数据和 JSON 中重复数据。


### **  查询字符串**

    GET 
    https://hook.make.com/yourunique32characterslongstring?name=Make&job=automate


### **  表单数据**

    POST 
    https://hook.make.com/yourunique32characterslongstring
    Content-Type: application/x-www-form-urlencoded
    name=Integrobot&job;=automate


### **  多部分**

    POST 
    https://hook.make.com/yourunique32characterslongstring
    Content-Type: multipart/form-data; boundary=---generatedboundary
    ---generatedboundary
    Content-Disposition: form-data; name="file"; filename="file.txt"
    Content-Type: text/plain
    Content of file.txt
    ---generatedboundary
    Content-Disposition: form-data; name="name"
    Make
    ---generatedboundary

为了接收使用 multipart/form-data 编码的文件，需要配置一个数据结构，其中包含一个 collection 类型字段，其中包含嵌套字段 name ， mime 和 data 。字段 name 是一个 text 类型，包含上传文件的名称。mime 是一个文本类型，包含\[MIME\]格式的文件。字段 data 是一个 buffer 类型，包含正在传输的文件的二进制数据。


### **JSON**

    POST 
    https://hook.make.com/yourunique32characterslongstring
    Content-Type: application/json
    {"name": "Integrobot", "job": "automate"}

要访问原始的 JSON，请打开 Webhook 的设置并启用 JSON 透传选项：

![](https://static.xiaobot.net/file/2024-11-18/720345/30e657559f91a00cebc3ad980356a51e.png)

允许的 Webhook 负载大小（ Content-Length ）为 5 MB（5,242,880 字节），与订阅层级无关。


## **  Webhook 头部**

要访问 Webhook 的标头，请在 Webhook 的设置中启用“获取请求标头”选项：

![](https://static.xiaobot.net/file/2024-11-18/720345/a0879b6fe57b14ae7becd198dadd7b29.png)

您可以使用 map\(\) 和 get\(\) 函数的组合来提取特定的标头值。下面的示例显示了一个公式，用于从 Headers\[\] 数组中提取 authorization 标头的值。该公式用于过滤器中，将提取的值与给定文本进行比较，只有在匹配时才传递 Webhooks。

![](https://static.xiaobot.net/file/2024-11-18/720345/80c46b208be0600a9d4758b7ef82cf32.png)


## **  响应 Webhooks**

默认情况下，对 Webhook 调用的默认响应仅包含简单文本“已接受”。在执行自定义 Webhook 模块期间，响应会立即返回给 Webhook 的调用方。您可以轻松地像这样测试它：

  1. 将自定义 Webhook 模块放置在您的场景中。

  2. 在模块的配置中添加一个新的 Webhook。

  3. 将 Webhook 的 URL 复制到剪贴板。

  4. 运行场景 - 自定义 Webhook 模块应该等待 Webhook 调用（请参见右侧）

  5. 在新的浏览器窗口中打开，粘贴复制的 URL 到地址栏中，然后按 Enter 键。

  6. 自定义 Webhook 模块将被触发，浏览器将显示以下页面：

这些是当场景不包含 Webhook 响应模块时的默认响应：

![](https://static.xiaobot.net/file/2024-11-18/720345/ea312fec04d9bb548648f33f90f03b72.png)


## **  HTML 响应示例**

将 Webhook 响应模块配置如下：

![](https://static.xiaobot.net/file/2024-11-18/720345/5de3bfdb5cf87797292a44c81af96991.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/8f9b9f218b52a44ea3d3f1e86290a6fd.png)

它将生成一个 HTML 响应，将在 Web 浏览器中显示如下：

![](https://static.xiaobot.net/file/2024-11-18/720345/bbf42093db6e76bec7e9ddf08573a32e.png)


## **  重定向示例**

将 Webhook 响应模块配置如下：

![](https://static.xiaobot.net/file/2024-11-18/720345/444cd2bc9020d6fcf5dff350c49e177b.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/fbc19921dc907b4d4902cdb33c33b0dc.png)


## **  自定义邮件挂钩**

Mailhook 是一个即时触发模块，可以通过向该模块生成的电子邮件地址发送电子邮件来触发。


### **邮件挂钩附件大小限制**

您发送到邮件挂钩的电子邮件的最大大小（包括附件）为 25 MB。


### **  使用示例**

Mailhook 将监视您的传入电子邮件，无需安排运行场景。

  1. 将自定义邮件挂钩添加到您的场景中（Webhooks > 自定义邮件挂钩）。

![](https://static.xiaobot.net/file/2024-11-18/720345/d02c3d9581c5b4b134ffdb9b85a4a0fb.png)
  2. 生成一个邮件挂钩电子邮件地址，并将该地址复制到剪贴板。

![](https://static.xiaobot.net/file/2024-11-18/720345/8276f3cb4c42f650ec49250cea5bce61.png)
  3. 保存并运行场景。

  4. 打开您的电子邮件帐户设置，并配置转发。将第 2 步（上文）中 Custom mailhook 模块生成的电子邮件地址用作转发地址。

 对于 Gmail：

  1. 点击右上角的齿轮，然后点击查看所有设置。

  2. 打开转发和 POP/IMAP 选项卡。 

![](https://static.xiaobot.net/file/2024-11-18/720345/715935ff8ccb1237ccb4da19dcc1cc5c.png)
  3. 点击“添加转发地址”按钮。

  4. 输入您在上面第 2 步生成并复制的电子邮件地址，然后单击“下一步”。

![](https://static.xiaobot.net/file/2024-11-18/720345/681c729ce5ea27da4b181d6209a39f4a.png)
  5. 之后，将会弹出一个窗口。点击“继续”。

  6. 确认链接已发送到您的 mailhook。运行自定义 mailhook 模块，在 Bundle > Text 下查看此代码的输出。

![](https://static.xiaobot.net/file/2024-11-18/720345/759aa3fbcecee254b85fa03b89bee82d.png)


### **  注意**

如果您在工作或学校中使用 Gmail，您无需验证转发地址。

  7. 启用转发，并保存更改。

![](https://static.xiaobot.net/file/2024-11-18/720345/bc4c9962dac170de397d90c820b4de40.png)

将其他所需的模块添加到场景中。然后保存并激活场景。

现在，每当您的电子邮件帐户收到新邮件时，Make 场景中的自定义邮件挂钩模块会被触发并接收邮件消息数据。


### **  提示**

发件人和各种收件人地址（收件人：抄送：和密送：）在传入邮件的数据结构中解析。回复地址：可以在标题部分找到。


## **  Webhook 速率限制**

Make 可以每秒处理多达 30 个传入的 Webhook 请求。

如果您每秒发送超过 30 个请求，系统将返回状态码 429 的错误。


## **  故障排除 Webhooks**


### **映射面板中缺少的项目**

如果在 Webhooks > 自定义 Webhook 模块设置中，模块设置中的映射面板中缺少某些项目，请单击 Webhooks > 自定义 Webhook 模块以打开其设置，并单击重新确定数据结构：

![](https://static.xiaobot.net/file/2024-11-18/720345/3669a4df7969d151b2fae3dd1e941f40.png)

然后按照“确定 Webhook 数据结构”部分中描述的步骤进行操作。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/61f1f2f7ff777786b012969abd469575.png)