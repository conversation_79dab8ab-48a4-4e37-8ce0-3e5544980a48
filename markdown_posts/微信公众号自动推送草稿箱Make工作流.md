---
title: "微信公众号自动推送草稿箱Make工作流"
created_at: "2025-03-30T08:44:10.000000Z"
uuid: "3d055190-2759-4002-9f83-30c2d924c61f"
tags: ['会员独家']
---

# 微信公众号自动推送草稿箱Make工作流

**标签**: 会员独家

![](https://static.xiaobot.net/file/2025-03-30/720345/ae2feef53c704d4872e95286d38adf4d.png)

微信公众号自动推送草稿箱Make工作流为微信公众号运营者补齐了“最后一块自动化拼图”，大幅节省了时间和精力，同时确保每篇文章的质量和一致性。在之前的markdown转微信公众号HTML的API下线后，翔宇一直在探索新的解决方案。随着Claude3.7和DeepSeek新V3模型在HTML编程能力上的大幅提升，以及上下文窗口的不断扩展，翔宇终于为大家带来了这个全新的自动化工作流。

本工作流采用"草稿箱推送"模式而非直接发布，这是因为Make平台的微信模块在处理草稿发布时，仅生成可访问链接而不会触发群发推送。基于此，翔宇建议采用"自动化推送+人工审核"的两个步骤：先通过工作流将内容推送至草稿箱，运营者审阅确认后再手动发布。这种设计具有双重优势：一方面确保运营者能够对文章内容进行最后把关，及时修正可能存在的疏漏，保证内容质量；另一方面有效规避了意外群发可能带来的运营风险。

本文使用到了向阳乔木的微信公众号排版提示词，在此感谢大佬分享。

**该方法基于大模型自编程可能存在轻微瑕疵！**

**本工作流是基于视频 24 设置的工作流，具体的知识库地址如下：**

[https://xiaoyuzaici.notion.site/66981d54bf6145e39354b07586dce863?v=183017a905fd814495c1000c62a20da8&pvs;=4](<https://xiaoyuzaici.notion.site/66981d54bf6145e39354b07586dce863?v=183017a905fd814495c1000c62a20da8&pvs=4>)

**新建了发布状态的属性，如果希望使用不同的工作流生成的结果需要自己修改第一个 notion 模块并在 Notion 知识库中新建发布状态的属性，添加开始和已完成两个状态。同时在附录之中只保留需要发布文章的 markdown 内容。**


## **主要功能亮点：**

  1. 工作流初始的Notion模块经过优化升级，现已支持自动发布由任意工作流生成并存储在附录中的内容。

  2. 智能化发布流程：基于WeChat模块的强大功能，实现从素材上传、封面设计到文章发布的全流程自动化，涵盖图片处理、文本格式化、微信素材库上传及草稿箱生成等环节，全程无需人工介入。

  3. 微信HTML内容采用Claude进行移动端网页设计，确保界面美观大方、布局简洁清晰，显著提升阅读体验。

  4. 封面设计自动化：运用DeepSeek进行HTML编程，自动生成视觉效果出众、品质卓越的封面图片。

  5. 高度自定义功能：在文章发布环节，运营者可自由配置标题、作者信息及网址等关键元素，满足个性化需求。


## **工作流局限性：**

  1. 文章字数不得过长，因为这是基于 claude 3.7进行网页的设计，受限于输出长度等因素，具体长度翔宇并未测试。

  2. 由于基于大模型性自编程，所以会出现封面文字不对齐、不完整等情况，需要仰赖于大模型能力的提示，翔宇暂时没有解决方案，但是自动化工作流，可以多运行一次解决。

  3. 由于大模型自编程输出可能会造成图片，文字部分不完整，随着大模型能力提升得以解决。


## **模块功能介绍**


### **1\. Notion Search Objects模块**

  - **功能** ：作为工作流的触发器，实时监控Notion数据库中发布状态为"开始"的条目，一旦发现符合条件的记录，立即启动整个自动化流程。


### **2\. Notion List Page Contents模块**

  - **功能** ：从Notion数据库中提取指定页面的所有内容，特别是附录部分，确保完整获取文章所需素材。


### **3\. Text aggregator模块**

  - **功能** ：将Notion中抓取到的分散内容进行智能整合，按照预设逻辑重新组织，形成结构完整、逻辑清晰的文章内容。


### **4\. Google Doc Create a Document模块**

  - **功能** ：在Google Drive中创建空白文档，为后续内容处理和存储提供基础容器。


### **5\. Replace模块**

  - **功能** ：自动识别并删除文章中的GIF格式图片，确保符合Make平台的上传规范。


### **6\. Match pattern模块**

  - **功能** ：通过正则表达式精准识别文章中的图片链接，为后续图片上传做准备。


### **7\. Get a file模块**

  - **功能** ：根据匹配到的图片链接，自动下载图片文件到本地缓存。


### **8\. Wechat Upload an Image模块**

  - **功能** ：将下载的图片文件上传至微信公众号素材库，获取微信官方图片链接。


### **9\. Google Doc Insert a Paragraph to a Document模块**

  - **功能** ：根据图片上传结果，智能判断并保存内容：图片上传成功则保存图文内容，失败则仅保存文本。


### **10\. DeepSeek Create a Chat Completion模块**

  - **功能** ：调用DeepSeek新V3模型，根据文章内容自动生成专业、美观的封面设计。


### **11\. Match pattern模块**

  - **功能** ：从DeepSeek生成的封面设计中提取HTML代码，为封面图片生成做准备。


### **12\. HCTI Create an Image with HTML/CSS模块**

  - **功能** ：将提取的HTML代码转换为高质量的封面图片文件。


### **13\. Get a file模块**

  - **功能** ：下载生成的封面图片文件到本地缓存。


### **14\. Wechat Upload a Permament Asset模块**

  - **功能** ：将封面图片作为永久素材上传至微信公众号素材库。


### **15\. Google Doc Get Content of a Document模块**

  - **功能** ：从Google文档中获取经过图片替换后的完整文章内容。


### **16\. Create a Chat Completion模块**

  - **功能** ：调用AI模型，将文章内容转换为符合微信公众号规范的HTML格式。


### **17\. Match pattern模块**

  - **功能** ：从AI生成的HTML代码中提取有效内容，确保代码规范。


### **18\. Wechat Create Drafts模块**

  - **功能** ：在微信公众号后台创建草稿，包含文章内容、封面图片等完整信息。


### **19\. Notion模块**

  - **功能** ：更新Notion数据库中的状态信息，标记工作流已完成，便于后续跟踪和管理。


## **工作流运行前提条件：**

  1. 内容标准化处理

为确保工作流顺利运行，请确保待发布内容已转换为标准Markdown格式，包含完整的图文结构。工作流将基于此格式进行后续处理。

  2. 图片格式限制

受微信API限制，当前工作流暂不支持GIF格式图片。系统内置的智能解析器将自动识别并移除所有GIF图片，确保内容符合平台规范。

  3. 图片上传规范

所有图片资源需上传至腾讯域名。工作流将通过正则表达式自动识别图片链接，并统一上传至微信公众号素材库，确保图片资源合规可用。

  4. 封面图片生成

文章发布需提供封面图片ID。工作流将自动调用HTCI的HTML to Image API，生成专业级封面图片，并作为永久素材上传至微信平台。

  5. 正文图片要求

为满足微信模块的正文分割需求，工作流要求文章末尾必须包含图片。系统将自动在Markdown文档末尾插入指定图片，确保流程完整性。

  6. 模块授权配置

在启动工作流前，请确保已完成Make与WeChat模块的授权配置，以保证各功能模块的正常调用与数据交互。


## **可定制版功能：**

  1. 二维码定制正文底部的图片可更换为您公众号的专属二维码，以便于引导粉丝关注。

  2. 作者信息设置作者名称可按需自定义，符合品牌形象或个人需求。

  3. 文章内容调整正文部分可以根据实际发布的文章内容进行灵活修改，满足个性化运营需求。

  4. 生成封面的图片风格自定义修改。


## 工作流下载地址：

[下载地址（点击）](<https://pan.quark.cn/s/86b3b6f0757c>)

提取码：aixT

![](https://static.xiaobot.net/file/2025-03-30/720345/ba81bc89406e84d5564762c346389567.png)


# 翔宇出品，请勿转载！