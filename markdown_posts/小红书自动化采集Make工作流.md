---
title: "小红书自动化采集Make工作流"
created_at: "2024-09-30T04:29:31.000000Z"
uuid: "02648d80-5a88-4c73-9fd6-6d3680a30e96"
tags: ['会员独家']
---

# 小红书自动化采集Make工作流

**标签**: 会员独家

![](https://static.xiaobot.net/file/2024-09-30/720345/4c1b77927bc8643e52071a94d3d8f6d9.png)

小红书内容采集工作流旨在帮助会员高效采集小红书笔记赛道的相关信息。采集的内容广泛，包括但不限于以下信息：

\- **标题**

\- **昵称**

\- **检索关键词**

\- **头像**

\- **笔记 ID**

\- **URL**

\- **笔记内容**

\- **话题**

\- **账户 ID**

\- **收藏数量**

\- **评论数量**

\- **喜欢数量**

\- **所有笔记的图片**

该工作流对笔记的各个方面进行了全面覆盖，会员可根据需求进行深度分析和内容再创作。请注意，工作流仅涵盖数据采集部分，具体的内容创作及赛道分析将在后续的视频教程中发布，敬请期待。本工作流基于[TikHub.io](<https://beta-web.tikhub.io/users/signup?referral_code=kHHZHSGN>)提供的丰富接口支持，确保采集过程的稳定性和高效性。


# 关于 TikHub.io：

[TikHub.io](<https://beta-web.tikhub.io/users/signup?referral_code=kHHZHSGN>)是一个功能强大的集成工具及服务平台，支持主流社交媒体及视频平台的数据获取。它的集成工具适用于多种平台，包括但不限于：

\- **Douyin（抖音）**

\- **TikTok**

\- **Xiaohongshu（小红书）**

\- **Kuaishou（快手）**

\- **Weibo（微博）**

\- **Instagram**

\- **YouTube（油管）**

\- **Twitter（X）**

此外，TikHub.io 还提供了验证码解决方案和临时邮箱 API，帮助用户自动化获取和管理数据。


# TikHub.io 的核心功能：

1\. **API 接口**

提供多种 RESTful API 接口，支持各大平台数据的访问，包括获取用户作品、视频信息、互动数据等。

2\. **功能定制**

用户可根据业务需求定制特定功能。平台允许社区成员接入自定义功能或接口，以灵活满足个性化需求。

3\. **数据爬取**

平台支持通过 API 获取各大社交媒体平台的用户信息、视频及互动数据，适合用于市场调研及数据分析。

4\. **用户管理**

TikHub.io 提供后台管理功能，用户可生成并管理 API Token，设定权限和有效期。

TikHub.io 的设计目标是帮助用户高效展开业务，同时提供灵活的工具，满足各类业务需求。不论是营销人员、数据分析师，还是内容创作者，都可以借助 TikHub.io 迅速获取社交媒体数据，实现业务转化。

**价格：**

![](https://static.xiaobot.net/file/2024-09-30/720345/f95cbde3048189b3ba54e030fd07a1dc.png)


# 注册与使用步骤：

1\. **注册账户：**

请访问以下链接完成注册：

[TikHub.io 注册](<https://beta-web.tikhub.io/users/signup?referral_code=kHHZHSGN>)

2\. **获取 API Key：**

注册后，登录控制台，进行账户充值并获取您的 API Key。

3\. **权限设置：**

进入接口管理页面，配置所需接口的使用权限。

![](https://static.xiaobot.net/file/2024-10-01/720345/a6d34f3b4553a43098777af9167f4e7f.png)![](https://static.xiaobot.net/file/2024-10-01/720345/6743cb3eda62c748e61016a7e22a44ed.png)

如果希望测试需要进行授权查看调用示例：

![](https://static.xiaobot.net/file/2024-09-30/720345/77c016556de339f535cad260caf2fcc2.png)

完成上述步骤后，您便可开始在小红书平台进行高效的数据采集工作。

有关具体的使用教程视频，您可以访问以下链接：

<https://tikhub.io/>

![](https://static.xiaobot.net/file/2024-09-30/720345/b4bdc2f9450b9ed56fedad7d643cde57.png)


# 工作流介绍：

![](https://static.xiaobot.net/file/2024-09-30/720345/6316313715d911c714c2c49e90a45c29.png)

1\. **搜索Notion数据库条目（连接小红书采集库）**

\- 使用“Search Objects”模块。

\- 复制以下两个Notion数据库（小红书赛道采集库、小红书笔记采集库），连接Notion账户，并设置搜索条件。当状态为“开始”时，根据关键词进行采集。

2\. **获取小红书笔记信息**

\- 使用“Make a Request”模块发送API请求。

\- 配置请求URL，并选择GET/POST方法获取笔记数据。

3\. **遍历笔记ID**

\- 处理API响应，将笔记ID进行遍历，获取每个检索到的笔记。

4\. **获取笔记详情**

\- 基于笔记ID，使用“Make a Request”模块获取详细的笔记信息。

5\. **下载头像图片**

\- 使用“Get a File”模块下载笔记作者的头像图片。

6\. **上传头像至ibb图床**

\- 使用“Upload a Photo”模块，将下载的头像图片上传至ibb图床。

7\. **保存笔记信息到Notion**

\- 使用“Create a Database Item”模块，将笔记的详细信息保存至Notion数据库。

8\. **遍历并下载笔记中的图片**

\- 遍历笔记，使用“Get a File”模块下载每个笔记中的图片。

9\. **将图片上传至ibb图床**

\- 检查笔记内容并使用“Upload a Photo”模块，将所有下载的图片上传至ibb图床。

10\. **附加图片链接到Notion**

\- 使用“Append to a Database Item”模块，将上传到ibb的图片链接附加到Notion笔记条目中。


# 资源链接：

小红书赛道采集库：

[https://xiaoyuzaici.notion.site/111017a905fd804bb6e7fb3db264c62a?v=111017a905fd81bf96b3000c5d1a7a38&pvs;=4](<https://xiaoyuzaici.notion.site/111017a905fd804bb6e7fb3db264c62a?v=111017a905fd81bf96b3000c5d1a7a38&pvs=4>)

小红书笔记采集库：

[https://xiaoyuzaici.notion.site/111017a905fd8096aa76c0308b0c11e3?v=111017a905fd817488ed000c5788a684&pvs;=4](<https://xiaoyuzaici.notion.site/111017a905fd8096aa76c0308b0c11e3?v=111017a905fd817488ed000c5788a684&pvs=4>)

工作流地址：

[小红书采集工作流](<https://pan.quark.cn/s/de3df0e895ec>)

提取码：VPW9

**注意事项** ：

由于第三方服务可能存在不稳定性，可能随时失效，建议会员采取小额充值、按需使用的策略，避免大额充值带来的资金浪费。此外，务必注意保护个人信用卡信息，防范网络诈骗或数据泄露风险。

另外，由于属于第三方服务，出现的任何使用问题与不稳定情况，可到相关网址进行解决方案的获取。

本文分享的所有数据获取方法和工具推荐仅供学习、交流和了解数据获取途径之用，禁止用于任何商业用途。会员在使用这些工具时应严格遵守相关法律法规，确保使用行为的合法性。