---
title: "Make中文教程：Cloudinary"
created_at: "2024-09-01T02:27:10.000000Z"
uuid: "4c985f41-3e51-4ce3-91b2-febe2347755c"
tags: ['Make基础教程']
---

# Make中文教程：Cloudinary

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Cloudinary的教程。

**教程简介** ：本教程讲解如何在Make.com中使用Cloudinary模块，包含如何连接Cloudinary账户、管理和监控资源、创建使用报告等内容，帮助用户高效使用Cloudinary的功能。

**学习目标** ：通过本教程，学会如何将Cloudinary与Make.com连接，掌握资源管理和监控的基本操作，以及了解如何创建使用报告，实现快速、高效的资源操作。

**学习内容** ：教程涵盖Cloudinary账户连接步骤、资源管理操作（监控、搜索、获取、上传、转换、删除）以及创建使用报告和进行API调用的方法。

**概念理解** ：Cloudinary是一种图像和视频管理平台，在Make.com中，Cloudinary模块可以帮助用户管理媒体资源，包括上传、转换、删除等操作，并生成使用报告以便监控资源使用情况。

**任务练习** ：

  1. 创建一个Cloudinary账户并获取云名称、API Key和API Secret。

  2. 在Make.com中添加Cloudinary模块并成功建立连接。

  3. 上传一个新资源到Cloudinary并尝试转换该资源的格式。

  4. 创建一个使用报告并进行一次API调用。


# **Cloudinary**

使用Make.com中的Cloudinary模块，您可以监控和管理资源，并创建使用报告。

要开始使用Cloudinary，请在cloudinary[.](<https://cloudinary.com/ip/gr-sea-gg-brand-home-base?utm_source=google&utm_medium=search&utm_campaign=goog_selfserve_brand_wk22_replicate_core_branded_keyword&utm_term=1329&campaignid=17601148700&adgroupid=141182782954&keyword=cloudinary&device=c&matchtype=e&adposition=&gad=1&gclid=Cj0KCQjwuZGnBhD1ARIsACxbAVhtrL3zK1XKLDeoIZwGd7ZMOsM5oJ1dd2iaytGeCRsRqV5cFHPqvUgaAlLlEALw_wcB>)com创建一个账户。

请参考Cloudinary API 文档以获取可用端点的列表。


## **将Cloudinary连接到Make.com**

要建立连接：

  1. 登录到您的Cloudinary账户。

  2. 在左侧边栏中，点击**Dashboard** 。

  3. 复制**Cloud Name** （云名称）、**API Key** （API密钥）和**API Secret** （API秘密）到安全的地方。

![](https://static.xiaobot.net/file/2024-10-04/720345/ecdac92f8a64fbe29c090b2d29cdbbde.png)
  4. 登录到您的**Make.com** 账户，添加一个Cloudinary模块到您的**场景** 中，然后点击**Create a connection** （创建连接）。

  5. 可选步骤：在**Connection name** （连接名称）字段中输入连接的名称。

  6. 粘贴您在步骤3中保存的**Cloud Name** （云名称）、**API Key** （API密钥）和**API Secret** （API秘密）。

  7. 点击**Save** （保存）。

  8. 如有提示，验证您的账户并确认访问权限。

您已经成功建立了连接。现在您可以编辑您的场景并添加更多的Cloudinary模块。如果您的连接需要重新授权，请按照这里的连接更新步骤进行操作。


## **构建Cloudinary场景**

在连接应用后，您可以执行以下操作：


### **资源**

  - 监控已上传的资源

  - 监控已标记的资源

  - 搜索资源

  - 获取资源

  - 上传资源

  - 转换资源

  - 删除资源


### **其他**

  - 创建使用报告

  - 进行API调用

* * *

完成以上步骤后，您就可以在Make.com中使用Cloudinary模块有效地管理和监控您的资源。对于更多功能与操作，请详细阅读Cloudinary的API文档。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-01/720345/a3b9f35befce03e7133387abcd069a5b.png)