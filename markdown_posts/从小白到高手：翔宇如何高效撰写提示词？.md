---
title: "从小白到高手：翔宇如何高效撰写提示词？"
created_at: "2025-04-06T05:39:39.000000Z"
uuid: "f38f2628-53cf-4c7f-ab6c-c3af367be372"
tags: ['AI教程与资源']
---

# 从小白到高手：翔宇如何高效撰写提示词？

**标签**: AI教程与资源

**翔宇面向新手内容创作者的提示词撰写教程：从"认识世界"到"改造世界"提示词创作实操**

![翔宇如何高效撰写提示词](https://xiangyugongzuoliu.com/wp-content/uploads/2025/04/<EMAIL>)

大家好，我是**翔宇** ，今天想和各位分享我在日常创作中总结出的高效提示词框架。作为一名长期研究AI辅助创作的实践者，我发现很多新手内容创作者虽然已经听说过像ChatGPT这样的通用大语言模型可以帮助撰写文章、脚本、文案等各种内容，但往往不知道如何有效地"驾驭"这些AI工具。

事实上，要让AI给出理想的输出，关键在于你给予它的提示词（Prompt）。提示词撰写被称为"提示词工程（Prompt Engineering）"，是一门正在兴起的实用技能：通过巧妙地设计和优化你给AI的指令，从而获得更准确、有创意且实用的回答。

在我多年的实践中，我逐渐形成了一套"认识世界—改造世界"的哲学方法论来指导提示词的撰写。这套方法论既有理论深度，又便于实际操作。本教程将带你从零开始，循序渐进地掌握这套提示词撰写的核心方法和技巧。

本文结构遵循"认识世界—改造世界"的思路展开：首先帮助你了解提示词的概念与价值，以及在撰写提示词前如何认清需求和目标（认识世界）；随后指导你如何有效地编写清晰指令来"指挥"AI（改造世界）。我将详细介绍我自用的提示词撰写核心框架（包括角色、任务、目标、格式规范、示例等要素），并提供提示词写作建议与常见错误的总结。在实践部分，我准备了实操案例演示如何应用所学理论，最后给出各类内容创作场景的提示词模板（如文章写作、视频脚本、社交媒体文案），方便你直接套用练习。

我想强调的是：掌握提示词撰写，能够大幅提升AI输出内容的质量，让AI真正成为你创作路上的得力助手。现在，让我们一起步入提示词工程的世界，解锁AI创作的无限可能！


# 一. 提示词的概念与价值

在深入技巧之前，我们需要弄清什么是提示词以及为什么提示词重要。

![提示词工程](https://xiangyugongzuoliu.com/wp-content/uploads/2025/04/<EMAIL>)


## 1.1 什么是提示词？

简单来说，提示词就是你给 AI 模型的输入指令，它可以是一段用自然语言描述的任务、问题或要求。通过提示词，我们告诉 AI 要做什么。例如，对文本生成模型而言，提示词可以是一个问题、一个命令，或者包含背景、指令和对话历史的更长描述。提示词工程则是设计、调整这些输入指令的过程，目的是让生成式 AI 模型产出我们期望的最佳结果。


## 1.2 为什么提示词很重要？

因为 AI 的回答质量很大程度取决于我们提供的提示质量。业内有句俗语：“Garbage In, Garbage Out（输入垃圾，则输出垃圾）”。在 AI 领域也是如此：如果你给出含糊不清、信息不完整或结构不良的提示，得到的结果往往让人不满意；反之，详细且具体的提示能引导 AI 产生更精确、有价值的内容。提示词工程的重要性就类似于"提问的艺术"：就像问老师一个清晰得当的问题可以得到更有用的答案一样，一个精心设计的提示可以"诱导"AI 给出更准确、有创意且切题的回应。

举个简单例子：如果你让 ChatGPT “写一篇关于微服务的博客文章”（缺乏细节），AI 可能只能给出泛泛而谈的内容。但如果你进一步限定角色和背景，比如“作为一名拥有15年经验的高级软件架构师，写一篇关于微服务架构模式的技术博客。”，AI 会基于这个专家视角提供更深入专业的讲解。可见，同样的AI，不同的提问方式，效果大不相同。

总而言之，提示词是我们与AI交流的方式，而学会编写高质量提示词，是充分释放AI潜力的基础。接下来，我们将按照"认识世界—改造世界"的思路，先学习在撰写提示词之前如何明确需求和目标，然后再学习如何将这些需求转化为有效的指令。


# 二. 认识世界：理解需求与目标，明确 AI 的任务范围

在开始动手写提示词之前，先按下"暂停键"：花一点时间认识和分析你所处的"世界"——也就是明确你让 AI 完成任务的需求、目标和边界。这一部分相当重要，属于提示词工程中的准备阶段。正所谓"磨刀不误砍柴工"，深刻理解问题才能提出正确的问题。

在"认识世界"阶段，你需要搞清楚以下几个方面：

  - **你的最终目的是什么？**  换言之，你希望AI生成的内容要达到什么目标或产生什么效果？明确的目标有助于为AI设定清晰的方向。例如，你是想获取一篇科普文章来教育读者，还是一份宣传文案来吸引顾客？目标不同，内容侧重和风格都会不一样。提前确定成功的判据（例如"读者看完会明白 X 概念"或"这文案能激发购买欲"），可以帮助你后续校验 AI 的输出是否符合期望。

  - **具体要完成的任务是什么？**  也就是你希望AI执行的具体动作/产出。是要写一篇文章、一段脚本、几个宣传点，还是要整理一份清单、分析一段文字？把任务尽量具体化、明确化。如果任务很复杂，大到可以拆分成多个子任务，那么在提示中直接要求AI一次性完成所有子任务可能会让结果混乱。这时可以考虑将任务拆解，一步步完成，或者在提示中要求AI按步骤完成。总之，确保你自己完全明白希望AI完成的具体任务是什么。

  - **需要满足哪些要求和限制？**  这包括内容范围和边界。例如，输出的篇幅有何要求（字数、段落数等）、风格语气上有什么偏好（幽默的？正式的？面向小白的？）、是否需要包含某些要点或避免某些话题，以及AI能力的限制。特别地，要考虑AI的知识范围和能力边界，避免让它做超出范围的事情（比如预测未来事件或提供未训练领域的专业意见），以免得到不可靠的结果。如果确实需要AI提供某些资料，而这些资料AI未必掌握，你可能要提供相关背景信息作为提示的一部分。这些都属于明确任务范围的一环。

  - **目标受众是谁？**  你的内容是写给谁看的？是小学生、普通大众，还是业内专家？了解受众很重要，因为它决定了内容的深度和表达方式。如果不考虑受众，AI 可能给出不合适的内容层次。例如，同样解释"市场细分"，针对外行和针对业内人士的措辞和详略是不同的。提示中如果说明受众（“向初学者解释…”，或"面向专业读者详细讨论…"），AI 就能调整回答的口吻和难度。

  - **有无特定上下文或背景需要提供？**  有时候，为了让AI更好地完成任务，我们需要在提示中提供额外的背景信息或上下文。例如，让AI续写故事时，要提供前文内容；请AI写分析文章时，提供基本事实或数据来源。这些上下文相当于把AI"带入场景"，避免因为缺少信息而出现偏差或泛泛之谈。记住，除非是常识性的知识，否则AI并不知道你脑海中的隐含前提——你不提供，它就无从得知。

综上，在正式撰写提示词前，请务必明确任务的方方面面：知道自己要什么，以及AI能做什么、不能做什么。这种"认识世界"的准备，会让你的提示词有据可依、不跑题。此外，清晰的需求分析还有助于你选择正确的提示策略：比如简单任务可以直接零样本提示，而复杂任务可能需要 Few-Shot 示例或思维链等高级技巧（稍后介绍）。

在下一节中，我们将进入“改造世界”阶段，也就是根据上述分析，将需求转化为明确的AI指令，编写出高质量的提示词。


# 三. 改造世界：如何有效"指挥 AI"，提出清晰提示词

![](https://xiangyugongzuoliu.com/wp-content/uploads/2025/04/<EMAIL>)

经过上一阶段的充分准备，我们已经"认识"了需求与目标。现在，就进入"改造世界"阶段——也就是将你的需求转换成AI能够理解并执行的清晰指令。这里，“改造世界"意为用你的语言去影响AI的"世界状态”，引导它产出你想要的结果。

撰写提示词时，可以遵循与人沟通类似的原则，但要更加明确直白。AI 并非人类，它无法通过语气或上下文揣测隐含意图，因此我们需要像对计算机下指令一样精确，同时像对新人说明任务一样清楚。下面是一些关键的指导方针：

  1. **清晰明确，避免歧义** 。提示词的措辞要尽量具体，减少模棱两可的表述。尽可能用简单明了的句子说明你要AI做什么，不要让AI去猜你的意思。例如，不要说"帮我写点东西"，而要说"请写一段描述产品X优势的广告文案，面向首次购房的年轻人"。如果有容易混淆的词，可以适当解释或换种说法，确保AI理解唯一的含义。

  2. **尽可能提供充分的背景和细节** 。不要指望AI自动填补你没说的细节。研究指出，提供充足的上下文能够让AI更好地理解你的问题范围，减少歧义并提高相关性。包括任务相关的具体信息、条件和例子，都可以让AI更准确地按你的预期执行。当然，这并不意味着提示词越长越好——而是该详细的地方要详细，不相关的内容不必罗列。要平衡细节和简洁，既不要让提示过于简短缺信息，也避免无关细节让AI迷失重点。

  3. **指令要有逻辑和条理** 。对于复杂任务，结构化你的提示会大大提高AI产出内容的可控性和有序性。你可以在提示中分条列出要求或者分步描述，让AI严格按照顺序完成。比如：“第一步做…, 第二步…, 然后…”。如果是一系列问题或多个条件要求，使用项目符号或编号列出来。结构清晰的提示能防止AI的回答东拉西扯，有助于获得井井有条的输出。

  4. **明确要求输出的格式** 。如果你希望答案以某种形式呈现，一定要在提示中说明。例如，你想要一个列表、表格、分段的文章、对话剧本等等，都应直接告诉AI。例子表明，仅仅请求"告诉我不同类型的数据库"会得到杂乱回答，而如果指定"请用以下格式比较 NoSQL 和 SQL 数据库：- 用例 - 性能 - 可扩展性 - 成本"，就能得到清晰对比的输出。指定格式可以包括：输出是几个要点、每点多少字，是否需要标题、小结等等。这样的格式约束其实也是一种“微指令”，帮AI把答案限定在你需要的呈现方式上。

  5. **善用角色和口吻设定** 。你可以在提示中指定AI扮演某个角色或采取某种风格口吻回答，这会影响答案的措辞和专业程度。例如：“你现在是一名资深律师，用专业严谨的语气回答以下问题……”，和"想象你是一位幽默的段子手，写一段吐槽……“得到的回答风格截然不同。指定角色可以提供上下文背景，让AI更好地贴合你期望的风格。研究发现，AI在被赋予专家角色时，往往能产出更高级和贴切的回应。因此，不妨让AI"穿上马甲”，以合适的身份来完成任务。

  6. **包含必要的约束条件** 。这类似于给AI划定边界，包括内容上的禁忌或重点、长度限制、输出风格等。在提示中明确这些限制条件，能确保AI的输出符合你的硬性要求。举例来说，如果需要100字以内的短文、避免提及某品牌、或者要求内容适合儿童阅读，这些都应该事先写入提示。这样AI会遵守这些规则，避免踩雷或跑题。

  7. **考虑提供示例** 。如果你能给出你想要的输出形式的示例（哪怕简短的片段），让AI有参照，它会更容易明白你的期望。例如，你希望AI写一条风格类似你之前写过的微博，不妨把那条优秀的微博当做例子给它看："范例：…“然后说"现在请写类似风格的内容…”。又或者提供格式示例，比如教AI按照一定模板输出。示例在提示工程中被称为Few-Shot提示（稍后详述），是提升准确度的有效手段。当然，并非所有场景都需要示例，但在风格、格式要求较特殊时，加一个例子效果拔群。

按照以上原则，就可以把前一阶段整理好的需求要点，逐一清晰地体现在提示词中：说明角色和背景、明确任务和目标、给出细节要求和输出格式，并提供例子或参考。当你把这些都写进提示后，你就相当于给AI戴上了"眼罩"并指明前进方向——让它只关注你想要的目标。正如有专家所说：提供足够的情境和指令，相当于给AI的视野加上了遮挡板，只让它看见你想让它专注的目标。这样一来，AI的输出会更贴近你的预期。

在下一章节，我们将更加系统化地总结高质量提示词应包含的核心要素，并逐一讲解每个要素的作用和撰写要点。这些要素构成了一个通用的提示词框架，可以套用在多数内容创作场景中，帮助你快速构建完善的提示。


# 四. 翔宇提示词核心框架：角色、任务、目标、格式规范、示例

![翔宇工作流](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/翔宇背景-1160x464.png)

根据许多提示工程实践经验，一个有效的提示往往包含几个核心要素。不同专家可能提法略有不同，但总体来说，Role（角色）、Task（任务）、Goal（目标）、Context（上下文）、Constraints/Format（约束与格式）和Examples（示例）是经常提到的关键部分。其中任务是不可或缺的核心，而其他要素的补充可以让结果更加符合预期。许多成功的提示词案例表明，在提示中明确指明这些要素，AI 的回答将更贴合用户需求。

本节我们重点介绍角色、任务、目标、格式规范、示例这五个元素（其中上下文和约束可以融入角色/任务/格式中一起考虑）。掌握这些要素及其撰写方法，你就可以搭建出大多数场景下的提示词框架。


## 4.1 角色 \(Role\)


### 4.1.1 角色定义：

角色是指你希望AI模仿的身份、口吻或专业领域定位。通过在提示中指明AI的身份，你等于为其提供了一个行为视角。角色设定会影响AI回答的措辞风格和内容深度。


### 4.1.2 角色作用：

赋予AI一个角色有两个好处：其一，让回答风格更契合角色定位，从而更符合你的期望。例如，角色是"有经验的客服"，回答可能礼貌耐心；角色是"幽默的段子手"，回答就诙谐风趣。其二，限定AI的专业领域，让其回答时倾向调用该领域的知识和语气，从而提高内容的专业性和相关性。研究和实践都表明，AI被赋予专家角色时，往往给出更翔实深入的答案。


### 4.1.3 角色如何撰写：

在提示的开头，你可以直接描述角色，例如：“你是一名资深营养学家……”、“假设你是我的英语老师……”。角色描述尽量具体，包括身份头衔、经验水平和相关领域等。例子中，提示以“你是一名拥有15年分布式系统构建经验的高级架构师”开头，就很好地设定了AI的专业背景和资历水平。你也可以加入角色的口吻或性格，比如"幽默的美食评论家"或"严谨的学术研究员"，让AI在回答时模拟相应的语气。


### 4.1.4 角色注意事项：

角色一旦设定，AI会尽量沿着这个身份来组织语言。但是要避免给出互相矛盾的角色信息（比如同时说"你是一位严肃的喜剧演员"就让AI无所适从）。此外，角色选择要服务于你的内容目标：如果你需要通俗易懂，就算主题专业也可以设定一个"善于给外行解释复杂概念的专家"这类角色，从而让回答浅显易懂又不失专业。


### 4.1.5 角色示例：

❌ 不设定角色： “写一篇介绍量子计算的文章。”（AI 不知道面向谁写，专业度和风格难以拿捏，可能产出晦涩难懂的内容）✅ 设定角色： “你是一位经验丰富的科普作家，擅长用生动比喻解释深奥概念。请写一篇给普通读者看的科普文章，主题是量子计算。”（明确了身份和擅长风格，AI 会以通俗易懂的方式来写


## 4.2 任务 \(Task\)


### 4.2.1 任务定义：

任务即你要AI执行的具体指令或动作，是提示词的核心部分。它回答了"让AI做什么"的问题，是提示中不可或缺的要素。


### 4.2.2 任务作用：

明确任务可以让AI把焦点放在正确的工作内容上，从而提供有针对性的回答。如果任务表述不清，AI 可能会产生与你需求不符的输出或漫无边际地闲聊。清晰地定义任务，相当于给AI下达了一道准确的"工作命令"。


### 4.2.3 如何撰写任务：

用一句话直接了当地说明你希望AI完成的任务。动词要明确，比如"撰写"、“列出”、“解释”、“比较”、“提供建议"等。避免只写一个模糊的主题，而是要写具体的指令和产出形式。例如，不要只提主题"Python 编程”，而要说"帮我检查这段 Python 代码并优化性能"。再比如中，坏的示例是“帮我看下我的 Python 代码。”（任务含糊），好的示例则改为"审查这个 Python 函数的性能优化。重点降低内存占用并优化时间复杂度，同时确保向后兼容。"——这样AI就清楚该做代码审查和优化，而且知道具体关注点。


### 4.2.4 任务要点：

  - 尽量具体：任务描述中应尽量包含内容的范围或数量要求。例如"提供五条建议"、"写一个200字左右的引言"等，让AI知道输出的规模。

  - 单一且聚焦：每个提示最好只包含一个主要任务。不要在一句提示里让AI做过多不相关的事情，否则它可能混为一谈。如果确实需要AI完成多个步骤，可以显式分步骤描述，或者考虑多轮对话分别要求。

  - 可执行：确认这个任务是AI基于其训练知识可以完成的。不要让AI去执行物理世界的动作（如"帮我搬桌子"）或者去预测股票这类超出能力的任务。任务必须是语言或知识层面的，否则AI只能编造。

  - 包含对象：如果任务涉及对某材料操作，要提供材料或指明来源。例如"总结下面这篇文章：<文章文本>"。总之，任务要有受词，AI才知道对什么进行操作。


### 4.2.5 任务示例：

  - 任务：“列出改进睡眠质量的5个方法。”（动作是列出，对象是方法，数量5个）

  - 任务：“将以下产品描述翻译成英文，并优化语句使其更吸引人：<中文描述>。”（动作是翻译和润色，对象是提供的中文描述）

  - 任务：“比较A产品和B产品的异同，写成一段不少于100字的评测。”（动作是比较，形式是评测，长度>100字）


## 4.3 目标 \(Goal\)


### 4.3.1 目标定义：

目标指的是你希望通过这次AI生成内容所达成的最终目的或希望侧重的成果要点。它关注输出要实现什么效果/满足什么需求，从而为AI的创作指明方向。

很多时候，任务和目标可以写在一起（任务描述中带出目标）。但将目标单独强调有助于AI把握回答的重点，确保不偏离你的意图。


### 4.3.2 目标作用：

明确目标能让AI的输出更加贴合你的需求，并在回答时有所侧重，不至于答非所问或泛泛而谈。对于创作类任务，目标通常体现为希望作品达到的效果；对于问答类任务，目标可能是你要得到的特定信息。清晰的目标就像给AI设定了评分标准，提醒它"什么才是好答案"。


### 4.3.3 如何撰写目标：

你可以在提示中加入一句描述目标的语句。例如：“目的是……”，或者"以……为最终目标"。目标可以是内容层面的，也可以是读者反应层面的。比如：“文章的目标是激励读者采取环保行动”，“回答的目标是给出可行的解决方案”。也可以更直接一点，比如"确保提供的建议切实可行并易于执行"——这其实也是对AI输出的一种质量要求。

目标往往回答"为什么要做这件事"：你让AI执行这个任务的原因是什么，希望达到什么效果。把这个原因告诉AI，可以让它在生成内容时有所取舍：哪些信息该突出，哪些可以略写。中提到，设定明确的目标可以使AI的响应更有针对性，并帮助你自己不被无关信息干扰。


### 4.3.4 目标示例：

假设任务是"为产品X写一段推广文案"。可能的目标包括"吸引潜在客户关注并产生购买兴趣"。我们可以将提示写成：“你是一名市场营销文案专家，请为产品X撰写一段推广文案，突出其独特卖点和用户收益。目标是吸引目标用户的兴趣并引导他们产生购买意愿。” 这里加粗部分就是明确告诉AI此文案最终想要达到的目的。AI 在写文案时就会围绕吸引兴趣和引导购买这个目标来组织语言，比如强调产品卖点、加入号召性用语等。

再比如写文章场景：任务是"介绍Python装饰器"，目标可以是"让初学者也能看懂并应用基础的装饰器"。那么提示可写："……写一篇教程介绍Python的装饰器概念和用法，目标是让没有经验的初学者也能理解并掌握基本使用方法。" 这样AI会注意用浅显易懂的方式解释，并确保涵盖基础用法示例以实现教学目标。

总之，目标为AI提供了评价标准：回答是否达成了预期的效果。如果没有明确目标，AI可能完成了任务表面要求但不见得满足你的深层需求。


## 4.4 格式规范 \(Format & Constraints\)


### 4.4.1 格式规范定义：

格式规范指对输出形式的要求，包括排版格式、结构层次，以及其他必须遵守的约束（如字数、风格、是否包含项目符号等）。这部分内容告诉AI，回答应该以什么样的形态呈现。


### 4.4.2 格式规范作用：

通过指定格式和约束，我们可以大幅提升AI输出的实用性和可控性。实例说明了这一点：提示要求"务必提供完整、有文档的代码"，结果AI就遵循提供了完整代码而非片段。明确的格式要求可以避免AI输出不符合你使用场景的内容。例如，要用表格比较信息、要逐条列出要点、要以markdown格式输出等等，这些只有在提示里说清楚，AI才会照做。


### 4.4.3 如何撰写格式规范：

直接在提示中列出格式和其他限制条件。例如：

  - 长度要求： “文章长度约1000字”，“每条建议不超过50字”，或者"回答控制在三段以内"。

  - 结构要求： 可以要求分段、列点、表格形式。例如：“请分三个段落回答”，“用Markdown表格列出比较结果”，“逐条列出1、2、3…”。如果希望包含标题、小标题，也可说明，例如：“提供一个题目，并将内容分为引言、主体、结论三部分”。

  - 风格/语气： 这也可视为一种约束。“语气活泼幽默”，“用正式商务风格撰写”，"语言简洁明了，避免专业术语"等等。

  - 内容约束： 哪些点必须提到，哪些避免提及。例如：“文中需包含实际案例”，“不要提及竞争对手品牌A”。

建议将这些要求单独列行写出，或用分号隔开，让AI一目了然。格式规范通常放在提示的后半部分，在描述完任务和目标后，附加一句或几句"要求：…"。


### 4.4.4 格式规范示例：

"请撰写一篇介绍产品X的新功能的新闻稿，包含以下要求：

  - 结构： 标题+导语+正文，正文分2段。

  - 长度： 全文不超过300字。

  - 风格： 行文简洁客观，以新闻报道口吻撰写。

  - 其他： 第一段包含发布日期和地点。"

通过这样的格式规范，AI 会严格按照标题、导语、正文的结构来写，并控制字数和口吻。又如在技术问答中，你可以要求：“请给出步骤分项列表，每步尽量控制在一两句话。”，那么AI会输出一个清晰的步骤列表而不是一整段叙述。这使你得到的结果更方便后续使用，减少编辑加工的工作量。

需要注意的是，如果对格式没有特殊要求，也不必生造限制。但一旦有偏好，一定要清楚地告诉AI，否则它会按照默认习惯生成（比如ChatGPT常会给出额外的客套前言，如果不想要就要在提示里排除）。定义格式规范，就是在为AI的输出上"画框"，框画得越清晰，AI的内容就越不会越界。


## 4.5 示例 \(Examples\)


### 4.5.1 示例定义：

示例即在提示中提供给AI的参考样例，让它依据这些例子来理解你的要求或模仿输出风格。这是 Few-Shot 提示的核心，即在正式任务前给模型展示一到多个范例。


### 4.5.2 示例作用：

示例可以极大提升AI对任务的理解，尤其在格式和风格方面。指出，通过在提示中加入示例、约束和期望结果，可以引导AI产出更准确、有用的答案。这有点类似"示范教学"——给AI看一个例子，再让它举一反三。

示例的用途主要有两种：一是提供输出格式或答案风格的样例，让AI按类似形式回答；二是提供任务的参考实例（包括输入->输出对），让AI根据这些少量样本学习如何从输入得到输出。


### 4.5.3 如何撰写示例：

  - 作为格式样例： 可以在提示的结尾用引号或特殊标记包含一个假想的"范例输出"。例如：“范例输出：『嗨！我是AI，很高兴回答你的问题！』（这是语气范例）现在请以类似的语气回答下列问题……”。或者提供一段你期望的答案样子，然后说"仿照以上风格…"。注意注明这是范例，以防AI误以为是正式回答内容的一部分。

最后AI会续写答案。这种方法在提示工程中称为Few-Shot 提示，能够明显改善模型在特定任务上的性能。


### 4.5.4 示例注意事项：

示例要简短且典型，不宜过多过长，否则会占用提示长度并可能使AI关注了不必要的细节。1到3个示例通常足够。示例内容要准确，因为模型可能会延续示例中的错误。此外，要确保示例和实际任务高度相关。在Few-Shot情况下，示例应当和实际要求是同一类型的任务，让AI真正起到"现学现用"的效果。


### 4.5.5 示例使用示例：

本文前面的讲解已经穿插了一些"Bad Prompt" vs "Good Prompt"的例子，实际上那也是在给你展示如何写提示词的示例。对于AI也是一样，比如我们给模型一个例子：

“不良提示： 写一首诗。优秀提示： 你是一位浪漫主义风格的诗人，请以"春天"为主题写一首八行的抒情诗，使用丰富的意象和隐喻。”

这样的示例放在提示中，AI 自然明白应当产出后一种风格和格式的内容，而避免简短随意的回答。


## 4.6 提示词模板：

综合以上要素，我们可以把它们组合起来形成提示词模板：

    [角色]，请你根据以下要求执行任务：[任务]。
    目标：[希望实现的目标效果]。
    要求：
    输出格式/结构：[格式规范细则]
    语气风格：[规范细则]
    其他约束：[规范细则]。
    如果需要，可参考这个例子：[示例]。

这样的框架并非所有部分每次都要写全，但它提供了一个清单，帮助你检查提示是否完整涵盖了必要的信息。实际应用时，你可以根据场景增删要素。

例如，要AI写小说创意而不限制它发挥，可能就不需要太多约束格式；要AI列清单则必须注明格式等。灵活运用这些要素，能让你的提示既面面俱到又简洁有效。下一节我们将介绍几种在实际应用中常见的提示词范式，它们是很多高级用户在这些核心要素基础上发展出的技巧套路。


# 五. 提示词写作建议与常见错误

即使了解了上述原则，新手在撰写提示词时仍可能犯一些典型错误。以下归纳常见的几个错误及应对方法，帮助你在实践中避开这些坑，提高提示效果：

  1. **过度复杂** ：提示包含太多要求、主题发散，导致 AI 无所适从。过于宽泛繁杂的请求会让模型困惑，输出可能杂乱或偏题。**应对** ：尽量聚焦单一主题，必要时分解任务。保证每条提示句子清晰简洁，不要一条中塞入过多子任务。

  2. **细节不足** ：给的信息不够，提示太笼统。如果提示缺乏背景和细节，AI 往往只能给出泛泛而谈的答案。**应对** ：提供必要的上下文和明确细节。包括场景、目标、限制等，让 AI 有据可循。例如别只说“给些写作建议”，要具体成“针对科技博客写作，提供 5 条提高可读性的建议”。

  3. **超出 AI 能力** ：要求 AI 做超出其训练知识或逻辑能力范围的事。例如让它预测未来数据、执行物理操作或提供法律/医学决定。这会导致 AI 编造答案或直接无法完成。**应对** ：了解模型的局限。不要让 AI 去完成需要最新资讯（除非接入检索）、高度专业判断（除非辅助参考）、或者违反已知限制的任务。可以转而调整问题，如改问“据现有趋势分析……”而非让 AI 直接预测结果。

  4. **忽视受众和目的** ：没有在提示中体现出内容面向的读者或用途，AI 给出的回答就可能不合适。例如让 AI 解释概念，却没说明听众是小学生还是专业人士，结果可能太深奥或太浅薄。**应对** ：在提示中交代受众/场景。例如：“用面向小白的口吻解释……”，“以投资人角度撰写建议……”。确保回答风格和深度符合你的预期用途。

  5. **措辞含糊** ：提示中使用了模棱两可的词语或不明确的指代，使 AI 理解产生偏差。含糊的要求会让 AI 摸不清你真正想要什么。**应对** ：具体且精确地描述。必要时定义你指的是哪个方面。例如，不要说“讲讲这个产品”，改为“介绍这个产品的主要功能和三大优点”。如果某词容易误解，可以换更明确的说法。

  6. **忽略结构** ：提示写成一整段长句或杂乱无章，没有逻辑顺序。这容易导致 AI 的回答也是混乱的。**应对** ：整理提示结构。使用项目符号、序号、分段来提示 AI 分别回答。逻辑复杂的问题按步骤列出。结构化的提示能换来结构化的答案。

  7. **不迭代优化** ：一遍提示不满意就放弃，觉得“AI 不行”。实际上初始回答不理想时，完全可以通过追问或重写提示改进。**应对** ：学会迭代。先阅读 AI 的初步回答，从中看出哪里不符期望，然后调整提示再问。例如：“请针对上面第 3 点再详细展开”，或者修改原提示加入更多要求。多几轮对话，经不断 refine，往往能得到令人满意的结果。把 AI 当作可以反馈调教的助手，而非一次性给出完美答案的神谕。

以上几点是新手经常遇到的问题，对应的解决思路也已经给出。归纳起来就是：明确、具体、有条理，并且不断完善。另外，保持对话的引导和控制也很重要——如果 AI 跑偏了，就把话题拉回来；如果答非所问，就直接指出重新要求。大模型是语言工具，不会介意你对它“严厉”一些。

作为补充，再提供几点实用的提示建议：

  - **善用系统消息/开场白** ：在与 ChatGPT 这类对话模型交互时，你可以先用一段话设定整体基调（相当于“系统提示”）。例如一开始声明：“我们的目标是完成 XX，请你遵循以下规则…”。这可以在对话一开始就奠定风格和边界。之后再具体提问执行任务。

  - **一步一问 vs. 一次成型** ：针对复杂任务，不妨尝试将对话拆成多步。例如先让 AI 给提纲，再让它根据提纲写全文。很多时候分步更容易得到满意结果，比一开始就要它写长文然后大改来得高效。

  - **避免偏见引导** ：提问措辞尽量中立客观，不要暗示 AI 某种主观倾向（除非你的目的就是这个）。否则 AI 可能迎合你的措辞输出偏颇内容。比如问：“为什么 XX 这么糟糕？”不如问“请客观评价 XX 的优缺点？”来得中立全面。

  - **注意 OpenAI 使用政策** ：有些请求可能触发 AI 的内容限制。写提示时尽量避免违规词汇和非法要求，否则 AI 会拒答。这方面要遵守平台规范。

了解并实践这些建议，你将能写出更高质量的提示词，最大程度地发挥 AI 助手的价值。接下来，我们通过一个具体的中文实操案例，演示从需求分析到提示撰写、再到结果调整的全过程，加深对以上知识的理解。


# 六. 提示词撰写实操案例

让我们一起走完从需求到提示再到结果的实际流程。假设场景如下：


## **6.1 场景** ：

你是一名公众号的新手作者，想写一篇关于“繁忙上班族如何保持健康饮食”的推文。希望文章提供实用的饮食建议，语言轻松亲切，字数在 1000 字左右，并以条目形式列出具体建议，方便读者阅读。

现在我们运用“认识世界”的思路分析需求，并据此撰写提示词：


## **6.2 需求分析（认识世界）** ：

**主题** ：忙碌上班族的健康饮食。

**目标** ：给读者实用可行的建议，帮助他们在繁忙工作中也能吃得健康。希望读者看完能付诸行动改善饮食习惯。

**受众** ：城市里朝九晚五的上班族，工作繁忙，缺少时间关注饮食健康。这些读者可能缺乏营养知识，需要简明直接的指导。

**内容要点** ：可能包含准备健康便当、选择健康零食、合理规划三餐、饮水等方面的建议。希望有 10 条左右具体建议，每条都有简要说明。

**风格** ：口吻上要友好、有代入感，不能太生硬说教。像朋友分享经验那样，轻松鼓励。

**格式** ：倾向于“清单式”文章，即先有一两段引言，然后列出编号的建议条目。

**约束** ：字数约 1000 字；内容要贴近职场生活场景；不要使用过于专业的术语，要通俗易懂。

**AI 能力** ：这属于生活常识类内容，AI 应能胜任。我们不需要最新数据，只要普遍适用的建议，所以无需联网查询。


## **6.3 提示词撰写（改造世界）** ：

根据以上分析，我们开始编写提示词，涵盖角色、任务、目标、格式等要素。逐步构建可能是这样的：

  - **角色** ：为了让文风更贴近，我们可以让 AI 扮演一位“营养师”或“健康生活达人”。这里用“资深营养师”以确保建议科学靠谱，但同时要求语气亲和。

  - **任务** ：撰写推文文章。

  - **目标** ：提供 10 条实用建议，帮助上班族改善饮食习惯。

  - **受众/风格** ：已经分析，在提示中强调“忙碌的上班族”“通俗易懂”“轻松语气”。

  - **格式** ：明确要列出编号清单，每条有解释；总字数在 1000 字左右。

  - **其他** ：可以邀请读者行动，如结尾给出鼓励。

综合成最终提示词如下（中英对照只是讲解，这里实际我们用中文提示）：

       你是一名经验丰富的营养师兼健康生活博主，  
       任务：请为忙碌的上班族撰写一篇关于保持健康饮食的微信公众号文章。  
       目标：提供 10 条切实可行的饮食建议，帮助读者即使工作繁忙也能健康饮食。  
       要求：
       - 格式：先用一段引言吸引读者，然后以编号列表的形式列出 10 条建议，每条用加粗标题给出概括，再用简短语句阐述理由或方法。
       - 风格：语气轻松亲切，如同朋友分享；内容通俗易懂，避免过专业术语。
       - 篇幅：文章约 1000 字。

这个提示几乎把文章的写作要求都穷尽了。角色“营养师兼博主”保证内容专业又接地气，格式和风格都有清晰指示，AI 基本上只需发挥具体写作即可。


## **6.4 获取 AI 输出并验证** ：

将上述提示词提交给 ChatGPT，我们预期会得到一篇带有 10 条建议的文章，每条建议加粗标题并有描述，前有引言后有结尾鼓励语。我们需要做的是检查输出是否符合要求：

  - 有没有 10 条？

  - 条目是否编号并加粗？

  - 内容是否贴合“忙碌上班族”场景？

  - 有没有跑题或者给出不切实际的建议？

  - 语气是否友好通俗？

  - 有没有用太多专业词？

  - 字数是否在 1000 字左右？（如果明显超长或太短，可要求它扩写或压缩）

若有不满足的地方，我们可以迭代提示。例如如果只有 8 条建议，我们可以追问“请补充到 10 条”；如果某条建议不够具体，我们可以让 AI“将第 5 条展开举例说明”。通过交互，把文章调整到满意为止。


## 6.5 提示词结果：

最终，我们得到一篇题为《忙碌上班族也能吃得健康：10 个贴心小建议》的推文，里面的每条建议例如“1. 利用周末做好膳食准备：周末花一点时间准备健康食材……”“2. 常备健康零食：在办公室抽屉放些坚果和水果……”，整体语气温馨，读者读完会感到这些建议很实用且容易尝试。

通过这个案例，你可以看到：明确的提示词几乎可以让 AI 一次出稿就很接近需求，大大减少了后期修改。而这一切的前提正是我们在撰写提示词时花心思明确了需求、仔细规定了输出格式和侧重点。

希望这个实例给你直观的感受：编写提示词就像给作者下任务指令，你当的“策划编辑”角色越称职，AI 这个“写手”交出的初稿就越让你省心。

接下来，为了便于你举一反三，我们按照常见的内容创作场景整理了一些提示词模板。你可以直接套用或稍作修改，用在自己的创作实践中。


# 七. 各类内容创作场景的提示词模板

在不同的内容创作场景下，提示词的侧重点和写法略有不同。下面提供几种常见场景的提示词模板，包括文章写作、视频脚本、社交媒体文案。这些模板旨在帮你快速上手，你可以根据具体需求替换其中的关键信息。


## 7.1 场景一：文章写作

无论是写博客、公众号推文还是新闻稿，一般都需要明确主题、受众和文章结构。下面是一个通用的文章写作提示模板：

    你是一位资深的[领域]作者。
    任务：请撰写一篇关于「[文章主题]」的文章。
    目标：[文章目的，例如 “全面介绍…/提供实用技巧…/分析…等”]。
    要求：
    受众：面向[目标读者，如“小白读者/有经验读者”等]，确保内容[浅显易懂/深入专业]。
    结构：文章需包含[引言/背景]、[几个要点小节]和[结论/总结]。
    风格：[风格要求，如“幽默风趣/正式客观/热情鼓舞”等]。
    篇幅：约[字数]字。

示例填充：

    你是一位资深的科技评论作者。
    任务：请撰写一篇关于「人工智能如何改变未来教育」的文章。
    目标：分析AI技术在教育领域的应用现状和未来趋势，并提出可能的挑战与对策。
    要求：
    受众：面向对教育科技感兴趣的普通读者，确保内容通俗易懂且具有启发性。
    结构：文章需包含引言（说明话题背景），主体（分几个小标题讨论AI在教学、自适应学习、教育公平等方面的影响），结论（总结观点并展望未来）。
    风格：论述风格，保持客观理性，同时结合实例使内容生动。
    篇幅：约1500字。

使用以上提示，AI 很可能会产出一篇结构清晰、层次分明的文章，涵盖AI教育应用的各方面。这比起仅仅输入“写一篇人工智能与教育的文章”要强得多。


## 7.2 场景二：视频脚本创作

为视频（如YouTube或哔哩哔哩）撰写脚本，需要考虑画面节奏和口语风格，以及是否包含分镜头、音效提示等。提示词可突出脚本格式和视听元素。

    你是一名富有创意的[风格]视频脚本编剧。
    任务：为一段时长约[分钟]的[类型，如“解说/剧情/教学”]视频撰写脚本，主题是「[主题]」。
    目标：[视频的目的，例如“向观众科普…/讲述一个…的故事/推广…产品”等]。
    要求：
    结构：脚本包含[开场白]、[主体部分]、[结尾总结/Call-to-action]。分镜头清晰，每个镜头或场景单独描述。
    风格：语言上尽量口语化，符合[幽默诙谐/紧张悬疑/温馨励志等]的风格基调。对白和画外音都要自然流畅。
    其他：如需要，可加入[音效/画面切换]提示，例如“（转场音效）”或“【出现字幕】”等。
    时长控制：脚本全文大约[字数]字，以符合[分钟]左右的视频长度。

示例填充：

    你是一名富有创意的幽默风格视频脚本编剧。
    任务：为一段时长约5分钟的科普解说视频撰写脚本，主题是「蜜蜂如何酿造蜂蜜」。
    目标：用通俗有趣的方式向观众科普蜜蜂酿蜜的过程。
    要求：
    结构：脚本包含开场白（直接抛出话题，引发兴趣），主体部分（按照时间顺序描述蜜蜂采蜜、酿蜜的步骤，有两个小节分别讲采花粉和酿造成蜜），结尾（总结并引出蜜蜂对生态的重要性）。采用分镜头形式，每个镜头有简短描述。
    风格：语言幽默风趣，略带拟人化口吻，好像蜜蜂在和观众“对话”。对白和解说词口语化，避免生硬科普腔。
    其他：加入适当音效/画面提示，如“（嗡嗡声）镜头展示蜜蜂在花丛中忙碌”。
    时长控制：脚本约800字，符合5分钟讲解的语速。

这个提示确保了脚本的格式和语气。生成的脚本可能会按照镜头来组织内容，里面有诙谐的旁白，能直接用于配音和剪辑，大大减少你修改格式的工作。


## 7.3 场景三：社交媒体文案

社交媒体文案（如微博、推特、朋友圈、小红书等）通常字数有限，要求言简意赅、有吸引力，并且可能包含话题标签或表情符号以增强表现力。撰写此类提示时，应强调平台风格和主题创意。

    你是一位擅长打造爆款内容的社交媒体文案专家。
    任务：为[平台，如微博/推特/朋友圈]撰写一则[用途，如“新品推广/活动预告/心情分享”]文案，主题是「[主题]」。
    目标：引起[粉丝/用户]的兴趣和互动，提高[转发/点赞/点击]率。
    要求：
    长度：不超过[字符数]（含标点和表情）。
    语言风格：[活泼俏皮/简洁有力/感性煽情等]，符合平台调性。
    必须包含：[品牌名称/@账号] 和 [相关话题标签]。
    如果合适，可加入表情符号增强语气，但不要过度。
    结尾附上[号召行动/提问引导互动]（如“你怎么看？”或“快来参与吧！”）。

示例填充：

    你是一位擅长打造爆款内容的社交媒体文案专家。
    任务：为微博撰写一则新品推广文案，主题是「智能保温杯上市」。
    目标：引起粉丝的兴趣和互动，提高新品曝光和链接点击率。
    要求：
    长度：不超过140字（含标点和表情）。
    语言风格：活泼亲切，一句话突出卖点，符合微博平台快节奏阅读。
    必须包含：品牌名称和话题标签#智能保温杯#。
    适当加入1-2个表情符号增强语气，例如惊喜或疑问的表情。
    结尾附上号召行动，如“点击链接了解更多”。

这个提示会让AI输出一条精炼的微博文案，比如：“新品上线！XX品牌智能保温杯来了🎉一键控温，24小时暖心相伴☕\#智能保温杯\
# 这么懂你的杯子，你爱了吗？戳链接了解👉 \[短链接\]”

可以看到，包含了品牌、卖点、话题和表情还有引导，非常符合微博的风格和限制。

以上模板涵盖了常见的内容创作类型。当然，你在实际使用中可以根据需要对模板做调整。如有的场景可能需要英文输出，只要在提示中用英文写要求或明确指出输出语言即可。


# 八. 总结：

最后，务必记住：提示词写作本身也是一个创作和优化的过程。本教程提供的原则、技巧和模板，目的在于帮助你跨过新手期的摸索，少走弯路。但随着你实践增多，一定会形成自己的一套风格和偏好。不要害怕尝试新方法，也不要拘泥于某个模板——灵活运用，勤于迭代，才能真正把AI这把“创作之剑”舞得挥洒自如。

翔宇希望这篇万字长文的超详实教程能帮助你从零开始掌握提示词撰写的精髓。在内容创作的道路上，愿AI成为你得心应手的创作伙伴，释放你的灵感，创造出更多优质、有趣的内容！

![翔宇工作流](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/翔宇工作流.png)