---
title: "Make中文教程：项目数据类型（Item data types）"
created_at: "2024-09-01T10:06:22.000000Z"
uuid: "12d34904-9c2e-4d51-9247-7d8644c544f8"
tags: ['Make基础教程']
---

# Make中文教程：项目数据类型（Item data types）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于项目数据类型的教程。

**教程简介**  ：本教程介绍了Make.com平台中的不同类型的数据项目，包括文本、数字、布尔值、日期、时间、缓冲区、集合和数组等，并且解释了它们的使用和验证方法。

**学习目标**  ：了解和掌握Make.com中各种项目数据类型及其应用场景，理解如何在自动化流程中使用和验证这些数据类型，以提高流程的准确性和效率。

**学习内容**  ：

  - 项目类型综述：文本、数字、布尔、日期、时间、缓冲区、集合和数组。

  - 不同数据类型的输入格式和验证规则。

  - 数据类型之间的转换和使用示例。

**概念理解**  ：

  - 文本（字符串）：包含字母、数字和特殊字符的数据。

  - 数字：具有特定范围和格式的数值数据。

  - 布尔值：只有真或假两种可能值的数据。

  - 日期和时间：以特定格式表示的时间数据。

  - 缓冲区：用于传输文件内容的二进制数据。

  - 集合和数组：包含多个子项目或同类值的数据结构。

**任务练习**  ：

  1. 创建一个包含文本、数字和布尔值的项目数据包，验证数据格式。

  2. 输入并格式化一个ISO 8601日期，查看其在个人资料设置时区中的显示。

  3. 添加一个带附件的电子邮件模块，返回附件数组并读取每个附件的属性。


## **一、基本数据类型解释**


### **1\. 文本类型（Text/String）**

    定义：包含字母、数字和特殊字符的数据
    例如：姓名、地址、描述文字
    特点：有长度限制，需要验证

![](https://static.xiaobot.net/file/2024-11-18/720345/a807e6d25ec6a1adf7ea5be79b8519f9.png)


### **2\. 数字类型（Number）**

    定义：纯数字数据
    例如：价格、数量、编号
    特点：可以设定最大/最小值范围


### **3\. 布尔类型（Boolean）**

    定义：只有两种可能值的数据类型
    值域：true(是) 或 false(否)
    应用：开关选项、条件判断

![](https://static.xiaobot.net/file/2024-11-18/720345/8137ad4029c36be52222dc7ba34f92e7.png)


### **4\. 日期类型（Date）**

    定义：表示具体日期时间的数据
    格式：ISO 8601标准（2015-09-18T11:58Z）
    特点：可调整时区，支持日历选择

![](https://static.xiaobot.net/file/2024-11-18/720345/6972f489cd22339c6acc0b10fc2a7bf7.png)


### **5\. 时间类型（Time）**

    定义：专门表示时间的数据类型
    格式：时:分:秒（14:03:52）
    用途：精确时间记录


### **6\. 二进制数据（Buffer）**

    定义：文件内容的原始形式
    应用：图片、视频、文档等文件
    特点：可自动转换文本数据


## **二、复杂数据类型**


### **1\. 集合（Collection）**

    定义：多个子项组成的数据结构
    例子：邮件发件人信息
    {
        姓名: "张三",
        邮箱: "<EMAIL>"
    }


### **2\. 数组（Array）**

    定义：相同类型数据的有序集合
    例子：邮件附件列表
    [
        {文件1: "报告.pdf"},
        {文件2: "图片.jpg"}
    ]


## **三、数据验证机制**


### **1\. 验证规则**

    - 文本长度限制
    - 数字范围检查
    - 日期格式验证
    - 必填项检查


### **2\. 错误处理**

    情况：数据不符合要求
    结果：触发DataError
    例子：Twitter文字超280字符


## **四、实际应用案例**


### **1\. 表单数据处理**

    场景：收集用户信息
    数据类型：
    - 姓名（文本）
    - 年龄（数字）
    - 是否学生（布尔）
    - 出生日期（日期）


### **2\. 文件上传处理**

场景：处理用户上传文件 涉及类型：

  - 文件名（文本）

  - 文件大小（数字）

  - 文件内容（Buffer）

  - 上传时间（日期时间）

记住：了解数据类型就像认识不同的容器，知道每种容器适合装什么，才能正确存储和处理数据！


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/0adc415f6ef6df91c228725de05da2e8.png)