---
title: "Make中文教程：PDF4me"
created_at: "2024-09-01T03:06:33.000000Z"
uuid: "06054fad-16b5-4841-9bde-8e9fbf4c795d"
tags: ['Make基础教程']
---

# Make中文教程：PDF4me

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于PDF4me教程的教程。

  1. **教程简介** ：本教程介绍如何在Make.com上使用PDF4me模块，包括文件转换、PDF操作、条形码操作、从PDF转换、PDF安全设置和文档生成等功能，并指导如何连接PDF4me账户。

  2. **学习目标** ：学习在Make.com上使用PDF4me模块的各种功能，实现PDF文件的多种操作和转换，包括创建、编辑、拆分、合并、添加水印、签署、OCR等，并掌握模块连接和配置方法。

  3. **学习内容** ：教程内容涵盖PDF4me与Make的连接方法，各种PDF操作如转换、压缩、添加水印、旋转、合并、提取页面、数字签名、条形码操作、OCR、添加和删除密码、邮件合并及表单填写等功能。

  4. **概念理解** ：

     - **情景（Scenario）** ：Make中的自动化工作流程，包含触发器和操作。

     - **API Token** ：用于身份验证和授权的密钥，确保PDF4me模块与Make正确连接。

     - **OCR（光学字符识别）** ：将扫描图像或文档中的文字转换为可编辑文本。

  5. **任务练习** ：创建一个Make情景，连接PDF4me模块，完成以下操作：将一个HTML文件转换为PDF，添加文本水印，旋转PDF页面，最后提取PDF中的特定页面，并生成文档报告。


# **PDF4me**

使用 Make 中的 PDF4me 模块,您可以:

  - 转换为 PDF,将 HTML 转换为 PDF,压缩 PDF,向 PDF 添加文本和图像水印,拆分 PDF,根据条形码和文本拆分 PDF,提取页面,删除页面,旋转页面,合并多个 PDF,合并 PDF 叠加,向 PDF 添加条形码,向 PDF 添加边距,创建条形码,以及检索文档信息

  - 将 PDF 转换为 Word 和 Excel，从 PDF 创建图像，以及 PDF OCR

  - 添加和删除 PDF 密码,创建 PDF/A

  - 邮件合并,使用数据文本\(文档列表和单个文档\),生成文档,并填写 PDF 表单

要开始使用 PDF4me,请在 pdf4me.com 创建一个帐户。

![](https://static.xiaobot.net/file/2024-11-26/720345/c8b0bdf1c89571733d81ed80e612286c.png)


## **  连接 PDF4me 以生成**

要连接 PDF4me 应用程序,您需要从您的 PDF4me 开发者账户获取令牌值,并将其插入到 PDF4me 模块场景中。

  1. 登录您的 PDF4me 开发者账户。

  2. 点击个人头像图标 > 账户设置 > API 并复制主密钥到安全的地方。

![](https://static.xiaobot.net/file/2024-11-26/720345/fe498d1d8262d4b7a683da2ea388265f.png)
  3. 可选:您可以通过创建应用程序来生成令牌。点击"创建应用程序">"输入所需的详细信息">"创建应用程序"。

  4. 点击键并将主键复制到安全的地方。

  5. 登录您的 Make 帐户,插入一个 PDF4me 模块场景,然后单击创建连接。

  6. 在"连接名称"字段中输入连接的名称。

  7. 在代币字段中输入在步骤 2 中复制的详细信息,然后单击保存。

![](https://static.xiaobot.net/file/2024-11-26/720345/f5168ce721ca29c8e4a0359f0a4a9429.png)

您已成功建立连接。您现在可以编辑您的场景并添加更多的 PDF4me 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤操作。


## **  构建 PDF4me 方案**

连接应用程序后,您可以执行以下操作:

**  转换为 PDF**

  - **  转换为 PDF**

  - **  将 HTML 转换为 PDF**

  - **  将 URL 转换为 PDF**

  - **  将 Markdown 转换为 PDF**

**  功能**

  - **  压缩 PDF**

  - **  线性化 PDF**

  - **为 PDF 添加文字水印**

  - **给 PDF 文件添加图片水印**

  - **  旋转页面**

  - **旋转 PDF 单页**

  - **  合并多个 PDF 文件**

  - **  合并 2 个 PDF 文件**

  - **  合并 PDF 重叠**

  - **  获取文档信息**

  - **  签署 PDF**

  - **  修理 PDF**

  - **添加页码到 PDF**

  - **发送文档至 PDF4me-Workflow**

  - **  从 PDF 创建图像**

  - **  提取页面**

  - **  删除页面**

  - **  解析文档**

  - **  分类文档**

**  条形码**

  - **  给 PDF 添加条形码**

  - **  创建条码**

  - **  读取条形码**

  - **  创建瑞士 QR 账单**

  - **  瑞士 QR 账单**

**  从 PDF 转换**

  - **  PDF 转 Word**

  - **  PDF 转 Excel**

  - **  PDF 转 PowerPoint**

  - **  PDF 光学字符识别**

**  安全**

  - **  给 PDF 添加密码**

  - **  从 PDF 中移除密码**

  - **  创建 PDF/A**

 生成文档

  - **使用数据文本的邮件合并\(文档列表\)**

  - **带数据的邮件合并（单个文档）**

  - **  填写 PDF 表单**

  - **  生成文档**

  - **  生成文档**

 分裂

  - **  拆分 PDF**

  - **  条形码分割**

  - **  按文本分割 PDF**

  - **  由瑞士 QR 码分割**


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-26/720345/75a6d322e52cd0a6c0e0ccd95364e864.png)