---
title: "Make中文教程：电子邮件（Email）"
created_at: "2024-09-19T13:04:14.000000Z"
uuid: "3bb62417-18ea-4dad-8889-a3d9f23d563e"
tags: ['Make基础教程']
---

# Make中文教程：电子邮件（Email）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于电子邮件的教程。

**教程简介** ：本教程详解如何在Make.com上使用Email模块，包括发送、收取、删除、标记和创建电子邮件草稿的步骤，并解释如何通过不同方法连接各种类型的电子邮件帐户。

**学习目标** ：掌握如何在Make.com上操作和管理电子邮件，学会将电子邮件帐户连接到Make.com，理解不同类型的连接方法，并能够根据特定需求进行自动化电子邮件处理。

**学习内容** ：

  1. 创建和管理各种电子邮件连接（Gmail、IMAP、Microsoft OAuth）

  2. 使用电子邮件模块发送和接收邮件

  3. 理解和配置触发器和动作

  4. 创建并管理草稿邮件

  5. 标记邮件为已读或未读，移动邮件到不同文件夹

**概念理解** ：

  1. **Email模块** ：用于处理在Make.com中的电子邮件操作，包括发送、接收、标记等功能。

  2. **IMAP连接** ：邮件服务器协议，用于接收邮件。

  3. **SMTP/IMAP OAuth** ：一种更安全的电子邮件连接方式，替代基本身份验证。

  4. **触发器** ：自动执行某些操作的事件，例如接收新邮件。

  5. **动作** ：在特定条件下执行的特定任务，例如发送邮件。

**任务练习** ：

  1. 使用Make.com Email模块连接您的Gmail帐户。

  2. 创建一个触发器，当有新邮件时，自动保存并标记为已读。

  3. 编写一个发送电子邮件的自动化脚本，并附带一个测试文件作为附件。

  4. 使用Microsoft SMTP/IMAP OAuth连接您的Microsoft邮箱，并验证连接是否成功。


# **电子邮件**

利用 Make 的电子邮件模块,您可以发送、复制、删除和检索电子邮件和/或草稿,在文件夹之间移动电子邮件,将电子邮件标记为已读或未读,并创建草稿。

![](https://static.xiaobot.net/file/2024-11-25/720345/1855048834973e6a0c21c3dcbb4a7b72.png)


## **连接您的电子邮件账户**

要建立连接,您可以授权 Make 与第三方应用程序互动,或使用客户端凭据的高级设置。

您可以使用以下类型的电子邮件帐户:

  - 个人账户（不推荐）:

个人账户是一组由个人使用的凭证,只有该个人可以使用。它们为该用户提供了适当的安全上下文,在正常情况下,帐户中的操作只能归因于该用户。例如,大多数公司为每个员工提供一个个人账户来管理自己的电子邮件。

  -  服务账户:

第三方应用程序的管理员通常会创建服务账户,该账户代表另一个应用程序或进程,而不是特定的个人,以提供适当的安全上下文。此类账户的凭据可能由少数人存储和管理,而不是由一个人私下维护。服务账户执行的所有操作\(例如访问、编辑或在第三方应用程序中创建记录\)都可归因于该服务账户以及任何有访问权限的人。例如,一家大公司可能会创建一个特殊的电子邮件账户，密码由多人共享。

您可以使用以下方法将您的电子邮件帐户连接到 Make:

  - 使用 Gmail 账户连接

  - 使用 IMAP 连接其他电子邮件类型

  - 使用 Microsoft SMTP/IMAP OAuth 进行连接


### **使用 Gmail 账户登录**

使用您的 Gmail 账户进行连接:

  1. 登录您的 Make 帐户,将电子邮件模块添加到方案中,单击"创建连接",并选择 Google Restricted 作为连接类型。

![](https://static.xiaobot.net/file/2024-11-25/720345/c214023abe1bb90f75090338a0f2a3cc.png)
  2. 在连接名称栏中输入一个名称。请参见《连接 Gmail 以使用》页面,了解在连接 Gmail 时可能遇到的任何问题。

  3.  点击保存。

  4. 点击"允许"以确认访问。

连接已建立。


### **对于其他类型的电子邮件,请使用 IMAP 进行连接**

 使用 IMAP 连接:

  1. 登录您的 Make 帐户,将电子邮件模块添加到场景中,单击创建连接,并选择其他 IMAP 作为连接类型。

![](https://static.xiaobot.net/file/2024-11-25/720345/17e73535d764d82a6f3714c831d8e6fd.png)
  2. 在"连接名称"字段中输入连接的名称。

  3. 在电子邮件提供商字段中,执行以下操作之一:

     1. 从电子邮件提供商选项中选择您的提供商,并输入以下内容:

        - 用户名：输入您的电子邮件地址。

        - 密码:请输入您的密码。

     2. 如果您的供应商未列在选项中,请选择"其他"并输入以下内容:

![](https://static.xiaobot.net/file/2024-11-25/720345/2603a28a989ae8a16b2723891dc930fb.png)

![](https://static.xiaobot.net/file/2024-11-25/720345/670d03a9018ce9298c4bc081dcb7f01e.png)![](https://static.xiaobot.net/file/2024-11-25/720345/810b7b4e347570382665f0079a97d411.png)
  4.  点击保存。

连接已建立。


### **使用 Microsoft SMTP/IMAP OAuth 连接**

Microsoft 已弃用 Exchange Online 中的基本身份验证。如果您的基本身份验证失败,请使用此连接方法。

您可以使用 Microsoft SMTP/IMAP 基本身份验证或客户端凭据连接应用程序。

**使用您的 Microsoft 帐户连接**

使用个人或工作帐户连接:

  1. 登录您的 Make 帐户，将电子邮件模块添加到一个场景中，单击"创建连接"，并选择 Microsoft SMTP/IMAP OAuth 作为连接类型。

![](https://static.xiaobot.net/file/2024-11-25/720345/9a0b1aca90dcd1a172ad7835222c2798.png)
  2. 在"连接名称"字段中输入连接的名称。

  3. 可选:点击"显示高级设置",并输入您的自定义应用程序的客户端凭据。有关如何创建您的凭据的详细信息,请参见"获取客户端凭据"部分。

  4.  点击保存。

  5. 确认您的帐户以完成连接。

如果您需要在场景中的多个模块中替换连接,请使用 DevTool 插件。

**获取客户凭据**

要使用客户凭据进行连接,您必须在 Microsoft Azure 门户中创建一个自定义应用程序。

![](https://static.xiaobot.net/file/2024-11-25/720345/761d027f08be733af533a0665e28efe2.png)

  1. 前往 Azure 门户 > Azure Active Directory > 应用程序注册 > 新注册。

![](https://static.xiaobot.net/file/2024-11-25/720345/79af28f2ba8f230fe97b777b3447f833.png)
  2. 输入应用程序的详细信息。

![](https://static.xiaobot.net/file/2024-11-25/720345/0d76e9705b965b835f30e8aea2bf90cf.png)
  3. 应用程序屏幕上显示客户端 ID 和租户 ID 详细信息。要添加客户端密钥,请单击"添加证书或密钥"。

![](https://static.xiaobot.net/file/2024-11-25/720345/96a57bdb6adadf49493ae81a75bc24b0.png)
  4. 单击"新建客户端密钥"，输入描述，选择客户端密钥的有效期，然后单击"添加"。

![](https://static.xiaobot.net/file/2024-11-25/720345/059cfe5ffa87f944caa5dd538dfd8ac2.png)
  5. 复制值, 这是客户端密钥。

![](https://static.xiaobot.net/file/2024-11-25/720345/b7bf7b10cbd884490501c7f77c4c6b22.png)
  6. 在左侧菜单中，单击 API 权限以添加应用程序权限。

选择 Microsoft 365 电子邮件应用程序并添加以下权限:

![](https://static.xiaobot.net/file/2024-11-25/720345/ce7733686fbb8f7166163a860b18d7ad.png)

对于电子邮件应用程序,选择 Microsoft Graph 应用程序并添加以下权限:

![](https://static.xiaobot.net/file/2024-11-25/720345/32c6daf4cb45cc9b0980d7c816d58453.png)

您现在拥有使用 Microsoft 登录电子邮件应用程序的客户端凭据。

欲了解更多信息,您可参考以下微软官方文档。

  -  微软 OAuth 服务

  - 微软 Azure 管理员同意工作流

  - 


## **触发器**


### **  查看电子邮件**

当根据指定的标准收到新电子邮件时触发处理。

![](https://static.xiaobot.net/file/2024-11-25/720345/8110e948cecc3de88c5d9a58879178f1.png)


## **  行动**


### **  发送电子邮件**

 发送一封新电子邮件。

![](https://static.xiaobot.net/file/2024-11-25/720345/83d5999c5c07fbfa82d998d5e4976c14.png)


### **创建草稿**

复制电子邮件或草稿到选定的文件夹。

![](https://static.xiaobot.net/file/2024-11-25/720345/56b97db279741b7627f0ba68cf03b3e2.png)


### **将电子邮件标记为已读**

将所选文件夹中的电子邮件或草稿标记为已读,方法是设置"已读"标志。

![](https://static.xiaobot.net/file/2024-11-25/720345/6c8fa25983ff15a75d3c77acafe5e714.png)


### **将电子邮件标记为未读**

将所选文件夹中的电子邮件或草稿标记为未读，方法是设置未读标志。

![](https://static.xiaobot.net/file/2024-11-25/720345/701ccd02c172c74379a1ebdef3ad28c3.png)


### **  移动电子邮件**

将选中的电子邮件或草稿移动到选定的文件夹。

![](https://static.xiaobot.net/file/2024-11-25/720345/517dc7a923f354503ba78a9279b738f1.png)


### **  复制电子邮件**

复制电子邮件或草稿到选定的文件夹。

![](https://static.xiaobot.net/file/2024-11-25/720345/3679187ec32bbb6bb71d5cf9bafc91f6.png)


### **  删除电子邮件**

从所选文件夹中删除电子邮件或草稿。

![](https://static.xiaobot.net/file/2024-11-25/720345/82ba455c77fc59948a5e28f57ac82ffb.png)


### **  获取电子邮件**

返回与指定条件匹配的电子邮件。

![](https://static.xiaobot.net/file/2024-11-25/720345/4c73793e0407f8e6bd8a5e3457d2a9b7.png)


### **给团队成员发送电子邮件**

向团队中某个成员发送电子邮件。

![](https://static.xiaobot.net/file/2024-11-25/720345/b8fc34ae55be33f9696a350f2f101c04.png)


### **IMAP 协议中的独特电子邮件 ID**

被称为"电子邮件 ID \(UID\)"的独特电子邮件 ID 是电子邮件的标识符。电子邮件 ID 对于每个电子邮件文件夹都是特定的。

![](https://static.xiaobot.net/file/2024-11-25/720345/b1e67701fa17d580aa21067edbb2060d.png)


## **  迭代器**


### **  逐个附件**

逐个迭代接收的附件。

电子邮件迭代器模块可让您单独管理电子邮件附件。例如,您可以设置监视电子邮件以迭代附有附件的电子邮件并接收警报。

![](https://static.xiaobot.net/file/2024-11-25/720345/37b9e245f5cfec719f3bcf5150edd97a.png)

关于迭代器的更多信息,请参见迭代器。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-19/720345/72170bbcff92bf6a6eb166e51f01dd5d.png)