---
title: "Make中文教程：Hugging Face"
created_at: "2024-12-05T13:44:10.000000Z"
uuid: "ee464dd2-e9a3-4967-b0d5-722f82ad84ad"
tags: ['Make基础教程']
---

# Make中文教程：Hugging Face

**标签**: Make基础教程

使用 Hugging Face 模块,您可以创建摘要、回答、文本分类、翻译和进行 API 调用。

要开始使用 Hugging Face,请在 huggingface.co/join 创建一个账户。

请参考 Hugging Face API 文档了解可用的端点列表。


## **将 Hugging Face 连接到 Make**

要建立连接,您必须先从您的 Hugging Face 账户获得一个 API 密钥。

  1. 登录您的 Hugging Face 账户。

  2. 在页面的右上角,点击您的个人资料图标>设置。

  3. 在左侧边栏中,单击访问令牌选项。

  4. 点击"新建令牌"按钮。

  5. 输入代币的名称,从下拉菜单中选择角色,然后点击"生成代币"按钮。

  6. 在您的令牌旁边，点击复制令牌到剪贴板图标。

  7. 登录您的 Make 账户,添加 Hugging Face 模块到您的场景中,然后点击创建连接。

  8. 在 API 令牌字段中,粘贴在步骤 6 中复制的 API 密钥。

  9.  点击保存。

您已成功建立连接。您现在可以编辑您的场景并添加更多的拥抱脸模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤操作。


## **构建 Hugging Face 场景**

连接应用程序后,您可以执行以下操作:

**  任务**

  -  创建摘要

  -  创建回答

  - 创建文本分类

  -  创建一个翻译

**  其他**

  -  发起 API 请求


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-05/720345/1b0329b569dc6a7263e944f19f99526c.png)