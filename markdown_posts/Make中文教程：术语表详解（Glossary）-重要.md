---
title: "Make中文教程：术语表详解（Glossary）-重要"
created_at: "2024-08-27T11:00:43.000000Z"
uuid: "2b5991a7-fc95-41f6-b35b-dd45d4bef6f1"
tags: ['Make基础教程']
---

# Make中文教程：术语表详解（Glossary）-重要

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于术语表的教程。

  1. **教程简介**  ：本教程介绍了Make.com平台中的常用术语，涵盖数据库事务处理、模块操作、场景管理、数据处理和错误处理等内容，帮助用户理解并准确使用这些术语。

  2. **学习目标**  ：通过学习本教程，用户将掌握Make.com平台的关键术语及其含义，了解如何在平台上创建和管理场景，处理数据和错误，从而提高平台使用效率。

  3. **学习内容**  ：

     - ACID属性

     - 操作模块与场景激活状态

     - 聚合器和数据处理模块

     - 蓝图和模块克隆

     - 数据包和数据存储

     - 错误处理指令和场景执行阶段

  4. **概念理解**  ：

     - **ACID**  ：代表原子性、一致性、隔离性和持久性，保证数据库事务的可靠性。

     - **操作模块**  ：用于创建、读取、更新和删除数据的模块。

     - **激活场景**  ：按计划或Webhook运行的场景。

     - **聚合器**  ：用于将多个数据包合并成一个的数据处理模块。

     - **蓝图**  ：用于导入和导出场景的JSON文件。

  5. **任务练习**  ：

     - 创建一个包含操作模块的场景，并尝试使用不同的激活状态。

     - 设计一个场景，使用数组聚合器合并多个数据包。

     - 导出当前场景的蓝图，修改一些设置后重新导入。

     - 使用错误处理指令在出现错误时停止场景执行并查看执行历史。


# **Make使用指南：术语表详解（Glossary）上**


## **1\. 场景（Scenario）：自动化的核心**


### **什么是场景？**

场景是 Make 平台中最基础的工作单元，可以将它理解为一条智能的"自动化生产线"。每个场景都是为了完成特定的自动化任务而设计的，比如：

  - 自动处理邮件

  - 同步数据库信息

  - 更新电子表格

  - 发送通知消息


### **场景的工作状态**

Make 中的场景有两种工作状态：

**1.1 活跃状态（Active）**

![](https://static.xiaobot.net/file/2024-11-18/720345/6243a5448bccef5cecfbc6a04d6405e5.png)

特点：

  - 按预设计划自动运行

  - 可以响应实时触发

  - 适合持续性任务

实际应用场景：

  - 每小时检查新订单

  - 实时监控网站表单提交

  - 定期备份数据

**1.2 非活跃状态（Inactive）**

特点：

  - 需要手动启动

  - 不会自动执行

  - 适合一次性任务

实际应用场景：

  - 历史数据迁移

  - 临时数据处理

  - 测试新功能


### **如何选择场景状态？**

参考以下决策树：

  1. 任务是否需要定期执行？

     - 是 → 选择活跃状态

     - 否 → 继续评估

  2. 是否需要实时响应？

     - 是 → 选择活跃状态

     - 否 → 继续评估

  3. 是否是一次性任务？

     - 是 → 选择非活跃状态

     - 否 → 选择活跃状态


## **2\. 模块（Module）：场景的构建模块**


### **什么是模块？**

模块是场景中的基本操作单元，就像积木一样可以自由组合。每个模块都代表一个具体的操作或功能。


### **模块的四大类型**

**2.1 触发器模块（Trigger Modules）**

  - 功能：启动场景的"开关"

  - 位置：总是在场景的开始

  - 特点：每个场景必须且只能有一个触发器

常见示例：

    - 新邮件监控器
    - 表格变更检测器
    - Webhook接收器
    - 定时执行器

**2.2 操作模块（Action Modules）**

  - 功能：执行具体的操作任务

  - 位置：可以在场景的任何位置

  - 特点：可以有多个，执行核心业务逻辑

典型用途：

    - 创建新记录
    - 更新数据
    - 发送消息
    - 上传文件

**2.3 搜索模块（Search Modules）**

  - 功能：查找和检索数据

  - 位置：通常在操作模块之前

  - 特点：用于数据验证和查询

使用场景：

    - 查找客户信息
    - 检索订单记录
    - 验证数据存在性
    - 筛选符合条件的记录

**2.4 工具模块（Tools Modules）**

  - 功能：数据处理和转换

  - 位置：在其他模块之间

  - 特点：提供辅助功能

常见工具：

    - 文本解析器
    - 数据格式转换器
    - 变量设置器
    - 条件判断器


### **模块使用的最佳实践**

  1. 规划阶段

     - 明确每个模块的具体职责

     - 确保模块之间的逻辑连贯

     - 避免功能重复

  2. 配置阶段

     - 仔细测试每个模块

     - 确认数据格式匹配

     - 设置适当的错误处理

  3. 维护阶段

     - 定期检查模块运行状态

     - 更新过期的配置

     - 优化处理流程


## **3\. 数据包（Bundle）：数据流转的载体**


### **什么是数据包（Bundle）？**

数据包是 Make 中最基本的数据传输单位，就像是在模块之间传递的"信息包裹"。每个数据包都包含了特定的数据结构和内容。


### **数据包（Bundle）的构成**

![](https://static.xiaobot.net/file/2024-11-18/720345/96690544ea1e3caa91a02f7015622182.png)

数据包（Bundle）包含以下要素：

  1. **数据项（Items）**

     - 文本类型（如：名称、描述）

     - 数字类型（如：金额、数量）

     - 日期类型（如：创建时间、更新时间）

     - 数组类型（如：多个标签、列表项）

  2. **数据映射**

![](https://static.xiaobot.net/file/2024-11-18/720345/cc2a56d618409e049d12d535a89ed4e2.png)

映射规则：

    - 使用 [ ] 表示数组
    - 点击三角访问下级内容
    - 支持嵌套数据结构


## **4\. 触发器（Trigger）：自动化的起点**


### **触发器的本质**

触发器是场景的启动机制，决定了自动化流程何时开始执行。


### **创建触发器**

![](https://static.xiaobot.net/file/2024-11-18/720345/03fd82ae84af4920aed842cf64b8f8da.png)


### **触发器类型详解**

**4.1 定时轮询触发器（Scheduled Trigger）**

特点：

  - 按固定时间间隔执行

  - 可设置具体执行时间

  - 支持复杂的定时规则

使用场景：

    - 每日数据备份
    - 定期报表生成
    - 周期性任务检查
    - 计划性数据同步

配置建议：

  - 选择合适的执行频率

  - 考虑时区设置

  - 避免过于频繁的检查

**4.2 即时触发器（Instant Trigger）**

特点：

  - 实时响应外部事件

  - 通过Webhook接收通知

  - 立即执行场景

使用场景：

    - 表单提交处理
    - 支付通知处理
    - API回调处理
    - 实时数据更新


## **5\. 执行流程详解**


### **5.1 测试执行**

![](https://static.xiaobot.net/file/2024-11-18/720345/f5d740d15f3f44695f37e541aaf2beca.png)

测试阶段建议：

  1. 初始测试

     - 使用小量数据

     - 验证基本功能

     - 检查数据流转

  2. 完整测试

     - 使用真实数据

     - 测试边界情况

     - 验证错误处理


### **5.2 执行历史**

![](https://static.xiaobot.net/file/2024-11-18/720345/f927354a44414050129a3fea0cdab36f.png)

监控要点：

  1. 执行状态
         
         - 成功：正常完成
         - 警告：完成但有警告
         - 错误：执行失败

  2. 性能指标
         
         - 执行时间
         - 数据处理量
         - 资源使用情况

  3. 问题诊断
         
         - 错误原因分析
         - 性能瓶颈识别
         - 优化建议


## **6\. 连接（Connection）管理**


### **连接的重要性**

连接是场景与外部服务交互的桥梁，确保：

  - 安全的数据传输

  - 正确的权限管理

  - 稳定的服务访问


## **实用技巧总结**


### **1\. 新手入门建议**

  - 从简单场景开始

  - 充分利用测试功能

  - 参考现有模板

  - 记录常见问题


### **2\. 进阶使用技巧**

  - 优化触发器配置

  - 完善错误处理

  - 建立监控机制

  - 定期优化场景


### **3\. 专业用户提示**

  - 实施版本控制

  - 建立标准化流程

  - 优化资源使用

  - 实现自动化测试


# **Make使用指南：术语表详解（Glossary）下**


## **1\. 聚合器（Aggregator）体系：数据整合的利器**


### **1.1 什么是聚合器？**

聚合器是Make平台中专门用于数据整合的工具，可以将多个独立的数据包合并成一个统一的输出。就像是一个智能的"数据整理工具"。


### **1.2 四大聚合器详解**

**A. 数组聚合器（Array Aggregator）**

功能定位：

  - 将多条数据合并为一个数组

  - 支持自定义数据结构

  - 保持数据的完整性

实际应用场景：

    1. 订单处理
       - 收集一天的订单记录
       - 合并为一个订单报表
       - 批量处理订单数据
    2. 用户数据整合
       - 收集用户行为数据
       - 合并用户属性信息
       - 创建用户画像报告
    3. 批量操作
       - 收集更新请求
       - 合并为批量更新
       - 提高处理效率

**B. 数字聚合器（Numerical Aggregator）**

功能特点：

  - 执行数值计算

  - 提供统计功能

  - 生成数据摘要

常用操作：

    - SUM（求和）
    - AVG（平均值）
    - MAX（最大值）
    - MIN（最小值）
    - COUNT（计数）

实际应用：

    1. 财务统计
       - 计算日营收
       - 统计交易笔数
       - 分析支出趋势
    2. 性能监控
       - 计算平均响应时间
       - 统计错误率
       - 监控系统负载

**C. 表格聚合器（Table Aggregator）**

功能特点：

  - 创建结构化表格

  - 支持行列配置

  - 格式化数据展示

使用场景：

    1. 报表生成
       - 周报自动生成
       - 销售数据汇总
       - 库存状态报告
    2. 数据展示
       - 用户行为分析
       - 产品性能对比
       - 服务质量评估

**D. 文本聚合器（Text Aggregator）**

功能特点：

  - 合并文本内容

  - 支持格式化

  - 处理多语言

应用实例：

    1. 内容整合
       - 合并邮件内容
       - 生成摘要报告
       - 整合用户反馈
    2. 文档处理
       - 合并日志信息
       - 生成说明文档
       - 整理知识库


### **1.3 聚合器选择指南**

**场景匹配表：**

**需求类型推荐聚合器适用场景** 数据批处理数组聚合器批量操作、数据收集数据统计数字聚合器财务计算、性能分析报表生成表格聚合器周期报告、数据汇总内容合并文本聚合器文档处理、信息整合


## **2\. 文本解析工具（Text Parser）**


### **2.1 基本功能介绍**

![](https://static.xiaobot.net/file/2024-11-18/720345/340473177278caa855832316d785536a.png)

核心功能模块：

**A. 模式匹配**

功能特点：

  - 支持正则表达式

  - 精确提取所需信息

  - 灵活的匹配规则

使用场景：

    1. 数据提取
       - 提取邮件地址
       - 解析电话号码
       - 识别订单编号
    2. 内容过滤
       - 过滤敏感信息
       - 提取关键词
       - 验证数据格式

**B. 内容替换**

功能特点：

  - 批量文本替换

  - 支持条件替换

  - 保持格式一致

应用场景：

    1. 数据清洗
       - 统一格式
       - 修正错误
       - 标准化处理
    2. 内容转换
       - 语言本地化
       - 格式转换
       - 编码转换


## **3\. 迭代器（Iterator）与路由器（Router）**


### **3.1 迭代器详解**

**A. 核心概念**

迭代器是数据处理的"分解器"，将复杂的数据包拆分成单个元素逐一处理。

**B. 工作原理**

大数据包

迭代器

元素1

元素2

元素3

单独处理

**C. 应用场景**

  1. 批量文件处理
         
         - 处理邮件附件
         - 批量图片处理
         - 文档转换

  2. 数据批处理
         
         - 用户数据更新
         - 订单批量处理
         - 库存同步


### **3.2 路由器功能**

![](https://static.xiaobot.net/file/2024-11-18/720345/458008205329f4ddf369d8180f04fdc2.png)

**A. 核心功能**

  - 数据分流处理

  - 条件判断路由

  - 多路径处理

**B. 实际应用**

  1. 订单处理流程

    订单进入 → 路由器判断
    ├─ 普通订单 → 标准处理
    ├─ VIP订单 → 优先处理
    └─ 问题订单 → 人工处理

  2. 数据分类处理

    数据输入 → 路由器分类
    ├─ 文本数据 → 文本处理流程
    ├─ 图片数据 → 图片处理流程
    └─ 视频数据 → 视频处理流程


## **4\. 场景管理高级工具**


### **4.1 蓝图（Blueprint）功能**

![](https://static.xiaobot.net/file/2024-11-18/720345/66c49e9de4de3c98277d3bd4ca696329.png)

**A. 蓝图的本质**

蓝图是使用JSON格式保存的场景配置文件，就像是场景的"DNA图谱"。它包含：

  - 所有模块的具体设置（如触发条件、数据处理规则）

  - 模块间的连接关系（数据如何流转）

  - 数据映射规则（字段对应关系）

  - 错误处理配置（如何应对异常）

重要说明：

  - 蓝图不包含连接凭证等敏感信息

  - 可以跨组织和团队分享

  - 导入时需要重新配置连接信息

**B. 主要用途**

蓝图通过统一的格式实现场景的可移植性：

  - 快速复制成功的自动化方案

  - 在团队间分享最佳实践

  - 创建场景模板

  - 备份重要配置


### **4.2 克隆功能详解**

**A. 模块克隆**

![](https://static.xiaobot.net/file/2024-11-18/720345/9938c147a2190144b038b45806869e71.png)

模块克隆可以理解为模块的"完全复制"：

  - 复制所有配置参数

  - 保留数据映射关系

  - 继承错误处理设置

  - 维持原有的变量定义

注意：克隆后的模块完全独立，修改不会影响原模块

**B. 场景克隆**

![](https://static.xiaobot.net/file/2024-11-18/720345/ecef6810b581327adbfe6cd355aac42c.png)

场景克隆创建场景的完整副本：

  - 复制所有模块及其配置

  - 保持模块间的连接关系

  - 复制所有路由和条件设置

  - 保留调度和执行设置

应用提示：克隆场景常用于创建测试版本或特定变体


## **5\. 执行控制与监控**


### **5.1 顺序处理机制**

![](https://static.xiaobot.net/file/2024-11-18/720345/0b812701a9ad3f278585482104910d3d.png)

顺序处理是Make的重要控制机制：

  - 确保执行顺序严格按照设定进行

  - 防止并发执行导致的数据混乱

  - 适用于需要严格顺序的业务流程

  - 可能会影响处理速度，但保证数据准确性


### **5.3 执行历史管理**

![](https://static.xiaobot.net/file/2024-11-18/720345/1e3bab893f99dd13e6bbf1cec457d3f9.png)

执行历史是场景运行的"黑匣子"：

  - 记录每次执行的开始和结束时间

  - 显示执行状态（成功/失败/警告）

  - 统计数据处理量和执行时间

  - 保存详细的错误信息和调试数据

  - 支持历史记录搜索和筛选


## **6\. 数据存储（Data Store）系统**


### **6.1 概念与原理**

数据存储是Make的内置数据库系统，特点是：

  - 提供持久化数据存储能力

  - 支持跨场景数据共享

  - 可用作临时数据缓存

  - 提供简单的数据库操作接口

  - 支持数据版本控制

  - 内置数据备份机制


### **6.2 数据结构（Data Structure）**

数据结构定义了存储的"架构图"：

  - 设置字段类型（文本、数字、日期等）

  - 定义字段规则（必填、唯一等）

  - 配置默认值和验证规则

  - 支持索引和关系设置

  - 可重复使用的结构模板


## **7\. Webhook 高级应用**


### **7.1 Webhook配置**

Webhook是实现系统间实时通信的桥梁：

  - 生成专用的接收URL

  - 支持多种认证方式

  - 可自定义数据格式要求

  - 提供响应配置选项

  - 内置安全保护机制

  - 支持高并发处理


## **8\. Make中的ACID是什么？**

想象你在用Make监控一个新闻网站的更新，ACID就是确保这个过程万无一失的四重保障。让我用RSS模块的Watch RSS feed items订阅新闻的动作来说明：


### **8.1 原子性（Atomicity）："要么全做，要么不做"**

就像转账必须"要么全部完成，要么完全不做"

  - 比如：从A账户转1000元到B账户

  - 不可能出现钱从A账户扣了，但B账户没收到的情况

  - Make中：如果一个自动化流程中有多个步骤，要么全部成功，要么全部回滚


### **8.2 一致性（Consistency）："数据永远对得上"**

    新闻网站：发布了5篇新文章
    你的系统：必须正好得到这5篇
             - 不会少一篇
             - 不会多一篇
             - 不会重复

就像照镜子，镜子里的影像永远和实物一模一样。


### **8.3 隔离性（Isolation）："互不打扰"**

    同时监控多个新闻网站：
    科技新闻 → 独立处理
    体育新闻 → 独立处理
    财经新闻 → 独立处理

就像多个收银台，每个都能独立工作，互不影响。


### **8.4 持久性（Durability）："保存就是永久的"**

    文章处理完成 = 永久保存
    - 系统重启也不会丢
    - 断电也不会丢
    - 随时可以查看

就像写日记，写进本子就永远记录在那里。


## **实用小贴士**

  1. 重要数据一定要用带ACID徽章的模块

  2. 第一次使用先小量测试

  3. 定期检查是否正常运行

记住：ACID就像你的数据保镖，让你的RSS订阅更新永远准确可靠！


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/5803eae304f316634f9a29fc766658f4.png)