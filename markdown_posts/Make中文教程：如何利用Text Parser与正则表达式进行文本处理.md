---
title: "Make中文教程：如何利用Text Parser与正则表达式进行文本处理"
created_at: "2024-08-27T15:24:41.000000Z"
uuid: "61198455-9ad2-4f25-88c1-4e972b238735"
tags: ['Make基础教程']
---

# Make中文教程：如何利用Text Parser与正则表达式进行文本处理

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于获取文本解析结果的教程。

**教程简介**  ：本教程介绍如何在Make.com中使用正则表达式解析文件名的扩展名，并通过示例演示如何使用替换功能来提取文件类型。

**学习目标**  ：掌握使用正则表达式解析文件扩展名的技巧，以及如何在Make.com中应用这些表达式来获取所需信息。

**学习内容**  ：理解正则表达式的基础知识，使用括号捕获组提取文件扩展名，在Make.com中应用解析规则，替换文本中不需要的部分以获取目标内容。

**概念理解**  ：正则表达式是用于匹配文本模式的工具。通过在正则表达式中使用括号，特定的部分可以被捕获并提取出来。Make.com中的文本解析器和替换功能允许用户根据特定规则处理和提取文本内容。

**任务练习**  ：

  1. 使用正则表达式. \(.+\)在Make.com中解析文件名"sample.txt"并提取其扩展名"txt"。

  2. 在Make.com中使用替换功能，将字符串"example.docx"中的文件扩展名提取出来。


## **问题场景**

需要从文件名\(如"filename.docx"\)中提取扩展名，扩展名可能是docx、pdf、CSV等不同格式。


## **常见错误方式及分析**


### **1\. 直接使用  **\\..+ 表达式

看似正确但实际无法工作：

![](https://static.xiaobot.net/file/2024-11-18/720345/5262ac1d1ddcda13b380e387f46b622a.png)

在regex101.com上测试正确，但在Text Parser中无效：

![](https://static.xiaobot.net/file/2024-11-18/720345/272401f1b88f8ceb1553ed20bc7a55d5.png)

结果显示无匹配：

![](https://static.xiaobot.net/file/2024-11-18/720345/edd82c08114688097050643073620549.png)


## **正确解决方案**


### **方案1：使用捕获组**

  1. 将正则表达式修改为 \\.\(.+\)

  2. 括号创建捕获组，只提取所需部分

![](https://static.xiaobot.net/file/2024-11-18/720345/f6c1a480513df8ba933e0764ad554531.png)

结果展示：

![](https://static.xiaobot.net/file/2024-11-18/720345/2e0ceda5f0fcff6e42d8dc7e8c45a032.png)


### **方案2：使用replace函数**

    {{replace("abcdefghijklmno pqr stuvw xyz.docx"; "/.*\./"; ".")}}

使用时将示例文件名替换为你的文件名变量。


## **关键说明**

  - i后的数字\(如i1, i2\)表示匹配序号，用于指定要使用的匹配结果

  - 捕获组方法更直观且易于维护

  - replace函数方法提供了另一种灵活的解决方案


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/98912806c6b77114611ef21dfa83dfed.png)