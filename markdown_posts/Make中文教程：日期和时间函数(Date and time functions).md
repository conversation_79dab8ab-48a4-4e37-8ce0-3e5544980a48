---
title: "Make中文教程：日期和时间函数(Date and time functions)"
created_at: "2024-09-19T08:25:45.000000Z"
uuid: "4b5f09c2-5caa-4d53-ae52-8743cb6a9a82"
tags: ['Make基础教程']
---

# Make中文教程：日期和时间函数(Date and time functions)

**标签**: Make基础教程

# **Make平台日期时间函数完全指南**

**翔宇工作流** 今天为大家带来关于日期和时间函数的教程。

**教程简介：**

今天我们学习在Make.com中如何使用日期和时间函数，帮助我们处理和转换日期时间数据。比如，可以用它们来改变日期格式、转换时区、或将文本和日期相互转换。

**学习内容：**

本教程会教大家以下几种常用的日期和时间操作：

• **格式化日期（formatDate）** ：把日期变成指定的文本格式，比如“2024年11月8日”变成“2024-11-08”。

• **解析日期（parseDate）** ：将文本形式的日期转成可操作的日期，比如将“2024-11-08”转成系统识别的日期。

• **日期加减操作（addDays、addHours等）** ：给日期加上或减去一定的天数、小时数。

• **设置特定时间（setMinute、setHour等）** ：调整日期的小时或分钟到指定值，比如设为下午3点。

**任务练习：**

练习将今天日期用formatDate函数转换成“YYYY-MM-DD”格式：

• 示例代码：formatDate\(now; "YYYY-MM-DD"\)

• 输出示例：如果今天是2024年11月8日，结果就是“2024-11-08”。

然后用parseDate把“2024-11-08”转换回系统识别的日期，方便后续做日期计算，比如查看今天到年底的天数。

**总结：**

这些日期和时间函数帮助我们更方便地处理日期，提升工作效率。


### **formatDate - 日期格式化**

**功能** ：当您有一个日期值并希望将其转换为一个文本值，如“12-10-2019 20:30”或“Aug 18, 2019 10:00 AM”。函数会将输入的日期转换为您指定格式的文本，返回值始终是文本类型（Text）。 **语法** ：

    {{formatDate(date, format, timezone)}}

参数说明：

  - date: 需要格式化的日期

  - format: 输出格式模板

  - timezone: \[可选\] 时区设置

**实例展示** ：

UTC时间格式

    {{formatDate(now, "YYYY-MM-DD HH:mm", "UTC")}}

结果：2024-11-08 12:30

北京时间格式

    {{formatDate(now, "MM/DD/YYYY", "Asia/Shanghai")}}

结果：11/11/2024

> 🌟 小贴士：不设置时区时，系统会使用组织默认时区


### UTC是什么意思？

UTC（协调世界时）是全球通用的时间标准，用于取代早期的格林尼治标准时间（GMT）。它作为统一的时间基准，不受时区影响。

特点：

1\. UTC 时间全球一致，不因地区不同而变化。

2\. 本地时间是根据 UTC 加上或减去时区差值计算得出。

UTC和本地时间的关系：

\- 北京时间是 UTC+8，比 UTC 快 8 小时。

\- 例如：UTC 时间是 12:30，北京时间就是 20:30。

如何在Make中修改时区，如下图：

![](https://static.xiaobot.net/file/2024-11-19/720345/a43fcfd9747fa0f7a951143974f480a9.png)

示例:

![](https://static.xiaobot.net/file/2024-11-19/720345/c837982ce94bd4b969b1f1cf32f8ab6a.png)


### **parseDate - 文本转日期**

**功能** ：当您有一个文本类型的日期值时，如“12-10-2019 20:30”，并需要将其转换为Date格式的日期值以便进行进一步的日期运算或处理时，可以使用此函数。 **语法** ：

    {{parseDate(text, format, timezone)}}

参数说明：

  - text: 日期文本（如："12-10-2019 20:30"）

  - format: 输入格式说明

  - timezone: \[可选\] 时区设置


### **addDays - 天数加减器**

**功能** ：增加或减少指定天数

增加2天

    {{addDays("2016-12-08T15:55:57.536Z", 2)}}

结果：2016-12-10T15:55:57.536Z

减少2天

    {{addDays("2016-12-08T15:55:57.536Z", -2)}}

结果：2016-12-06T15:55:57.536Z


### **addHours - 小时计算器**

**功能** ：增加或减少指定小时数

增加2小时

    {{addHours("2016-12-08T15:55:57.536Z", 2)}}

结果：2016-12-08T17:55:57.536Z


### **addMinutes - 分钟调整器**

**功能** ：增加或减少指定分钟数

增加2分钟

    {{addMinutes("2016-12-08T15:55:57.536Z", 2)}}

结果：2016-12-08T15:57:57.536Z


### **addMonths - 月份计算器**

**功能** ：增加或减少指定月数

增加2个月

    {{addMonths("2016-12-08T15:55:57.536Z", 2)}}

结果：2017-02-08T15:55:57.536Z


### **setHour - 小时设置器**

**功能** ：返回一个新日期，小时数设置为指定的值。

    {{setHour("2015-10-07T11:36:39.138Z", 10)}}

结果：2015-10-07T10:36:39.138Z

> 🔔 注意：
> 
> \- 接受 0 到 23 之间的数字作为小时数。
> 
> \- 如果输入超出范围的数字，则会自动调整到前一或下一天。


### **setMinute - 分钟设置器**

**功能** ：设置指定的分钟值（0-59）

    {{setMinute("2015-10-07T11:36:39.138Z", 10)}}

结果：2015-10-07T11:10:39.138Z

> 🔔 注意：
> 
> \- 接受 0 到 59 之间的数字作为分钟数。
> 
> \- 如果输入超出范围的数字，则会自动调整到前一或后一小时。


### **setDay - 星期设定器**

**功能** ：设置为指定的星期几 **语法** ：

    // 使用数字（1-7，表示周日到周六）
    {{setDay("2018-06-27T11:36:39.138Z", 1)}}
    // 使用英文名称
    {{setDay("2018-06-27T11:36:39.138Z", "monday")}}

结果：2018-06-25T11:36:39.138Z


### **setMonth - 月份设定器**

**功能** ：设置指定的月份

    // 使用数字（1-12）
    {{setMonth("2015-08-07T11:36:39.138Z", 5)}}
    // 使用英文月份名
    {{setMonth("2015-08-07T11:36:39.138Z", "January")}}

结果：2015-05-07T11:36:39.138Z


## **实用日期计算公式**


### **计算本月最后一天**

    {{addDays(setDate(addMonths(now, 1), 1), -1)}}

结果示例：2023-11-30T00:00:00.000Z


### **计算本月最后一毫秒**

    {{formatDate(addDays(setDate(addMonths(now, 1), 1), -1), "YYYY-MM-DDT23:59:59.999Z")}}

结果示例：2023-11-30T23:59:59.999Z


### **计算上个月最后一天**

    {{addDays(setDate(now, 1), -1)}}

结果示例：2023-10-31T00:00:00.000Z


## **特殊日期计算**


### **计算月份中的特定星期几**

**功能** ：查找某月的第n个星期几

    {{addDays(setDate(1.date, 1), 1.n * 7 - formatDate(addDays(setDate(1.date, 1), "-" + 1.dow), "E"))}}

参数说明：

  - 1.n：第几个（如：2表示第二个）

  - 1.dow：星期几（1=周一，7=周日）

  - 1.date：指定月份的日期


### **计算两个日期之间的天数**

    {{round((2.value - 1.value) / 1000 / 60 / 60 / 24)}}


## **时间转换公式**


### **秒数转换为时分秒格式**

    {{floor(1.seconds / 3600)}}:{{floor((1.seconds mod 3600) / 60)}}:{{(1.seconds mod 3600) mod 60}}


## **实用技巧**


### **1\. 时区处理**

  - UTC是全球统一时间标准

  - 北京时间是UTC+8

  - 建议在处理国际业务时使用UTC

    // UTC时间
    {{formatDate(now, "YYYY-MM-DD HH:mm", "UTC")}}
    // 北京时间
    {{formatDate(now, "YYYY-MM-DD HH:mm", "Asia/Shanghai")}}


### **2\. 日期范围处理**

建议使用半开区间方式：

    // 某月完整时间范围
    开始：YYYY-MM-01 00:00:00
    结束：YYYY-MM+1-01 00:00:00


## **常见应用场景**


### **1\. 定时任务设置**

设置每天早上9点执行

    {{setHour(setMinute(setSecond(now, 0), 0), 9)}}


### **2\. 数据统计时段**

获取上月数据范围

    开始：{{setDate(addMonths(now, -1), 1)}}
    结束：{{addDays(setDate(now, 1), -1)}}


### **3\. 活动时间管理**

检查是否在活动时间内

    {{now > startDate && now < endDate}}


## **最佳实践建议**

  1. **时区统一**

     - 存储使用UTC

     - 显示使用本地时区

  2. **日期格式化**

     - 保持格式一致性

     - 使用ISO标准格式存储

  3. **边界处理**

     - 总是考虑极限情况

     - 注意月份天数差异

> 💡 小贴士：
> 
>   - 处理日期时请始终考虑时区问题
> 
>   - 使用半开区间可以避免很多边界问题
> 
>   - 关键业务逻辑建议使用UTC时间
> 
> 


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-19/720345/077949ca48951bbe7fd9c052e190fe6a.png)