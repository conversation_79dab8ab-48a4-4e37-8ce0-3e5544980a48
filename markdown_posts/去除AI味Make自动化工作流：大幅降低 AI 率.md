---
title: "去除AI味Make自动化工作流：大幅降低 AI 率"
created_at: "2025-04-05T07:34:50.000000Z"
uuid: "47feac50-be3d-4a0e-ba4d-a1a4fb530325"
tags: ['会员独家']
---

# 去除AI味Make自动化工作流：大幅降低 AI 率

**标签**: 会员独家

视频链接：<https://xiangyugongzuoliu.com/make-workflow-with-less-ai-detection/>

在 AI 与自动化技术迅速发展的今天，如何让内容既具有人性化的表达，又不留下明显的 AI 痕迹，成为了众多内容创作者面临的难题。今天，翔宇介绍一个全新的 Make 自动化工作流，它能够一键实现文章的智能润色和优化，并有效去除 AI 的痕迹。本文将详细解析该工作流的简介、原理以及 Make流程概要。

![去除 AI 味的 Make 自动化工作流](https://xiangyugongzuoliu.com/wp-content/uploads/2025/04/<EMAIL>)

* * *


## 工作流简介

用户只需要将需要优化的文章作为输入，整个工作流便会自动运行，生成经过润色的高质量文章，并附带 AI 特征分析报告和优化策略。


## 下载地址：

[工作流（点击）](<https://pan.quark.cn/s/bb70e20aed61>)

提取码：gCUF


## **工作流流程详解**


### **1.基础触发器模块**

工作流的第一步是基础触发器模块。在这里，用户只需输入需要改写的文章文本。这个环节简洁明了，确保原始文本能够准确无误地进入后续处理环节，为整体改写打下坚实基础。


### **2.谷歌 Gemini 模块**

接下来进入关键的润色阶段，工作流调用了谷歌的 Gemini 模块，该工作流借鉴和使用海拉鲁编程客大佬的去除 AI 味提示词，在此表示感谢。同时工作流选择了拥有免费额度的 Gemini 2.5 Pro Experimental 大模型，该模型具有较高的上下文窗口方便，方便进行内容改写。

• **结构预设** ：在提示词中预设了输出的结构，确保最终结果能够包含文章、AI 特征分析报告和润色策略三个部分。


### **3.正则表达式处理模块**

生成的文本经过润色后，输出内容中包含了多个信息块。此时，正则表达式处理模块发挥了重要作用。

• **内容分离** ：该模块按照预设正则表达式将文本精准分为改写后的文章、AI 特征分析报告和润色优化策略三个部分。

• **高效提取** ：这种正则表达式提取不仅提高了输出的清晰度，也方便用户后续查阅和使用各个部分的内容。


### **4.设置变量模块**

最终，通过设置变量模块分别输出文章、AI 特征分析报告和润色优化策略三个部分。整个工作流可以嵌入其它工作流进行使用，润色优化输出文章，显著降低 AI 生成率。**注意：**

**该工作流由于会对文章改写，所以写作风格有可能会变化，根据自己需求进行使用！**

**翔宇只对谷歌 Gemini 大模型进行了有限测试，可能存在 AI 率不降反升的情况，并非 100% 有效。**

**因为 AI 检测里面有很多情况也包含了查重，所以尽量选用新文章进行测试。**


## **总结**

这款去除 AI 痕迹的 Make 自动化工作流集成了基础触发、先进的谷歌 Gemini 润色以及正则表达式精准分割三个核心模块，不仅提升了文章的质量和原创性，同时也极大地提高了内容处理的效率。翔宇希望今天的工作流能帮助各位内容创作之路！

![翔宇工作流](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/翔宇工作流.png)