---
title: "Make中文教程：日期时间解析符号（Tokens for date/time parsing）"
created_at: "2024-09-19T08:42:59.000000Z"
uuid: "dd05ed5b-10d0-4fb3-ae5c-e6e014831429"
tags: ['Make基础教程']
---

# Make中文教程：日期时间解析符号（Tokens for date/time parsing）

**标签**: Make基础教程

# **Make平台日期/时间解析符号指南**

**翔宇工作流** 今天为大家带来关于日期时间格式化的教程。

**教程简介** ：本教程介绍如何使用日期/时间格式化的**符号** 来改变日期/时间数据的显示格式。通过本教程，用户将学习如何将时间戳转换为不同的日期/时间格式，例如年份、月、日、季度、星期几等。

**学习目标** ：掌握使用Make.com中的日期/时间格式化**符号** ，将日期/时间数据转换为指定格式的能力。通过熟悉不同tokens的使用，灵活应用于各种日期/时间的数据处理和展示需求。

**学习内容** ：本教程涵盖了年份、月份、日期、星期几、小时、分钟、秒、毫秒和时区等方面的格式化tokens，包括常见的格式转换、带前导零的数字、缩写和全称及ISO标准的日期/时间格式。

**概念理解** ：**token** 是预定义的符号，用于指定日期/时间格式化的规则。例如，YYYY表示4位数字的年份，MM表示带前导零的月份，hh表示带前导零的12小时制时间。通过不同的tokens组合，可以将时间数据格式化为所需的展示方式。


## **什么是解析符号？**

解析**符号** 就像是**解日期密码的小工具** ，专门帮助电脑识别各种日期和时间的写法。

比如，你看到“2024年11月10日”就知道这是一个日期，但电脑不懂，它需要有人告诉它“年份在前面，接着是月份和日期”。解析**符号** 就起到了这个作用，它告诉电脑：“YYYY”代表年份，“MM”代表月份，“DD”代表日期。这样，电脑就能把“2024年11月10日”读出来了。简单说，解析令牌就是教电脑**怎么看懂日期和时间** ，无论是“2024-11-10”还是“10/11/2024”，它都能搞明白！


### **1\. 年月日标记详解**

![](https://static.xiaobot.net/file/2024-11-19/720345/60e29f256b068f0a0956fdf87d3ff11e.png)

**实用示例**

    // 解析完整日期
    {{parseDate("2024-01-15", "YYYY-MM-DD")}}
    // 解析带季度的日期
    {{parseDate("Q1 2024", "Q YYYY")}}
    // 解析Unix时间戳
    {{parseDate("1410715640579", "x")}}


### **2\. 周相关标记详解**

![](https://static.xiaobot.net/file/2024-11-19/720345/f31c5570427c546b7397fdb1c312246e.png)

**应用场景**

    // 解析星期几
    {{parseDate("Monday", "dddd")}}
    // 解析ISO周
    {{parseDate("2024-W02", "GGGG-[W]WW")}}
    // 解析工作日
    {{parseDate("3", "E")}}  // 星期三


### **3\. 时间标记详解**

![](https://static.xiaobot.net/file/2024-11-19/720345/c2c3c36fb69d6af8f53a5dd88398bee6.png)

**常用组合**

    // 解析12小时制时间
    {{parseDate("2:30 PM", "h:mm A")}}
    // 解析24小时制时间
    {{parseDate("14:30:25", "HH:mm:ss")}}
    // 解析带时区的时间
    {{parseDate("14:30 +08:00", "HH:mm Z")}}


## **💻 实战练习**


### **练习1：基础日期解析**

    // 输入：2024-01-15 14:30
    {{parseDate("2024-01-15 14:30", "YYYY-MM-DD HH:mm")}}


### **练习2：友好格式转换**

    // 输入：Mon, Jan 15 2024, 2:30:00 PM
    {{parseDate("Mon, Jan 15 2024, 2:30:00 PM", "ddd, MMM DD YYYY, h:mm:ss A")}}


### **练习3：年度天数提取**

    // 获取当前是一年中的第几天
    {{formatDate(now, "DDD")}}


## **❓ 常见问题解答**

  1. **Q: 如何处理不同地区的日期格式？**
         
         // 美式日期
         {{parseDate("01/15/2024", "MM/DD/YYYY")}}
         // 欧式日期
         {{parseDate("15.01.2024", "DD.MM.YYYY")}}

  2. **Q: 如何解析不确定格式的时间？**
         
         // 尝试多种格式
         {{parseDate(input, ["YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY"])}}

  3. **Q: 如何处理时区转换？**
         
         // 指定时区解析
         {{parseDate("2024-01-15 14:30 +08:00", "YYYY-MM-DD HH:mm Z")}}


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载** ！

![](https://static.xiaobot.net/file/2024-11-19/720345/681d87a5a0db6ffb529d7cbbe26fe633.png)