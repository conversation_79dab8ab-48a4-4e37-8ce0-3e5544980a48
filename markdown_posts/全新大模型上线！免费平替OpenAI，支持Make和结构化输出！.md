---
title: "全新大模型上线！免费平替OpenAI，支持Make和结构化输出！"
created_at: "2024-12-09T12:58:54.000000Z"
uuid: "17cb50a4-b256-46cf-90aa-c874f34b980a"
tags: ['API']
---

# 全新大模型上线！免费平替OpenAI，支持Make和结构化输出！

**标签**: API

![](https://static.xiaobot.net/file/2024-12-09/720345/e4e205925cce2d51983cf45fedd6a57e.png)

如果你是刚入门Make自动化工作流的小伙伴，如果你不方便在OpenAI官网上绑卡充值API，如果你想找一个支持结构化输出的免费大模型，那么今天这个文章非常适合你。该模型现在免费调用，支持Chat、FIM、Agents、Embeddings、Classifiers、Files、Fine Tuning、Models、Batch等功能，几乎覆盖所有使用场景。


# 介绍

今天给大家介绍的事Mistral AI，这是一家总部位于巴黎的人工智能初创公司，成立于2023年，迅速崛起为开源大语言模型领域的重要参与者，而且Make.com已经为Mistral AI开发了原生模块，用户只需要绑定一下API密钥即可快速开始使用。

![](https://static.xiaobot.net/file/2024-12-09/720345/cc5bfd0bcd31c096a1563fe9c5821ee2.png)


# 使用方法

Mistral AI的注册也非常简单，来到官网[https://mistral.ai/，](<https://mistral.ai/>)使用邮箱登录，登陆后来到[**La Plateforme**](<https://console.mistral.ai/>)的管理台，点击左侧的Billing页面进入之后绑定手机号即可，如果没有海外手机可以使用接码平台<https://sms-activate.guru/cn>进行手机注册，之后选择Experiment选项，即可获得API密钥。

![](https://static.xiaobot.net/file/2024-12-09/720345/812b2f79ab0992371c691c4332bf7e77.png)![](https://static.xiaobot.net/file/2024-12-09/720345/345d321ec090bea42d227dc5ab6ab775.png)

来到Make之中，搜索模块Mistral，之后新建模块，添加账号，输入刚刚获得密钥即可完成配置。

![](https://static.xiaobot.net/file/2024-12-09/720345/edb5affda77a10f4e94ea7c95360db0e.png)

在此之后可以调用该模块进行大模型的创作工作，在创作过程中模型可以选择mistra -large-latest这个模型，如果有其他需求可以访问如下网址<https://docs.mistral.ai/getting-started/models/models_overview/>，来筛选适合的模型。

如果需要结构化输出，可以打开高级选项，将回复格式选择JSON，在提示词中明确要求JSON格式输出即可开始调用，简单方便。

![](https://static.xiaobot.net/file/2024-12-09/720345/dd1e81e9cca7d25274e6c445bc70a9b5.png)

现在翔宇测试，毕竟是初创公司，而且是免费的大模型，偶尔会出现超时的报错，输出效果各位可以根据实际提示词进行测试，该模块可以平替OpenAI模块，实现结构化输出，非常值得一试，翔宇也会出专门出一期视频介绍这一神器！


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**