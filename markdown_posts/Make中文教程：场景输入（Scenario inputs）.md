---
title: "Make中文教程：场景输入（Scenario inputs）"
created_at: "2024-09-21T09:17:57.000000Z"
uuid: "fedcf2cd-6afd-4085-90ef-26de5e558453"
tags: ['Make基础教程']
---

# Make中文教程：场景输入（Scenario inputs）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于情景输入的教程。

  1. **教程简介** ：本教程介绍了Make.com中“场景输入”功能，主要包括定义输入结构、使用场景输入与设置场景、编辑和删除场景输入结构、以及通过API与场景输入交互的方法。

  2. **学习目标** ：学习如何在Make.com中使用场景输入功能，以便在运行自动化场景时传递必要数据，实现如创建新员工账户等复杂任务的自动化。

  3. **学习内容** ：教程主要涵盖以下内容：场景输入功能的适用范围、定义和使用场景输入、编辑和删除场景输入、以及通过API与场景输入交互的方法。

  4. **概念理解** ：场景输入是一种允许用户在每次运行场景时动态输入数据的功能。用户需定义输入数据的结构，以确保每次运行场景时能提供必要的数据，使场景能够正确执行。

  5. **任务练习** ：请尝试在Make.com中创建一个新“场景”，定义一个包含姓名、电子邮箱和员工ID的输入结构，运行场景以自动化创建新员工账户的过程。


# **场景输入**

场景输入功能使每个人只需填写所需的场景输入就能运行复杂的场景。

场景输入允许您在每次运行场景时将具有预定义结构的数据传递给该场景。首先,您定义预期的数据结构。然后,每当您运行该场景时,Make 都会显示一个表单,您可以在其中输入数据。您的场景输入数据随后会提供给该场景中的所有模块。

![](https://static.xiaobot.net/file/2024-11-24/720345/4b14e749ee61a0264dfd79bfc4ce9f3b.png)

想象一下,一位新员工即将开始在您的公司工作。您可能需要在几个不同的系统\(例如 JIRA、Slack、电子邮件、Miro 等\)中为新员工创建新账户。为创建这些账户,您需要新员工的姓名、电子邮件、员工 ID,以及可能的其他个人信息。如果没有这些数据,您无法继续进行账户创建。一旦拥有了所有必要的信息,您就可以开始为新员工设置账户了。

要自动化此操作,您可以设置一个情景,它将为您的新员工在所需的所有不同系统中创建新帐户。使用情景输入功能来定义输入结构作为您的情景的起点。您可以使用姓名、电子邮件和员工 ID 作为您的情景输入。一旦您输入并将员工信息保存到情景中,该情景就会运行并创建所有帐户,使您的新员工可以开始工作\!如果没有您定义为必需的所有数据,情景就无法继续。

此外，您无需了解其背后的复杂方案逻辑以及如何在不同系统中创建帐户。Make 专家可以为您预先构建场景。您只需输入所需的输入（姓名、电子邮件、员工 ID）,场景就会运行。通过这种方式,即使是普通用户也可以使用场景输入运行复杂的自动化。

在以下情况下您也可以使用场景输入:

  - 在多个系统中同步新客户的信息

  - 将离线线索添加到 CRM、电子邮件列表,并创建一份提案

  - 通过仅提供一组输入就可以创建新的记录/发票/电子邮件/订单


## **  观看视频**

<https://www.youtube.com/watch?v=DmQ-C55oM34>


## **定义场景输入结构**

![](https://static.xiaobot.net/file/2024-11-24/720345/a4fc9cc0608dc7b7fcf406e80ae74971.png)

  1. 在场景编辑器中,单击场景输入图标。

![](https://static.xiaobot.net/file/2024-11-24/720345/7d9bd53c079e28268ea1287c6b9bc995.png)
  2.  点击添加项目。

这个过程与定义数据结构的过程相同。

![](https://static.xiaobot.net/file/2024-11-24/720345/c739e1f93ee766214f0aa13e0fd55d27.png)
  3. 输入名称。此字段为必填。

![](https://static.xiaobot.net/file/2024-11-24/720345/564242200786d5881d0ae0ae1984b401.png)
  4. 在规格说明中，从下拉菜单选择输入数据类型。

![](https://static.xiaobot.net/file/2024-11-24/720345/0743548145e764b642343fcd38906df5.png)
  5. 可选方案:默认情况下,输入您希望获得的默认值。

  6. 在"必填"中, 选择此输入是否必填, 以启动您的场景。

     - 如果您选择是,除非您提供所需的输入,否则您的场景无法运行。

![](https://static.xiaobot.net/file/2024-11-24/720345/bc27f351d6e8a61afbe0e6714c355442.png)
     - 如果您选择"否"，该方案可以在没有特定输入的情况下运行。

  7. 可选: 如果您选择了文本、数组或集合作为您的规范, 在多行中, 选择您希望您的文本显示的方式。

     - 选择"是"将显示多行文本。

     - 选择否仅显示一行文本。

![](https://static.xiaobot.net/file/2024-11-24/720345/b61f5c55b02cc9f52ccf7b550f5f813c.png)
  8. 如果您需要添加更多项目,请重复该过程。

  9.  点击保存。

![](https://static.xiaobot.net/file/2024-11-24/720345/61e176f3c2627f3e3e9a5830e8a530da.png)![](https://static.xiaobot.net/file/2024-11-24/720345/43cb8fbf7822b53ee927fe93faec13f2.png)
  10. 点击保存图标保存您的场景。

![](https://static.xiaobot.net/file/2024-11-24/720345/fd002140fa3904da231251bda0eccb7d.png)

您可以将方案输入用作方案中的可映射值。您可以在映射对话框的"变量"选项卡下找到它们。

![](https://static.xiaobot.net/file/2024-11-24/720345/80f44d61adbd1b22383a5601f5e6137a.png)![](https://static.xiaobot.net/file/2024-11-24/720345/6c71b9cfc0086fa3db966fd6e5f00cfe.png)![](https://static.xiaobot.net/file/2024-11-24/720345/3b022ff5853a6d9c8a39810df98594af.gif)![](https://static.xiaobot.net/file/2024-11-24/720345/bbe1ccea648479720370f79bad8744b3.png)


## **  使用情景投入**

一旦您定义了您的情景输入,您定义的情景输入就会作为可映射的值出现在映射对话框的变量选项卡中。您可以在您的情景中使用情景输入可映射的值。

有了定义好的情景输入，每次你触发一个情景\(通过"运行一次"或通过 API\)，情景输入窗口就会弹出。

只有当您用正确的数据类型填写所有必填字段时,带有情景输入的情景才能运行。

![](https://static.xiaobot.net/file/2024-11-24/720345/4a02a3563ba1ca1a332f657fa97b4c3c.gif)


## **编辑场景输入结构**

![](https://static.xiaobot.net/file/2024-11-24/720345/94da63cbab7734210e0f6dc3195b1aae.png)

  1. 在场景编辑器中导航到场景输入图标。

  2. 点击您想要编辑的项目。

  3.  做出你的改变。

![](https://static.xiaobot.net/file/2024-11-24/720345/df7cebc7ef901a1d81754463894e2a75.png)![](https://static.xiaobot.net/file/2024-11-24/720345/f8b49383fc292824fb328161f7e07148.png)
  4.  点击保存。

一旦您保存了更改,这些更改会自动在您使用特定输入可映射值的所有位置更新。这并不指场景输入名称\(见上文\)。


## **删除方案输入结构中的项目**

![](https://static.xiaobot.net/file/2024-11-24/720345/7f8b8dbfc4581bc7016e49d369b1a5f2.png)

  1. 要从输入结构中删除一个项目,请导航到场景编辑器中的场景输入图标。

  2. 找到您想要删除的项目。

  3. 点击 X 删除该项目。

要删除所有项目,请点击所有项目的 X。

![](https://static.xiaobot.net/file/2024-11-24/720345/45f973fa23a99b6b12fd967a46b81492.png)
  4. 点击保存以保存您的更改。


## **  相关 API**

该 Make API 允许您以编程方式定义场景输入,并使用 API 将输入值传递给场景并运行它。

在 API 中,场景输入的定义称为场景接口。

您也可以使用"Make"应用程序的"运行场景"模块。请检查"Make"应用程序的文档。

以下端点可用:

  -  获取场景接口

  - 更新场景界面

  -  执行方案


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/58cf0b63a4a5605675f7d94f24c8d8d8.png)