---
title: "Make13.完整教程：Make和EXA AI全自动撰写微信公众号图文及小红书笔记"
created_at: "2024-08-24T03:41:26.000000Z"
uuid: "f3dff274-89ce-476a-af52-502de7c92955"
tags: ['资源包']
---

# Make13.完整教程：Make和EXA AI全自动撰写微信公众号图文及小红书笔记

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/13-make-exa-ai-wechat-xiaohongshu-automation/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载](<https://pan.quark.cn/s/59d7ec14cf1e>)

**3\. Notion 数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[数据库模版](<https://xiaoyuzaici.notion.site/c7093c5ad40644b7b4dbcc5e7f70984a?v=4a90d9d038fb46e687565554ae766051&pvs=4>)

**4\. 视频配套PPT**

<https://gamma.app/docs/MakeEXA-AI-139btzwr68fztix>

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！


# **视频介绍**

该工作流程旨在自动化生成高质量原创图文并茂的文章和小红书笔记，大幅提升自媒体运营效率。

**流程步骤：**

**获取热门标题:** 通过 RSS 订阅源捕捉热门标题，获取最新热点话题和流行趋势。 也可以在 Notion 中自行输入文章主题。

"当然也可以通过在notion之中自行输入文章的主题来生成文章"

**深度素材检索:** 利用 Exa AI 深度检索与热门标题相关的丰富知识和信息。

"EXA将你的AI与全球的知识相连，使其无所不知且保持最新"

**关键词提取:** 利用 ChatGPT 从素材中提取关键词，用于图片检索。

"第三步是利用chat GPT将所获取的这些信息进行关键词的提取，提取出与素材相关的关键词，用于图片的检索"

**图片获取:** 利用 Pexels 无版权图片库 API，根据关键词获取与文章相关的图片链接，增添视觉效果。

"我们利用这些检索的关键词在Pexels中无版权的这种图片库的API获取与文章相关的这种图片链接，为文章增添视觉效果"

**整合生成:** 将所有信息整合，生成图文并茂的完整文章和小红书笔记，并系统化保存到 Notion 知识库中。

"最后一步，将所有的信息进行整合，生成图文并茂的完整文章和小红书的笔记，并系统化的保存到我们notion知识库中"

**工具介绍:**

  - EXA AI:

  - 为 AI 模型提供干净的互联网抓取数据。

  - 基于语义的检索，理解语义而非简单的关键词匹配。

  - 从任何网页抓取完整和清洁的内容。

  - 实时抓取最新知识和信息。

  - 强大的过滤器，可按域名、日期、数据类别等筛选。

  - 主要应用场景：

  - 搜索引擎：实时获取最新信息。

  - 写作助手：获取灵感和信息，整合写作素材。

  - 模型训练：精选数据集用于模型训练。

  - 竞争分析：获取竞争对手最新动态详细信息。

  - Pexels:

  - 高质量的图库和视频共享平台。

  - 庞大的素材库，涵盖各行各业。

  - 免费访问，快速集成。

  - 多样的搜索选项。

**工作流搭建：**

  - 使用 Make.com 平台搭建自动化工作流。

  - 利用 RSS 模块获取热门标题。

  - 利用 Notion 模块输入自定义主题。

  - 利用 HTTP 模块调用 EXA AI 和 Pexels API。

  - 利用 ChatGPT 模块生成文章、提取关键词、生成小红书笔记。

  - 利用数组和集合的概念处理数据。

  - 利用正则表达式删除空图片。

  - 最终将所有内容保存到 Notion 知识库。

**工作流特点：**

  - 模块化设计，可根据需求灵活调整。

  - 利用函数和公式简化流程，减少操作数。

  - 使用随用随付的 API 服务，节省成本。

  - 通过案例讲解，帮助用户快速学习 Make 的使用技巧。

**需要注意的是:**

  - Pexels 图库中的图片可能不够精细，难以满足特定需求。

  - ChatGPT 生成的内容可能需要人工审核和修改。

总体来说，该工作流程提供了一种高效、便捷的自媒体内容生成方案，能够帮助用户快速生成原创、高质量的文章和小红书笔记，提高运营效率。