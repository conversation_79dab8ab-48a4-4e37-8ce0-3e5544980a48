---
title: "Make中文教程：场景执行、循环和阶段（Scenario execution, cycles, and phases）"
created_at: "2024-09-21T14:56:35.000000Z"
uuid: "6b16b4b4-d172-49a0-bd51-8795d4fb71b2"
tags: ['Make基础教程']
---

# Make中文教程：场景执行、循环和阶段（Scenario execution, cycles, and phases）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于场景执行与循环阶段的教程。

  1. **教程简介** ： 该教程详细描述了Make.com中场景的执行过程，包括初始化、循环操作和终结阶段。它强调了在各个阶段中执行操作、提交和回滚的机制，并介绍了ACID模块的应用与示例。

  2. **学习目标** ： 学习本教程后，用户能够掌握Make.com场景的执行逻辑，理解不同阶段的作用，以及如何利用ACID模块确保数据一致性与可靠性，特别是在数据库操作中。

  3. **学习内容** ： 教程内容涵盖场景的初始化、循环操作（包括读写操作、提交和回滚）、终结阶段，同时还详细介绍了支持和不支持回滚的ACID模块及其典型应用示例。

  4. **概念理解** ：

  - **初始化** ：建立必要的连接，检查模块状态。

  - **循环操作** ：执行读写任务，按需提交或回滚。

  - **提交** ：确认操作成功，提交数据变化。

  - **回滚** ：出错时撤消操作，恢复初始状态。

  - **终结** ：关闭连接，结束场景。

  - **ACID模块** ：支持事务，确保回滚操作的一致性。

  5. **任务练习** ： 创建一个包含MySQL、MSSQL和PostgreSQL数据库操作的场景，确保在操作成功时提交数据，在出现错误时能够回滚所有更改，以验证你对初始化、循环操作、提交、回滚和终结阶段的理解。


# **场景执行、循环和阶段**

低代码平台是一个事务性系统,类似于关系型数据库。每个场景执行都始于初始化阶段,继续至少一个由操作和提交/回滚阶段组成的循环,并最终以终止阶段结束。

  1.  初始化

  2.  循环 \#1

     1. 操作\(读取或写入\)

     2.  提交或回滚

  3.  第二个循环

     1. 操作（读取或写入）

     2.  提交或回滚

  4. \---

  5.  循环 \#N

     1. 操作\(读取或写入\)

     2.  提交或回滚

  6.  最终定稿

![](https://static.xiaobot.net/file/2024-11-24/720345/9e71298fbcbb8738004f193396d95295.png)


## **  初始化**

在初始化阶段,所有必要的连接\(连接到数据库、电子邮件服务等\)都会被创建。并且检查每个模块是否能够执行其预期的操作。


## **  循环**

每个周期代表一个不可分割的工作单元,由一系列操作组成。可以在场景设置中设置最大周期数。默认数量是 1。


## ** ** 操作

在操作阶段，执行读取和/或写入操作

  - 读取操作包括从提供服务获取数据,然后根据预定义的场景由其他模块进行处理。例如,Dropbox>监视文件模块会返回自上次场景执行以来创建的新文件包。

  - 写入操作包括向特定服务发送数据以进行进一步处理。例如,Dropbox > 上传文件模块将文件上传到 Dropbox 文件夹。


## **  提交**

如果所有模块的操作阶段都成功,则开始提交阶段,在此阶段提交所有模块执行的操作。这意味着 Make 会将操作阶段成功的信息发送给所有参与的服务。


## **  回退**

如果在任何模块的操作或提交阶段发生错误,该阶段将被中止,回滚阶段将启动,使该周期内的所有操作无效。某些模块不支持回滚,这些模块执行的操作无法撤回。更多信息请参见 ACID 模块部分。


## **  最终决定**

在最终确定阶段,打开的连接\(如 FTP 连接、数据库连接等\)被关闭,并且该场景完成。


## **  ACID 模块**

支持回滚（事务性模块）的模块已被标记为 ACID 标签

![](https://static.xiaobot.net/file/2024-11-24/720345/18bb66efbd1b043ce5853c810cc3cb1d.png)

没有使用此标签的模块不支持回滚,如果其他模块发生错误,无法恢复到最初的状态。

一个典型的例子是电子邮件发送电子邮件模块。在其操作阶段,模块发送电子邮件后,发送无法撤消。


## **  范例**

**在数据库之间转移捆绑包**

以下示例展示了如何连接三个 ACID 模块。下面的场景旨在从 MySQL 数据库获取新行、将其插入\(转移\)到 MSSQL 数据库,然后将 MSSQL 数据库中行的 ID 插入到 PostgreSQL 数据库。

![](https://static.xiaobot.net/file/2024-11-24/720345/7a69d48058fa1b4f1d2aaa17af41473f.png)

当场景开始时,首先执行初始化阶段。Make 逐一验证与 MySQL、MSSQL 和 PostgreSQL 数据库的连接。如果一切顺利并且连接成功,Make 就进入操作阶段。如果发生错误,则会启动终结阶段而不是操作阶段,并终止该场景。

接下来是操作阶段。预设程序从 MySQL 中选择（读取）表格行（包）。这些行然后被传递到下一个模块,该模块将它们写入 MSSQL 数据库中的所选表格。如果一切正常,最后将调用 PostgresSQL 程序将前一模块返回的行 ID 插入到该表中。

如果操作阶段成功完成,则提交阶段开始。make 将为每个数据库发出 SQL COMMIT 命令,写入操作将被提交。

但是,如果由于错误\(例如连接失败\)导致操作或提交阶段失败,Make 会调用回滚。在回滚阶段,Make 会依次遍历所有模块,并为每个模块执行 SQL ROLLBACK 命令,将每个数据库恢复到初始状态。

最后,在最终确定阶段,每个模块将关闭它与数据库的连接。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/95c966dffd048ae62c2bca5dc4851ae9.png)