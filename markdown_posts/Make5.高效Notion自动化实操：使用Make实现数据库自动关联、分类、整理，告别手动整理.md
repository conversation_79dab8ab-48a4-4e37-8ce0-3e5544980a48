---
title: "Make5.高效Notion自动化实操：使用Make实现数据库自动关联、分类、整理，告别手动整理"
created_at: "2024-08-24T03:36:48.000000Z"
uuid: "86e6a93a-3920-4663-bbe9-adb86aa8b134"
tags: ['资源包']
---

# Make5.高效Notion自动化实操：使用Make实现数据库自动关联、分类、整理，告别手动整理

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/5-notion-make-database-automation/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版图文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载](<https://pan.quark.cn/s/dcb4647195c4>)

**3\. Notion 数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[数据库模版](<https://xiaoyuzaici.notion.site/bf6e45ca849343bf987e6fb59549826e?v=0a251b146e384cfc8a98ce2413c03554&pvs=4>)

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！


# **视频介绍：**

本文档将对提供的视频内容进行详细分析，提炼出核心主题和关键信息，并佐以原文引用，帮助您更好地理解和应用 Notion 数据库自动化关联技术。

**一、核心主题：**

本视频教程的核心主题是如何利用第三方自动化工具 Make.com 实现 Notion 数据库的自动关联、分类和整理，告别手动操作，提高知识管理效率。

**二、关键信息提炼：**

**关联属性的意义和作用：**

  - 关联属性是 Notion 数据库中的一种特殊属性，它允许将一个数据库的条目与其他数据库的条目相关联，从而打通不同数据库之间的信息壁垒，实现知识的交叉应用和整合。

“关联属性……它允许让我们的一个条目的数据或一个数据库的数据关联到其他的数据库之中……方便用户在不同的数据之中快速的访问和整合相关数据。”

  - 视频中以“知识库”和“应用场景”两个数据库为例，演示了如何通过关联属性将知识条目按照应用场景进行自动分类和关联，方便用户从不同维度审视知识。

**自动化关联的优势：**

  - **促进知识的系统化和结构化：** 通过关联关系，可以将分散在不同数据库中的信息进行整合，形成结构化的知识体系，提高知识检索效率。

“这 种关联关系……它促进了知识的系统化和结构化……提高知识检索的效率。”

  - **便于知识的交叉应用和整合：** 关联关系允许用户从不同的角度审视知识，例如从“项目”数据库关联到“文档库”和“教程库”，可以更全面地了解项目相关信息。

“这种知识的交叉和应用和整合非常便捷……可以新增我们对知识审视的这种角度”

  - **解放双手，提升效率：** 自动化关联规则可以避免手动操作的繁琐，节省时间和精力，提高知识管理效率。

“这种自动关联……就是可以解放双手……减少我们手动维护的这一个工作量，从而提供这种提升知识管理的便捷性和效率”

**自动化关联的实现步骤：**

  - **审视 Notion 数据库结构和关联需求：** 明确需要关联的数据库以及希望实现的关联关系。

  - **获取关联数据库的 ID：** Make.com 通过 Notion API 实现数据库连接和操作，需要获取关联数据库的 ID 进行映射。视频中演示了如何通过 Make.com 获取应用场景数据库中每个条目的 ID。

  - **利用 OpenAI 进行自动分类：** 将需要分类的知识条目标题、关键词、内容等信息发送给 OpenAI，利用其强大的自然语言处理能力进行自动分类，并返回相应的分类 ID。

  - **更新关联属性：** 利用 Make.com 将 OpenAI 返回的分类 ID 映射到目标数据库的关联属性中，实现自动关联。

**注意事项：**

  - **运行形式的匹配：** Make.com 的“watch DATABASE item ID”模块并非实时监控，需要根据知识库更新频率设置合理的运行时间和抓取条目数量。

  - **固定分类的局限性：** 当前案例中的分类是预先定义好的，如果需要实现更灵活的动态分类，需要进一步学习和探索更高级的自动化方法，例如 Webhook 技术。

**三、总结：**

通过学习本视频教程，您可以掌握利用 Make.com 实现 Notion 数据库自动化关联的基本方法，告别手动操作，提高知识管理效率。同时，视频也指出了当前方法的一些局限性，并鼓励用户持续学习和探索更高级的自动化技巧，以应对更复杂的知识管理需求。