---
title: "Make9.微信公众号图文混排文章自动化实战：利用Make 批量制作"
created_at: "2024-08-24T03:39:21.000000Z"
uuid: "8ddc6dcd-e8cc-411e-8df1-0b8eff851bfe"
tags: ['资源包']
---

# Make9.微信公众号图文混排文章自动化实战：利用Make 批量制作

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/9-wechat-make-batch-article-automation/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载](<https://pan.quark.cn/s/3b90d0ddb1a5>)

**3\. Notion 数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[数据库模版](<https://xiaoyuzaici.notion.site/395c0d704435488eaf5ae599fe41aeb0?v=76f8e583ba78497d93e0b3c9beb5ae93&pvs=4>)

**4\. 视频配套PPT**

<https://gamma.app/docs/Make--qkojaom088076q7>

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！


# **视频介绍**

**主题：** 利用 Make（自动化工作流工具）批量生成微信公众号图文混排文章，以 GitHub 开源项目推荐公众号为例进行实战演示。

**目标受众：** 微信公众号运营者、程序员、对开源项目感兴趣的用户、自媒体从业者等。


### 公众号定位

**实战分析：** 以 GitHub 每日推荐类公众号为例，其特点是面向程序员和普通用户推荐最新、最热门的开源项目，吸引对技术动态感兴趣的读者。

**理论总结：** 公众号定位需明确主题、目标群体以及内容方向，才能吸引精准受众，打造成功公众号。


### 信息获取

**实战分析：** 利用谷歌搜索 GitHub Trend 的 RSS 订阅源，选择全语言热门项目订阅源，获取包含标题、链接和全文的 XML 格式内容。

"我们可以直接点击daily这个网址，这个网址就是它的一个订阅源，它里面推送的内容就是我们XML格式的一个内容...它实际上有全文的内容推送，因为我们知道GitHub上面的整个它的...它的这个文件，它就是说我们的介绍的页面是是个mark down格式的，我们就可以通过GitHub，GitHub这个订阅源来进行获取。"

**理论总结：** 推荐使用 RSS 订阅源进行信息获取，其优势在于：

  - **实时推送:** 获取网站最新发布信息。

  - **结构化数据:** XML 格式易于后期处理。

  - **定制化订阅:** 可按需组合、筛选信息源，满足垂直领域需求。


### 内容生成

**实战分析：**

  1. 利用 Make 中的模块将 RSS 获取的 HTML 格式信息转换为 Markdown 格式。

  2. 利用 Chat GPT 对 Markdown 内容进行翻译、润色，生成符合要求的中文图文混排文章。

  3. 将最终生成的 Markdown 内容保存至 Notion 知识库，方便后续发布和调用。

**理论总结：**

  - 不同平台对内容格式要求不同，需选择合适的工具进行素材整理和优化。

  - 推荐使用 Markdown 格式进行图文混排内容制作，其优势在于：

  - 易于编辑和排版。

  - 支持图片链接，方便跨平台发布。

  - 未来将介绍更多内容制作方法，例如：

  - 利用 HTML 在图片中嵌入文字。

  - 将网页、PDF 等格式转换为 Markdown。


### 信息发布

**实战分析：**

  - 利用国内外自媒体同步工具或开源平台实现多渠道分发。

  - 国外平台可利用 API 直接发布内容。

  - 国内平台可借助 RPA 工具实现自动化发布。

**理论总结：**

  - 信息发布的最终目的是提高效率。

  - 自动化工具可以帮助我们实现从信息获取到内容发布的全流程自动化，大幅提升工作效率。


### 总结

本教程以 GitHub 开源项目推荐公众号为例，详细介绍了如何利用 Make 自动化生成微信公众号图文混排文章。

**核心要点：**

  - 明确公众号定位，找准目标受众。

  - 利用 RSS 订阅源高效获取信息。

  - 选择合适的工具进行内容生成和优化。

  - 利用自动化工具实现多渠道分发，提高效率。

希望大家关注翔宇工作流，学习更多自动化工作流教程，利用自动化工具提升工作效率。