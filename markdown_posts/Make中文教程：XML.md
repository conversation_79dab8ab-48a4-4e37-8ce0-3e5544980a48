---
title: "Make中文教程：XML"
created_at: "2024-09-19T12:24:25.000000Z"
uuid: "0d60a7f1-8dac-47ee-8e50-01ce78bfec2b"
tags: ['Make基础教程']
---

# Make中文教程：XML

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于XML教程的教程。

  1. **教程简介** ：本教程介绍如何使用Make.com中的XML模块，来解析和创建XML格式的文本。内容包括解析XML文本为数据包、创建XML文本，以及处理XML属性和数据结构的具体方法和示例。

  2. **学习目标** ：理解和掌握如何在Make.com中解析XML格式的文本数据，并将数据转换为XML格式文本。学会使用这些模块来处理复杂的数据转换需求，实现数据在不同系统之间的无缝传输。

  3. **学习内容** ：

     - 如何解析XML格式文本

     - 如何创建XML格式文本

     - 处理XML属性和数据结构的技巧

     - 用实际示例演示从URL下载和解析XML文件

     - 将Google电子表格中的数据转换为XML文件

  4. **概念理解** ：

     - **解析XML（Parse XML）** ：解析XML格式的文本并输出包含所有提取信息的数据包，方便后续模块使用。

     - **创建XML（Create XML）** ：将数据包转换为XML格式的文本，用于数据的输出和传输。

     - **数据结构** ：描述XML的结构，用于定义数据的组织方式，使解析和创建XML模块能够正确处理数据。

  5. **任务练习** ：

     - 使用XML解析模块，解析一个从URL下载的XML文件，并提取其中的信息。

     - 使用XML创建模块，将Google表格中的数据转换为XML格式，并通过Email发送生成的XML文件。

     - 练习映射和处理XML属性，创建一个带有复杂节点和属性的XML文件。


## **开始使用 XML**

XML 应用程序可以让您:

  - 通过"XML > 解析 XML"模块解析 XML 格式的文本,并将其转换为包以使数据可供其他模块使用

  - 将捆绑包转换为 XML 格式的文本，通过 XML > 创建 XML 模块


## **解析 XML**

XML > 解析 XML 模块解析 XML 格式的文本并输出一个包含从 XML 中提取的所有信息的单个捆绑包。

![](https://static.xiaobot.net/file/2024-11-23/720345/672ad89f20003bbaf0bc941858eca889.png)![](https://static.xiaobot.net/file/2024-11-23/720345/ee64034e749796812e2a30cca6ae3055.png)


### **解析 XML 的示例**

一个典型的用例是从 URL 下载一个 XML 文件并解析其内容。以下是实现这一目标的分步指南:

  1.  创建一个新的场景

  2. 插入 HTTP > 获取文件模块

  3. 打开模块的配置，并按照以下方式进行配置:

**URL**<https://siftrss.com/f/rqLy05ayMBJ>

![](https://static.xiaobot.net/file/2024-11-23/720345/e24d2d6a62bc72fcaf7d577299cf3921.png)
  4. 关闭模块的配置。

  5. 添加 XML > 解析 XML 模块，将其连接到 HTTP > 获取文件模块之后，并进行如下配置:

**  数据结构**

     1.  点击"添加"。

     2.  点击生成器

     3. 在您的网络浏览器中,打开一个新标签页/窗口。

     4. 在地址栏中输入您在第三步中使用的 URL,并获取 XML 文件。

     5. 选择所有的 XML 文本并复制到剪贴板。

     6. 关闭标签页/窗口并回到您的情境

     7. 将复制的 XML 文本粘贴到示例数据字段中。

     8.  点击保存。

     9. 验证数据结构已成功生成。

     10. 点击保存以保存数据结构。


### **  注意**

您可以跳过步骤 2-9 来提供一个空的数据结构。这样,该模块的输出将无法在映射面板中显示,直到该模块至少执行一次以处理 XML 输入。

**XML**

将 HTTP > 获取文件的输出中的 Data 项映射到该字段。使用 toString\(\) 函数将其值从 Buffer（二进制数据）类型转换为 Text 类型。

您可以将公式的代码复制并粘贴到该字段中: \{\{toString\([1.data](<http://1.data>)\)\}\}

![](https://static.xiaobot.net/file/2024-11-23/720345/ce72e79011f28c66e58104b20ac8bf9d.png)


### **解析 XML 属性**

默认情况下,XML > 解析 XML 模块会将属性放在一个特殊的集合 \_attributes 中,作为具有这些属性的节点的子节点。如果节点是文本节点并且它有属性,则会添加两个特殊属性: \_attributes 用于属性, \_value 用于节点的文本内容。

**  示例**

    The Hobbit J.R.R. Tolkien 1937
    
        Hello, World

将被转换为此捆绑包:

![](https://static.xiaobot.net/file/2024-11-23/720345/015a604b48c2b973669846b08b3bcf50.png)


## **创建 XML**

XML > 创建 XML 模块将包转换为 XML 格式的文本。

![](https://static.xiaobot.net/file/2024-11-23/720345/b357555c2c75abc1c731c4600feb25a9.png)


### **  示例**

一个典型的用例是将数据从 Google 电子表格转换为 XML。以下是将数据转换为 XML 文件的十个步骤的操作过程:

  1. 将 Google 工作表 > 选择行模块放入您的方案中以获取数据。设置该模块以从您的 Google 电子表格中检索行，并将返回的最大行数设置为较小的数字,但大于一个用于测试目的\(例如三个\)。执行 Google 工作表模块\(右键单击它并选择"仅运行此模块"\)并验证模块的输出。

  2. 在 Google 表格模块之后连接 Array Aggregator 模块。在模块的设置中,在源节点字段中选择 Google 表格模块。暂时将其他字段保持原样。

  3. 在数组聚合器模块之后连接 XML > 创建 XML 模块。该模块的设置需要一个数据结构来描述 XML 输出的结构。单击"添加"以打开数据结构设置。创建此数据结构的最简单方法是从 XML 示例自动生成它。单击"生成器"并将您的 XML 示例粘贴到"示例数据"字段中:

![](https://static.xiaobot.net/file/2024-11-23/720345/15ba223ac4d5359765b120f1cb4737e0.png)
  4. 点击保存。数据结构设置中的规格字段现在应该包含生成的结构。

  5. 将您的数据结构的名称更改为更具体的名称\(例如"我的 XML 数据结构"\),然后单击保存。如果一切顺利,则根 XML 元素对应的字段应该作为可映射的字段出现在 XML 模块的设置中。

  6. 在该字段旁边的地图上点击，并将 Array 聚合器模块输出的 Array\[\] 项映射到该地图上:

![](https://static.xiaobot.net/file/2024-11-23/720345/ad98498058324113bbd7282e8cedb2cb.png)
  7. 单击"确定"关闭 XML 模块的设置。

  8. 打开阵列聚合器模块的设置。将目标结构从自定义更改为相应于父级 XML 元素的 XML 模块字段。将 Google Sheets 模块输出的项目映射到适当的字段:

![](https://static.xiaobot.net/file/2024-11-23/720345/2f60d2ab3a81a1e47e2633b2a08df42c.png)
  9. 单击"确定"关闭 Array Aggregator 模块的设置。

  10. 运行该场景。如果一切顺利,XML 模块应该输出正确的 XML 文件。打开 Google Sheets 模块的设置,将返回的最大行数增加到大于您的电子表格中的行数,以处理所有数据。然后可以将生成的 XML 保存到 Dropbox,通过电子邮件发送为附件,或通过 FTP 上传到服务器等。


### **添加 XML 属性**

如果您要向复杂节点（包含其他节点的节点）添加属性,则必须在自定义数据结构中为该节点添加一个名为 \_attributes 的集合,该集合将映射到节点属性

如果您要向文本节点添加属性\(示例: abc \),则必须在您的自定义数据结构中为该节点添加一个属性集合 \_attributes 和一个文本属性 \_value 。

**  示例**

    {
        "name": "node",
        "type": "collection",
        "spec": [
            {
                "name": "_attributes",
                "type": "collection"
                "spec": [
                     {
                         "name": "attr1",
                         "type": "text"
                     }
                ]
            },
            {
                "name": "_value",
                "type": "text"
            }
        ]
    }


## **排除 XML 故障**


### **无法从解析 XML 模块映射数据**

确保数据结构定义正确。或者你可以使用一个空的数据结构,至少执行一次模块来处理 XML 输入。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-19/720345/df332cf80e573b4eaee8bc67cd5c6aa1.png)