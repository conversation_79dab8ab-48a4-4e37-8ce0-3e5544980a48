---
title: "HTTP第三方API调用：Replicate"
created_at: "2024-09-22T03:21:46.000000Z"
uuid: "00f2a006-cd75-4b44-877e-97a41e9240fc"
tags: ['快捷工具']
---

# HTTP第三方API调用：Replicate

**标签**: 快捷工具

**使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 Make.com 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 <https://jsonformatter.org/点击“Format> / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下文档**

[HTTP第三方API调用：Replicate](<https://kdocs.cn/l/coN1OelPPGEd>)

图片生成：

    {
      "subflows": [
        {
          "flow": [
            {
              "id": 8,
              "module": "http:ActionSendData",
              "version": 3,
              "parameters": {
                "handleErrors": false,
                "useNewZLibDeCompress": true
              },
              "mapper": {
                "url": "https://api.replicate.com/v1/models/black-forest-labs/flux-schnell/predictions",
                "serializeUrl": false,
                "method": "post",
                "headers": [
                  {
                    "name": "Authorization",
                    "value": "Bearer $REPLICATE_API_TOKEN"
                  },
                  {
                    "name": "Content-Type",
                    "value": "application/json"
                  }
                ],
                "qs": [],
                "bodyType": "raw",
                "parseResponse": true,
                "authUser": "",
                "authPass": "",
                "timeout": "",
                "shareCookies": false,
                "ca": "",
                "rejectUnauthorized": true,
                "followRedirect": true,
                "useQuerystring": false,
                "gzip": true,
                "useMtls": false,
                "contentType": "application/json",
                "data": "{\n  \"input\": {\n    \"prompt\": \"black forest gateau cake spelling out the words \\\"FLUX SCHNELL\\\", tasty, food photography, dynamic shot\"\n  }\n}",
                "followAllRedirects": false
              },
              "metadata": {
                "designer": {
                  "x": 210,
                  "y": 265,
                  "name": "HTTP"
                },
                "restore": {
                  "expect": {
                    "method": {
                      "mode": "chose",
                      "label": "POST"
                    },
                    "headers": {
                      "mode": "chose",
                      "items": [null, null]
                    },
                    "qs": {
                      "mode": "chose",
                      "items": [null]
                    },
                    "bodyType": {
                      "label": "Raw"
                    },
                    "contentType": {
                      "label": "JSON (application/json)"
                    }
                  }
                },
                "parameters": [
                  {
                    "name": "handleErrors",
                    "type": "boolean",
                    "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                    "required": true
                  },
                  {
                    "name": "useNewZLibDeCompress",
                    "type": "hidden"
                  }
                ],
                "expect": [
                  {
                    "name": "url",
                    "type": "url",
                    "label": "URL",
                    "required": true
                  },
                  {
                    "name": "serializeUrl",
                    "type": "boolean",
                    "label": "Serialize URL",
                    "required": true
                  },
                  {
                    "name": "method",
                    "type": "select",
                    "label": "Method",
                    "required": true,
                    "validate": {
                      "enum": ["get", "head", "post", "put", "patch", "delete", "options"]
                    }
                  },
                  {
                    "name": "headers",
                    "type": "array",
                    "label": "Headers",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "qs",
                    "type": "array",
                    "label": "Query String",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "bodyType",
                    "type": "select",
                    "label": "Body type",
                    "validate": {
                      "enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]
                    }
                  },
                  {
                    "name": "parseResponse",
                    "type": "boolean",
                    "label": "Parse response",
                    "required": true
                  },
                  {
                    "name": "authUser",
                    "type": "text",
                    "label": "User name"
                  },
                  {
                    "name": "authPass",
                    "type": "password",
                    "label": "Password"
                  },
                  {
                    "name": "timeout",
                    "type": "uinteger",
                    "label": "Timeout",
                    "validate": {
                      "max": 300,
                      "min": 1
                    }
                  },
                  {
                    "name": "shareCookies",
                    "type": "boolean",
                    "label": "Share cookies with other HTTP modules",
                    "required": true
                  },
                  {
                    "name": "ca",
                    "type": "cert",
                    "label": "Self-signed certificate"
                  },
                  {
                    "name": "rejectUnauthorized",
                    "type": "boolean",
                    "label": "Reject connections that are using unverified (self-signed) certificates",
                    "required": true
                  },
                  {
                    "name": "followRedirect",
                    "type": "boolean",
                    "label": "Follow redirect",
                    "required": true
                  },
                  {
                    "name": "useQuerystring",
                    "type": "boolean",
                    "label": "Disable serialization of multiple same query string keys as arrays",
                    "required": true
                  },
                  {
                    "name": "gzip",
                    "type": "boolean",
                    "label": "Request compressed content",
                    "required": true
                  },
                  {
                    "name": "useMtls",
                    "type": "boolean",
                    "label": "Use Mutual TLS",
                    "required": true
                  },
                  {
                    "name": "contentType",
                    "type": "select",
                    "label": "Content type",
                    "validate": {
                      "enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]
                    }
                  },
                  {
                    "name": "data",
                    "type": "buffer",
                    "label": "Request content"
                  },
                  {
                    "name": "followAllRedirects",
                    "type": "boolean",
                    "label": "Follow all redirect",
                    "required": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "metadata": {
        "version": 1
      }
    }

获取输出：

    {
      "subflows": [
        {
          "flow": [
            {
              "id": 8,
              "module": "http:ActionSendData",
              "version": 3,
              "parameters": {
                "handleErrors": false,
                "useNewZLibDeCompress": true
              },
              "mapper": {
                "url": "https://api.replicate.com/v1/predictions/gm3qorzdhgbfurvjtvhg6dckhu",
                "serializeUrl": false,
                "method": "get",
                "headers": [
                  {
                    "name": "Authorization",
                    "value": "Bearer $REPLICATE_API_TOKEN"
                  }
                ],
                "qs": [],
                "bodyType": "raw",
                "parseResponse": true,
                "authUser": "",
                "authPass": "",
                "timeout": "",
                "shareCookies": false,
                "ca": "",
                "rejectUnauthorized": true,
                "followRedirect": true,
                "useQuerystring": false,
                "gzip": true,
                "useMtls": false,
                "contentType": "application/json",
                "data": "",
                "followAllRedirects": false
              },
              "metadata": {
                "designer": {
                  "x": 210,
                  "y": 265,
                  "name": "HTTP"
                },
                "restore": {
                  "expect": {
                    "method": {
                      "mode": "chose",
                      "label": "GET"
                    },
                    "headers": {
                      "mode": "chose",
                      "items": [null, null]
                    },
                    "qs": {
                      "mode": "chose",
                      "items": [null]
                    },
                    "bodyType": {
                      "label": "Raw"
                    },
                    "contentType": {
                      "label": "JSON (application/json)"
                    }
                  }
                },
                "parameters": [
                  {
                    "name": "handleErrors",
                    "type": "boolean",
                    "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                    "required": true
                  },
                  {
                    "name": "useNewZLibDeCompress",
                    "type": "hidden"
                  }
                ],
                "expect": [
                  {
                    "name": "url",
                    "type": "url",
                    "label": "URL",
                    "required": true
                  },
                  {
                    "name": "serializeUrl",
                    "type": "boolean",
                    "label": "Serialize URL",
                    "required": true
                  },
                  {
                    "name": "method",
                    "type": "select",
                    "label": "Method",
                    "required": true,
                    "validate": {
                      "enum": ["get", "head", "post", "put", "patch", "delete", "options"]
                    }
                  },
                  {
                    "name": "headers",
                    "type": "array",
                    "label": "Headers",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "qs",
                    "type": "array",
                    "label": "Query String",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "bodyType",
                    "type": "select",
                    "label": "Body type",
                    "validate": {
                      "enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]
                    }
                  },
                  {
                    "name": "parseResponse",
                    "type": "boolean",
                    "label": "Parse response",
                    "required": true
                  },
                  {
                    "name": "authUser",
                    "type": "text",
                    "label": "User name"
                  },
                  {
                    "name": "authPass",
                    "type": "password",
                    "label": "Password"
                  },
                  {
                    "name": "timeout",
                    "type": "uinteger",
                    "label": "Timeout",
                    "validate": {
                      "max": 300,
                      "min": 1
                    }
                  },
                  {
                    "name": "shareCookies",
                    "type": "boolean",
                    "label": "Share cookies with other HTTP modules",
                    "required": true
                  },
                  {
                    "name": "ca",
                    "type": "cert",
                    "label": "Self-signed certificate"
                  },
                  {
                    "name": "rejectUnauthorized",
                    "type": "boolean",
                    "label": "Reject connections that are using unverified (self-signed) certificates",
                    "required": true
                  },
                  {
                    "name": "followRedirect",
                    "type": "boolean",
                    "label": "Follow redirect",
                    "required": true
                  },
                  {
                    "name": "useQuerystring",
                    "type": "boolean",
                    "label": "Disable serialization of multiple same query string keys as arrays",
                    "required": true
                  },
                  {
                    "name": "gzip",
                    "type": "boolean",
                    "label": "Request compressed content",
                    "required": true
                  },
                  {
                    "name": "useMtls",
                    "type": "boolean",
                    "label": "Use Mutual TLS",
                    "required": true
                  },
                  {
                    "name": "contentType",
                    "type": "select",
                    "label": "Content type",
                    "validate": {
                      "enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]
                    }
                  },
                  {
                    "name": "data",
                    "type": "buffer",
                    "label": "Request content"
                  },
                  {
                    "name": "followAllRedirects",
                    "type": "boolean",
                    "label": "Follow all redirect",
                    "required": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "metadata": {
        "version": 1
      }
    }