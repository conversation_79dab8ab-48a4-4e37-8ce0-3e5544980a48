---
title: "Make中文教程：工具（Tools）"
created_at: "2024-11-18T08:11:27.000000Z"
uuid: "0d864ebb-61e6-456c-8958-7ea3477cabc6"
tags: ['Make基础教程']
---

# Make中文教程：工具（Tools）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于工具的教程。

  1. **教程简介** ：本教程介绍了Make.com中多个工具模块，包括触发器、增量功能、休眠、变量设置和获取、聚合器及转换器。这些工具可以有效增强和优化场景流程。

  2. **学习目标** ：了解并掌握Make.com中各工具模块的功能和使用方法，能够在各种场景中应用这些工具，提高工作流程的自动化和效率。

  3. **学习内容** ：教程主要包括触发器的创建、自定义包的定义、增量功能的使用、场景流程的休眠、变量的设置与获取、数据的聚合以及数据类型转换等内容。

  4. **概念理解** ：

     - **触发器** ：用于创建自定义事件触发点，启动特定任务。

     - **增量功能** ：每次操作后值增加1，可用于轮询分配任务或提醒。

     - **休眠** ：暂停场景流程指定时间，减少服务器负载。

     - **设置/获取变量** ：存储和检索场景运行中的数据。

     - **聚合器** ：将多个数据包合并为一个，用于表格、文本或数值数据的整合。

     - **转换器** ：将数据转换为不同的数据类型和编码。

  5. **任务练习** ：

     - 创建一个自定义触发器并定义其输入包。

     - 使用增量功能模块实现一个轮询分配任务的场景。

     - 配置一个休眠模块，暂停场景5分钟后继续执行。

     - 设置一个变量并在另一个模块中获取该变量的值。


### **触发器**


### **  基本触发器**

允许您创建自定义触发器并定义其输入包。

![](https://static.xiaobot.net/file/2024-11-18/720345/7b2bc3a734a0442f739e66f030fef066.png)


## **  递增函数**

在每个模块操作后返回增加 1 的值。可以配置模块以重置该值：

  -  经过一个循环

  -  执行一个场景后

  -  从不

**  例子**

以下情景在每次奇数情景运行后向第一个收件人发送电子邮件，并在每次偶数情景运行后向第二个收件人发送电子邮件。

![](https://static.xiaobot.net/file/2024-11-18/720345/16cc125166b3f60f14abf60110287609.png)

配置模块以永不重置数值：

![](https://static.xiaobot.net/file/2024-11-18/720345/c0bd6c9680ac47e3453e8bc5394041b8.png)

路由器模块后面有两个条件

1\. 奇数 - 使用模数数学函数设置条件，使其等于 1 。

![](https://static.xiaobot.net/file/2024-11-18/720345/745f7c50c53b9a7bc74e570eebbb3781.png)

不要忘记将默认的文本运算符更改为数值运算符！

![](https://static.xiaobot.net/file/2024-11-18/720345/26f65656d11db50a9862e7a83c2c55ed.png)

2\. 偶数 - 使用模数数学函数设置条件，使其等于 0

![](https://static.xiaobot.net/file/2024-11-18/720345/4cfa0269d7807262688bd94eb5ca24df.png)


## **  睡眠**

允许您延迟最多 300 秒（5 分钟）的场景流程。

![](https://static.xiaobot.net/file/2024-11-18/720345/7243e9f34dc0edea86365624445b27e3.png)

第一个场景最终会将所有必要信息与当前时间戳一起存储在数据存储中。第二个场景会定期检查数据存储中时间戳早于预期延迟的记录，检索记录，完成数据处理，并从数据存储中删除记录。


### **设置变量**

创建一个变量，可供路由中的其他模块映射，或供场景中每个路由的“获取变量”模块使用。

![](https://static.xiaobot.net/file/2024-11-18/720345/2ad4624c0fafb7390ef7b164d4c0c349.png)


## **  设置多个变量**

创建多个变量，可以由路由中的其他模块映射，也可以由场景中每个路由的“获取多个变量”模块映射，所有这些在单个操作中完成。

模块的主要优点是：

  - 一个设置多个变量的模块可以替代一整套设置变量模块

  - 一个设置多个变量的模块只消耗一次操作

![](https://static.xiaobot.net/file/2024-11-18/720345/f4bea52968c2b625def3c61af65c0083.png)

设置获取（多个）变量模块的可能用途：

  - 为了将计算出的值存储以便以后使用，甚至在不同的路径中。特别是在值在多个模块中使用且计算值的公式过于复杂的情况下。

  - 调试公式。如果模块中使用的公式似乎没有提供正确的结果，请复制该公式并将其粘贴到一个“设置变量”模块中，该模块应插入到相关模块之前。在“设置变量”模块之后断开模块，并执行该场景。验证“设置变量”模块的输出，调整/简化公式，再次执行该场景，并继续这样做，直到问题得到解决。** **


## **获取变量**

检索之前由设置变量模块创建的值。

检索之前由“设置变量”模块创建的值。请注意，此模块可以读取在场景中任何位置设置的变量。唯一的要求是在“工具”>“设置变量”模块之前（时间上）执行“工具”>“获取变量”模块。

![](https://static.xiaobot.net/file/2024-11-18/720345/99a2a300dbd0eb2a19afd90f63771a9f.png)


## **  获取多个变量**

检索之前由“设置多个变量”模块在单个操作中创建的值。

模块的主要优点是：

  - 一个获取多个变量的模块可以替代一整套获取变量的模块

一个获取多个变量的模块只消耗一个操作

![](https://static.xiaobot.net/file/2024-11-18/720345/b4e760113be1267eb2d0e9b25b364b57.png)

** ** 设置获取（多个）变量模块的可能用途：

  - 为了将计算出的值存储以便以后使用，甚至在不同的路径中。特别是在值在多个模块中使用且计算值的公式过于复杂的情况下。

  - 调试公式。如果模块中使用的公式似乎没有提供正确的结果，请复制该公式并将其粘贴到相关模块之前插入的设置变量模块中。在设置变量模块之后断开模块，并执行该场景。验证设置变量模块的输出，调整/简化公式，再次执行该场景，并继续这样做，直到问题得到解决。


## **  聚合器**


### **  表格聚合器**

将接收到的捆绑包中所选字段的值合并到一个捆绑包中，使用指定的列和行分隔符（从而可以创建表格）。

![](https://static.xiaobot.net/file/2024-11-18/720345/75227671e1fbb87e86bf336003fbc189.png)


### **  文本聚合器**

将接收到的捆绑包的选定字段值合并到单个捆绑包中。

**  示例**

您可以使用文本聚合工具将更多数值（例如客户姓名或备注）插入到一个捆绑包中，并发送一封包含所有数值的电子邮件，可以是电子邮件正文或电子邮件主题。


### **  数值聚合器**

该模块允许您检索数值，然后应用所选函数之一（SUM，AVG，COUNT，MAX 等），并将结果一次性返回。

**  示例**

该模块将在数字参数下的值求和。

![](https://static.xiaobot.net/file/2024-11-18/720345/9d05fe713cb8dcc72c9654fc90142395.png)


### **变形器**


### **组成一个字符串**

将任何值转换为字符串数据类型（文本）。在映射例如二进制数据时，使映射更容易。


### **转换文本的编码**

将输入的文本（或二进制数据）转换为所选的编码。

![](https://static.xiaobot.net/file/2024-11-18/720345/6baf741c868ca1e3651098edec4257a9.png)


### **  开关**

检查输入值是否与提供的值列表匹配，并根据结果返回相应的输出。

![](https://static.xiaobot.net/file/2024-11-18/720345/5e4f0a8128882f80a67160d9bbef117f.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/3a835f1c67fac9c4f2b4843556a08fbb.png)