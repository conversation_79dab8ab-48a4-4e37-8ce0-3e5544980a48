---
title: "Make中文教程：OpenAI (ChatGPT、Whisper、DALL-E)"
created_at: "2024-09-01T02:48:09.000000Z"
uuid: "2fe22e2e-cb71-49d2-8a70-e910d20287d6"
tags: ['Make基础教程']
---

# Make中文教程：OpenAI (ChatGPT、Whisper、DALL-E)

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于OpenAI 使用指南的教程。

  1. **教程简介** ：本教程介绍如何在Make中使用OpenAI模块（包括ChatGPT、Whisper和DALL-E）。通过该模块，你可以实现聊天、提示完成、图像生成、文件管理等多种AI功能。

  2. **学习目标** ：掌握将OpenAI连接到Make并使用各类AI功能模块的操作方法，能够通过Make平台实现各种OpenAI API调用和自动化任务。

  3. **学习内容** ：教程涵盖如何获取并使用OpenAI的API密钥和组织ID，如何在Make中创建连接，如何使用各类AI功能模块，如聊天、图像生成、转录、翻译等。

  4. **概念理解** ：Make中的OpenAI模块允许用户通过API调用实现AI功能。你需要获取自己的API密钥和组织ID才能连接和使用这些模块，支持的模型包括GPT-3.5、GPT-4等。

  5. **任务练习** ：创建一个包含OpenAI模块的Make场景，尝试使用“生成图像”功能模块，输入一个简单的提示词（例如：“一只猫在草地上”），并生成相应的图像。


# **OpenAI \(ChatGPT, Whisper, DALL-E\)**

在 Make 中使用 OpenAI（ChatGPT、Whisper、DALL-E）模块，您可以创建聊天或提示完成，编辑和调节内容，生成图像，管理文件和批次，并调用 API。

要开始使用 OpenAI,您需要一个 OpenAI 账户。

![](https://static.xiaobot.net/file/2024-11-26/720345/6d3fd1eebc83e02cc88b343635a47906.png)


## **  连接 OpenAI 到Make**

要连接 OpenAI 到 Make，您必须从您的账户中获取 API 密钥和组织 ID。

![](https://static.xiaobot.net/file/2024-11-26/720345/f1d0c7ebfe85571a3b959dfe55c8574a.png)

  1. 登录您的 OpenAI \(ChatGPT、Whisper、DALL-E\)账户。

  2. 点击右上角的个人资料图标>查看 API 密钥。

![](https://static.xiaobot.net/file/2024-11-26/720345/f1429029c4db9cbe327613390d4f64d4.png)
  3. 点击"创建新的密钥"，给密钥一个名称\(可选\)，然后点击"创建密钥"。

  4. 复制密钥,将其存储在安全的地方,因为您将无法再次查看它,然后点击完成。

  5. 请转到您的帐户设置页面,并将您的组织 ID 复制到剪贴板上。

![](https://static.xiaobot.net/file/2024-11-26/720345/cc35e2930a84d9a04856a6d18147be5a.png)
  6. 登录您的 Make 账户,将 OpenAI \(ChatGPT、Whisper、DALL-E\) 模块添加到场景中,然后单击"创建连接"。

  7. 在"连接名称"字段中输入连接的名称。

  8. 在 API 密钥字段中输入在步骤 4 中复制的密钥。

  9. 在"组织 ID"字段中,输入在步骤 5 中复制的组织 ID,然后单击"保存"。

您已成功建立连接。您现在可以编辑您的场景并添加更多 OpenAI\(ChatGPT、Whisper、DALL-E\)模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤操作。


## **  触发器**


### **  批次完成**

批次完成时触发

![](https://static.xiaobot.net/file/2024-11-26/720345/325bbd4eaec84722c7d4d395c15e55f1.png)


## **人工智能**


### **  在线与助手沟通**

向指定的或新创建的线程发送消息并无缝执行。此操作可将您的函数调用的参数发送到指定的 URL（仅使用 POST HTTP 方法）。适用于 Assistants v2。

![](https://static.xiaobot.net/file/2024-11-26/720345/0c48815fd3e5ae4a89a38f8516498ffa.png)


### **创建完成\(提示\)\(GPT-3, GPT-3.5, GPT-4\)**

为提供的提示或聊天生成完成内容。

请查看 OpenAI 模型端点兼容性部分以了解支持的模型列表。

![](https://static.xiaobot.net/file/2024-11-26/720345/cf400dadd0ac2527ca7acd97ded481c4.png)


### **将文本转换为结构化数据**

从提示的原始文本中识别指定信息,并将其作为结构化数据返回。

![](https://static.xiaobot.net/file/2024-11-26/720345/cd5505cd9276c3345e9fd7cc20c902ee.png)


### **分析图像\(s\) - 视觉**

接受以图像数组作为输入,并根据提示中指定的说明为每个图像提供分析结果。

![](https://static.xiaobot.net/file/2024-11-26/720345/7e0979ca7e6589975a1f267c874cb19a.png)


### **生成图像**

根据给定的提示,使用 DALL E 生成图像。

![](https://static.xiaobot.net/file/2024-11-26/720345/215734412d60c4d29fed6a6dc86d2582.png)


### **编辑图像**

编辑或扩展图像。

![](https://static.xiaobot.net/file/2024-11-26/720345/ff9bbd5c5e21640d57857095ccaa7661.png)


### **创建一个翻译\(Whisper\)**

创建音频的英语翻译。

![](https://static.xiaobot.net/file/2024-11-26/720345/d77359f106548f5cb8822dd5c5029959.png)


### **创建转录\(Whisper\)**

将音频转录为文本。

![](https://static.xiaobot.net/file/2024-11-26/720345/0adb69e4a4caac6b5e55a35145d40d3a.png)


### **  创造一个审核机制**

确定提供的图像或文本是否包含暴力、仇恨、非法或成人内容。

![](https://static.xiaobot.net/file/2024-11-26/720345/26c8f37600ef643fceab64b3d36d8065.png)


### **  生成音频**

根据输入的文本和设置生成音频文件。

![](https://static.xiaobot.net/file/2024-11-26/720345/ef83a5ad5a09e150c748953d3be8fe6d.png)


## **文件**


### **将文件添加到向量存储区**

将文件添加到指定的矢量存储区中,如果未指定,则根据配置创建新的矢量存储区。

![](https://static.xiaobot.net/file/2024-11-26/720345/8c787fa9db58f17c9514a096cb0f06dc.png)


### **  上传文件**

上载文件,该文件可在 OpenAI 平台上进一步使用。

![](https://static.xiaobot.net/file/2024-11-26/720345/1a89cd449a01762e71f07fc73acb0c49.png)


## **  批次**


### **  批处理列表**

检索批次列表。

![](https://static.xiaobot.net/file/2024-11-26/720345/613e36547582adbffa98e5c178b74f05.png)


### **  获得一批**

获取指定批量的详细信息。

![](https://static.xiaobot.net/file/2024-11-26/720345/cd692cf2a56fb546374ed0537d528e7b.png)


### **  创建批次**

创建并执行一批 API 调用。

![](https://static.xiaobot.net/file/2024-11-26/720345/620f32f89132044a16bf3500a92bb4fd.png)


### **  取消批次**

取消正在进行的批次。批次将处于"取消中"状态 10 分钟左右,然后变为"已取消",届时输出文件中会有部分结果\(如果有的话\)可供查看。

![](https://static.xiaobot.net/file/2024-11-26/720345/d9e64a45793c39bc5f5121e2b38bd927.png)


## **  其他**


### **  发起 API 请求**

执行任意授权 API 调用。

![](https://static.xiaobot.net/file/2024-11-26/720345/abf0c96287f73480b915a59d9e35cf5b.png)![](https://static.xiaobot.net/file/2024-11-26/720345/8a503a25ba7c90733385cb03be57853a.png)

**使用示例 - 列表模型**

以下 API 调用可从您的 OpenAI 帐户中返回所有页面。

  /v1/models

 方法： GET

![](https://static.xiaobot.net/file/2024-11-26/720345/0c153b8ccce04033597b60352182238b.png)

搜索结果可在模块的输出中的"Bundle > Body > data"部分找到。

在我们的示例中，返回了 69 个模型:

![](https://static.xiaobot.net/file/2024-11-26/720345/1c0934e5757d488aa36473665baccb87.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-01/720345/41b7688ed65d9829d0d11a69410307f8.png)