---
title: "RSS订阅工具汇总（会员独家）"
created_at: "2024-10-04T03:45:40.000000Z"
uuid: "3fb1a892-cc7b-4775-943c-9aa1d414897b"
tags: ['AI教程与资源']
---

# RSS订阅工具汇总（会员独家）

**标签**: AI教程与资源

# 什么是RSS？

RSS，全称为 Really Simple Syndication（简易信息聚合），是一种用于格式化网站内容的标准，能够让网站向订阅用户提供文章的标题、摘要、全文等信息。通过订阅不同网站的 RSS 链接，用户可以将各种信息源整合在一个工具中，方便地阅读这些内容。

简单来说，RSS 是一种可以订阅网站内容更新的协议。虽然这种协议看似“古老”，在社交媒体和聚合阅读工具的影响下逐渐失去了主流地位，甚至 Google 在 2013 年关闭了 Google Reader 服务，但它依然有其独特的优势。那么，为什么我们仍然选择 RSS 呢？


# Make自动化工作流为什么使用 RSS？

自动化的核心在于数据的规范化和结构化，而 RSS 非常符合这些要求。RSS 采用 XML 格式，结构化的内容按时间顺序进行推送，这使得它非常适合与 make 工具配合，满足自动化内容输出的需求。通过 RSS，系统能够及时获取更新的信息，并对这些信息进行加工处理，完成从信息获取到内容制作的整个自动化流程。这种方式可以提高工作效率，实现信息流的快速响应和持续更新。


# RSS订阅源汇总资源


## **ALL about RSS：全面的 RSS 资源指南**

**GitHub 项目地址：**[** *ALL about RSS***](<https://github.com/AboutRSS/ALL-about-RSS>)

![ALL about RSS](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-3-1160x1129.png)

ALL-about-RSS 是一个 GitHub 上的开源项目，它是一个关于 RSS 相关的资源列表，内容主要基于 Telegram 频道 @AboutRSS \(中文\) 中的帖子整理而成。该列表涵盖了 RSS 的各个方面，包括 RSS 的定义、Web Feed 规范、RSS 阅读器（应用、微信小程序、托管阅读器、自托管阅读器等）、RSS 服务、RSS Feed 生成、RSS Feed 定制、API、OPML 管理、Feed 资源推荐、RSS 相关社区等等。此外，还包括与 Podcast 相关的 RSS 应用、聚合器、Feed 验证器、Feed 定制等内容。

该项目不仅是初学者的入门指南，也是资深用户的资源宝库，助力用户高效利用 RSS 工具。


## **MoreRSS**

**官方网站** ：[ *MoreRSS*](<https://morerss.com/zh>)

![](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-10-1160x1129.png)

MoreRSS 是一个现代化的 RSS 聚合平台，致力于帮助用户发现、订阅和管理各类信息源。该网站为用户提供直观清晰的界面，使你能够轻松整合新闻、博客、专题报道等不同来源的内容，并实时跟踪更新。通过个性化订阅和分类筛选功能，用户可以定制专属的信息流，享受高效而精准的阅读体验。平台支持中文界面，非常适合中文用户使用，同时也鼓励社区交流与内容共享。


## **Kindle4RSS**

**官方网站** ：[ *Kindle4RSS*](<https://kindle4rss.com/feeds/explore/?bundles=zh>)

![Kindle4RSS](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-4-1160x528.png)

Kindle4RSS 是一项为 Amazon Kindle 电子书阅读器设计的 RSS 订阅内容推送服务​。它能够定时将所订阅RSS源的新文章汇总打包，发送到用户Kindle的邮箱，从而使用户每天在Kindle上收到一个更新杂志，离线也能阅读当天的资讯。

Kindle4RSS 提供网页版管理界面，用户可以在平台上添加RSS源、配置推送时间等，非常适合习惯用Kindle看书看报的人。**翔宇主要是利用该网站获得中文、英文订阅源。**


### **功能与特色**

  - 将RSS源内容定期推送至Kindle

  - 对Kindle进行了排版优化，提供类似报刊杂志的阅读体验

  - **网站提供丰富的中文和英文RSS订阅源列表，方便用户直接复制链接订阅**

  - 支持添加自定义RSS源

该服务已稳定运营多年，大大拓展了Kindle的功能，让用户能够在电子墨水屏上追踪网络资讯，提供一个无干扰、专注的阅读环境，特别适合想要减少手机使用但又不想错过重要信息的用户。


## **Top RSS List**

**GitHub 项目地址** ：[ *Top RSS List*](<https://github.com/weekend-project-space/top-rss-list>)

![Top RSS List](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-7-1160x1020.png)

这个 GitHub 仓库由 weekend-project-space 维护，名为 **top-rss-list** ，主要整理并收集了大量优质的 RSS 订阅源。仓库内汇总了各类新闻、博客、技术、生活等领域中优秀的 RSS 源列表，为对 RSS 聚合阅读有兴趣的用户提供了一份便捷的参考资料和入口。仓库的目标是帮助大家快速找到并订阅高质量的信息源，同时也欢迎社区成员参与补充、改进和更新这份清单。对于希望通过 RSS 聚合多渠道资讯、节省时间和精力的用户来说，这份资源无疑是一个不错的选择。你可以在 GitHub 上自由浏览、下载和贡献这份列表，整个项目采用开源模式，让更多人可以一起参与维护，共同打造一份“最佳 RSS 清单”。


### 网站特点

• **订阅排名** ：按订阅人数排序，便于选择高人气 RSS 源。

• **多样化内容** ：涵盖科技、文化、新闻等领域。

• **中文资源** ：专注推荐中文优质 RSS 源。


## Top RSS Hub

<https://toprss.webfollow.cc/>

![](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/xiangyugongzuoliu-HTr3rGFL-1160x556.jpg)

TopRSS 是由 WebFollow 打造的一个 RSS 聚合平台，专注于为用户提供精选的、高质量的 RSS 订阅源集合。该网站通过清晰的分类和直观的界面，让用户可以方便地发现和浏览来自新闻、科技、娱乐、生活等多个领域的 RSS 订阅源。无论你是想丰富个人阅读内容，还是建立一个个性化的 RSS 信息聚合器，TopRSS 都为你提供了一个高效、易用的入口。


## WhereMyLife

[ *https://wheremylife.cn/home*](<https://wheremylife.cn/home>)

![](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/xiangyugongzuoliu-Jncn6BkJ-1160x760.jpg)

WhereMyLife是一个提供 RSS 订阅推送服务的网站。它精选互联网上优质的订阅源，并以杂志般的设计呈现，旨在为用户提供更稳定、更优质的 RSS 订阅体验。该网站特别提示，为了获得最佳体验，建议在电脑或手机上打开，因为 Kindle 浏览器可能不兼容。总的来说，它是一个聚合和优化 RSS feed 内容的平台。


## **FeedX**

[ *https://toprss.webfollow.cc*](<https://toprss.webfollow.cc/>)

**官方网站** ：[ *FeedX*](<https://feedx.net/>)

![FeedX](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-6-1160x1162.png)

FeedX 是一个专为那些依旧钟情于 RSS 订阅体验的用户打造的网站。它的主要功能是将各大新闻网站、报刊、博客等提供的摘要或不完整的 RSS 源，通过手工定制的技术（主要采用 PHP 和正则表达式）转换为全文 RSS，让用户在一个平台上就能阅读到完整的文章内容，而无需再点击进入原网站查看全文。


### **主要功能**

  - **RSS 源聚合** ：整合多个 RSS 源，集中管理和阅读。

  - **自定义分类** ：支持个性化订阅源分类。

  - **实时更新** ：确保用户第一时间获取最新信息。

  - **简洁界面** ：清晰直观的用户界面，便于导航。

  - **注意：** 由于时间长，部分 RSS 源已失效，可根据情况进行选择使用。

网站涵盖了丰富的内容分类，如新闻、报刊、轻松阅读、科技、英语学习等，旨在为用户提供一个干净、无干扰的订阅环境。虽然服务器偶有短暂宕机，但站长坚持不断优化，服务了大量忠实的 RSS 爱好者。

总的来说，FeedX 就是献给那些仍对 RSS 抱有热情的人们的一个全能聚合平台，通过全文输出的方式，让用户无需跳转就能获取各大媒体的详细内容，同时保持了订阅体验的纯粹和高效。


## **anyfeeder**

**官方网站** ：[ *anyfeeder*](<https://plink.anyfeeder.com/>)

![anyfeeder](https://xiangyugongzuoliu.com/wp-content/uploads/2025/01/image-9-1160x1129.png)

AnyFeeder专注于帮助用户创建、分享和管理 RSS 订阅源。


### **主要功能**

• **RSS 源列表** ：涵盖新闻、科技、财经、娱乐等领域。

• **推荐阅读器** ：支持 Qi Reader 等主流 RSS 阅读器。

• **OPML 文件下载** ：快速导入所有订阅源。

• **部分订阅源失效** ：经测试部分订阅源失效。

用户可以轻松导入 RSS 源到阅读器，随时随地获取资讯。


# 使用 RSS 阅读器

  - **Feedly\(推荐5星\)**

Feedly 是当前最流行的在线 RSS 阅读器之一，界面现代，功能强大，支持 Web 和移动应用，但是更多功能**需要付费** 。用户可以通过以下步骤订阅 RSS 源：

注册并登录 Feedly

<https://feedly.com/>

使用搜索框输入网站的名称或直接输入 RSS 源链接。

点击订阅按钮，将 RSS 源添加到您的阅读列表中。

  - **Inoreader（推荐5星）**

Inoreader 是一款功能强大的 RSS 阅读器，支持分类管理和高级筛选。输入您要订阅的内容的 RSS 链接，或通过搜索找到网站的 RSS 源，可以自定义标签来对订阅内容进行分类。

<https://www.inoreader.com/>

  - **Feeder（推荐4星）**

Feeder 是一款简单易用的在线 RSS 阅读器，支持浏览器插件和移动设备，可以让用户轻松跟踪最新的新闻和更新。

<https://feeder.co/>

可以在浏览器中添加插件，直接订阅和查看 RSS 源。

在移动设备上安装 Feeder 应用程序，随时随地获取更新。

  - **Feedspot（推荐4星）**

Feedspot是一个专注于RSS源的聚合平台，旨在帮助用户发现和管理各种主题的RSS订阅。该网站提供了丰富的RSS源列表，涵盖多个领域，包括销售、市场营销、新闻、广告等，适合不同用户的需求。

    - **发现顶级RSS源** ：用户可以根据特定主题查找最新和最受欢迎的RSS源，如金融新闻、数字营销、旅行等。

    - **丰富的分类** ：Feedspot提供多种分类的RSS源列表，例如书评、加密货币、音乐和食品等，便于用户快速找到感兴趣的内容。

    - **更新频繁** ：网站定期更新内容，确保用户获取最新的信息和资源。

  - <https://rss.feedspot.com/>

  - **The Old Reader（推荐3星）**

The Old Reader 以提供与经典 Google Reader 相似的体验而著称，非常适合喜欢简单界面的用户。

<https://theoldreader.com/>

  - **NewsBlur（推荐3星）**

NewsBlur 是一个功能齐全的 RSS 阅读器，支持浏览器和移动应用程序，可以以故事形式查看内容。

<https://newsblur.com/>


# 查找 RSS 源的几种方法


### 直接查找网站提供的 RSS 链接

  - **检查网站的底部或头部** ：通常情况下，网站的 RSS 链接会显示在主页底部或头部，标志通常是一个橙色的 RSS 图标或写有 "RSS" 的链接。用户可以点击该链接以获取 RSS 源地址。

示例：访问某博客网站时，可以在页面的底部找到类似于“订阅 RSS”或橙色图标的链接，点击即可看到该网站的 RSS 源。

  - **查看浏览器地址栏** ：某些网站在用户访问时，会在浏览器地址栏中显示一个小的 RSS 图标。点击该图标，用户就能直接订阅该网站的 RSS 源。

示例：在使用 Firefox 浏览器访问一个新闻网站时，如果该网站支持 RSS，浏览器地址栏的右侧可能会出现一个橙色的 RSS 图标，点击即可查看和订阅。

  - **手动尝试 URL 修改** ：有些网站默认使用特定的 URL 格式来提供 RSS 源，用户可以尝试在网站的 URL 后加上 /feed 或 /rss 来查看是否提供 RSS 源。

示例：对于一个名为 https://example.com 的博客，用户可以尝试访问 https://example.com/feed 或 https://example.com/rss，看看是否能够访问到该网站的 RSS 源页面。

  - **右键查看页面源代码** ：如果在网页上没有直接找到 RSS 链接，可以右键点击网页并选择“查看页面源代码”，然后在源代码中搜索 rss 或 feed 字样，通常可以找到相关的 RSS 链接。

示例：在某些新闻网站的源代码中，使用快捷键 Ctrl+F 查找 rss，可能会找到类似 的内容，这就是该网站的 RSS 链接。


# 使用第三方工具查找 RSS 源

  - **Follow（推荐5星）**

通过用户ID关注获得订阅源的获取，具体方法参见如下教程：

<https://xiaobot.net/post/3fb1a892-cc7b-4775-943c-9aa1d414897b>

  - **RSS.app（推荐5星）**

<https://rss.app/>

可以帮助用户生成网站的 RSS 源，即使目标网站没有明确提供 RSS 服务。

  - **RSSHub（推荐5星）**

RSSHub 是一个开源的、基于 Node.js 的 RSS 生成工具，可以为各种网站和服务提供 RSS 源，特别适合那些不提供 RSS 源的网站。RSSHub 是一个制作 RSS 订阅源的工具，类似于 Huginn 和 Feed43 等。与这些工具不同，RSSHub 已经预先编写了抓取规则，用户只需简单地编辑地址即可获取订阅源。**注意由于网站规则瞬息万变，会出先订阅链接失效的问题，请根据实际情况使用。**

例如，若要订阅 YouTube 上的 Linus Tech Tips 视频，只需找到其用户名「LinusTechTips」，然后在 RSSHub 官方文档中找到对应链接，输入 <https://rsshub.app/youtube/user/LinusTechTips>便可轻松创建订阅源。

  - **RSSHub Radar（推荐5星）**

RSSHub Radar 是 RSSHub 的衍生项目，旨在帮助用户快速发现并订阅当前网站的 RSS 和 RSSHub 订阅源。

RSSHub Radar 是一款浏览器扩展，帮助用户快速查找并生成网站的 RSS 链接

<https://github.com/DIYgod/RSSHub-Radar>

在 Chrome 或 Firefox 的扩展商店中搜索并安装RSSHub Radar。

使用非常简单。当您访问一个新页面时，RSSHub Radar 会自动检测该页面是否有 RSS 和 RSSHub 支持。如果有，右下角会显示提示。点击扩展图标后，会弹出列表，显示当前页面的 RSS、适用于该页面的 RSSHub 及其他支持的 RSSHub，用户可以选择复制链接或一键订阅到 Feedly、Inoreader 或 TinyTinyRSS。

**功能设置**

在设置页面，您可以自定义 RSSHub 域名、快捷键、更新规则、选择订阅平台以及开启角标提醒等功能。

**工作原理**

RSSHub Radar 是开源项目，您可以在 GitHub 查看源码。它通过分析页面中的特定 MIME 类型的 link 标签（如 \`link\[type="application/rss+xml"\]\`）来检测 RSS 链接。此外，RSSHub Radar 每 5 小时自动从 GitHub 更新规则，确保支持列表保持最新。

对于一键订阅，Feedly、Inoreader 和 TinyTinyRSS 都提供了接口，Feedly 需要用户确认订阅，而 Inoreader 和 TinyTinyRSS 则可直接订阅。通过 RSSHub 和 RSSHub Radar，用户能够更便捷地享受 RSS 订阅的便利。

  - **使用 Google News 订阅 RSS（推荐5星）**

在这篇教程中，我们将介绍如何获取和使用Google新闻的RSS源。这将帮助您在RSS阅读器中阅读Google新闻，或在您的WordPress网站上显示最新新闻。**如果感觉使用复制，可使用Feedly和inoreader阅读器都带有Google News 订阅 RSS功能。**

**获取头条新闻的RSS源**

要获取头条新闻的RSS源，只需在以下URL后面添加/rss：

<https://news.google.com/rss>

**按主题获取RSS源**

如果您想按主题获取RSS源，请访问您感兴趣的主题页面（如商业、体育、科技等），然后在URL后面添加/rss。例如，科技主题的RSS源格式如下：<https://news.google.com/rss/topics/>

**示例** ：科技主题的RSS源URL可能是：<https://news.google.com/rss/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNRGRqTVhZU0FtVnVHZ0pKVGlnQVAB>

**按主题下的特定部分获取RSS源**

类似于按主题获取RSS源，您也可以为特定部分获取RSS源。只需导航到该部分并在URL后面添加/rss：https://news.google.com/rss/topics//sections/

**示例** ：某个特定部分的RSS源URL可能是：[https://news.google.com/rss/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNRFp1ZEdvU0FtVnVHZ0pKVGlnQVAB/sections/CAQiSkNCQVNNUW9JTDIwdk1EWnVkR29TQldWdUxVZENHZ0pKVGlJT0NBUWFDZ29JTDIwdk1EZGljekFxQ2dvSUVnWlVaVzV1YVhNb0FBKi4IACoqCAoiJENCQVNGUW9JTDIwdk1EWnVkR29TQldWdUxVZENHZ0pKVGlnQVABUAE?hl=en-IN≷=IN&ceid;=IN%3Aen](<https://news.google.com/rss/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNRFp1ZEdvU0FtVnVHZ0pKVGlnQVAB/sections/CAQiSkNCQVNNUW9JTDIwdk1EWnVkR29TQldWdUxVZENHZ0pKVGlJT0NBUWFDZ29JTDIwdk1EZGljekFxQ2dvSUVnWlVaVzV1YVhNb0FBKi4IACoqCAoiJENCQVNGUW9JTDIwdk1EWnVkR29TQldWdUxVZENHZ0pKVGlnQVABUAE?hl=en-IN&gl=IN&ceid=IN%3Aen>)

**按自定义搜索关键字获取RSS源**

如果您希望获取包含特定关键字的新闻，可以使用以下格式：https://news.google.com/rss/search?q=

例如，要获取包含“Instagram”的新闻，可以使用：https://news.google.com/rss/search?q=instagram

**按国家和语言获取RSS源**

要获取中文新闻的RSS源，您可以使用类似的URL格式，其中hl代表语言，gl代表国家，ceid结合了国家代码和语言代码。

如果您想获取中国地区的简体中文新闻，您可以使用以下URL：

[https://news.google.com/rss?hl=zh-CN≷=CN&ceid;=CN:zh-CN](<https://news.google.com/rss?hl=zh-CN&gl=CN&ceid=CN:zh-CN>)

解释：

hl=zh-CN：语言设置为简体中文（zh-CN代表中文简体）。

gl=CN：国家设置为中国（CN代表中国）。

ceid=CN:zh-CN：国家和语言代码组合，指定了中国和简体中文。

  - **通过Google Alerts订阅RSS（推荐5星）**

Google Alerts是一个非常方便的方式，可以帮助你实时跟踪特定关键词的相关内容更新。Google Alerts会在监测到新内容时生成一个通知，而将其通过RSS订阅，可以让你在RSS阅读器中轻松查看这些通知。以下是详细的教程步骤：

**进入Google Alerts** ：

打开Google Alerts的页面，网址为：<https://www.google.com/alerts>

**创建一个新的Alert（提醒）** ：

\- 在搜索框中输入你想跟踪的关键词或主题。比如，你可以输入“人工智能”、“区块链新闻”或者任何你感兴趣的主题。

\- 实时预览会展示该关键词目前的热门结果。

**调整设置** ：

\- 点击搜索框右侧的“显示选项”按钮，调整提醒的详细设置。

\- **频率** ：选择你希望收到提醒的频率。可以选择“随时”、“每天一次”或“每周一次”。

\- **来源类型** ：你可以选择所有来源，或者特定来源，如新闻、博客、网页等。

\- **语言** ：选择你想要监控的语言。

\- **地区** ：选择你感兴趣的国家或地区。

\- **数量** ：选择是否希望收到所有结果，还是仅收到最相关的结果。

\- **投递至** ：在这里，你可以选择将提醒发送到邮箱或者RSS订阅。\*\*选择RSS订阅\*\*。

**选择创建快讯生成RSS订阅链接** ：

\- 在“发送到”选项中，选择“RSS Feed”。

\- 设置完成后，点击“创建快讯”按钮。你的提醒会出现在页面的下方。

**获取RSS链接** ：

\- 在Google Alerts页面中，你可以看到刚刚创建的提醒。

\- 点击该提醒旁边的“RSS”图标，它会生成一个RSS提要链接。复制这个链接即可。

  - **FetchRSS（推荐3星）**

<https://fetchrss.com/>

FetchRSS可以生成几乎任何网站的 RSS 源，用户只需输入目标网页的 URL，并跟随步骤生成符合自己需求的 RSS 链接。

  - **PolitePol（推荐2星）**

<https://politepol.com/>

PolitePol是一款简单的 RSS 生成器，适合用户为特定网页（尤其是没有 RSS 支持的网页）生成 RSS 链接。

  - **FiveFilters Feed Creator（推荐2星）**

<https://createfeed.fivefilters.org/>

输入任何网页的URL，工具将自动生成一个 RSS 源。


## 重要媒体平台订阅方法


### 微信公众号

  - **今天看啥** ：今天看啥（<https://www.jintiankansha.me/>）是一款提供微信公众号 RSS 服务的平台，用户可以在其网站上查找并生成特定公众号的RSS 链接。

  - **WeweRSS** ：WeweRSS（<https://github.com/cooderl/wewe-rss>）是另一款帮助生成微信公众号 RSS 源的工具，用户只需输入公众号的名称即可生成对应的 RSS 链接。

  - **Wechat2RSS** ：Wechat2RSS（<https://wechat2rss.xlab.app/>）是一款开源工具，允许用户为指定的微信公众号生成RSS 源，并将其添加到自己的 RSS 阅读器中。

  - 具体教程参考：<https://xiaobot.net/post/d7e45c3b-c381-429b-be3e-6738b28849f5?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>


## RSS 订阅的技巧和高级用法


### 聚合多个 RSS 源

有些 RSS 阅读器（如 Inoreader 和 Feedly）支持将多个相似类别的 RSS 源聚合到一起，方便用户统一查看。可以通过创建自定义文件夹来整理相似内容。


### 自定义关键词过滤

一些 RSS 阅读器（如 Inoreader）提供高级内容过滤功能，可以根据特定关键词设置规则，只获取感兴趣的内容。这对于高流量订阅源非常有用，可以节省时间。


## 总结

RSS 是一个强大的工具，可以帮助您集中管理和查看来自多个网站的内容。通过选择合适的 RSS 阅读器（如 Feedly、Inoreader 等），并掌握查找和订阅 RSS 源的方法，您可以更高效地获取自己感兴趣的信息。此外，一些插件和生成工具可以帮助处理那些没有明确提供 RSS 的网站，让 RSS 订阅的覆盖面更加广泛。

希望本指南能够帮助您轻松开始使用 RSS，管理自己的信息流！

![](https://static.xiaobot.net/file/2025-03-21/720345/16a9eaab0c65a43bd98db5d684a2a14f.png)