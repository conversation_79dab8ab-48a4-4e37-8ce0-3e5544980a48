---
title: "HTTP第三方API调用：豆包大模型（50万免费Token）"
created_at: "2024-11-25T05:44:02.000000Z"
uuid: "e0907337-32e5-4890-bcec-84f2b3fbb1e5"
tags: ['快捷工具']
---

# HTTP第三方API调用：豆包大模型（50万免费Token）

**标签**: 快捷工具

豆包大模型调用稍显复制，首先访问<https://www.volcengine.com/>，下滑点击豆包大模型，后续页面点击立即体验，登陆后实名认证。

![](https://static.xiaobot.net/file/2024-11-25/720345/6baa159eb2cce0e30f177557d3add8fd.png)

而后点击做成开通管理，选择需要开通的大模型，点击开通服务。

![](https://static.xiaobot.net/file/2024-11-25/720345/e056a671b692e770372fbc2813d2c450.png)

选择全部需要访问大模型，点击立即开通。

![](https://static.xiaobot.net/file/2024-11-25/720345/83759a9b78d58555dc23674621175f30.png)

开通之后，主界面点击左侧API key管理，创建API Key。

![](https://static.xiaobot.net/file/2024-11-25/720345/be1771bc5728960877c956b51e7258a8.png)

创建好之后，主界面点击在线推理，创新新的接入点

![](https://static.xiaobot.net/file/2024-11-25/720345/b7ea3f80272af463627acc494002ef86.png)

选择一个主力使用的大模型和版本确定新建推理点。

![](https://static.xiaobot.net/file/2024-11-25/720345/948e29440e7319607bf3792cddafc64c.png)

输入基本信息，点击接入模型。

![](https://static.xiaobot.net/file/2024-11-25/720345/602eb05b52396141b8c51188dd320900.png)

接入之后可以获得接入点id，也就是一般使用的模型名称。

![](https://static.xiaobot.net/file/2024-11-25/720345/91085ae65212df54995eb35ec7c5aaac.png)

使用快捷工具代码来到Make中快速粘贴获得HTTP调用模块，输入上面生成接入点id和API key，完成设置进行调用。

![](https://static.xiaobot.net/file/2024-11-25/720345/3b8435dae89bf6add5cf6fe10f6df6b3.png)

**如需复制，请访问以下文档：**

[HTTP第三方API调用：豆包大模型](<https://kdocs.cn/l/cekTwuJKkbkK>)

对话补全：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://ark.cn-beijing.volces.com/api/v3/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Content-Type","value":"application/json"},{"name":"Authorization","value":"Bearer ea764f0f-3b60-45b3-****-************"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\n    \"model\": \"ep-20240704******-*****\",\n    \"messages\": [\n        {\n            \"role\": \"system\",\n            \"content\": \"You are a helpful assistant.\"\n        },\n        {\n            \"role\": \"user\",\n            \"content\": \"Hello!\"\n        }\n    ]\n}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}


## **翔宇工作流制作**

翔宇工作流致力于持续优化和更新便捷化工具，以提升 [Make.com](<http://Make.com>) 工作流的创建与使用效率。通过内置的常用 API 调用模块代码，减少了重复配置的时间，并增强了操作的一致性和可靠性。

![](https://static.xiaobot.net/file/2024-11-25/720345/e86e2628917151f7604140c80cf9b589.png)