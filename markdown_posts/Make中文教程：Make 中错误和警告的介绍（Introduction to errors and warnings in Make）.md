---
title: "Make中文教程：Make 中错误和警告的介绍（Introduction to errors and warnings in Make）"
created_at: "2024-08-30T01:47:39.000000Z"
uuid: "0414bbf6-0caf-4522-9dfd-a71a37288348"
tags: ['Make基础教程']
---

# Make中文教程：Make 中错误和警告的介绍（Introduction to errors and warnings in Make）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Make错误与警告介绍的教程。

  1. **教程简介** ： 本教程介绍了在Make.com中处理错误和警告的机制。内容涵盖错误的性质、错误处理器的使用以及警告与其影响的区别。

  2. **学习目标** ： 了解Make.com中错误和警告的区别，学习如何处理这些问题，确保自动化场景的顺利运行，并掌握使用错误处理器的基本方法。

  3. **学习内容** ：

     - Make.com中的错误与警告的定义与区别。

     - 错误处理的策略和方法。

     - 使用错误处理器继续场景运行。

     - 错误和警告的通知机制。

     - 常见错误和警告的情境与解决方法。

  4. **概念理解** ：

     - **错误** ：场景中未被处理的意外事件导致的中断，需要暂停检查并解决。

     - **警告** ：问题存在但不影响场景继续运行，提醒用户注意可能存在的隐患。

     - **错误处理器** ：用于处理场景中错误的工具，确保场景继续运行。

  5. **任务练习** ： 创建一个简单的Make场景，并人为制造一个错误。设置错误处理器来处理该错误，确保场景在处理错误后继续运行。查看场景执行历史，以理解错误处理器的运行效果。


# Make 中错误和警告的介绍

有时候,自动化并没有按照计划进行,而是走了错误的路径。当这种情况发生时,Make 会根据具体情况给出警告或错误提示。

你还可以从另一个角度看待错误和警告:它们通过阻止意外数据的处理来保护你的方案,从而节省运营使用。


## **Make中的错误**

错误通知您,您的方案遇到了未被错误处理程序处理的意外事件。由于这种情况,您应该检查该方案。

当模块从前一个模块或模块应用程序接收到错误数据时,该模块会输出错误。当您在场景编辑器中打开您的场景时,Make 会用"警示"标志突出显示输出错误的模块:

![](https://static.xiaobot.net/file/2024-12-02/720345/1f804a4c18b9d78f41f312ef2ebc3a58.png)

当模块输出错误时,Make 会停止当前场景运行并启动回滚场景阶段。回滚阶段会尽可能撤销更改,并将轮询触发纪元恢复到场景运行之前的时间。

当连续的情景运行以错误结束时，Make 会禁用该情景的进一步调度。禁用该情景使您可以检查错误并防止在以错误结束的情景运行中消耗操作。

模块输出错误的最常见情况包括:

  - 在模块中将一个值映射到所需的字段，当该值有时为空并导致缺少所需的数据。

  - 当您在第三方应用程序中耗尽资源时。例如,当您无法在应用程序中存储更多数据时。

  - 当应用程序不可用时。例如,当应用程序因维护而停机时。

  - 当您的应用程序中的身份验证或授权发生变更且您未更新连接时。例如,当您的 API 密钥过期或您更换团队并失去对某些应用程序功能的访问权限时。

处理您场景中的错误的最佳方式是使用错误处理程序。错误处理程序连接到一个模块,其中包含错误处理路由。当模块输出错误时,错误处理路由启动并运行错误处理程序。

当所有错误得到处理后,Make 会继续安排场景运行,而不是禁用该场景。

如需更多信息,请查阅 Make 中错误处理概述或关于错误类型、错误处理程序或特定错误处理策略的页面。


## **  错误通知**

当发生错误且未被任何错误处理程序处理时，Make 会向您发送电子邮件通知

![](https://static.xiaobot.net/file/2024-12-02/720345/61301a043a1d588180c0fa0446efd716.png)

当您的场景由于重复错误而被禁用时，Make 也会发出通知。

您可以在此处了解有关电子邮件通知及其设置的更多信息。


## **Make中的警告**

警告提醒您在该情景运行过程中出现了问题,但并不像错误那样严重。同时,当错误问题通过错误处理得到处理时,该情景也可以处于警告状态。

当一个场景中的模块返回警告时，您的场景会继续运行并保持启用状态。但最好检查场景执行历史记录以找出警告的原因。

您会收到警告的情况包括:

  - 当模块输出错误时，但您已在场景设置中启用了存储不完整执行的功能。

  - 当您在场景中耗尽数据存储的全部容量时。

  - 当方案运行时间超过您订阅的时间限制时。

在此了解更多关于警告的信息。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/02a09a94abf8e4335fcbec137cab9c44.png)