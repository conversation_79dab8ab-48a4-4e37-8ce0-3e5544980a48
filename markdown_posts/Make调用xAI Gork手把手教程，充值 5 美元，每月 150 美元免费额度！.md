---
title: "Make调用xAI Gork手把手教程，充值 5 美元，每月 150 美元免费额度！"
created_at: "2025-02-21T11:55:41.000000Z"
uuid: "0004527c-2a6c-412b-b623-a7e7ede12736"
tags: ['API']
---

# Make调用xAI Gork手把手教程，充值 5 美元，每月 150 美元免费额度！

**标签**: API

xAI 公司重磅发布了全新一代大语言模型 Grok 3，其卓越的性能表现令业界瞩目。为了回馈广大开发者，xAI 平台目前正在开展一项超值优惠活动 - 只需充值 5 美元，即可获得每月高达 150 美元的 API 调用额度。现在可以直接通过 make 平台直接调用 Gork 3，是一个性价比不错的大模型。

为了帮助大家更便捷地使用 xAI 平台,翔宇特别准备了这份详细的充值教程。在开始之前,请注意以下几点:

1\. 本教程推荐使用虚拟信用卡进行充值<https://bit.ly/4hLZ91r>

当然,您也可以尝试使用国内信用卡\(翔宇未进行测试\)

2\. 最低充值金额为 5 美元


### 手把手教程：

1\. 访问 xAI 官网 \(<https://x.ai/>\)，

点击顶部导航栏的 API 按钮，进入 API 页面

![](https://static.xiaobot.net/file/2025-02-21/720345/50866ca027b1cbde2aedf41e6141b276.png)

2.点击 "Start building now" 按钮，使用谷歌账号或 X 账号登录系统

![](https://static.xiaobot.net/file/2025-02-21/720345/de53d03b6080dd3954a910c6fd7bf38d.png)

3\. 进入控制台后,点击"购买点数"按钮

![](https://static.xiaobot.net/file/2025-02-21/720345/cbaefc68b91591494bd778676bb67aeb.png)

4\. 在弹出的地址页面中,根据野卡虚拟卡的地址信息进行填写

![](https://static.xiaobot.net/file/2025-02-21/720345/a250fddf5e6f20c13d567861b438d10e.png)

野卡地址，点击注册：

<https://bit.ly/4hLZ91r>

5.输入完成继续，购买点数流程:

\- 选择购买金额为 5 美元

\- 在侧边弹出的银行卡页面中填写野卡虚拟卡信息

\- 点击"继续"完成支付

![](https://static.xiaobot.net/file/2025-02-21/720345/48cecbac815c05d67cdcd76596e54307.png)![](https://static.xiaobot.net/file/2025-02-21/720345/b5471da6574f3610fe437507fb9d6cdc.png)![](https://static.xiaobot.net/file/2025-02-21/720345/9bde7dba8e714b87063b34c3e8133dd2.png)![](https://static.xiaobot.net/file/2025-02-21/720345/c4db0c3f51142f54c5db2580516088e8.png)

8\. 完成购买后:

\- 点击 "Share data"

![](https://static.xiaobot.net/file/2025-02-21/720345/b21cd7b3733285706433918b78ef60ce.png)

\- 允许将请求开放给 GORK

\- 获得每月 150 美元的调用额度

![](https://static.xiaobot.net/file/2025-02-21/720345/cc5eab1e3237bbcba45c65fd33f51334.png)

9.点击左侧 api 按钮，新建 API key，并复制该 key

![](https://static.xiaobot.net/file/2025-02-21/720345/37b550f1a433b1f27a5616a715e95759.png)![](https://static.xiaobot.net/file/2025-02-21/720345/d54589c839187441313968b3d22dca0d.png)![](https://static.xiaobot.net/file/2025-02-21/720345/d394385523c7cdb998f575210c5959d1.png)

10.来到 Make 画布中，搜索 xai新建模块，点击添加键，粘贴刚复制的 APIkey，保存完成模块的链接。

![](https://static.xiaobot.net/file/2025-02-21/720345/2f2baa28e6f9aa87a021ec3877541529.png)

11.选择模型，输入提示词即可对话

![](https://static.xiaobot.net/file/2025-02-21/720345/d008c967bc218788666c0eb945de6a9a.png)![](https://static.xiaobot.net/file/2025-02-21/720345/55cf7241f63ff60dbc7530bfe6f41bb4.png)

需要注意的是,这些模型暂不支持结构化数据输出,但可以进行基础的对话交互。