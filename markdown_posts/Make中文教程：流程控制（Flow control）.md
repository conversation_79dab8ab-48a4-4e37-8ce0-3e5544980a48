---
title: "Make中文教程：流程控制（Flow control）"
created_at: "2024-09-19T13:28:30.000000Z"
uuid: "28c6187f-a083-4abe-a2a3-12b5780de08b"
tags: ['Make基础教程']
---

# Make中文教程：流程控制（Flow control）

**标签**: Make基础教程

翔宇工作流今天为大家带来关于流程控制的教程。

  1. **教程简介** ：本教程介绍如何在Make.com中使用重复器、迭代器和数组聚合器模块来实现流程控制。包括发送多封电子邮件、处理附件数组及整合多个数据包等操作。

  2. **学习目标** ：掌握Make.com中重复器、迭代器和数组聚合器模块的基本使用方法，能够在不同的情境下正确应用这些模块来实现复杂的自动化工作流程。

  3. **学习内容** ：了解重复器和迭代器模块的设置与应用; 学习迭代器的故障排除方法；学习如何使用数组聚合器来整合数据，并通过示例情境理解其实际应用。

  4. **概念理解** ：

     - **重复器（Repeater）** ：用于多次重复执行某个任务，通过输出包含数字项目的多个包来实现任务的多次执行。

     - **迭代器（Iterator）** ：将一个数组转换成一系列包，每个数组项作为单独的包输出，用于逐个处理数据。

     - **数组聚合器（Array Aggregator）** ：将多个包合并成一个包，用于汇总和处理多个数据包的结果。

  5. **任务练习** ：

     - 创建一个重复器模块，设置重复次数为5，连接一个发送电子邮件模块，发送5封主题各异的邮件。

     - 在情境中创建一个迭代器模块，处理一个包含多个附件的电子邮件，将每个附件保存到Google Drive不同的文件夹中。

     - 使用数组聚合器模块，将几个分开的数据包整合成一个，输出包含所有数据包的聚合结果。


## **一、重复器\(Repeater\)模块详解**


### **1\. 什么是重复器?**

重复器就像是一个自动复制机，可以帮你把某个任务重复执行指定的次数。


### **2\. 实际应用案例**

假设你想发送5封邮件，主题分别是：

  - "Hello 1"

  - "Hello 2"

  - "Hello 3"

  - "Hello 4"

  - "Hello 5"

实现方法很简单：只需要将**电子邮件 > 发送邮件** 模块连接到**重复器** 模块后面即可：

![](https://static.xiaobot.net/file/2024-11-18/720345/0e907c5ee00e45192b69cc4637be8df3.png)


### **3\. 具体设置方法**

按照下面的截图来配置模块：

![](https://static.xiaobot.net/file/2024-11-18/720345/d525c29791a012341345175ed2dea1dd.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/daa97e71130128a51d1bf35cf8869954.png)


### **4\. 重复器的工作原理 📝**

想象重复器是一个包裹\(bundle\)生成器：

  - 每个包裹里都有一个叫i的数字

  - i的初始值你可以在"初始值"栏设置

  - 重复次数决定会产生多少个包裹

  - 每次重复，i的值会增加一个固定数值\(默认是1\)


### **5\. 重要设置项说明**

  1. **初始值\(Initial value\)**

     - 设置起始数字

     - 这个数字将作为第一次执行时的值

  2. **重复次数\(Repeats\)**

     - 设置要重复多少次

     - 决定了自动化流程要执行几次

  3. **步进值\(Step\)**

     - 每次增加的数值（默认为1）

     - 在"显示高级设置"中可以看到

     - 可以设置为正数或负数


### **6\. 使用小贴士**

  - **适用场景** ：需要按固定次数重复执行任务时使用

  - **灵活设置** ：步进值可正可负，满足不同需求

  - **调试建议** ：首次使用时，建议先设置较小的重复次数进行测试


### **7\. 新手常见问题**

  1. **为什么要用重复器？**

     - 当你需要重复执行相同任务时，可以避免手动重复操作

     - 特别适合批量处理任务

  2. **步进值有什么用？**

     - 控制每次重复时数值的变化

     - 例如：步进值=2，则数列为：2,4,6,8...

  3. **如何确定需要设置的重复次数？**

     - 根据你需要处理的任务总数来设定

     - 建议先用小数值测试，确认无误后再设置实际需要的次数


## **二、迭代器\(Iterator\)模块详解**


### **1\. 什么是迭代器？**

迭代器是一个特殊的模块，它的主要功能是：

  - 将数组转换成一系列独立的包裹\(bundle\)

  - 每个数组项都会变成一个单独的包裹

  - 特别适合处理批量数据


### **2\. 迭代器设置指南**

![](https://static.xiaobot.net/file/2024-11-18/720345/f41f9c502a7e0d6f461b45218f20b142.png)

设置步骤：

  1. 和设置其他模块一样简单

  2. 主要填写"数组"字段

  3. 这个数组就是你要拆分的数据来源


### **3\. 实际应用案例**

**案例一：保存邮件附件到Google Drive**

![](https://static.xiaobot.net/file/2024-11-18/720345/f6f323d04b9f4b4d0d94d3cf507d1b04.png)

工作流程：

  1. 获取带附件的邮件

  2. 使用迭代器拆分附件数组

  3. 每个附件单独保存到Google Drive指定文件夹

**案例二：使用专门的迭代器**

![](https://static.xiaobot.net/file/2024-11-18/720345/c3ecfa1dc3de4c6dbdd01570be832953.png)

很多应用都提供了专门的迭代器模块：

  - 例如：邮件应用有"遍历附件"迭代器

  - 设置更简单，不需要指定数组

  - 直接选择源模块即可


### **4\. 常见问题解决**

**问题：映射面板没有显示可映射项**

症状表现：

![](https://static.xiaobot.net/file/2024-11-18/720345/14f220c24f2bd6e6f4c0781ed26f0254.png)

当迭代器下只显示这两项时：

  - Total number of bundles（包裹总数）

  - Bundle order position（包裹序号）

原因分析：

  - 模块无法获知数组项的结构信息

  - 这种情况常见于：

    - JSON解析模块

    - 自定义Webhook

    - 缺少数据结构定义的模块

解决方法：

  1. 手动执行场景

  2. 让模块学习输出项的结构

  3. 执行后就能正确显示所有可映射项


### **5\. 实际操作示例**

以JSON解析模块为例：

  1. 初始状态：

![](https://static.xiaobot.net/file/2024-11-18/720345/bc1048dd2a2040dbac48194c79d7d273.png)
  2. 连接迭代器后：

![](https://static.xiaobot.net/file/2024-11-18/720345/56b8b9a75802bdbc2f9397161f657831.png)
  3. 执行场景后：

![](https://static.xiaobot.net/file/2024-11-18/720345/6434a9db3e23df9ce3405c17bca2e293.png)
  4. 最终结果：

![](https://static.xiaobot.net/file/2024-11-18/720345/f8d7bc35faa4f8e761a5236b5c1fb43f.png)


### **6\. 使用技巧和建议**

  1. **调试技巧**

     - 首次设置时先手动运行测试

     - 确认数据结构正确再进行后续配置

     - 可以使用"仅运行此模块"功能

  2. **最佳实践**

     - 处理数组数据时优先考虑迭代器

     - 注意检查数据结构完整性

     - 善用专门的迭代器模块

  3. **注意事项**

     - 确保源数据是数组格式

     - 检查数组项的结构是否完整

     - 注意处理可能的空值情况


## **三、数组聚合器\(Array Aggregator\)模块详解**


### **1\. 数组聚合器简介**

数组聚合器是一个用于将多个包裹\(bundle\)合并成单个包裹的聚合模块。这个工具在处理已经被拆分的数据时特别有用。


### **2\. 基本设置说明**

![](https://static.xiaobot.net/file/2024-11-18/720345/4b573aa31efe3e85cde75cfcdd4eb637.png)

**主要设置项：**

**1\. 源模块 \(Source Module\)**

从哪个模块开始捆绑聚合。源模块通常是一个迭代器或搜索模块，输出一系列的捆绑包。一旦设置聚合器的源模块（并关闭聚合器的设置），源模块和聚合器之间的路线将被包裹在灰色区域中，以便可视化聚合的开始和结束。

**2\. 目标结构类型 \(Target structure type\)**

  - 将数据聚合到目标结构中。默认选项为自定义，允许您选择要聚合到数组聚合器输出包的 Array 项的项目。

![](https://static.xiaobot.net/file/2024-11-18/720345/a14fc46aa93119392b2c9e6a4894f292.png)
  - 一旦您在数组聚合器模块之后连接更多模块并返回到模块的设置时，目标结构类型下拉菜单将包含所有以下模块及其字段，这些字段的类型为数组集合，例如 Slack > 创建消息模块的附件字段：

![](https://static.xiaobot.net/file/2024-11-18/720345/0f5ab559879450f1219497ba58e3ce4c.png)

**3\. 分组依据 \(Group by\)**

聚合器的输出可以通过“按字段分组”功能分成多个组。按字段分组中可以包含一个公式，该公式会针对每个聚合器的输入包进行评估。然后，聚合器会根据每个不同公式值输出一个包。每个包包含两个项目：

  - Key 包含独特的数值。

  - Array 包含了从捆绑包中聚合数据的值，这些数据的公式计算结果为 Key 。

**4\. 空聚合后停止处理**

  - 默认情况下，聚合器会输出聚合的结果，即使没有捆绑到达聚合器（例如，因为它们在途中全部被过滤掉）。如果启用了在空聚合后停止处理选项，聚合器将不会在这种情况下产生任何输出捆绑，流程将停止。

源模块输出的捆绑包以及源模块和聚合器模块之间的其他模块输出的捆绑包都不会被聚合器输出，因此聚合器后的流程中的模块无法访问这些捆绑包中的项目。

如果您需要访问从源模块和聚合器模块之间的任何其他模块输出的包中的项目，请确保在 Array 聚合器模块的设置中将它们包含在聚合字段中。

如果项目是嵌套的（即包含在集合项目中），目前无法在数组聚合器的聚合字段中轻松选择它们。例如，如果捆绑包含集合项目 User ，其中包含两个项目 Name 和 Email ：

![](https://static.xiaobot.net/file/2024-11-18/720345/557bda8a6e3f4e18f0702bfec4a01f39.png)

然后只有可以选择 User 集合项：

![](https://static.xiaobot.net/file/2024-11-18/720345/d913c481773b0df3f8f26575ec68023a.png)

此设置将生成以下输出：

![](https://static.xiaobot.net/file/2024-11-18/720345/348bea144ad8784e7893ecc3b4d8efd1.png)


### **3\. 自定义输出结构**


### **定制化输出**

如果您希望完全自定义数组聚合器的输出结构，请按照以下步骤进行：

  1. 在数组聚合器模块后插入 JSON > 创建 JSON 模块

![](https://static.xiaobot.net/file/2024-11-18/720345/a4b48ffc840ae0d3abcd4ed96cce486c.png)
  2. 打开 JSON > 创建 JSON 模块的设置。

  3. 设置一个数据结构，用于从数组聚合器中输出所需的项目。数据结构应该是一个包含项目的集合数组，这些集合应该包含您想要包含在输出中的项目。这里是一个包含两个文本项目 Name 和 Email 的示例数据结构：

![](https://static.xiaobot.net/file/2024-11-18/720345/728ed5292b4b63f19624d32e891f85a1.png)
  4. 打开数组聚合器模块的设置。

  5. 在目标结构类型字段中，选择 JSON > 创建 JSON 模块的数组字段

![](https://static.xiaobot.net/file/2024-11-18/720345/018c80a265f544f9c16bca4a720e04ec.png)

  6. 在数组聚合器模块的设置中将显示与步骤 3 中创建的数据结构对应的字段。根据需要将任何项目映射到字段中。您现在可以使用映射面板轻松映射嵌套项目，甚至可以使用公式。

![](https://static.xiaobot.net/file/2024-11-18/720345/a9c2e5518d9945a7b9286050664f0a70.png)

  7. 数组聚合器模块的输出现在将呈现如下形式：

![](https://static.xiaobot.net/file/2024-11-18/720345/03f9bd17a7295d044ec2a0687deb65ee.png)

如果您想保存由虚拟 JSON > 创建 JSON 模块执行的操作，请将其放在路由器后的禁用路由上：

![](https://static.xiaobot.net/file/2024-11-18/720345/9eb6dc77b2aeac22cffa8fa0ecd9adfc.png)

如果您希望有条件地从模块的输出中省略一个项目，请使用一个计算为 ignore 关键字的公式

![](https://static.xiaobot.net/file/2024-11-18/720345/0f1186e329a593ae086610c1bb44318c.png)

如果 4\. User: Email 为空，则 Email 项将被完全省略


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/69d6c65f0e9b1e5292c95c41d4c6841f.png)