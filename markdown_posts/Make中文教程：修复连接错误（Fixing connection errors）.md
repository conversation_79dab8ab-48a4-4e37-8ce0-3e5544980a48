---
title: "Make中文教程：修复连接错误（Fixing connection errors）"
created_at: "2024-08-30T03:00:19.000000Z"
uuid: "41bc923e-3634-4352-91d5-b8f5590d6d6f"
tags: ['Make基础教程']
---

# Make中文教程：修复连接错误（Fixing connection errors）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于解决连接错误的教程。

  1. **教程简介** ：该教程讲解了如何在Make.com平台上处理连接错误（ConnectionError），包括错误识别、检查应用状态及使用Break错误处理器来自动重新运行场景。

  2. **学习目标** ：了解如何处理和管理Make.com的连接错误，掌握利用Break错误处理器的策略，确保场景连续运行，提升自动化任务的可靠性。

  3. **学习内容** ：

     - 如何识别ConnectionError。

     - 检查模块应用程序的状态。

     - 设置和使用Break错误处理器来处理连接错误。

     - 启用场景的顺序处理和未完成执行功能。

  4. **概念理解** ：

     - **ConnectionError** ： Make.com在应用程序无法访问时返回的错误，通过HTTP 503和504状态码识别。

     - **Break错误处理器** ： 用于处理连接错误的工具，允许设置延迟和尝试次数以重新运行出错的模块。

     - **顺序处理** ： 在场景设置中启用，使触发模块按照数据接收顺序处理。

     - **未完成执行** ： 保存因错误导致未完成的执行数据，以便重新尝试。

  5. **任务练习** ：

     - 创建一个包含Webhook触发器的简单场景，确保触发器会偶尔导致ConnectionError。

     - 添加Break错误处理器，设置延迟和尝试次数。

     - 启用顺序处理和未完成执行功能，模拟场景并观察错误处理的效果。


# **修复连接错误**

应用程序模块在应用程序不可用时输出 ConnectionError 。例如,应用程序可能由于维护而处于离线状态。

使用 HTTP 503 和 504 状态码来标识 ConnectionError 。

![](https://static.xiaobot.net/file/2024-11-28/720345/947d7051123fec40477c0f0059093394.png)

当您的方案中的模块输出 ConnectionError 时,您应该检查该模块应用程序的状态页面。很可能状态页面会有 https://status.domain 这样的 URL,例如 https://status.make.com。

当 Make 识别模块输出为 ConnectionError 并且您没有使用任何错误处理时,Make 会自动安排重新运行该场景。


## **如何处理 ConnectionError**

要处理 ConnectionError ，您可以使用处理 RateLimitError 的策略。最有效的策略是使用 Break 错误处理程序在延迟后重新运行方案。

  1. 如果您的场景使用即时触发器（例如自定义 Webhook 模块）启动，请考虑在场景设置中启用顺序处理。使用顺序处理时，触发模块会按接收到的数据顺序逐个处理传入的数据。

![](https://static.xiaobot.net/file/2024-11-28/720345/28bed5106ccd38c6590c0d489bf0fb11.png)

否则，跳过这一步。

![](https://static.xiaobot.net/file/2024-11-28/720345/768f76936dcdcdce962a3d152562f63c.png)
  2. 将 Break 错误处理程序添加到导致错误的模块中。

根据您的场景的重要性和时间安排,请考虑设置延迟时间和尝试次数。

举例而言,如果该应用程序偶尔需要进行维护而暂时无法使用几个小时,最好设置较少的尝试次数且尝试之间的时间间隔较长。

另一方面,如果该应用程序偶尔因过载而无法使用,而该场景对您很重要,最好使用较短的时间段\(几分钟\)并尝试更多次。

  3. 在场景设置中启用不完整的执行。Make 将保存导致错误的捆绑包。

例如,如果您使用 Webhook 触发器向 ChatGPT 提出问题,但 ChatGPT 应用程序有时会因请求过多而过载并返回错误,您的场景和带有错误处理的场景设置可能如下所示:

![](https://static.xiaobot.net/file/2024-11-28/720345/8762381d470f2731f4a53f52647648de.png)![](https://static.xiaobot.net/file/2024-11-28/720345/7107adfbbb22ae91c831ecf57f8a552e.png)

当 OpenAI 服务器过载或无法访问时,如果 Create a completion 模块输出 ConnectionError ,Make 会创建一个不完整的执行版本。

在 Break 错误处理程序中设置的延迟后,Make 会重新运行"创建完成模块"。如果重新运行成功,Make 将继续安排新的方案运行。

如果重新运行失败,在延迟后再次运行该模块,直至达到在 Break 错误处理程序设置中设置的尝试次数。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-08-30/720345/5b4b33d9898f153c32e14b80a31e6d0d.png)