---
title: "Make中文教程：Make工具升级Gmail（Upgrading Gmail and Email App Versions Using Make DevTool）"
created_at: "2024-08-30T01:30:13.000000Z"
uuid: "a0fa3c3a-7a7e-4d65-b49c-894dbc328947"
tags: ['Make基础教程']
---

# Make中文教程：Make工具升级Gmail（Upgrading Gmail and Email App Versions Using Make DevTool）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于使用Make工具升级Gmail的教程。

  1. **教程简介**  ：本教程介绍如何使用Make DevTool工具将Gmail应用模块升级到最新版本，适用于已在场景中存在旧版本Gmail或Email应用的用户。

  2. **学习目标**  ：掌握使用Make DevTool的Swap App工具来升级场景中的Gmail应用模块，确保应用在最新版本上运行进行优化。

  3. **学习内容**  ：教程详细讲解了打开Chrome开发者工具、进入Make标签和使用Swap App工具的步骤，并指导用户填写相关字段和执行应用替换操作。

  4. **概念理解**  ：Swap App工具：这是Make DevTool中的一个功能，用来替换场景中已存在应用模块的版本，以最新版Gmail应用为例，提高应用性能和安全性。

  5. **任务练习**  ：尝试在一个包含旧版Gmail应用的Make场景中，打开开发者工具，使用Swap App工具将Gmail应用模块升级到最新版本，验证模块是否成功升级。、


# **使用Make DevTool升级Gmail和Email应用**


## **⚠️ 重要提醒**

请在完全了解操作影响的情况下使用Make DevTool的Swap App工具。


## **前置条件**

  - 场景中存在旧版Gmail或Email应用


## **升级步骤**


### **1\. 打开开发者工具**

  - Windows: 按Ctrl+Shift+I 或 F12

  - Mac: 按Command+Option+I

![](https://static.xiaobot.net/file/2024-11-18/720345/c498550c44d8c605f1f5079b7ddc32a3.png)


### **2\. 使用Swap App工具**

  1. 导航到: Make标签页 > Tools > Swap App

![](https://static.xiaobot.net/file/2024-11-18/720345/b8f75706090a24ee89feb6bb8856b71b.png)
  2. 配置以下字段:

**字段说明** App to be Replaced选择要替换的旧版Gmail应用Version选择要替换的旧版本Replace with选择新的Gmail应用Version选择最新版本

  3. 执行升级

  - 完成配置后点击Run按钮

![](https://static.xiaobot.net/file/2024-11-18/720345/62df12020b1583d2e4cd6ecbfdcc414e.png)


### **3\. 验证升级**

  - 确认所有旧版Gmail模块已更新到目标版本

  - 测试功能是否正常

  - 检查配置是否完整保留


## **最佳实践**

  1. 升级前备份场景

  2. 在非生产环境中先测试

  3. 记录所有更改

  4. 验证关键功能


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/b0cb9ff1c54a548015be02d3fc7e48ca.png)