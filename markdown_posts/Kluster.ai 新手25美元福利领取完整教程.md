---
title: "Kluster.ai 新手25美元福利领取完整教程"
created_at: "2025-05-13T05:50:14.000000Z"
uuid: "102339f0-c063-46ef-9409-7f70e3452054"
tags: ['API']
---

# Kluster.ai 新手25美元福利领取完整教程

**标签**: API

![](https://static.xiaobot.net/file/2025-05-13/720345/7c6a749fe0c804b1fedccf0dd71f6040.png)

根据网友 Supwuji 推荐，现在可以领取 25 美元福利，不过其中 20 美元 7 天过期，有需要的可以根据步骤领取。


## **1\. Kluster.ai 是什么？**

Kluster.ai 是面向开发者的 AI 云平台，主打一键接入的大模型推理与精调。兼容 OpenAI SDK，只需将 base\_url 改成自家地址即可调用；Trial 计划可达 30 RPM，升级后可扩至 600 RPM。其自研 **Adaptive Inference** 会在实时、异步、批量三类场景间自动分配算力，平均价格较竞品低 50%。当前已托管 **Qwen 3-235B-A22B-FP8、DeepSeek-R1/V3、Llama 3** 等热门模型，也支持上传自有权重做微调，让团队无需自建 GPU 即可在小时级完成批量推理或 Fine-tune，并借助队列、回调、专用端点等机制无缝落地生产。

**特色说明开发者友好** 完全兼容 OpenAI SDK，1 行代码即可切换到自家推理后端**模型丰富** 既有自研大模型，也接入 **Qwen3-235B-A22B-FP8、DeepSeek-R1 / V3**  等热门模型**自适应推理** 支持 Real-time、Asynchronous、Batch 三种推理模式，批量任务价格更低**弹性计费** 按调用量计费，结合免费额度与优惠券，前期试用成本极低

* * *


## **2\. 快速领取 $25 福利**


### **Step 1 — 访问官网并点击  Get started **

  - 网址：[https://kluster.ai](<https://kluster.ai/>)

  - 顶部或首页中央均有 **Get started / Start building**  按钮

  - 单击后会跳转到控制台注册页

![](https://static.xiaobot.net/file/2025-05-13/720345/b58ae994e98e6667aba85b90839ac0c5.png)![](https://static.xiaobot.net/file/2025-05-13/720345/399ef288193a99c56a62935cedbac8fb.png)


### **Step 2 — 完成注册 / 登录**

  1. **一键登录** ：也可直接选择 **Google**  或 **GitHub**  第三方授权

  2. 首次注册成功后，系统会自动向账户注入 **$5 余额**

![](https://static.xiaobot.net/file/2025-05-13/720345/60daf076d4375d3f3e73cd25020a9f9d.png)


### **Step 3 — 打开  Billing / 计费 页面 **

  - 控制台左侧边栏选择 **Billing**

  - 右上角会看到当前余额（示例：$5.01，多出的 $0.01 为系统精度差）

![](https://static.xiaobot.net/file/2025-05-13/720345/6da51f4d03d179f0d76c612acfdc5f93.png)


### **Step 4 — 输入兑换码  KLUSTERGEMMA **

  1. 在 **Credits**  模块下找到 **Or**  下面的输入框

  2. 粘贴兑换码 **KLUSTERGEMMA**

  3. 点击 **Submit**  按钮，账户即时增加 **$20**

  4. 余额合计 ≈ **$26** ，可立即调用任意支持模型

> ✅ **完成！**  你已获得相当于 ~260 万左右的 OpenAI‐compatible token 推理额度（具体取决于所选模型与上下文长度）。

* * *


## **3\. 常见问题（FAQ）**

**问题解答如何查看剩余额度？** 控制台右上角余额徽章 & **Billing > Credits**  中均可实时查看。**如何调用模型？** 兼容 OpenAI SDK：仅需把 base\_url 改成 https://api.kluster.ai/v1，将 api\_key 换成控制台生成的 Key。文档内还给出了 Python / TypeScript / cURL 示例。

* * *

> **想了解更多 AI 自动化与实战干货？**
> 
>   - **翔宇工作流官网** ：<https://xiangyugongzuoliu.com/>每周分享 n8n / Make.com 自动化、RAG 知识库、AI 工具生态最新玩法，欢迎订阅 ⭐️
> 
>