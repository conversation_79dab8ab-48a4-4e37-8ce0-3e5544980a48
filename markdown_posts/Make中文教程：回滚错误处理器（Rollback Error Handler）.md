---
title: "Make中文教程：回滚错误处理器（Rollback Error Handler）"
created_at: "2024-08-30T05:50:25.000000Z"
uuid: "5bef1368-7cd1-4a0a-9133-b76e57c30312"
tags: ['Make基础教程']
---

# Make中文教程：回滚错误处理器（Rollback Error Handler）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于回滚错误处理的教程。

  1. **教程简介** ： 本教程介绍如何使用回滚错误处理器在Make.com中处理事务错误，包括场景操作、添加错误处理器以及其效果和限值。

  2. **学习目标** ： 掌握在Make.com中使用回滚错误处理器的方法，确保在事务模块中发生错误时能够有效撤销更改，维护数据一致性。

  3. **学习内容** ：

     - 回滚错误处理器的作用和使用场景

     - 支持事务模块的识别（带有ACID标签）

     - 自动提交选项及其对事务处理的影响

     - 添加回滚错误处理器的具体步骤

  4. **概念理解** ： 回滚错误处理器：用于在场景中发生错误时停止运行并撤销在支持事务模块中所做的更改。支持事务的模块通常带有ACID标签，设置合理的错误处理器可确保数据一致性。

  5. **任务练习** ： 创建一个包含JSON解析、迭代器和数据存储模块的场景。故意在数据存储模块中制造错误，并为该模块添加回滚错误处理器。运行场景并观察数据的变化，验证回滚效果。


# **回滚错误处理器**

回滚错误处理程序会停止场景运行并撤消支持事务的模块所做的更改。它们始终使用数据库应用程序,如 MySQL 或数据存储。Make 无法撤消不支持事务的模块所做的操作,如 Gmail>发送电子邮件或 Dropbox>删除文件。

导致错误的捆绑包在场景流中不继续。此外,Make 不处理任何剩余的捆绑包。

标记该场景在场景历史中运行为错误。 Make 不会因连续错误而禁用该场景。

![](https://static.xiaobot.net/file/2024-11-28/720345/9eeadd0a51a1af9d9bb6d28fa84aeb93.png)

例如:这个演示场景包含五个模块。该场景对于测试和展示错误处理程序的效果很有用:

  1. JSON - 解析 JSON 数据可提供由三个记录 ID 组成的测试数据数组。

  2. 迭代器将数组拆分为单独的捆绑包。

  3. 数据存储 - 更新记录：更新数据存储中的数据。

  4. 数据存储 - 更新记录：本模块再次更新数据。这次模块的工作方式有所不同。在模块映射中，有一个映射明确地创建了一个错误：

![](https://static.xiaobot.net/file/2024-11-28/720345/bc412baa047a282e758cab77301a3b0b.png)

映射将 null 值插入必需的密钥字段中,这总是会创建 BundleValidationError 。

有两个数据存储模块执行相同的操作,但其中一个模块失败,这将成为提交和回滚错误处理程序的良好示例。

  5. Slack - 发送消息：发送一条消息到私密测试频道。

这就是示例场景的样子:

![](https://static.xiaobot.net/file/2024-11-28/720345/27f191fe43ea0d02dc63a5a0e42d2474.png)

当我们运行示例场景时,我们将得到 BundleValidationError :

![](https://static.xiaobot.net/file/2024-11-28/720345/703b785f5a4f39888f9009df9889c8f6.png)![](https://static.xiaobot.net/file/2024-11-28/720345/0bdc0659c7d64762c5ecf7e9627bba3f.png)

如果我们将回滚错误处理程序添加到更新记录模块,回滚错误处理程序将在该场景中停止处理捆绑包。Make 将不会处理剩余的捆绑包。

![](https://static.xiaobot.net/file/2024-11-28/720345/89799e5c9e02119205b2921dd918b7b0.png)

让我们也检查一下数据存储中的数据。

在运行该方案之前,数据存储中包含以下数据:

![](https://static.xiaobot.net/file/2024-11-28/720345/164c1a8dba98f97c205e2365c0860ba2.png)![](https://static.xiaobot.net/file/2024-11-28/720345/019b07393d72887c97292cceb1a34976.png)![](https://static.xiaobot.net/file/2024-11-28/720345/10338f258f51ea01cc8be0bd9c65bc23.png)

更新记录模块的映射。第一个模块将 ID 列更新为数字 4 ，将 Name 列更新为文本 Test 4 。

第二个模块将 ID 列更新为数字 5 ，并将 Name 列更新为文本 Test 5 。

如果你在方案设置中禁用自动提交选项,Make 会撤消在支持事务的模块中处理捆绑包时发生的更改。

![](https://static.xiaobot.net/file/2024-11-28/720345/0c6d41983cb381f92b32fd09a4271f89.png)

  1. 第一个数据包成功通过了方案流,并在数据存储中更新了第一行数据,两次都如此。第一行包含来自第二个"更新记录"模块的更新: ID = 5, Name = Test 5 。

  2. 第二个包成功地到达了第一个更新记录模块，但在第二个模块中引发了错误。回滚错误处理程序撤销了第二个包的更新并停止该场景。

第一行现在只包含来自第一个模块的更新: ID = 4, Name = Test 4 。

第二行包含原始数据: ID = 2, Name = Test 2 。

  3. 因为 Rollback 错误处理程序已经停止了场景运行,所以 Make 没有更新第三行。第三行的数据保持不变： ID = 3, Name = Test 3 。

如果您保持自动提交选项启用,Make 会撤销错误模块输出的更改,前提是该模块支持事务。

![](https://static.xiaobot.net/file/2024-11-28/720345/e24cbbbeaef6f5bab8633f461b4bc210.png)

  1. 第一批数据成功通过场景流程,并在数据存储中更新第一行数据,两次均如此。所有更改均已提交,以后无法回滚。

第一行包含从第二个更新记录模块的更新： ID = 5, Name = Test 5 。

  2. 第二个软件包成功到达第一个更新记录模块。提交所有更改,以后无法撤回。第二个软件包在第二个模块中出现错误。

回退错误处理程序阻止了第二个模块中的更新并停止了该场景。第二行仅包含来自第一个模块的更新: ID = 4, Name = Test 4 。

  3. 创建不会更新第三行,因为回滚错误处理程序已经停止了场景运行。第三行的数据保持不变: ID = 3, Name = Test 3 。

您可以使用回退错误处理程序来停止场景运行并在模块输出错误时撤消更改。

关于错误处理策略的更多信息请查看错误处理概述。


## **当发生错误时,撤消对您的数据所做的更改**

使用回滚错误处理程序,您可以在模块输出错误时停止场景并撤消更改。您只能撤消支持事务的模块中的更改。

![](https://static.xiaobot.net/file/2024-11-28/720345/15880a70608b13dd3ca25d1c42286d77.png)

例如,以下场景在 Data Store 应用程序模块中输出错误:

![](https://static.xiaobot.net/file/2024-11-28/720345/1d165f29c85f212fc618bdf843630d09.png)![](https://static.xiaobot.net/file/2024-11-28/720345/711e4e80a8cf6be336b2d5c1a1319ef2.png)

发生错误时,按以下步骤停止场景并尽可能撤销更改:

  1. 右键单击导致错误的模块。在菜单中选择添加错误处理程序。

  2. 选择回滚错误处理程序。

  3. 可选操作:进入场景设置并关闭自动提交选项。

出现错误时,输出错误的模块如果支持事务,则会恢复更改。如果禁用自动提交选项,场景中支持事务的所有模块都会撤消更改。

  4.  保存您的场景。

您在您的场景中添加了回滚错误处理程序。当数据存储模块发生错误时,该场景将停止,并且 Make 将撤消支持事务的模块中错误包所做的更改。

![](https://static.xiaobot.net/file/2024-11-28/720345/4a62de27b65400fe28648dee666897ec.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/d50f09e819c607cf6829e85fa4792bc9.png)