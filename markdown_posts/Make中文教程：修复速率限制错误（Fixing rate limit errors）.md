---
title: "Make中文教程：修复速率限制错误（Fixing rate limit errors）"
created_at: "2024-08-30T02:57:18.000000Z"
uuid: "b3812a9d-8937-4ec4-bc1a-85f9a0b3bb23"
tags: ['Make基础教程']
---

# Make中文教程：修复速率限制错误（Fixing rate limit errors）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于修复速率限制错误的教程。

  1. **教程简介** ：本教程介绍如何处理API速率限制错误（RateLimitError）。通过几种策略避免频繁出错，包括使用Sleep模块延迟请求、Break错误处理器以及高级延迟控制方法。

  2. **学习目标** ：了解并学会使用Make.com的错误处理机制，尤其是针对API速率限制错误的处理方法，确保场景流程平稳运行，减少因过多请求导致的失败。

  3. **学习内容** ：

     - 识别API速率限制错误

     - 使用Sleep模块延迟请求

     - 使用Break错误处理器

     - 高级延迟控制策略

     - 设置场景以处理不完整的执行

  4. **概念理解** ：

     - **RateLimitError** ：API速率限制错误，通常对应HTTP 429状态码，表示请求过于频繁。

     - **Sleep模块** ：用于在流程中添加延迟，调整请求频率。

     - **Break错误处理器** ：处理错误并设置重试次数和延迟。

     - **顺序处理** ：按顺序处理进入的请求数据，确保数据顺序性。

  5. **任务练习** ：

     - 创建一个包含多个API调用的场景，观察速率限制错误产生的条件。

     - 添加Sleep模块，设置适当的延迟时间以处理速率限制。

     - 添加Break错误处理器，设置重试次数和延迟，确保处理错误和重试逻辑正常工作。

     - 尝试启用顺序处理，验证处理过程的顺序性。


# **修复速率限制错误**

当您达到应用程序的 API 速率限制时,应用程序模块会返回 RateLimitError 。速率限制是在一段时间内\(通常为一秒或一分钟\)您可以发送到应用程序的请求数量。 RateLimitError 对应于 HTTP 错误代码 429。

![](https://static.xiaobot.net/file/2024-11-28/720345/ccbec24bce0251af1e9eca6396992b00.png)

当您达到应用程序速率限制时，该应用程序将阻止进一步请求,直到限制时间段结束。例如,这些是 Google Sheets 应用程序的 API 速率限制。

在计划场景运行中,如果模块返回了 RateLimitError ,而且您没有使用任何错误处理,Make 会自动在 20 分钟后重新运行该场景。

![](https://static.xiaobot.net/file/2024-11-28/720345/c15a4d3dca5329f2e2c56011d0dabc44.png)


## **如何处理速率限制错误**

您可以使用以下策略来处理 RateLimitError :

  - 在应用模块之前使用 Sleep 模块添加延迟,以延迟请求。

  - 添加中断错误处理程序到模块中,以处理错误并防止关闭方案时间表。

  - 将两个选择结合在一起。

在以下步骤中,我们将延迟与 Sleep 模块和 Break 错误处理程序结合使用,以处理即使在延迟后也可能发生的错误。

  1. 如果您的场景使用即时触发器（例如自定义 Webhook 模块）启动，请考虑在场景设置中启用顺序处理。使用顺序处理时，触发模块会按接收到的数据顺序逐个处理传入的数据。

![](https://static.xiaobot.net/file/2024-11-28/720345/733c0fef2943a47451cc150021882c19.png)

否则，跳过这一步。

![](https://static.xiaobot.net/file/2024-11-28/720345/646fd82e6b705f7970bf481cefc46ad6.png)
  2. 在导致错误的模块之前添加睡眠模块。将延迟设置为符合时间限制内的请求数。例如,如果应用程序每分钟限制 10 个请求,将延迟设置为 6 秒。

如果你想更精确地控制延迟,请查看高级策略。

  3. 将 Break 错误处理程序添加到导致错误的模块中。

如果睡眠模块延迟无法防止错误,中断处理程序会在另一个延迟之后再次运行包含剩余场景流程的模块。在处理程序设置中设置重试次数和重试延迟。

  4. 在场景设置中启用不完整的执行。Make 将保存导致错误的捆绑包。

以下是上述源文本的简体中文翻译: 例如,如果您使用 Webhook 触发器发送数据,但 OpenAI 模块出现错误,您的场景和带有错误处理的场景设置将如下所示:

![](https://static.xiaobot.net/file/2024-11-28/720345/826a6cfc7496dd2dbdf45deabf8c5745.png)![](https://static.xiaobot.net/file/2024-11-28/720345/f24978a8a3e6ed688da80231c24fa963.png)


## **高级限速错误处理策略**

当上述提到的策略不适合您的用例或对您无效时,您可以在以下部分找到更多信息。在此,我们分享了在某种情况下处理速率限制错误的高级方法。


### **微调睡眠延迟**

这种策略可以帮助你,如果你想在模块处理了一定数量的批次后触发延迟。

如果您在场景中使用的模块输出一个可用于对其进行编号的值,您可以设置一个"延迟路由"。您可以使用过滤器设置延迟频率,并使用睡眠模块设置延迟时长。

列举捆绑包的选项包括:

  - 如果您使用迭代器模块创建捆绑包，则可以使用元变量捆绑包顺序位置。

  - 你可以使用 Array 聚合器模块将传入的软包转换为数组,然后使用迭代器。

  - 您可以使用增量函数模块。

![](https://static.xiaobot.net/file/2024-11-28/720345/2765120fefcefcbcf7df45e7c0aed7e7.png)

  1. 设置您的场景。在我们的示例中,我们将使用数据存储>搜索记录和 HTTP>发出请求模块。发出请求模块返回 RateLimitError 。

  2. 在"数据存储 > 搜索记录"模块后添加数组聚合器。

     1. 将源模块设置为搜索记录模块。

     2. 从数据存储中选择任何数据以创建数组。

  3. 在数组聚合器之后添加迭代器模块。遍历聚合器输出的数组。

使用数组汇总器 -> 迭代器流,我们创建了一个数组,遍历该数组以获取迭代器输出中的捆绑包订单位置元变量。在下一步中,我们使用此变量来延迟发出请求模块,以避免 RateLimitError 。

  4. 在迭代器模块后添加路由器。

  5. 将 Sleep 模块添加到路由器上的第一个路由。这个路由将成为您的"延迟路由"。在 Sleep 模块设置中设置时间延迟。

  6. 在路由器和睡眠模块之间创建过滤器。

     1. 把过滤条件设置为使用从迭代器模块输出的捆绑订单位置变量。

     2. 使用 mod 数学运算符设置延迟的频率。

     3. 将过滤器操作设为数字：等于 0。

例如,以下过滤器每隔 60 个批次即触发延迟:

![](https://static.xiaobot.net/file/2024-11-28/720345/7688d48e982fc7a9a17b32bbe95a404a.png)

处理 60 个包后,睡眠延迟触发。如果 APP API 的速率限制为每秒 60 个请求,使用睡眠模块添加 1 秒延迟可防止 RateLimitError 。

  7. 将 HTTP > 发出请求模块添加到路由器的第二个路由

您也可以为"发送请求"模块添加错误处理程序。

我们完成的示例场景看起来像这样:

![](https://static.xiaobot.net/file/2024-11-28/720345/51a88beb9718d75bbca087a458f6f1eb.png)

当您运行该场景时,每第 60 个捆绑件都会触发 Sleep 模块延迟。该设置只需额外消耗两个操作即可计算捆绑件数量。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-08-30/720345/e6f0797c8bc3a2861dd981db169b84c1.png)