---
title: "Make中文指南：迭代器（Iterator）"
created_at: "2024-08-27T11:37:30.000000Z"
uuid: "8e7b4c95-b220-44b4-ad3c-24e8af9f5013"
tags: ['Make基础教程']
---

# Make中文指南：迭代器（Iterator）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于迭代器的教程。

  1. **教程简介**  ：本教程详细介绍了Make.com中的Iterator模块，讲解其设置方法、应用示例及故障排除，帮助用户将数组转换为单独的数据包。

  2. **学习目标**  ：通过学习本教程，用户将掌握如何在Make.com中设置和应用Iterator模块，将数组拆分成独立数据包，并解决常见问题。

  3. **学习内容**  ：教程内容包括Iterator模块的设置步骤、实际应用示例（如将邮件附件保存到Google Drive）、专用Iterator模块简介及常见故障的解决方法。

  4. **概念理解**  ：Iterator是一种特殊模块，用于将数组转换为一系列单独的数据包，每个数组项作为独立数据包输出。专用Iterator模块则简化了设置，无需用户指定数组。

  5. **任务练习**  ：尝试创建一个场景，使用Iterator模块将一组邮件附件分别保存到Google Drive，确保数组的每个附件都被处理和保存。


## **1\. 迭代器的定义与作用**

迭代器是一种特殊模块，其核心功能是将数组转换成一系列独立的数据包（bundles）。每个数组项都会被转换为独立的数据包输出。


## **2\. 迭代器配置**


### **2.1 基本设置**

![](https://static.xiaobot.net/file/2024-11-18/720345/b79b00923a2b1afe9a047c0a36044e0b.png)

配置迭代器的方式与其他模块相同：

  - 在Array字段中指定要转换/拆分的数组

  - 设置会将该数组拆分成独立的数据包

  - 每个数据包包含一个数组项


### **2.2 专用迭代器**

![](https://static.xiaobot.net/file/2024-11-18/720345/b83bfece2c0096588c10ca640e3df2af.png)

Make提供了多个专用的迭代器模块，例如：

  - Email > Iterate attachments（邮件附件迭代器）

  - 这些专用迭代器提供简化的设置

  - 无需手动指定数组，只需选择源模块


## **3\. 应用示例：保存邮件附件至Google Drive**

![](https://static.xiaobot.net/file/2024-11-18/720345/3ddfb3f37bb111c75aad9e78286f126e.png)

工作流程：

  1. 获取含附件的邮件

  2. 使用迭代器分别处理每个附件

  3. 将每个附件单独保存到指定的Google Drive文件夹

迭代器在此场景中的作用：

  - 拆分附件数组

  - 每个附件生成独立数据包

  - 实现逐个文件的保存


## **4\. 故障排除：映射面板问题**


### **4.1 问题表现**

![](https://static.xiaobot.net/file/2024-11-18/720345/9e84f255def948dcdbe3e37159e0b0ae.png)

当迭代器缺少数组项结构信息时，映射面板仅显示：

  - Total number of bundles（总数据包数）

  - Bundle order position（数据包序号）


### **4.2 问题原因**

  - 部分模块可能无法提供输出项信息

  - 常见于JSON解析或自定义Webhook模块

  - 缺少数据结构定义时会出现此情况


### **4.3 解决方案**

以JSON解析模块为例：

  1. 初始状态：

![](https://static.xiaobot.net/file/2024-11-18/720345/c75685391542c92844e6aeb1dff60fa0.png)

  2. 问题：无法映射到迭代器的Array字段：

![](https://static.xiaobot.net/file/2024-11-18/720345/1fd6b33e8003d805667862638fa34cf8.png)

  3. 解决步骤：

  - 在场景编辑器中手动执行场景

  - 或右键点击模块选择"仅运行此模块"

  - 模块会学习并记住输出项结构

  4. 解决后：

![](https://static.xiaobot.net/file/2024-11-18/720345/fc49acb45f8c4d05afdc0c7651ebadd0.png)

迭代器后的模块也将显示数组项内容：

![](https://static.xiaobot.net/file/2024-11-18/720345/7c234ef48b2f70aa6faf25e00e0a1ce4.png)

关键提示：当映射面板中看不到某些项目时，运行一次场景可以让模块学习输出项结构，并将这些信息提供给后续模块。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/ba14fa9c61f7c987a10e29df7a3f633a.png)