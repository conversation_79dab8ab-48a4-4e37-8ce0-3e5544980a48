---
title: "Make中文教程：JSON"
created_at: "2024-09-19T12:48:03.000000Z"
uuid: "caa604d3-ec6d-419f-9390-c3b16df594a3"
tags: ['Make基础教程']
---

# Make中文教程：JSON

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来一篇关于 JSON 的详细教程。

**教程简介** ：本教程将介绍如何在 Make.com 平台上使用 JSON 应用程序，包括创建、解析和转换 JSON 数据，并演示如何将数据记录转换为 JSON 格式。

**学习目标** ：通过学习本教程，您将掌握在 Make.com 中处理 JSON 数据的基本方法，能够创建和解析 JSON 数据，并学会将不同格式的数据记录转换为 JSON。

**学习内容** ：教程内容涵盖创建 JSON、将 JSON 转换为 XML、解析 JSON、将数据记录转换为 JSON、使用解析 JSON 模拟模块输出以及聚合 JSON 的多项操作，并提供详细的实践步骤。

**概念理解** ：

  1. **数据结构** ：定义 JSON 数据的组织方式，使其能够在场景中的不同模块之间进行映射。

  2. **数组与对象** ：数组是包含多个元素的列表，用方括号定义；对象是包含键值对的集合，用花括号定义。

**任务练习** ：

  1. 通过 Make.com 创建一个 JSON 对象，描述一本书的详细信息（如 ID、标题、作者）。

  2. 解析一个包含多本书的 JSON 数组，并将其转换为 Google Sheets 中的各行记录。


# **JSON**


## **开始学习 JSON**

JSON 应用程序提供处理 JSON 格式数据的模块,以便 Make 可以进一步处理数据内容或创建新的 JSON 内容。


## **  创建 JSON**

这个模块帮助你创建 JSON。这个模块的字段取决于你的数据结构。

数据结构描述了 JSON 数据的组织方式,并支持将单个 JSON 项映射到您场景中的其他模块。

![](https://static.xiaobot.net/file/2024-11-25/720345/62a82c1e24fdf8e77c9b34f1231aa17a.png)

选择数据结构,输入字段详情以创建 JSON。模块中提供的数据以 JSON 格式返回在输出中。

![](https://static.xiaobot.net/file/2024-11-25/720345/b54bb993c1d89ad5b72bc94f26929773.png)


## **  将 JSON 转换为 XML**

将给定的 JSON 数据转换为 XML 格式。

![](https://static.xiaobot.net/file/2024-11-25/720345/dbe126d484e91bd18749bced52276dfe.png)


## **  解析 JSON**


### **  数据结构**

数据结构描述了 JSON 数据的组织方式,并允许将个别 JSON 项映射到您的方案中的其他模块。如果您不提供数据结构,您可以手动执行该模块,且 Make 将从提供的 JSON 构建该结构。

  1. 在"解析 JSON"模块中填写 JSON 字符串字段。

  2. 不要连接其他以下模块。因为 Make 不知道 JSON 数据的结构，无法将数据从 Parse JSON 模块映射到您方案中的其他模块。

  3. 手动运行该场景。Parse JSON 模块将识别您提供的 JSON 的结构。

  4. 您现在可以连接以下模块。来自 Parse JSON 模块的项目现已可用于映射。


### **  集合 与 数组**

JSON 字符串字段可以包含集合

\{ ... \}

\{ "name" : "Peter", "ID" : 1\}

这种情况下，输出将是一个单一的包含集合项目的包:

或者它可以包含一个数组。

\[ ... \]

:

\[ \{ "name" : "Peter", "ID" : 1 \}, \{ "name" : "Mike", "ID" : 2 \}\]

在这种情况下，输出将是一系列捆绑包，每个捆绑包包含一个数组的项目:


## **将数据记录转换为 JSON**

让我们假设您希望将要转换为 JSON 格式的数据记录位于 Google 电子表格中。以下是将数据记录转换为 JSON 格式的十个步骤过程:

  1. 将 Google Sheets > Select rows 模块放入您的场景以获取数据。设置该模块以从您的 Google 电子表格中检索行，并将返回行数的最大值设置为小于一的数字\(例如,三个\)以进行测试。执行 Google Sheets 模块\(右键单击它并选择"仅运行此模块"\)并验证其输出。

  2. 将数组聚合器模块连接到 Google Sheets 模块之后。在模块设置中,在源节点字段中选择 Google Sheets 模块。暂时保留其他字段不变。

  3. 在数组聚合器模块之后连接 JSON > 创建 JSON 模块。该模块的设置需要一个数据结构来描述 JSON 格式。单击"添加"按钮打开数据结构设置。创建此数据结构的最简单方法是从 JSON 样本自动生成。单击"生成器"按钮并将 JSON 样本粘贴到"示例数据"字段:

\{ "books": \[ \{ "id": "ID", "title": "Title", "author": "Author" \} \]\}

![](https://static.xiaobot.net/file/2024-11-25/720345/b3072379d2386c0acd5b07c84ab7b2bc.png)
  4. 点击保存。数据结构设置中的规范字段现在应该包含生成的结构。

  5. 将您的数据结构的名称更改为更具体的名称\(例如,"图书"\)并单击保存。如果一切顺利,根数组属性对应的字段应该会出现在 JSON 模块的设置中可映射。

  6. 点击字段旁边的地图按钮并绘制地图

Array\[\]

从数组聚合模块输出的项目:

![](https://static.xiaobot.net/file/2024-11-25/720345/7967a71050e24eff3559472e6f51b8f6.png)
  7. 点击确定关闭 JSON 模块的设置。

  8. 打开数组聚合器模块的设置。将目标结构从自定义更改为 JSON 模块对应于根数组属性的字段。将 Google 表格模块输出的项目映射到适当的字段:

![](https://static.xiaobot.net/file/2024-11-25/720345/2178f2ee99dfc44d67e1914fcd335e34.png)
  9. 单击"确定"关闭"数组聚合器"模块的设置。

  10. 运行该情景。如果一切顺利,JSON 模块应该输出正确的 JSON 格式。打开 Google 表格模块的设置,将返回的最大行数增加到大于电子表格中的行数,以处理所有数据。生成的 JSON 可以用作 HTTP 请求的正文,作为 Webhook 的响应返回等。


## **使用 Parse JSON 模拟模块输出**

使用 Make 可以模拟现有模块的数据并针对您的场景执行测试。您可以使用来自应用程序部分的 Parse JSON 工具来执行此操作。

  1. 使用"解析 JSON"执行要模拟输出数据的模块。

  2. 点击上方模块中的生成输出。

  3. 从下拉菜单中点击"下载输出包"。

包含原始模块以 JSON 格式输出的 bundle 的窗口弹出。

![](https://static.xiaobot.net/file/2024-11-25/720345/f55e99ea84367aabe9a64b03431fb5a4.png)
  4. 复制窗口内容并点击关闭。

![](https://static.xiaobot.net/file/2024-11-25/720345/b08bdee17cb01d789f76a154bd32110e.png)
  5. 在原始模块后插入"工具>解析 JSON"模块,打开其配置,并将步骤 4 中复制的 JSON 数据粘贴到 JSON 字符串字段中。

![](https://static.xiaobot.net/file/2024-11-25/720345/8be28314e06a71ca539f36eadbe2953e.png)
  6. 将该原始模块与场景取消连接。

请勿删除原始模块。

![](https://static.xiaobot.net/file/2024-11-25/720345/80fdcd126555d318d1bb902fb9fc8802.png)
  7. 执行模拟 JSON 模块并使用其输出而不是原始模块的输出在您的场景中。

  8. 建立并测试您的情况。

一旦完成测试,请使用 DevTool 的 Remap Source 工具将整个场景中的模拟模块映射更改为原始模块。

在模拟 JSON 模块之前放回原始模块,并删除 JSON 模块。

![](https://static.xiaobot.net/file/2024-11-25/720345/862396f9bccb9c2f03cd6c0907f86403.png)

在重新映射源代码部分，除非您不想在整个场景中重新映射原始模块的所有值，否则无需填写编辑模块字段。


## **  聚合到 JSON**

将多个输出包转换为单个包。

您需要将此聚合器模块连接到您想将其输出转换为单个模块的源模块;例如,Parse JSON 模块会以多个捆绑包的形式输出。

选择源模块后，选择数据结构，其余字段会从源模块自动填充。

![](https://static.xiaobot.net/file/2024-11-25/720345/e791ae17f57500d1e269ab9a1a5ee08f.png)

保存场景并执行它。您可以看到输出只有一个包。

![](https://static.xiaobot.net/file/2024-11-25/720345/175d8233dad4f3a783f5349a91d89978.png)


## **  故障排除 JSON**

**无法从 Parse JSON 模块映射数据**

确保 JSON 内容被正确映射到 Parse JSON 模块中,并且数据结构被正确定义。有关更多详细信息,请参见上述部分 JSON 处理。


# **翔宇工作流：专注于 AI 与自动化的频道**

**翔宇工作流** 希望通过本教程，帮助您在 **Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注 **翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-25/720345/1080c1b3aa33ef9da2906d6101970b92.png)