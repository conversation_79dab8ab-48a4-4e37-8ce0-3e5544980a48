---
title: "Make工作流图床必备：Cloudflare R2 存储桶手把手使用教程"
created_at: "2025-03-02T13:20:06.000000Z"
uuid: "7b22c05d-79b3-4a94-a24f-156d13dc94c6"
tags: ['API']
---

# Make工作流图床必备：Cloudflare R2 存储桶手把手使用教程

**标签**: API

#
# 为什么需要在自动化工作流中使用文件直链？

在当今的自动化工作环境中，文件直链\(direct link\)是指能够无需额外认证、重定向或复杂访问流程就能直接获取文件内容的URL链接。这种直链对自动化工作流至关重要，主要有以下原因：

**1\. 系统间无缝集成** ：当多个系统需要访问同一文件时，直链允许不同平台无需复杂的认证过程即可获取文件，大大提高了工作效率。

**2\. 流程自动化** ：在处理上传文件的工作流中，直链可以被轻松地编程使用，比如在API请求参数中直接传递文件链接，而不必处理文件上传的复杂逻辑。

云存储服务作为现代自动化工作流的基础设施，提供了以下关键功能：

  - **持久化存储** ：确保工作流中的文件安全存储且随时可访问

  - **直链生成** ：支持为存储的文件生成可直接访问的链接

  - **灵活的访问控制** ：允许设置直链的有效期、访问权限等，保障文件安全


## 一、Cloudflare R2是什么？为什么选择它？

Cloudflare R2是一种现代化的对象存储服务，类似于Amazon S3、腾讯云COS、阿里云OSS和七牛云KODO。R2的最大亮点在于**不限访问流量** ，这意味着不管用户下载存储在R2中的数据多少次，都无需支付任何出口费用。


### R2的核心优势：

  - **零出口费用** ：不像其他云存储服务，R2不收取数据传出费用，这对于高流量网站或应用可节省大量成本

  - **免费入门额度** ：提供10GB免费存储空间和每月大量免费API请求操作

  - **S3兼容API** ：完全兼容Amazon S3的API，便于从其他平台迁移

  - **全球分布式网络** ：利用Cloudflare的全球网络提供低延迟访问

  - **无缝集成Workers** ：与Cloudflare Workers原生集成，便于自定义请求处理和认证


## 二、Cloudflare R2的详细定价

R2采用简单透明的定价模式，主要包括以下几个方面：

![](https://static.xiaobot.net/file/2025-03-02/720345/3209dffefe65628200a479a03cc5e7b6.jpeg)

这种定价模式使R2特别适合：

  - 需要频繁访问存储内容的网站或应用

  - 个人博客、图床或小型网站

  - 预算有限但需要可靠存储解决方案的开发者


## 三、注册Cloudflare账号

  1. 访问[Cloudflare官网](<https://www.cloudflare.com/>)

![](https://static.xiaobot.net/file/2025-03-02/720345/9f47ab892bfe8a0fe35fd920791e94d7.png)

2.点击右上角的登录按钮

3.点击使用谷歌继续，初次登录可能需要邮箱进行验证。

![](https://static.xiaobot.net/file/2025-03-02/720345/3ba6bb4f390db140e40d9a9c9a4717af.png)

4.来到个人账户主页

![](https://static.xiaobot.net/file/2025-03-02/720345/5b9d4ac831ecc7d4d8bfc16ff2398e1b.png)

5.在左侧菜单栏中，点击"R2"选项

![](https://static.xiaobot.net/file/2025-03-02/720345/eedfa6ca1644f3fbe92cbc2406b8b0a7.png)

6.如果您是首次使用R2，会看到开通服务的选项

![](https://static.xiaobot.net/file/2025-03-02/720345/590aa9e93654d2c5c5cb38e745e61d01.jpeg)

7.点击”将R2 订阅到我的账号” 按钮

8.确认并完成购买流程


## 五、创建存储桶（Bucket）

1.存储桶是R2中存储对象的容器，类似于文件系统中的文件夹：

2.在Cloudflare控制面板中，导航到R2部分

3.点击创建存储桶按钮

![](https://static.xiaobot.net/file/2025-03-02/720345/87baca088680b55b0499b5eefda292b2.jpeg)

4.输入存储桶名称（名称必须唯一，只能包含小写字母、数字和连字符）

![](https://static.xiaobot.net/file/2025-03-02/720345/a376088b8de735b11f9d3b4a26aa3326.jpeg)

5.选择存储桶的地理位置和默认存储类，这里默认即可，点击"创建存储桶"完成设置

![](https://static.xiaobot.net/file/2025-03-02/720345/63efa75be0d9a9a1d99d542cb7e371ea.jpeg)

6.点击从计算机中选择试图上传一个图片以供测试

![](https://static.xiaobot.net/file/2025-03-02/720345/a91a002c95b95f5e79da3655111f6218.jpeg)


## 六、配置R2访问方式

默认情况下，R2中的资源不是公开可访问的。您有两种方式配置访问：


### 启用r2.dev访问（最简单）

1.在R2控制面板中，选择您创建的存储桶

2.点击"设置"选项卡

![](https://static.xiaobot.net/file/2025-03-02/720345/7c37603b1c8dff851dde63f859340a33.jpeg)

3.找到"公共访问"部分

4.启用"r2.dev子域访问"选项

![](https://static.xiaobot.net/file/2025-03-02/720345/ff3e52572882debd6e861f2db64fd846.jpeg)

5.输入 allow点击允许

![](https://static.xiaobot.net/file/2025-03-02/720345/16218001f8a65923656e77e3d9f6758b.jpeg)

6.回到对象页面，点击上传的图片即可获得图片的直链。这样以后直接在对象页面直接上传文件，然后点击该文件即可获得文件的直链。

![](https://static.xiaobot.net/file/2025-03-02/720345/dc0b4d87b629a0e0dd4b57540ed57e7f.jpeg)


### 常见问题

**Q: 如何通过R2使用图床功能？** A: 创建一个R2存储桶，启用公共访问，然后上传图片。使用生成的URL作为图片链接。

**Q: R2的数据安全性如何？** A: R2提供高度安全性，支持细粒度的访问控制和可选的加密功能。建议只将需要公开的内容设为公开访问。

**Q: 免费计划有什么限制？** A: 免费计划限制存储容量为10GB，A类操作100万次/月，B类操作1000万次/月。这对个人博客或小型网站通常已经足够。

**Q: 如何监控R2的使用情况？** A: 在R2控制面板中，可以查看存储使用情况和API请求的统计信息，帮助您了解资源使用情况。

翔宇希望通过本教程，让各位会员了解如何注册和使用Cloudflare R2服务，R2作为一个零出口费用的对象存储服务，为自动化工作流、图床、静态网站和应用程序提供了经济高效的解决方案。

![](https://static.xiaobot.net/file/2025-03-02/720345/1992cefd9358a2c1b6a443c9573a08e3.png)