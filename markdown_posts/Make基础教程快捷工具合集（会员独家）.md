---
title: "Make基础教程快捷工具合集（会员独家）"
created_at: "2024-11-10T09:25:37.000000Z"
uuid: "008e7fa9-1cd4-4769-b2c1-4597f846afe6"
tags: ['Make高级应用']
---

# Make基础教程快捷工具合集（会员独家）

**标签**: Make高级应用

#
## 翔宇花费了三个月的时间，精心整理了Make.com中常用的第三方工具的HTTP调用方法，涵盖了KIMI、OpenRouter、Jina、Exa、博查、DeepSeek、302.AI、Firecrawl、Deepbricks、Replicate、智谱AI、Google Gemini、硅基流动、Anthropic Claude官方、Fal.ai等20多种工具的调用代码。


### 这一过程耗时且复杂，翔宇对每一个工具的调用方式都进行了细致的测试和验证，确保内容的准确性和实用性。这些整理成果被精心汇总在一个表格中，旨在方便会员们能够快速调用，减少摸索和调试的时间。本文档仅供会员使用，请勿转载。文档地址可能随时更新添加新内容，如遇到无法访问请回到该页面查询最新地址。


# [**快捷工具合集链接**](<https://kdocs.cn/l/cn96iMt0JeM2>)


# **使用步骤：**

  1. **访问快捷工具网站：**

     - 打开金山文档中的快捷工具页面（无需登录即可复制单元格，界面操作简便）。

     - 找到符合你需求的模块，可以根据用途进行筛选，直至定位到具体的数据条目。

     - 在表格的代码栏目中，点击目标单元格，并使用键盘快捷键（Ctrl+C 或 Command+C）复制代码。（**为防止复制失败重复按键多复制几次** ）

![](https://static.xiaobot.net/file/2024-11-10/720345/f0355c51ed614a84096e348ebbf2bc01.png)
     - 打开Make.com的工作流页面，在合适的位置右键选择粘贴，或者使用快捷键（Ctrl+V 或 Command+V）完成代码的导入。

![](https://static.xiaobot.net/file/2024-11-10/720345/ba332128c55d2fc585922c6ecb78baef.png)
  2. **定位并复制请求体内容：**

     - 返回金山文档页面，找到对应的请求体（Request Content）。

     - 使用键盘快捷键复制请求体的内容。（注意要请求体和代码需要对应好，在同一行）

![](https://static.xiaobot.net/file/2024-11-10/720345/fbf3f11f7f116edac66b863618a63e1c.png)
  3. **创建请求体数据结构：**

     - 在Make.com中选择JSON模块，点击"Create JSON"选项。

![](https://static.xiaobot.net/file/2024-11-10/720345/740f439117f9c38cedc4ec20c1e9cb28.png)
     - 调整链接两个模块，顺序如图所示。

![](https://static.xiaobot.net/file/2024-11-10/720345/c32ba6037432ba0963614eed41f7fc6a.png)
     - 点击"添加"按钮，输入对应的字段名称。

![](https://static.xiaobot.net/file/2024-11-10/720345/fed917b13d4728f9d52d68b20e1a7fa3.png)![](https://static.xiaobot.net/file/2024-11-10/720345/b69937c71d9265d2d72d00c28c263ae2.png)
     - 点击"Generate"按钮，将步骤2复制到的请求体的数据粘贴到样例数据之中，以便创建新的数据结构。

![](https://static.xiaobot.net/file/2024-11-10/720345/56cbdd3eb21002f3073fc80921454db6.png)
     - 在弹出的界面无需修改内容点击保存。

![](https://static.xiaobot.net/file/2024-11-10/720345/3b3ddba3a98bfe41483fc81560f8862c.png)
     - 根据请求体的内容填写实际内容保存。

![](https://static.xiaobot.net/file/2024-11-10/720345/2d926d409b46dd3a6c734122b1716185.png)
  4. **设置HTTP模块：**

     - 进入HTTP模块，将之前"Create JSON"生成的JSON映射替换掉原有内容。

![](https://static.xiaobot.net/file/2024-11-10/720345/2cefd56bdfc368ce9012f22778d2d150.png)
     - 查找需要替换的字段，将API密钥替换为你的个人密钥，确保能够正确调用工具。

![](https://static.xiaobot.net/file/2024-11-10/720345/d8a65591cebc950ee03f7aa4f6cbf2a1.png)
  5. **后续模块数据映射：**

     - 为方便后续的HTTP模块使用，可以直接复制当前模块中的映射数据条目。

![](https://static.xiaobot.net/file/2024-11-10/720345/6837161c926cb44a40e9d1045ae62ebd.png)
     - 只需修改需要的序号或其他变量，即可将数据粘贴到后续的模块中。

![](https://static.xiaobot.net/file/2024-11-10/720345/7f593eb862b0e3b8b762212b9af6a16a.png)
     - 这样无需重复运行模块，既可以节省操作步骤，又可以提高整个工作流的执行效率。

翔宇工作流将持续更新相关内容，不断添加更多有趣且实用的工具，以帮助会员们更高效地使用Make.com的工作流功能。请持续关注翔宇的工作，获取最新的工作流改进与创新内容。


# API 密钥样式分类：

![](https://static.xiaobot.net/file/2024-11-10/720345/83a2f6e6d19d9fc68f904aceb5f1fbe2.png)

在填写 API 密钥时，可以根据上述几种样式进行区分，具体格式需依照所使用 API 的请求端口文档要求。同时需注意请求头中 Authorization 或相关认证字段的格式和要求，以确保密钥传递的正确性和安全性。

1\. Bearer + 空格格式

   \- **格式** ：Bearer 

   \- **示例** ：

     Bearer abcdefghijklmnopqrstuvwxyz123456

   \- **特点** ：

     \- 常用于基于 OAuth 2.0 的 API 验证机制。

     \- "Bearer" 表示持有者，授权方假定请求发送者具有合法权限。

2\. Key + 空格格式

   \- **格式** ：Key 

   \- **示例** ：

     Key abcdefghijklmnopqrstuvwxyz123456

   \- **特点** ：

     \- 常见于自定义认证机制。

     \- Key 是一个简单的前缀，与 API 密钥分隔开。

3\. 直接 API Key 格式

   \- **格式** ：

   \- **示例** ：

     abcdefghijklmnopqrstuvwxyz123456

   \- **特点** ：

     \- 最简单的密钥形式，没有前缀或额外修饰。

![](https://static.xiaobot.net/file/2024-11-10/720345/478af0de5778c54895beedc3c89a9245.png)

翔宇工作流将持续更新相关内容，不断添加更多有趣且实用的工具，以帮助会员们更高效地使用Make.com的工作流功能。请持续关注翔宇的工作，获取最新的工作流改进与创新内容。