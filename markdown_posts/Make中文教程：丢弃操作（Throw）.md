---
title: "Make中文教程：丢弃操作（Throw）"
created_at: "2024-08-30T02:02:02.000000Z"
uuid: "f734bd26-de1c-432b-bfbe-eb3c7cec2c0b"
tags: ['Make基础教程']
---

# Make中文教程：丢弃操作（Throw）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于丢弃操作的教程。

  1. **教程简介** ： 本教程介绍了在Make.com中模拟“抛出错误”操作的方法，通过配置不同的模块和使用变通方案实现条件抛出错误的功能。

  2. **学习目标** ： 学习如何在Make.com中模拟“抛出错误”的操作，理解如何通过JSON解析和HTTP模块等方式实现条件性错误处理，并掌握相关的错误处理指令。

  3. **学习内容** ：

     - 使用JSON > Parse JSON模块抛出错误。

     - 配置错误处理指令（Rollback、Commit、Ignore、Break）。

     - 使用HTTP模块作为替代方案。

     - 设置Webhook触发器与HTTP模块的链路。

  4. **概念理解** ：

     - **Throw** 错误指令：用于主动抛出错误。

     - **错误处理指令** ：用于控制错误发生后的场景行为，如Rollback、Commit等。

     - **HTTP模块** ：用于发送HTTP请求并处理响应，可以配置为条件性抛出错误。

  5. **任务练习** ： 尝试在Make.com中创建一个场景，使用JSON > Parse JSON模块故意触发一个错误，并配置Rollback错误处理指令，观察场景执行的回滚效果。


# **丢弃操作**

![](https://static.xiaobot.net/file/2024-11-29/720345/727976dfc2efe38d6cf508a0e56ecafa.png)

Make 不提供 Throw 错误处理指令。该功能实现正在分析和评估中。


## **  备选方案**

要有条件地抛出错误,您可以配置一个模块,使其在操作期间可选地故意失败。一种可能性是使用配置为可选抛出错误的 JSON > 解析 JSON 模块\(在本例中为 BundleValidationError \):

![](https://static.xiaobot.net/file/2024-11-29/720345/7c94d10a59f1a27cddd6ba0b151a602f.png)

您可以将错误处理指令之一附加到错误处理路由上以:

  - 强制场景执行停止并执行回滚阶段:回滚

  - 强制场景执行停止并执行提交阶段：提交

  - 停止路线处理：忽略

  - 停止处理路线并将其存储在未完成的执行队列中：中断

以下示例演示了回滚指令的使用:

![](https://static.xiaobot.net/file/2024-11-29/720345/82f80582cafc1323fcee5f21a946c3ef.png)

绕过 - 使用 HTTP 模块

用例 - 如果没有找到记录,请在一段时间后重试,使用中断指令。通常,当您的记录不能立即更新时,这一点很重要,您希望在自动化流程中稍后处理它。使用这个中断指令可以简化设置的复杂性。

当前障碍 - Make 不提供可以轻松地有条件地生成\(抛出\)错误的模块。

为了让您更好地理解,以下是未经修改的当前设置:这种情况下,如果在 Zendesk 中没有找到相关内容,它不会强制抛出错误并再次搜索,您需要通过保存记录来实现一个复杂的过程。

![](https://static.xiaobot.net/file/2024-11-29/720345/8ae3c147ab53f8b513c58e480d7c781b.png)

解决方案 - 要有条件地抛出错误,您可以将要抛出错误的模块替换为 HTTP 模块,然后在第二个场景中执行搜索,并使用 webhook 将 HTTP 模块链接到第二个场景。如果未找到结果,您可以自定义该模块以抛出错误。

 情景1-

  - 将希望抛出错误的模块替换为 HTTP > 发出请求模块

  - 将查询参数中的 URL 配置为来自自定义 Webhook 模块的 URL,并添加可选的查询参数来搜索电子邮件

  - 启用高级设置并检查将所有状态评估为错误。

![](https://static.xiaobot.net/file/2024-11-29/720345/90250cb415c82bb241ad0dd49bcc773b.png)
  - 在该 HTTP 模块中添加一个 Break 处理程序,并配置设置在稍后运行。

![](https://static.xiaobot.net/file/2024-11-29/720345/991b1f7a261fce1ae59ee007230f1151.png)

 场景 2-

  1. 设置 Webhooks > 自定义 Webhooks 作为触发器,并复制该 URL 在之前步骤中的 HTTP 模块中使用。

  2. 使用 Zendesk > 搜索用户模块，利用 HTTP 模块的参数执行查询。即使模块没有返回任何结果，也继续执行路由。

  3. 添加路由器并创建两条路由

     1. 设置第一个记录存在筛选器

     2. 记录中没有第二个

  4. 连续设置 webhook 响应模块

     1. 如果找到结果 - 以 JSON 格式发送包含所需正文的 200 状态。

     2. 如果没有找到结果 - 发送 4xx/404,在 JSON 中写明"没有找到记录"

![](https://static.xiaobot.net/file/2024-11-29/720345/455305ad2517f791a8227072064e5b58.png)

以下示例返回结果 - 您会注意到,当 Zendesk 模块执行 API 时,它不会发送任何错误消息,但在该操作中,我们正在使用 HTTP 模块复制错误。

![](https://static.xiaobot.net/file/2024-11-29/720345/39ccd76845a091f95663a8b6e4d446d3.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-29/720345/2a84fdc5c7b76a0b3636996a5fdb5e1f.png)