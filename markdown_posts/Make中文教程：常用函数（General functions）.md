---
title: "Make中文教程：常用函数（General functions）"
created_at: "2024-09-19T07:49:33.000000Z"
uuid: "a6be0aba-a083-4986-a18c-8c57123cea88"
tags: ['Make基础教程']
---

# Make中文教程：常用函数（General functions）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于通用函数的教程。

**教程简介** ：

欢迎来到翔宇工作流的通用函数教程！今天我们将带您了解如何在 Make.com 中使用通用函数，以帮助您在自动化场景中更灵活地调整和映射数据。重点内容包括如何使用常用函数（如 get、if、ifempty、switch、omit、pick）来处理映射数据。

**学习内容** ：

  1. 变量：理解executionId的作用。

  2. 函数：学会使用get, if, ifempty, switch, omit, pick等函数对数据进行判断和操作。

**概念理解** ：

  1. executionId：当前执行的唯一标识符也就是运行id，用于工作流日志记录和监控。

  2. 通用函数：get用于获取对象或数组中的值，if用于条件判断，ifempty用于处理空值问题，switch用于多条件判断，omit用于删除集合中的元素，pick用于选取集合中的元素。

**任务练习** ：

  1. 使用get函数从包含嵌套对象的数组中提取特定值。

  2. 实践if函数，设置表达式判断并返回相应结果。

  3. 测试ifempty函数，通过模拟空值和非空值场景理解其逻辑。

  4. 运用switch函数，实现多条件判断和匹配。

  5. 使用omit和pick函数，操控集合中的元素。


# **通用函数**

通用函数允许您在场景（scenario）中微调映射。例如，您可以选择或排除数组中的某些不需要的项目。


### **executionId**

返回当前执行的ID。对于工作流日志记录和监控目的非常有用，该函数也可以作为针对运行条目的唯一名称来使用，方便对不同运行流程进行区分。


### **get \(对象或数组; 路径\)**

返回对象或数组的某项值。要访问嵌套对象，请使用点符号。数组中的第一个项目索引为1。

**示例：**

> 
### **get\(array;1+1\)**
> 
> • array 表示你要访问的数组。
> 
> • 1+1 表示获取数组中第 2 个元素（因为 1+1 计算结果是 2）。
> 
> • 在这个语法中，数组的索引从 1 开始，而不是 0。
> 
> • **用途** ：获取 array 中的第二个元素。
> 
> 
### **get\(array;5.raw\_name\)**
> 
> • array 仍然是一个数组。
> 
> • 5.rawname 表示获取数组中第 5 个元素的 rawname 值。
> 
> • **用途** ：访问 array 中第 5 个对象的 raw\_name 值。
> 
> 
### get\(object;raw\_name\)
> 
> • object 是一个对象，而不是数组。
> 
> • rawname 表示要访问 object 中的 rawname 属性。
> 
> • **用途** ：直接获取对象的 raw\_name 属性。
> 
> 
### get\(object;rawname.subraw\_name\)
> 
> • object 是一个包含嵌套属性的对象。
> 
> • rawname.subraw\_name 表示访问对象的嵌套属性。
> 
> • **用途** ：获取 object 中 rawname 属性下的 subraw\_name 子属性。


### **if \(表达式; 值1; 值2\)**

在这个语法中，if \(表达式; 值1; 值2\)函数用于条件判断： 表达式是条件表达式。

如果条件表达式为真，则返回值1。

如果 条件表达式为假，则返回值2。

**示例：**

> 1\. if\(1=1; A; B\)
> 
> 条件 1=1 为真，因此返回 A。
> 
> 2\. if\(1=2; A; B\)
> 
> 条件 1=2 为假，因此返回 B。


### ifempty\(值1; 值2\)

如果值1不为空，则返回值1。否则，返回值2。

**示例：**

> 
### ifempty\(A; B\)
> 
> 返回 A，因为 A 不为空
> 
> 
### ifempty\(unknown; B\)
> 
> 返回 B，因为 unknown 是空值
> 
> 
### ifempty\(""\); B\)
> 
> 返回 B，因为 "" 是空字符串


### **switch\(表达式; 值1; 结果1; \[值2; 结果2; ...\]; \[其他\]\)**

switch 函数用于将一个表达式与多个可能的值进行匹配，并返回第一个匹配的结果。如果没有找到匹配项，则返回“其他”的值（如果有提供）。

• **语法** ：switch\(表达式; 值1; 结果1; 值2; 结果2; ...; \[其他\]\)

• **解释** ：表达式和值列表依次匹配，第一个匹配的“值”将返回对应的“结果”。如果没有匹配项，则返回“其他”（如果指定了）。

**示例：**

> 
### switch\(B; A; 1; B; 2; C; 3\)
> 
> \
# 表达式 B 与 B 匹配，返回结果 2
> 
> 
### switch\(C; A; 1; B; 2; C; 3\)
> 
> \
# 表达式 C 与 C 匹配，返回结果 3
> 
> 
### switch\(X; A; 1; B; 2; C; 3; 4\)
> 
> \
# 表达式 X 没有匹配项，返回“其他”值 4


### **omit\(集合; 键1; \[键2; ...\]\)**

omit 函数用于从集合中删除指定键的元素，返回一个不包含这些键的集合。

• **语法** ：omit\(集合; 键1; 键2; ...\)

• **解释** ：从集合中移除所有指定的键及其对应的元素。这在传递集合给 API 时非常有用，尤其是当 API 要求集合中有确切数量的元素时。

**示例：**

假设有一个集合 \{"name": "Alice", "age": 30, "location": "NY"\}

> 
### omit\(\{"name": "Alice", "age": 30, "location": "NY"\}; "age"; "location"\)
> 
> 返回 \{"name": "Alice"\}，删除了 age 和 location 键


### **pick\(集合; 键1; \[键2; ...\]\)**

pick 函数用于从集合中选取指定键的元素，返回一个只包含这些键的集合。

• **语法** ：pick\(集合; 键1; 键2; ...\)

• **解释** ：从集合中选择指定的键及其对应的元素，生成一个新的集合。这在需要将集合的特定子集传递给 API 时非常有用，确保符合所需的键要求。

**示例：**

假设有一个集合 \{"name": "Alice", "age": 30, "location": "NY"\}

> 
### pick\(\{"name": "Alice", "age": 30, "location": "NY"\}; "name"; "location"\)
> 
> 返回 \{"name": "Alice", "location": "NY"\}，只包含 name 和 location 键


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-20/720345/ff831c88361d6c4a4e6331d436a79a6f.png)