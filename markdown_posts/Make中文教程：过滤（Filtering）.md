---
title: "Make中文教程：过滤（Filtering）"
created_at: "2024-09-21T12:28:03.000000Z"
uuid: "81f0e174-7cec-4708-92ec-569cc483de27"
tags: ['Make基础教程']
---

# Make中文教程：过滤（Filtering）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于过滤操作的教程。

**教程简介** ：本教程介绍了如何在Make.com中使用过滤器来处理符合特定条件的数据包，包括添加过滤器、设置过滤条件以及使用操作符等内容。

**学习目标** ：学会如何在Make.com中添加和配置过滤器，以便只处理符合特定条件的数据包，从而提高自动化流程的效率和精准度。

**学习内容** ：教程涵盖了添加过滤器的步骤、设置过滤条件、使用各种操作符，以及在Make.com场景编辑器中复制过滤器的方法。

**概念理解** ：过滤器用于在两个模块之间筛选数据包，只允许符合特定条件的数据包继续传递。操作符用于定义数据包属性的条件，如存在性、文本比较、数值比较等。

**任务练习** ：在Make.com中创建一个简单的自动化场景，添加一个Facebook触发器和一个Google Drive操作，并设置过滤器使其只处理包含特定关键词的帖子。


## **过滤**

在某些情况下，您可能只需要处理符合特定标准的软件包。过滤器将帮助您选择这些软件包。

您可以在两个模块之间添加一个过滤器,并检查从前一个模块收到的包是否满足特定的过滤条件。如果是,这些包将传递给场景中的下一个模块。如果不是,它们的处理将被终止。

例如,如果你想创建一个带有 Facebook 触发器"关注帖子"的场景,并且你只想处理包含特定词语或由特定作者撰写的帖子,过滤器可以确保你只收到这些帖子,而不是其他任何内容。


## **  添加过滤器**

要在两个模块之间添加一个过滤器，请单击它们之间的连接线。

![](https://static.xiaobot.net/file/2024-11-24/720345/5c4a0dcb2a90053bb38dd0cc5c546de0.png)

这将打开一个面板,您可以在其中输入要创建的筛选器的名称,并定义一个或多个筛选条件。

![](https://static.xiaobot.net/file/2024-11-24/720345/7ab75dbb5bbac8bc7e12cdb441e55ceb.png)

对于每个条件,你可以输入一个或两个操作数和一个操作符,该操作符将决定它们之间的关系。在操作数字段中,你可以以与映射它们相同的方式输入值。

在上面的例子中,您可以看到如何连接 Gmail 触发器"监控电子邮件"和 Google Drive 动作"上传文件"。过滤器会自动将条件应用于第一个模块的传入包,并且仅允许包含附件的包传递到下一个模块。


## **  运算符**

对于每种条件,您可以使用几种不同的运算符。


### **  基本运算符**

  - 存在 - 检查是否填写了特定的捆绑项目。使用此运算符,您可以创建一个筛选器,例如,仅允许包含照片的 Facebook 帖子通过到场景中的下一个模块。

  - 不存在 - 存在的对立面。它仅允许某个特定项目未填写的那些捆绑包。


### **  其他运算符**

你可以使用的其他运算符有:文本比较运算符、数值运算符、时间和日期运算符,以及用于处理数组的运算符。


## **  复制过滤器**

场景编辑器目前还没有原生支持复制滤镜的功能,不过如果你使用谷歌 Chrome 浏览器,可以采用以下解决方法:

  1. 安装 Make DevTool Chrome 扩展程序。

  2. 在 Make 中打开您的场景。

  3. 在 Chrome 中打开开发者工具。您可以从 Chrome 主菜单中选择开发者工具命令或者直接按 Ctrl+Shift+I 或 F12。

![](https://static.xiaobot.net/file/2024-11-24/720345/9af23c70580b3a26512dac1efa82e8cb.png)
  4. 在开发者工具中,点击"制作"标签页。

  5. 在左侧边栏中单击工具图标。

  6. 点击复制滤镜工具,并在右侧面板中进行配置。

  7. 设置源模块字段 - 您希望复制的过滤器后面的模块。

  8. 设置目标模块字段 - 您希望复制过滤器的模块。

  9. 点击"运行"按钮。

![](https://static.xiaobot.net/file/2024-11-24/720345/e04c04ee15ded3ab7d8ce6648a3e6170.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/bd99060f4d1fdb504ec8f82b0921c452.png)