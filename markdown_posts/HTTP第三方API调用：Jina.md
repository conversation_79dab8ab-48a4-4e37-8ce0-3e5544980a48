---
title: "HTTP第三方API调用：Jina"
created_at: "2024-09-10T03:06:29.000000Z"
uuid: "0bcf3c73-817e-4f19-966d-09f7891eb097"
tags: ['快捷工具']
---

# HTTP第三方API调用：Jina

**标签**: 快捷工具

**使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 Make.com 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 <https://jsonformatter.org/> 点击“Format / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下金山文档**

[HTTP第三方API调用：Jina Reader](<https://kdocs.cn/l/clCS2Dpx0IRV>)

Jina Reader：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://r.jina.ai/https://example.com","serializeUrl":false,"method":"get","headers":[{"name":"Accept","value":"application/json"},{"name":"Authorization","value":"Bearer jina_e1454bb20670da60536f939108d5ke3trXlI09wFJH6d91e51JhgtBVmdbK1"},{"name":"X-No-Cache","value":"true"},{"name":"X-Return-Format","value":"markdown"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"Jina"},"restore":{"expect":{"method":{"mode":"chose","label":"GET"},"headers":{"mode":"chose","items":[null,null,null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}

Jina Segmenter：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://segment.jina.ai/","serializeUrl":false,"method":"post","headers":[{"name":"Content-Type","value":"application/json"},{"name":"Authorization","value":"Bearer jina_e1454bba60536d920670d16f939108d5ke3trXlI09wFJHe51JhgtBVmdbK1"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"content\":\"\\n  Jina AI: Your Search Foundation, Supercharged! 🚀\\n  Ihrer Suchgrundlage, aufgeladen! 🚀\\n  您的搜索底座，从此不同！🚀\\n  検索ベース,もう二度と同じことはありません！🚀\\n\",\"return_tokens\":\"true\",\"return_chunks\":\"true\",\"max_chunk_length\":\"1000\"}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"Jina Segment"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}


# Jina embeddings

    {
      "subflows": [
        {
          "flow": [
            {
              "id": 8,
              "module": "http:ActionSendData",
              "version": 3,
              "parameters": {
                "handleErrors": false,
                "useNewZLibDeCompress": true
              },
              "mapper": {
                "url": "https://api.jina.ai/v1/embeddings",
                "serializeUrl": false,
                "method": "post",
                "headers": [
                  {
                    "name": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "name": "Authorization",
                    "value": "Bearer jina_43643a6695ec4ad0b01607576f0c0b69J8zPoz-iWAk2wBx-w8YL3CxXxGx3"
                  }
                ],
                "qs": [],
                "bodyType": "raw",
                "parseResponse": true,
                "authUser": "",
                "authPass": "",
                "timeout": "",
                "shareCookies": false,
                "ca": "",
                "rejectUnauthorized": true,
                "followRedirect": true,
                "useQuerystring": false,
                "gzip": true,
                "useMtls": false,
                "contentType": "application/json",
                "data": "{\"model\":\"jina-embeddings-v3\",\"task\":\"text-matching\",\"dimensions\":1024,\"late_chunking\":false,\"embedding_type\":\"float\",\"input\":[\"Organic skincare for sensitive skin with aloe vera and chamomile: Imagine the soothing embrace of nature with our organic skincare range, crafted specifically for sensitive skin. Infused with the calming properties of aloe vera and chamomile, each product provides gentle nourishment and protection. Say goodbye to irritation and hello to a glowing, healthy complexion.\",\"Bio-Hautpflege für empfindliche Haut mit Aloe Vera und Kamille: Erleben Sie die wohltuende Wirkung unserer Bio-Hautpflege, speziell für empfindliche Haut entwickelt. Mit den beruhigenden Eigenschaften von Aloe Vera und Kamille pflegen und schützen unsere Produkte Ihre Haut auf natürliche Weise. Verabschieden Sie sich von Hautirritationen und genießen Sie einen strahlenden Teint.\",\"Cuidado de la piel orgánico para piel sensible con aloe vera y manzanilla: Descubre el poder de la naturaleza con nuestra línea de cuidado de la piel orgánico, diseñada especialmente para pieles sensibles. Enriquecidos con aloe vera y manzanilla, estos productos ofrecen una hidratación y protección suave. Despídete de las irritaciones y saluda a una piel radiante y saludable.\",\"针对敏感肌专门设计的天然有机护肤产品：体验由芦荟和洋甘菊提取物带来的自然呵护。我们的护肤产品特别为敏感肌设计，温和滋润，保护您的肌肤不受刺激。让您的肌肤告别不适，迎来健康光彩。\",\"新しいメイクのトレンドは鮮やかな色と革新的な技術に焦点を当てています: 今シーズンのメイクアップトレンドは、大胆な色彩と革新的な技術に注目しています。ネオンアイライナーからホログラフィックハイライターまで、クリエイティビティを解き放ち、毎回ユニークなルックを演出しましょう。\"]}",
                "followAllRedirects": false
              },
              "metadata": {
                "designer": {
                  "x": 210,
                  "y": 265,
                  "name": "HTTP"
                },
                "restore": {
                  "expect": {
                    "method": {
                      "mode": "chose",
                      "label": "POST"
                    },
                    "headers": {
                      "mode": "chose",
                      "items": [null, null]
                    },
                    "qs": {
                      "mode": "chose",
                      "items": [null]
                    },
                    "bodyType": {
                      "label": "Raw"
                    },
                    "contentType": {
                      "label": "JSON (application/json)"
                    }
                  }
                },
                "parameters": [
                  {
                    "name": "handleErrors",
                    "type": "boolean",
                    "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                    "required": true
                  },
                  {
                    "name": "useNewZLibDeCompress",
                    "type": "hidden"
                  }
                ],
                "expect": [
                  {
                    "name": "url",
                    "type": "url",
                    "label": "URL",
                    "required": true
                  },
                  {
                    "name": "serializeUrl",
                    "type": "boolean",
                    "label": "Serialize URL",
                    "required": true
                  },
                  {
                    "name": "method",
                    "type": "select",
                    "label": "Method",
                    "required": true,
                    "validate": {
                      "enum": ["get", "head", "post", "put", "patch", "delete", "options"]
                    }
                  },
                  {
                    "name": "headers",
                    "type": "array",
                    "label": "Headers",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "qs",
                    "type": "array",
                    "label": "Query String",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "bodyType",
                    "type": "select",
                    "label": "Body type",
                    "validate": {
                      "enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]
                    }
                  },
                  {
                    "name": "parseResponse",
                    "type": "boolean",
                    "label": "Parse response",
                    "required": true
                  },
                  {
                    "name": "authUser",
                    "type": "text",
                    "label": "User name"
                  },
                  {
                    "name": "authPass",
                    "type": "password",
                    "label": "Password"
                  },
                  {
                    "name": "timeout",
                    "type": "uinteger",
                    "label": "Timeout",
                    "validate": {
                      "max": 300,
                      "min": 1
                    }
                  },
                  {
                    "name": "shareCookies",
                    "type": "boolean",
                    "label": "Share cookies with other HTTP modules",
                    "required": true
                  },
                  {
                    "name": "ca",
                    "type": "cert",
                    "label": "Self-signed certificate"
                  },
                  {
                    "name": "rejectUnauthorized",
                    "type": "boolean",
                    "label": "Reject connections that are using unverified (self-signed) certificates",
                    "required": true
                  },
                  {
                    "name": "followRedirect",
                    "type": "boolean",
                    "label": "Follow redirect",
                    "required": true
                  },
                  {
                    "name": "useQuerystring",
                    "type": "boolean",
                    "label": "Disable serialization of multiple same query string keys as arrays",
                    "required": true
                  },
                  {
                    "name": "gzip",
                    "type": "boolean",
                    "label": "Request compressed content",
                    "required": true
                  },
                  {
                    "name": "useMtls",
                    "type": "boolean",
                    "label": "Use Mutual TLS",
                    "required": true
                  },
                  {
                    "name": "contentType",
                    "type": "select",
                    "label": "Content type",
                    "validate": {
                      "enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]
                    }
                  },
                  {
                    "name": "data",
                    "type": "buffer",
                    "label": "Request content"
                  },
                  {
                    "name": "followAllRedirects",
                    "type": "boolean",
                    "label": "Follow all redirect",
                    "required": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "metadata": {
        "version": 1
      }
    }


# Jina reranker

    {
      "subflows": [
        {
          "flow": [
            {
              "id": 8,
              "module": "http:ActionSendData",
              "version": 3,
              "parameters": {
                "handleErrors": false,
                "useNewZLibDeCompress": true
              },
              "mapper": {
                "url": "https://api.jina.ai/v1/rerank",
                "serializeUrl": false,
                "method": "post",
                "headers": [
                  {
                    "name": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "name": "Authorization",
                    "value": "Bearer jina_43643a6695ec4ad0b01607576f0c0b69J8zPoz-iWAk2wBx-w8YL3CxXxGx3"
                  }
                ],
                "qs": [],
                "bodyType": "raw",
                "parseResponse": true,
                "authUser": "",
                "authPass": "",
                "timeout": "",
                "shareCookies": false,
                "ca": "",
                "rejectUnauthorized": true,
                "followRedirect": true,
                "useQuerystring": false,
                "gzip": true,
                "useMtls": false,
                "contentType": "application/json",
                "data": "{\"model\":\"jina-reranker-v2-base-multilingual\",\"query\":\"Organic skincare products for sensitive skin\",\"top_n\":3,\"documents\":[\"Organic skincare for sensitive skin with aloe vera and chamomile: Imagine the soothing embrace of nature with our organic skincare range, crafted specifically for sensitive skin. Infused with the calming properties of aloe vera and chamomile, each product provides gentle nourishment and protection. Say goodbye to irritation and hello to a glowing, healthy complexion.\",\"New makeup trends focus on bold colors and innovative techniques: Step into the world of cutting-edge beauty with this seasons makeup trends. Bold, vibrant colors and groundbreaking techniques are redefining the art of makeup. From neon eyeliners to holographic highlighters, unleash your creativity and make a statement with every look.\",\"Bio-Hautpflege für empfindliche Haut mit Aloe Vera und Kamille: Erleben Sie die wohltuende Wirkung unserer Bio-Hautpflege, speziell für empfindliche Haut entwickelt. Mit den beruhigenden Eigenschaften von Aloe Vera und Kamille pflegen und schützen unsere Produkte Ihre Haut auf natürliche Weise. Verabschieden Sie sich von Hautirritationen und genießen Sie einen strahlenden Teint.\",\"Neue Make-up-Trends setzen auf kräftige Farben und innovative Techniken: Tauchen Sie ein in die Welt der modernen Schönheit mit den neuesten Make-up-Trends. Kräftige, lebendige Farben und innovative Techniken setzen neue Maßstäbe. Von auffälligen Eyelinern bis hin zu holografischen Highlightern – lassen Sie Ihrer Kreativität freien Lauf und setzen Sie jedes Mal ein Statement.\",\"Cuidado de la piel orgánico para piel sensible con aloe vera y manzanilla: Descubre el poder de la naturaleza con nuestra línea de cuidado de la piel orgánico, diseñada especialmente para pieles sensibles. Enriquecidos con aloe vera y manzanilla, estos productos ofrecen una hidratación y protección suave. Despídete de las irritaciones y saluda a una piel radiante y saludable.\",\"Las nuevas tendencias de maquillaje se centran en colores vivos y técnicas innovadoras: Entra en el fascinante mundo del maquillaje con las tendencias más actuales. Colores vivos y técnicas innovadoras están revolucionando el arte del maquillaje. Desde delineadores neón hasta iluminadores holográficos, desata tu creatividad y destaca en cada look.\",\"针对敏感肌专门设计的天然有机护肤产品：体验由芦荟和洋甘菊提取物带来的自然呵护。我们的护肤产品特别为敏感肌设计，温和滋润，保护您的肌肤不受刺激。让您的肌肤告别不适，迎来健康光彩。\",\"新的化妆趋势注重鲜艳的颜色和创新的技巧：进入化妆艺术的新纪元，本季的化妆趋势以大胆的颜色和创新的技巧为主。无论是霓虹眼线还是全息高光，每一款妆容都能让您脱颖而出，展现独特魅力。\",\"敏感肌のために特別に設計された天然有機スキンケア製品: アロエベラとカモミールのやさしい力で、自然の抱擁を感じてください。敏感肌用に特別に設計された私たちのスキンケア製品は、肌に優しく栄養を与え、保護します。肌トラブルにさようなら、輝く健康な肌にこんにちは。\",\"新しいメイクのトレンドは鮮やかな色と革新的な技術に焦点を当てています: 今シーズンのメイクアップトレンドは、大胆な色彩と革新的な技術に注目しています。ネオンアイライナーからホログラフィックハイライターまで、クリエイティビティを解き放ち、毎回ユニークなルックを演出しましょう。\"]}",
                "followAllRedirects": false
              },
              "metadata": {
                "designer": {
                  "x": 210,
                  "y": 265,
                  "name": "HTTP"
                },
                "restore": {
                  "expect": {
                    "method": {
                      "mode": "chose",
                      "label": "POST"
                    },
                    "headers": {
                      "mode": "chose",
                      "items": [null, null]
                    },
                    "qs": {
                      "mode": "chose",
                      "items": [null]
                    },
                    "bodyType": {
                      "label": "Raw"
                    },
                    "contentType": {
                      "label": "JSON (application/json)"
                    }
                  }
                },
                "parameters": [
                  {
                    "name": "handleErrors",
                    "type": "boolean",
                    "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                    "required": true
                  },
                  {
                    "name": "useNewZLibDeCompress",
                    "type": "hidden"
                  }
                ],
                "expect": [
                  {
                    "name": "url",
                    "type": "url",
                    "label": "URL",
                    "required": true
                  },
                  {
                    "name": "serializeUrl",
                    "type": "boolean",
                    "label": "Serialize URL",
                    "required": true
                  },
                  {
                    "name": "method",
                    "type": "select",
                    "label": "Method",
                    "required": true,
                    "validate": {
                      "enum": ["get", "head", "post", "put", "patch", "delete", "options"]
                    }
                  },
                  {
                    "name": "headers",
                    "type": "array",
                    "label": "Headers",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "qs",
                    "type": "array",
                    "label": "Query String",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "bodyType",
                    "type": "select",
                    "label": "Body type",
                    "validate": {
                      "enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]
                    }
                  },
                  {
                    "name": "parseResponse",
                    "type": "boolean",
                    "label": "Parse response",
                    "required": true
                  },
                  {
                    "name": "authUser",
                    "type": "text",
                    "label": "User name"
                  },
                  {
                    "name": "authPass",
                    "type": "password",
                    "label": "Password"
                  },
                  {
                    "name": "timeout",
                    "type": "uinteger",
                    "label": "Timeout",
                    "validate": {
                      "max": 300,
                      "min": 1
                    }
                  },
                  {
                    "name": "shareCookies",
                    "type": "boolean",
                    "label": "Share cookies with other HTTP modules",
                    "required": true
                  },
                  {
                    "name": "ca",
                    "type": "cert",
                    "label": "Self-signed certificate"
                  },
                  {
                    "name": "rejectUnauthorized",
                    "type": "boolean",
                    "label": "Reject connections that are using unverified (self-signed) certificates",
                    "required": true
                  },
                  {
                    "name": "followRedirect",
                    "type": "boolean",
                    "label": "Follow redirect",
                    "required": true
                  },
                  {
                    "name": "useQuerystring",
                    "type": "boolean",
                    "label": "Disable serialization of multiple same query string keys as arrays",
                    "required": true
                  },
                  {
                    "name": "gzip",
                    "type": "boolean",
                    "label": "Request compressed content",
                    "required": true
                  },
                  {
                    "name": "useMtls",
                    "type": "boolean",
                    "label": "Use Mutual TLS",
                    "required": true
                  },
                  {
                    "name": "contentType",
                    "type": "select",
                    "label": "Content type",
                    "validate": {
                      "enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]
                    }
                  },
                  {
                    "name": "data",
                    "type": "buffer",
                    "label": "Request content"
                  },
                  {
                    "name": "followAllRedirects",
                    "type": "boolean",
                    "label": "Follow all redirect",
                    "required": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "metadata": {
        "version": 1
      }
    }


## **翔宇工作流制作**

翔宇工作流致力于持续优化和更新便捷化工具，以提升 Make.com 工作流的创建与使用效率。通过内置的常用 API 调用模块代码，减少了重复配置的时间，并增强了操作的一致性和可靠性。

![](https://static.xiaobot.net/file/2024-09-10/720345/63bb772b829a11b1ee318e511d681cfc.png)