---
title: "Make可调用免费API 2"
created_at: "2024-08-24T06:00:02.000000Z"
uuid: "0ef447b6-545c-4a82-9977-8fba18d728cb"
tags: ['API']
---

# Make可调用免费API 2

**标签**: API

**前言**

Make.com的自动化流程通过API的集成可以实现更广泛的操作。API能够将不同的系统、应用和服务连接起来，使工作流具备跨平台的数据交互能力，从而打破单一平台的局限性，实现复杂的自动化操作。

在自媒体制作中，自动化工作流显著提升效率。韩小韩WebAPI提供了丰富资源，通过Make.com的HTTP模块即可调用，优化内容制作。翔宇工作流在这里为大家介绍这些API的功能，并探讨其高效应用。

韩小韩WebAPI网址：

<https://api.vvhan.com/>

感谢韩小韩WebAPI的免费服务。由于免费API的特性，可能会出现访问不稳定或因不可抗力导致下架等情况。如遇API下架，可及时寻找其他替代方案。请随时关注API状态，并根据实际情况调整使用策略。


### API分类与功能介绍

韩小韩WebAPI接口提供了多种接口，涵盖查询类、娱乐类和实时类等多个领域。以下是这些API的具体功能介绍：

**查询类API**

1\. **获取IP信息API接口** ：通过IP地址查询详细的地理位置信息。

2\. **手机号码归属地查询API接口** ：查询手机号码的归属地信息，便于用户数据统计与分析。

3\. **二维码生成API接口** ：生成包含电话、文字、链接的二维码。

4\. **天气情况API接口** ：查询城市或县区的天气情况，适用于每日内容更新。

5\. **访客相关信息获取API接口** ：获取访客的操作系统、浏览器信息及IP地址等详细数据。

6\. **IP天气签名档API接口** ：获取系统及浏览器和当地天气信息，适用于个性化展示。

**娱乐类API**

1\. **Bing每日图片API接口** ：获取必应每日壁纸，适用于每日视觉更新。

2\. **风景图片API接口** ：随机输出超清风景图片，用于视觉内容创作。

3\. **二次元图片API接口** ：随机输出超清动漫图片，适合ACG文化内容创作。

4\. **电脑分辨率美图API接口** ：随机输出电脑分辨率的cosplay或福利姬等美图，适合二次元和视觉内容创作。

5\. **手机分辨率美图API接口** ：随机输出手机分辨率的cosplay或福利姬等美图，适合移动设备展示。

6\. **推荐头像API接口** ：随机输出推荐头像图片，便于个性化展示。

7\. **精选头像API接口** ：随机输出精选头像图片，提升个人形象。

8\. **动漫头像API接口** ：随机输出动漫头像图片，适合个人资料展示。

9\. **男生头像API接口** ：随机输出男生头像图片，适合社交媒体使用。

10\. **女生头像API接口** ：随机输出女生头像图片，适合社交平台展示。

11\. **小众头像API接口** ：随机输出小众头像图片，满足个性化需求。

12\. **一言句子API接口** ：随机输出动漫文学、网络鸡汤、文学诗词等文案。

13\. **骚话API接口** ：随机输出一句骚话，用于趣味性内容创作。

14\. **情话API接口** ：随机输出一句撩人的情话，适合社交媒体互动。

15\. **笑话API接口** ：随机输出一段笑话，增添内容的趣味性。

16\. **满屏雪花下雪API接口** ：为网站或博客添加满屏雪花下雪效果，提升视觉体验。

17\. **透明波浪特效API接口** ：为网站或博客添加SVG透明波浪特效，丰富页面视觉效果。

18\. **樱花效果API接口** ：为网站或博客添加樱花飘落效果，增加页面的美感。

19\. **梅花特效API接口** ：为网站或博客添加梅花特效，适合冬季或节日氛围营造。

20\. **春节灯笼API接口** ：为网站或博客添加春节灯笼效果，营造节日气氛。

**实时类API**

1\. **微博实时热搜热榜API接口** ：获取微博实时热搜榜单，适合热点追踪。

2\. **知乎实时热搜热榜API接口** ：获取知乎实时热搜榜单，捕捉最新话题。

3\. **抖音热点热榜API接口** ：获取抖音热点榜单，帮助创作者了解热门趋势。

4\. **36氪24小时热榜API接口** ：获取36氪24小时热榜，聚焦科技新闻热点。

5\. **哔哩哔哩全站日榜API接口** ：获取哔哩哔哩每日热门榜单，助力视频内容创作与推广。

6\. **百度热点热榜API接口** ：获取百度热点热榜，方便抓住最新热门话题。

7\. **豆瓣小组精选API接口** ：获取豆瓣小组精选内容，适合社区内容创作。

8\. **IT资讯热榜API接口** ：获取IT行业最新资讯热榜，帮助技术内容创作者了解行业趋势。

9\. **虎嗅网24小时热榜API接口** ：获取虎嗅网24小时内的热门文章，适合商业及科技内容创作。

10\. **人人都是产品经理热文日榜API接口** ：获取“人人都是产品经理”平台热文日榜，适合产品经理及创业内容制作。

11\. **星座运势API接口** ：每日更新十二星座运势，适用于星座相关内容创作。

12\. **每日一句励志英语API接口** ：输出每日一句励志英语，适合教育或励志内容的日常更新。

13\. **热搜热榜聚合API接口** ：聚合各大平台实时热搜榜单，提供一站式热点内容参考。


### 示例应用1：自动化热点资讯内容创作工作流

1\. **实时资讯获取**

实时类API为Make工作流提供了除RSS外的另一种资讯获取方式。通过调用这些API，可以获取丰富的实时资讯。韩小韩的资讯类API涵盖微博、知乎、抖音、36氪、哔哩哔哩、百度、豆瓣、IT资讯、虎嗅网、人人都是产品经理等平台的实时热搜和热门榜单，以及每日星座运势和每日一句励志英语等内容。这些接口为内容创作者提供最新的热点话题资源，帮助快速响应趋势，优化内容创作。

2\. **热点新闻关键词检索**

获取热点新闻后，可以将其作为检索关键词，利用EXA AI的数据抓取模块进一步抓取与该热点相关的全文资源。这一步骤确保您获取到最新、最相关的详细资讯，为后续内容创作提供了丰富的素材。

3\. **自动化内容创作**

使用ChatGPT模块，将抓取到的详细信息整理撰写为微信公众号文章或小红书笔记。通过这一自动化工作流，内容创作变得快捷高效，从资讯获取到最终内容发布的整个流程都可以自动化完成，大大提升了创作效率和内容质量。


### 示例应用2：自动化小红书壁纸赛道内容生成工作流

1\. **壁纸图片获取**

在Make中使用HTTP模块调用韩小韩WebAPI的娱乐类接口，获取每日更新的Bing壁纸、超清风景图片或二次元动漫图片。这些图片将用于小红书壁纸赛道的内容创作，满足用户对不同风格壁纸的需求。

2\. **图片上传与处理**

使用Make中的图床模块，将获取的图片上传到在线存储平台并获取链接。接着，通过Make中的Replicate模块对图片进行高清处理或漫画化处理，提升图片的质量与风格。这一步骤可以批量处理图片，生成适合不同用户群体的高质量壁纸。

3\. **内容自动化生成**

在Make中整合处理后的壁纸图片，生成适合小红书发布的图文内容，包括相关文案编写。最后，自动将这些内容发布到小红书相应账号，实现快速响应用户对最新壁纸的需求。整个流程通过Make实现了高效自动化。


### 示例应用3：自动化小红书头像赛道内容生成工作流

1\. **头像图片获取**

在Make中使用HTTP模块调用韩小韩WebAPI的娱乐类接口，随机获取推荐头像、精选头像、动漫头像、男生头像、女生头像以及小众头像图片。这些图片用于小红书头像赛道的内容创作，满足用户对不同类型头像的需求。

2\. **图片上传与优化**

通过Make中的图床模块上传获取的头像图片，并利用Make中的AI工具模块对图片进行优化处理，如调整尺寸、增强画质等，以确保头像图片符合各类社交媒体的使用标准。可以批量处理大量头像，满足用户的个性化需求。

3\. **自动化内容生成与发布**

使用Make自动化工作流，将优化后的头像图片整合成适合小红书发布的图文内容，包括头像预览和推荐理由等。最后，自动将内容发布到小红书相应账号，帮助用户快速选择和使用最新、个性化的头像。整个流程在Make中实现，极大地简化了内容制作，提高了创作效率。

未来翔宇工作流也会发布相关的自动化工作流，以满足相关自媒体内容制作的自动化需求。