---
title: "HTTP第三方API调用：MINIMAX（15元免费额度）"
created_at: "2024-11-26T03:11:43.000000Z"
uuid: "8edac204-a4b6-46d4-ae2c-92129799fba6"
tags: ['快捷工具']
---

# HTTP第三方API调用：MINIMAX（15元免费额度）

**标签**: 快捷工具

# MiniMax介绍

MiniMax是一家成立于2021年12月的人工智能公司，总部位于中国上海。它专注于通用人工智能（AGI）技术的开发，致力于通过多模态融合（文本、语音、视觉）推动人工智能的应用和商业模式创新。


## 公司背景

MiniMax由多位来自商汤科技的计算机视觉专家创立，最初获得了米哈游的投资。2024年3月，阿里巴巴集团领导了一轮6亿美元的融资，使MiniMax的估值达到25亿美元\[2\]。该公司被誉为中国的“AI老虎”之一，反映了其在行业中的重要地位和影响力。


## 使用方法：

  - 使用手机号注册登录如下网址

<https://platform.minimaxi.com/examination-center/text-experience-center/cc_v2>

  - 点击账户管理和接口密钥，创建新的秘钥即可API调用

![](https://static.xiaobot.net/file/2024-11-26/720345/5ec933cac1ac18ec21d195531c78fb28.png)

  - 复制下方代码粘贴替换秘钥和对话内容即可体验。


## 主要产品


### ABAB大模型

2023年8月，MiniMax推出了ABAB大模型，这是一种基于Transformer架构的预训练语言模型，具备强大的语言理解和生成能力。该模型经过大规模语料库训练，在多个自然语言处理任务中表现优异。


### Talkie与Hailuo AI

\- **Talkie** ：这是MiniMax的国际市场产品，允许用户与虚拟角色进行对话。自2024年推出以来，该应用在美国等地迅速获得了大量用户。

\- **Hailuo AI** ：这是一个多模态大型语言模型平台，提供文本和音乐生成服务。2024年9月，该平台还推出了文本转视频模型video-01，进一步扩展了其产品线、。


### 语音合成技术

MiniMax开发了一种新一代语音大模型，能够生成高保真、个性化的语音，并支持多种语言。这项技术在多个商业应用中得到了应用，包括社交、播客、有声书等场景、。


### 技术优势

MiniMax的技术团队由来自全球顶尖高校和科技公司的专家组成，拥有丰富的自然语言处理、计算机视觉等领域的经验。公司在全球范围内申请了大量专利，并在人工智能研究方面取得了显著成果。

总结来说，MiniMax不仅在技术研发上具有领先优势，还在市场应用上展现出强大的潜力，是当前人工智能领域的重要参与者。


# **快捷调用工具使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 [Make.com](<http://Make.com>) 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 <https://jsonformatter.org/> 点击“Format / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下文档**

[HTTP第三方API调用：MINIMAX](<https://kdocs.cn/l/chpyUjWG6SbT>)

![](https://static.xiaobot.net/file/2024-11-26/720345/6aa629221242ab2019e737ed40f5af27.png)