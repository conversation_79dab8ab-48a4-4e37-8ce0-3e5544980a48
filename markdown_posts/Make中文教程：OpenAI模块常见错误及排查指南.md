---
title: "Make中文教程：OpenAI模块常见错误及排查指南"
created_at: "2024-08-23T14:06:47.000000Z"
uuid: "6644c74c-970c-43bf-a6e1-ad8ebaa532aa"
tags: ['Make基础教程']
---

# Make中文教程：OpenAI模块常见错误及排查指南

**标签**: Make基础教程

我是翔宇工作流的小宇，今天将为大家分享在使用Make.com的OpenAI模块时可能遇到的常见错误及其解决方法。在使用Make中的OpenAI模块（包括ChatGPT、Whisper、DALL-E）时，你可以创建聊天或提示的补全、编辑和审查，生成图像，管理文件和批处理。本指南将帮助您排查和解决使用这些模块时可能遇到的常见错误。

首先需要注意的是使用 OpenAI 模块时，你需要在 OpenAI 官方网站添加有效的付款方式并进行充值，才能正常使用该服务。该模块的使用与 OpenAI Plus 会员资格无关，需要单独购买和付费才能访问相关功能。

如果没有合适的付款方式，您可以考虑使用 Wildcard 的虚拟信用卡进行支付，具体注册链接为 <https://wildcard.com.cn/i/8XMREZFM>。请注意，该方式属于第三方服务，可能存在不稳定性，因此建议小额充值，随用随付。关于如何使用Wildcard 虚拟信用卡为 OpenAI API 充值的具体教程，您可以参考此链接：<https://help.bewildcard.com/zh-CN/articles/8129507-openai-api-%E7%BB%91%E5%8D%A1%E5%8F%8A-key-%E7%94%B3%E8%AF%B7%E6%95%99%E7%A8%8B>。注意wildcard有开卡费用，如希望性价比，可选国产大模型智谱AI，API调用价格极低近似免费，也可更换其他中转API。

如遇到困难，您可以联系在线客服进行解决。

下面具体介绍错误可以分为如下几类：


# **1\. 连接错误**

**错误类型1：API Key或Organization ID无效**

**解决方法：**

确保你使用的API Key和Organization ID是从OpenAI账户中正确获取的。确保在Make中输入的API Key和Organization ID无误。如果API Key已经过期，重新生成新的API Key并更新Make中的连接配置。

**错误类型2：未正确建立连接**

**解决方法：**

在Make中创建连接时，确认所有必填字段（API Key和Organization ID）都已正确填写，并点击“保存”以完成连接。如果连接失败，尝试重新创建连接，并确保网络环境正常。

**错误示例代码1：**

Error: 401 Unauthorized

\{“error”:“invalid\_api\_key”,“message”:“提供的API密钥无效。”\}

**解决方案：**

1\. 登录OpenAI账户，重新生成API Key，并确保将其正确复制到Make连接设置中。

2\. 检查API Key和Organization ID是否正确，确保在Make中没有输入错误或遗漏。

**错误示例代码2：**

401 - 无效认证

原因：你的 API 密钥或认证信息有误。这可能是因为密钥过期、被撤销，或者与请求的组织或项目不匹配。

**解决方法：**

检查密钥：确保你在请求中使用的 API 密钥是正确的，且与你的组织或项目关联。

生成新密钥：如果不确定密钥是否有效，可以重新生成一个。

权限验证：确认你的密钥拥有执行当前操作的权限。

**错误示例代码3：**

401 - 提供的 API 密钥不正确

原因：你提供的 API 密钥有误，可能是拼写错误、多余空格或使用了错误的密钥。

**解决方法：**

检查密钥：仔细核对 API 密钥，确保没有输入错误。

清除缓存：清除浏览器缓存，防止旧的密钥被使用。

生成新密钥：如果问题仍然存在，可以生成一个新的 API 密钥。

**错误示例代码4：**

401 - 您必须是组织的成员才能使用 API

原因：你的账号不在任何组织中，或者没有权限访问该 API。

**解决方法：**

加入组织：联系你的组织管理员，申请加入组织。

检查权限：确认你的账号在组织中拥有正确的权限。

**错误示例代码5：**

429 - 请求速率限制已达

原因：你发送请求的频率过高，超过了 API 的限制。

**解决方法：**

降低频率：减少请求的频率，避免短时间内发送大量请求。

检查配额：查看你的账户配额，了解你的使用限制。

升级计划：如果需要更高的配额，可以考虑升级你的订阅计划。

**错误示例代码6：**

429 - 您已超出当前配额

原因：你已经用完了当月的配额。

**解决方法：**

检查配额：查看你的账户配额，了解剩余的配额。

升级计划：如果需要更大的配额，可以考虑升级你的订阅计划。

**错误示例代码7：**

500 - 服务器内部错误

原因：服务器在处理你的请求时出现了问题。

**解决方法：**

重试请求：稍后重试你的请求。

检查状态：查看 OpenAI 的服务状态，看是否有任何服务中断。

**错误示例代码8：**

503 - 服务过载

原因：服务器负载过高，无法处理你的请求。

**解决方法：**

重试请求：稍后重试你的请求。

检查状态：查看 OpenAI 的服务状态，看是否有任何服务中断。


# **2\. 模型选择错误**

**错误类型1：模型不可用**

**解决方法：**

确保你选择的模型在你的OpenAI账户中有权限使用。Make模块仅显示你账户中可用的模型。如果你尝试使用未列出的模型，可能会导致请求失败。

**错误类型2：未选择正确的模型**

**解决方法：**

在创建完成请求时，选择或映射你希望使用的模型。请确保你已根据任务需求选择适合的模型，例如GPT-3.5、GPT-4或GPT-4o mini。


# **3\. 请求参数错误**

**错误类型1：参数名称或类型错误**

**解决方法：**

对照OpenAI文档，确保所有请求参数的名称和类型正确无误。特别是在复杂请求中，确保每个参数都符合API的要求。

**错误类型2：无效的最大令牌数或温度设置**

**解决方法：**

确保最大令牌数和温度参数设置在合理范围内。例如，最大令牌数的默认值是16，而温度值在0到1之间。过高或过低的设置可能导致模型行为异常。

**错误示例代码1：**

Error: 422 Unprocessable Entity

\{“error”:“invalid\_parameter”,“message”:“参数设置无效。”\}

**解决方案：**

1\. 检查并调整请求参数，使其符合API文档中的规定。

2\. 根据任务需求，合理设置最大令牌数和温度值，避免设置不当导致错误。

**错误示例代码2：**

\[400\] max\_tokens is too large: 5000. This model supports at most 4096 completion tokens, whereas you provided 5000.

**解决方案：**

1\. OpenAI的最大令牌数不正确，针对4o模型，现在的最大数量为4096（未来可能会变化）。请访问这个网站[https://platform.openai.com/docs/models/gpt-4o](<https://platform.openai.com/docs/models/gpt-4o，查询使用的模型对应的最大令牌数量，进行正确填写。>)，查询使用的模型对应的最大令牌数量，进行正确填写。

**错误示例代码3：**

The operation failed with an error. \[400\] 'messages' must contain the word 'json' in some form, to use 'response\_format' of type 'json\_object'.

**解决方案：**

这是由于提示词中提到了JSON格式输出，而高级选项设置中并未将输出格式调整为JSON，所有解决办法进入高级设置，将输出格式调整为JSON格式。


# **解决问题的通用步骤**

1\. 查看错误信息：仔细阅读错误信息，从中获取线索。

2\. 检查配置：检查OpenAI模块的配置，包括模型选择、请求参数和连接信息等。

3\. 对照API文档：确认请求的格式和参数是否符合OpenAI文档的要求。

4\. 调试：逐步调试你的流程，定位问题所在。

**注意**

• 细心：计算机代码是一个精细的工作，任何细小的错误都会导致问题，细心检查可解决大多数错误。

• 及时保存：在进行任何操作之前，建议先保存你的工作流，避免因工作流停滞或崩溃而导致的配置丢失。

希望翔宇工作流整理的错误排查方案能够帮助你在工作流中更好地解决Make.com OpenAI模块中常见的错误，顺利完成各种AI任务。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，[让您在**Make.com**](<http://让您在Make.com>)上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/ea24c02cb2ef721257b05eaaca42f046.png)