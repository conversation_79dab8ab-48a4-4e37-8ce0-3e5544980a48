---
title: "Make7.Kimi API实操办公自动化：Make工作流自动总结PDF文档"
created_at: "2024-08-24T03:38:15.000000Z"
uuid: "8b2da730-ca2f-4012-b0f1-0f9f191a4b74"
tags: ['资源包']
---

# Make7.Kimi API实操办公自动化：Make工作流自动总结PDF文档

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/7-kimi-api-make-pdf-automation/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载](<https://pan.quark.cn/s/9c3bf77bffce>)

**3\. Notion 数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[数据库下载](<https://xiaoyuzaici.notion.site/c857f46415394d6e99de0dc7134491c9?v=0221209cb6344467a6675a7a163ed39d&pvs=4>)

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！


# **视频介绍**


### 主要内容

本视频教程由翔宇工作流博主小宇讲解如何利用 Kimi API 在 make 中构建一套自动分析 PDF 文档的工作流，并通过一个具体的案例——分析特斯拉2023年研报，演示了该工作流的构建和运行过程。


### 核心观点

  1. **Kimi API 助力 PDF 文档自动化分析** : 利用 Kimi API 的文件接口功能，可以将 PDF 文档转化成文本格式，并输送到 Kimi 的规划功能模块中，借助其强大的长文本处理能力进行分析。

  2. **自动化解决重复性工作，提升效率** : 该工作流主要面向需要分析 PDF 文档的办公人员和金融从业者，通过自动化提高工作效率，解决日常 PDF 文档的重复性工作流程。

  3. “那么自动化解决的问题是我们日常工作之中重复的，而且有流程的这些工作... ...主要解决的是效率的问题，而不是有无的问题。”

  4. **Kimi API 使用门槛低，中文处理能力强** : Kimi API 获取方便，注册登录即可获得赠金，且充值简单。同时，Kimi 拥有强大的中文文本和长文本处理能力，可替代 OpenAI API 实现文档撰写、内容创作等功能。

  5. “Kimi是月之暗面所推出的一个文本的生成模型，主要是用于理解自然语言和书面语言，来帮助我们完成各种文本的这种对话，包括输出内容，生成摘要对话等创意写作... ... 它也是咱们现在国内比较出圈的一个大语言模型，它的长文本能力都非常强”

  6. **Make 中构建 Kimi API 工作流** : 视频详细介绍了在 make.com 中如何利用 Kimi API 实现文件分析功能，包括利用 HTTP 模块下载 PDF 文档、调用 Kimi API 进行文本抽取、处理文本格式、与 Kimi 进行对话生成核心观点、并将结果更新到 Notion 数据库等步骤。

  7. **Kimi API 文件接口功能强大** : Kimi API 的文件接口功能强大，可以处理 10 万字以上的文本，未来还将开放 200 万字的长文本处理能力，可用于拆书、长文本分析等场景。

  8. "Kimi 他在我们这个文档最多上传的一个这个文档数量是100个... ...我会把这些小的模块都给大家告诉大家，就会节省大家的时间... ...实际上是一个 DELETE 的功能"


### 注意事项

  1. **Notion 文件大小限制** : Notion 免费版上传文件大小限制为 5MB，可通过使用谷歌云盘等方式解决。

  2. **文本格式处理** : 使用 Text Parser 模块处理文本格式，去除空格、换行符、括号等符号，避免影响 JSON 数据结构。

  3. **HTTP 模块状态码筛选** : 添加状态码筛选条件，仅在状态码为 200 时运行后续模块，节省操作数。

  4. **超时时间设置** : 针对长文本处理，设置模块超时时间，避免程序报错。

  5. **Kimi API 模型选择** : 根据文本长度选择合适的 Kimi API 模型，例如 8K、32K 或 128K。


### 总结

本视频教程详细介绍了如何利用 Kimi API 和 make.com 构建自动化工作流，实现对 PDF 文档的自动分析和总结，并提供了详细的操作步骤和注意事项。该工作流可以有效提高工作效率，解放生产力，具有很强的实用价值。