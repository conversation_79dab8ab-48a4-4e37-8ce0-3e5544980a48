---
title: "Make中文教程：Make 中错误处理的概述（Overview of error handling in Make）"
created_at: "2024-08-30T02:42:23.000000Z"
uuid: "3b737e14-af82-4055-aaea-1f78de767282"
tags: ['Make基础教程']
---

# Make中文教程：Make 中错误处理的概述（Overview of error handling in Make）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Make错误处理概述的教程。

  1. **教程简介** ：本教程讲述了如何在Make.com中处理错误，包括使用不同类型的错误处理器和设置错误处理路由，保障场景继续正常运行。

  2. **学习目标** ：理解并应用各种错误处理器，设置错误处理路由，确保Make.com场景在发生错误时仍能可靠运行，提升用户的错误处理能力。

  3. **学习内容** ：学习如何识别和解决场景中的错误、错误处理器的类型及应用、错误处理路由的设置、场景设置对错误处理的影响以及如何启用不完整执行。

  4. **概念理解** ：错误处理器是用于处理场景中的错误的模块，可分为忽略、恢复、提交、回滚和中断五种类型。错误处理路由则是在错误处理器末端连接的路径，处理场景中的意外事件。

  5. **任务练习** ：创建一个包含多个模块的场景，故意制造一个错误，设置不同类型的错误处理器来处理该错误，并观察场景继续运行的结果。


# Make 中错误处理的概述

在创建场景时,您可能会遇到意外数据或意外情况。Make 会通过错误通知您这些事件,以保持您的场景可靠和正常运行。

您可以使用错误处理程序来处理场景中的错误或意外事件。错误处理程序连接到具有错误处理路由的模块。当模块输出错误时,错误处理路由激活并运行错误处理程序。

如果发生由错误处理程序处理的错误,Make 会继续调度您的方案,因为 Make 可以假定您已预料到该情况并做好了准备。

在 Make 中有 5 个错误处理程序:

  -  忽略

  -  简历

  -  提交

  -  回滚

  -  休息


## **  错误处理路由**

错误处理程序始终位于错误处理路径的末端。错误处理路径具有透明填充。

![](https://static.xiaobot.net/file/2024-11-28/720345/6945f3f5a50cfa162c636f22eae6171e.png)

当错误处理程序激活时，它不会消耗操作。Make 不会因为处理您方案中的意外事件而向您收费。

错误处理路由不一定需要错误处理程序。例如，在错误处理路由中，可以只有一个 Slack > 创建消息模块来在发生错误时向您发送 Slack 通知。

如果在错误处理路由中没有模块输出错误,Make 会忽略该错误。这意味着这两个错误处理路由工作原理相同:

![](https://static.xiaobot.net/file/2024-11-28/720345/1903da37af85d4cb30b3fb425ebd018b.png)![](https://static.xiaobot.net/file/2024-11-28/720345/b05d5c94f02303f7408052fde8774539.png)

如果错误处理路径中的模块输出错误,该场景运行将以错误结束。


## **  如何识别错误**

当您在场景编辑器中构建或检查您的场景时,Make 会在模块名称前面和捆绑包列表中用警告标志突出显示导致错误的模块。

当您点击带有警告标志的气泡时,您可以检查导致模块中错误的包

![](https://static.xiaobot.net/file/2024-11-28/720345/75f9dd9860f4a59f68d078bfb24409f2.png)![](https://static.xiaobot.net/file/2024-11-28/720345/cf1a16640d5fc2afbdda69ff959de6f4.png)

  1. 显示错误类型和错误消息。

  2. 红色的字段是快速操作按钮。您可以点击这些按钮将"忽略错误处理程序"连接到模块。

第一个按钮将忽略错误处理程序与模块连接起来,以忽略模块输出的所有错误。

第二个按钮插入忽略错误处理程序及过滤器。过滤器仅允许与当前错误类型匹配的错误通过。在上面的示例中，Make 仅忽略 DataError 。

  3. 紫色的操作按钮会在弹窗中打开"帮助中心"。


## **如何处理错误**

在 Make 中处理错误有多种选择。为确定正确的错误处理策略,您应该考虑以下因素:

  - 场景处理的数据有多重要?

对于非常重要的场景,Make 可以在不完整的执行中存储部分场景运行。您可以手动或自动解决不完整的场景运行。

  - 模块输出什么类型的错误以及出现的频率？

如果错误发生很少并且是临时性错误,像 RateLimitError 一样,您可以依赖于默认场景暂停或后退。但是如果错误是关键的,像 InvalidAccessTokenError 或 InconsistencyError 一样,您应该设置错误处理。

  - 错误的影响是什么?

如果错误对您的数据或流程没有影响,您可以使用忽略错误处理程序忽略该错误。如果错误对您的流程有较大影响,您应考虑在场景设置中启用不完整的执行。

关于具体的错误处理策略,您可以查看专门文章的列表。


## **影响错误处理的场景设置**

情境设置在错误处理中扮演关键角色。以下列表重点关注一些情境设置如何影响错误处理。有关情境设置的更多信息,请查看相关文章。


### **允许储存未完成的执行**

启用此选项可在场景中的某个模块输出错误时保存未完成的场景运行。

在启用不完整执行的情况下，Make 会将错误发生时的场景状态存储为不完整执行。然后您可以检查场景运行、调查错误发生的原因并修复它以成功完成场景运行。此外，所有场景错误都会变成警告。

  - 你随时都可以解决不完整的场景运行。

  - 未完成执行存储的最大大小。最大大小取决于您组织的定价方案。未完成执行存储是您组织内所有场景共享的。

  - 当你在你的场景中使用 Break 错误处理程序时,你必须启用不完整的执行。

  - 在这些条件下,Make 不会存储未完成的方案运行

    - 当错误发生在场景中的第一个模块时。

但是，您可以将 Break 错误处理程序添加到场景中的第一个模块。使用 Break 错误处理程序,即使场景中的第一个模块输出错误,Make 也会存储不完整的执行。

    - 当您的未完成执行存储已满时。如果您的未完成执行存储已满,请检查启用数据丢失设置:

      - 如果数据丢失被禁用,那么已禁用该场景。

      - 如果数据丢失已启用,Make 会继续运行方案,并在无法存储在您的帐户中时丢弃未完成的执行。

您可以在专门的文章中阅读更多关于未完成执行的信息。


### **  顺序处理**

启用此选项以推迟运行该场景,直到上一次运行完成并解决所有未完成的场景执行。顺序处理确保:

  - 场景运行以它们被触发的相同顺序完成。

  - 同时只能运行一个方案执行。

顺序处理对于以瞬时触发器（Webhook）开始的方案或具有已启用的不完整执行的方案具有最大影响。

触发瞬间即启动的情景会默认并行运行。例如:

如果您有一个场景,它从一个运行 5 分钟的 Webhook 开始,您在 12:00 收到一个 Webhook 包裹,再在 12:03 收到另一个,那么从 12:03 到 12:05 期间,这个场景将有两个实例同时并行运行。

此外,如果从 12:00 开始的场景实例运行时间长于平常,例如一直到 12:12,那么从 12:03 开始的场景实例会提前结束\(在 12:08\),尽管它是后开始的。

如果您想确保场景在前一次运行完成之前不会启动,请启用顺序处理。

有关更多信息,请查看 webhooks。

![](https://static.xiaobot.net/file/2024-11-28/720345/482bd15542c6a228131615c6221e7abf.png)

您可以在场景设置中读取更多信息。


### **  启用数据丢失**

启用数据丢失场景设置会影响场景未完成执行的存储。启用此选项可以继续运行场景,而不受存储未完成执行的空间不足的影响。

如果您启用数据丢失,Make 会丢弃不符合大小限制的数据并按计划继续运行该场景。否则,Make 会禁用场景调度。

根据您的组织计划,Make 设置了大小限制。查看 Make 定价明细或了解更多关于方案设置的信息。


### **连续错误的次数**

此设置允许您设置场景连续出错的次数上限,超过该上限后,Make 仍会为其安排后续运行。当场景连续出错的次数达到指定的次数上限时,Make 将禁用该场景。

要访问连续错误次数的设置,请在场景设置中切换高级设置开关。默认的连续错误次数为 3 次。

连续错误的数量不适用:

  - 当触发场景时以即时触发器\(webhook\)触发。如果发生错误,Make 会立即停用立即触发的场景。

  - 当出现以下类型的错误时:

    - AccountValidationError

    - OperationsLimitExceededError

    - DataSizeLimitExceededError

错误发生后,Make 会立即禁用场景调度。

  - 出现以下类型错误时，在方案的第一个模块中发生:

    - DataError

    - MaxFileSizeLimitExceededError

错误发生后,Make 会立即禁用场景调度。

  - 当您收到警告时。如果某个场景以警告结束,make 将继续安排后续场景运行。


### **  自动提交**

启用此选项以在更改发生后立即提交更改。例如,当用户触发更新其详细信息的方案时。

如果禁用此选项,Make 将在所有模块都成功完成后提交更改。

设置只会影响支持事务的模块。支持事务的模块带有"ACID"标签。它们大部分时间使用数据库应用程序,例如数据存储或 MySQL 应用程序。

不支持事务的模块会立即进行更改,并且不提供回滚功能。

你可以在场景设置文章中阅读更多关于自动提交选项的信息。


### **  提交触发器最后**

启用此选项来最后提交第一个模块在场景中所做的更改。否则，Make 会按照发生的顺序提交更改。

设置只会影响支持事务的模块。支持事务的模块带有"ACID"标签。它们大部分时间使用数据库应用程序,例如数据存储或 MySQL 应用程序。

不支持事务的模块会立即进行更改,并且不提供回滚功能。

您可以在场景设置文章中了解更多关于 commit 触发器的最后选项的信息。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/9ba4c2a677c3bc0a3969621b15ce2b7b.png)