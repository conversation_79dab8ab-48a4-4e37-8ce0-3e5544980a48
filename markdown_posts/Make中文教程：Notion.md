---
title: "Make中文教程：Notion"
created_at: "2024-08-27T13:56:15.000000Z"
uuid: "59e0ee29-fbfb-4547-9c69-8d49b2e8cf95"
tags: ['Make基础教程']
---

# Make中文教程：Notion

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Notion基础的教程。

  1. **教程简介** ：该教程详细介绍了在Make.com中使用Notion模块进行数据库与页面管理的基本操作，包括如何连接Notion账户、建立连接以及构建与管理Notion情景的具体步骤和注意事项。

  2. **学习目标** ：帮助用户掌握如何在Make.com中使用Notion模块，完成数据库与页面的连接、创建、监控和更新等操作，提高工作效率和任务自动化的能力。

  3. **学习内容** ：该教程包括Notion账户连接、内部和公共连接方法、获取客户端凭据、数据库和页面管理、API调用以及用户列表，具体步骤和注意事项等知识点。

  4. **概念理解** ：

     - **Notion Internal连接** ：内部连接，通过生成集成令牌来与Make.com建立安全连接。

     - **Notion Public连接** ：公开连接，通过OAuth认证与Make.com连接。

     - **API调用** ：通过API接口与Notion进行通讯和数据操作。

  5. **任务练习** ：

     1. 在Make.com中创建一个情景，并添加Notion模块。

     2. 使用教程提供的步骤，选择Notion Internal连接并完成连接配置。

     3. 创建一个新的Notion数据库项目，并在页面中根据教程步骤更新该项目内容。


# **Notion**

使用 **Make** 中的 Notion 模块，您可以监控、检索、创建、追加和更新数据库、数据库项目、页面、页面内容，还可以搜索对象、列出用户并进行 API 调用。

要使用 Notion 模块，您需要拥有一个 Notion 账户。您的 Notion 账户必须具有管理员权限才能在 **Make** 中建立连接。您可以在 notion.so 创建一个 Notion 账户。

有关可用端点的列表，请参阅 Notion API 文档。


## **连接 Notion 到 Make**

**Make** 提供了两种方法来连接 Notion：

  - Notion Internal 连接

  - Notion Public 连接


## 推荐选择Public连接方式，简单方便快捷：


### **建立 Notion** Public**连接**

![](https://static.xiaobot.net/file/2024-08-31/720345/cede7920433042dd37f77297d83c3dcc.jpeg)

来到Make.com新场景创建界面，选择点击加号，输入Notion，点击其中一个动作添加模块。

![](https://static.xiaobot.net/file/2024-08-31/720345/22e440124d56b402491b9652b0f4aa71.jpeg)

点击创建一个链接

![](https://static.xiaobot.net/file/2024-08-31/720345/502d05ba148f76e1ad26005aed744f07.jpeg)

选择Public分类。

![](https://static.xiaobot.net/file/2024-08-31/720345/0f4bb7ff60dc4c63cf772fcdc9af582c.jpeg)

点击保存开始过程，可以修改链接名称用于分辨多个账号

![](https://static.xiaobot.net/file/2024-08-31/720345/3f2e770be87740a8b5cb7d21ebd1ee18.jpeg)

选择你的Notion账户类型进行登陆。

![](https://static.xiaobot.net/file/2024-08-31/720345/57d2efdd9ccd3eaf4afc78c769140dbf.jpeg)

选择账号登陆。

![](https://static.xiaobot.net/file/2024-08-31/720345/b9cca62e84dccdd78756f7ddac80d2b6.jpeg)

勾选所有页面以便make方便各个页面的访问。

![](https://static.xiaobot.net/file/2024-08-31/720345/44d3462ad496023c42c2ef5dc487c980.jpeg)

等待链接成功后，显示如图，则代表链接成功，如需添加新账号可以点击Add进行链接。


### **将数据库添加到 Make 中可见**

对于 Notion i连接，默认情况下，Notion 数据库不会出现在 **Database ID** 字段的 **Search** 选项中。您必须手动将它们从 Notion 账户添加到 **Make** 应用中。

![](https://static.xiaobot.net/file/2024-11-06/720345/ce3e0d2e07e1faa9a5847550a2498353.png)

要将数据库从您的 Notion 账户添加到 **Make** 应用：

  1. 登录到您的 Notion 账户。

  2. 进入您要添加到 **Make** 的数据库，点击右上角的 **...** ，点击 **Add connections** ，搜索并点击之前创建的集成，然后点击 **Confirm** 。

![](https://static.xiaobot.net/file/2024-11-06/720345/59f6c1087378e5ff45608b0e4471c27b.png)

数据库成功共享，现在您可以在模块的 **Database ID > Search** 字段中看到它。

![](https://static.xiaobot.net/file/2024-11-06/720345/88b501a84bb938394e356c09e4d40919.png)


### **建立 Notion Internal 连接**

要建立 Notion internal 连接，请按照以下步骤进行：

  1. 登录到您的 Notion 账户。

  2. 点击 **设置与成员** > **我的连接** > **开发或管理集成** 或者访问 我的集成页面。

![](https://static.xiaobot.net/file/2024-08-29/720345/29ef54fac4a5ff7bb0d4303af9bbe884.jpeg)
  3. 点击 **New Integration** 。

![](https://static.xiaobot.net/file/2024-08-29/720345/cf587b5b397be83de75759f2e56bc632.jpeg)
  4. 填写集成名称，选择工作空间，选择internal，然后点击**保存** 。

![](https://static.xiaobot.net/file/2024-08-29/720345/ca1a9e69366d9b167afa4229c3d5efc7.jpeg)

  5. 前往 **Capabilities** ，如果需要评论功能可全部选择对号，我这里保持了默认，然后点击显示，复制密钥。

![](https://static.xiaobot.net/file/2024-08-29/720345/41ce2ea38564972c11162309180111f1.jpeg)
  6. 登录到您的 **Make** 账户，并将一个 Notion 模块添加到您的 **scenario** ，然后点击 **add** 。

![](https://static.xiaobot.net/file/2024-08-29/720345/dec763f08475d90c8ab28e3498a46b60.jpeg)
  7. 在 **Connection type** 下拉列表中选择 **Notion Internal，修改容易区分的名称，粘贴密钥，点击保存** 。

![](https://static.xiaobot.net/file/2024-08-29/720345/5744126a2ba6fd20ad123635772701fc.jpeg)

您已成功建立连接。现在，您可以编辑您的 **scenario** 并添加更多的 Notion 模块。


## **构建 Notion 模块支持的动作：**

连接应用后，您可以执行以下操作：


### **数据库项目**

  - 监控数据库项目

  - 获取数据库项目

  - 创建数据库项目

  - 追加数据库项目内容

  - 更新数据库项目


### **数据库/页面**

  - 监控数据库/页面

  - 搜索对象

  - 获取数据库

  - 获取页面

  - 创建数据库

  - 创建页面

  - 更新数据库

  - 更新页面


### **页面内容**

  - 监控页面内容

  - 列出页面内容

  - 列出页面属性项

  - 获取页面内容

  - 追加页面内容

  - 更新页面内容

  - 删除页面内容


### **其他**

  - 发起 API 调用

  - 列出用户


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-08-27/720345/9707cb94e172f4befd6522954d17e719.png)