---
title: "英文提示词示例合集"
created_at: "2024-10-16T14:41:32.000000Z"
uuid: "1cec4f93-eec2-4afe-a861-7c89921d809e"
tags: ['AI教程与资源']
---

# 英文提示词示例合集

**标签**: AI教程与资源

在人工智能时代，提示词工程已经成为了充分挖掘 AI 工具潜力的关键环节。无论是使用 ChatGPT、MidJourney 还是 Google 的 Gemini 模型，一个精心设计的 Prompt 提示词库可以帮助用户节省时间、提供创作灵感，并为任务提供结构化的指引。

**推荐的英文 Prompt 提示词库**

**1\. Google AI Studio Prompt Gallery**

<https://aistudio.google.com/app/gallery>

![](https://static.xiaobot.net/file/2024-10-16/720345/16879e3599ec492e3bb9f62ba246f390.png)

Google AI Studio 提供了涵盖各种应用场景的 Prompt，特别适合开发者、市场营销人员和企业主。用户可以在同一平台上使用 Google 的 Gemini 模型，并应用这些 Prompt 快速生成内容或数据分析报告，极大提高了工作效率。

**2\. Hero Page**

<https://hero.page/discover>

![](https://static.xiaobot.net/file/2024-10-16/720345/867d3d30c2d46bf882dc879d9489baf0.png)

Hero Page 是一个专为内容创作者、设计师和营销人员设计的 Prompt 平台。它允许用户自定义 Prompt，不论是用于文本生成还是图像创作，Hero Page 都能提供合适的工具，帮助用户快速提升创作效率。

**3\. Snack Prompt**

<https://snackprompt.com/prompts>

![](https://static.xiaobot.net/file/2024-10-16/720345/37f4904f870cf6fca4fa6ab534002dfe.png)

作为一个社区驱动的 Prompt 库，Snack Prompt 提供了大量经过社区评分和验证的 Prompt，保证了其质量。无论是免费还是付费用户，都能轻松找到适合自己的 Prompt，让内容创作变得更加高效。

**4\. OpenAI Platform Documentation**

<https://platform.openai.com/docs/examples>

![](https://static.xiaobot.net/file/2024-10-16/720345/4f9ac387637359274a30ad8cca50655f.png)

对开发者而言，OpenAI 提供的系统级 Prompt 是构建自定义 GPT 应用的基础。通过这些 Prompt，开发者可以创建聊天机器人、自动化工具等应用，并深入理解 GPT 模型的工作机制。

**5\. Anthropic’s Prompt Library**

<https://docs.anthropic.com/en/prompt-library/cosmic-keystrokes>

![](https://static.xiaobot.net/file/2024-10-16/720345/b4e59a09ed485d8901fba366946658b7.png)

针对 Claude 用户，Anthropic 提供了一个全面的 Prompt 库，支持双系统 Prompt 功能，帮助用户更精细地调整输出结果，是构建高级聊天机器人和内容生成器的理想选择。

**6\. PromptHero**

<https://prompthero.com/chatgpt-prompts>

![](https://static.xiaobot.net/file/2024-10-16/720345/0b9f8899d41040360ac8d5b1117b4e71.png)

PromptHero 专注于视觉创作，允许用户根据图像类型进行 Prompt 搜索，并预览生成效果，是数字艺术创作和社交媒体内容制作的得力工具。

**7\. GitHub ChatGPT Prompts**

<https://github.com/pacholoamit/chatgpt-prompts>

![](https://static.xiaobot.net/file/2024-10-16/720345/aad3dde27990fbc8eeea70f9f89e03e8.png)

GitHub 上的 ChatGPT Prompt 仓库提供了大量角色化的 Prompt，非常适合用户自行实验和学习。这个开源项目为创建角色化应用提供了丰富的灵感。

**8\. Google Cloud Vertex AI Prompt Gallery**

[https://console.cloud.google.com/vertex-ai/studio/prompt-gallery?project=new-slate-websit-1553874306995&pli;=1](<https://console.cloud.google.com/vertex-ai/studio/prompt-gallery?project=new-slate-websit-1553874306995&pli=1>)

![](https://static.xiaobot.net/file/2024-10-16/720345/5d868a30342ee1481e72e4bac9a1e684.png)

Google Cloud 的 Vertex AI Prompt Gallery 面向专业开发者，适用于复杂的数据分析和内容生成任务。尽管上手门槛较高，但一旦掌握，便能带来极大的灵活性和控制力。

**8\. Meta AI Prompting**

<https://www.llama.com/docs/how-to-guides/prompting/>

![](https://static.xiaobot.net/file/2024-10-19/720345/8b75f1def8ac34575e1b7b709bf955b8.png)

Llama 的提示指南介绍了如何通过提示设计提升语言模型的表现，强调撰写清晰简洁的提示和使用示例引导模型。

**8\. Cohere Crafting Effective Prompts**

<https://docs.cohere.com/v2/docs/crafting-effective-prompts>

![](https://static.xiaobot.net/file/2024-10-19/720345/af5eb39921ecc108832007da4ce5281e.png)

Cohere 的提示设计指南提供了优化语言模型输出的策略，强调提示的清晰性、简洁性和具体性。

**9\. Mistral Prompting Capabilities**

<https://docs.mistral.ai/guides/prompting_capabilities/>

![](https://static.xiaobot.net/file/2024-10-19/720345/4ac50ea8f683544ac96c895438d35d90.png)

Mistral AI 的提示能力指南介绍了模型的四大功能：分类、摘要、个性化和评估。它提供了有效提示设计的技巧，如少样本学习和分步骤指令，帮助用户更好地应用这些模型。

**总结**

不同的 Prompt 提示词库各具特色，从 Google AI Studio 提供的深度集成解决方案，到 PromptHero 针对视觉创作的工具，再到 Snack Prompt 的社区驱动模式，这些资源为不同领域的用户提供了丰富的选择。

![](https://static.xiaobot.net/file/2024-10-16/720345/62b9e20523855de9dad1f173a2703209.png)