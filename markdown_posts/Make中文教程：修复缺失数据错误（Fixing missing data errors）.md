---
title: "Make中文教程：修复缺失数据错误（Fixing missing data errors）"
created_at: "2024-11-28T07:22:09.000000Z"
uuid: "8dfad570-fa10-4d03-968c-91598ff48df9"
tags: ['Make基础教程']
---

# Make中文教程：修复缺失数据错误（Fixing missing data errors）

**标签**: Make基础教程

当输入的包缺少所需的数据时,应用程序模块会输出 BundleValidationError 。例如,如果您从未返回任何结果的搜索模块映射数据。

当您的场景中的模块输出 BundleValidationError 时,您应该检查错误的原因。如果您可以避免错误,以使您的自动化更加稳健,则应该考虑重新设计您的场景。您可以使用 if 或 ifempty 函数,或使用过滤器。

否则,你将不得不处理错误以保持您的场景处于活动状态。

在以下示例中，我们将考虑两种情况:

  1. 忽视丢失的数据或使用占位符。

缺失的数据并不是问题。你只需保持你的方案处于启用状态即可。

  2. 获取错误通知并存储场景运行情况。

缺失的数据是不可接受的。如果你得到了 BundleValidationError ，你想要详细检查发生了什么。

要了解如何在 Make 中处理错误,您可以查看错误处理概览。


## **忽略缺失数据或使用占位符**

您可能会使用包含缺失值的数据。您无需修复这些缺失值或查明其原因。您的主要目标是避免破坏您的场景。

我们将使用以下场景进行测试:

![](https://static.xiaobot.net/file/2024-11-28/720345/17bda14ee67fa1be8e5682f7ea8aa818.png)

该场景读取数据、修改它们并将它们用于数据存储中的搜索。某一个数据为空,这导致了搜索模块中的 BundleValidationError 。

![](https://static.xiaobot.net/file/2024-11-28/720345/0273b94b71156f27ce87ff1468fad9b3.png)

为了即使在错误发生时也能继续运行您的场景,您可以使用忽略或恢复错误处理程序。

  1. 右击导致错误的模块。

  2. 选择添加错误处理程序。

  3. 出现一个弹窗。从错误处理程序列表中,选择忽略错误处理程序。

可选：选择恢复错误处理程序。将占位符映射到缺少的包中。

您可以使用占位符标记问题数据。例如,如果用户缺少电子邮件,您可以用标签 noemail 代替缺失的电子邮件地址,并稍后检查。

最终情景应该是这样的:

![](https://static.xiaobot.net/file/2024-11-28/720345/debc9418c05c2fe49b430e54f1b4d93f.png)


## **获取错误通知并存储场景运行**

当发生错误时,您可能需要仔细检查某些场景。您可以通过自定义错误处理设置来实现。如果您的场景中发生错误,您将收到一封包含错误描述和场景链接的电子邮件。此外,您还会将错误包存储为未完成的执行,以手动解决。

我们将使用以下场景进行测试:

![](https://static.xiaobot.net/file/2024-11-28/720345/ace879696f719c48e16a306c4429cca9.png)

该场景读取数据、修改它们并将它们用于数据存储中的搜索。某一个数据为空,这导致了搜索模块中的 BundleValidationError 。

![](https://static.xiaobot.net/file/2024-11-28/720345/cf65961d73969e89aef5e5bfc796c842.png)

要存储未完成的场景运行并在发生错误时收到自定义电子邮件通知,请按照以下步骤操作:

  1. 右击导致错误的模块。

  2. 选择添加错误处理程序。

  3. 弹出一个窗口。从错误处理程序列表中,选择中断错误处理程序。

  4. 弹出一个对话框,显示 Break 错误处理程序的设置。将自动完成设置为否,以手动解决错误。用确定按钮确认设置。要了解更多关于 Break 错误处理程序设置的信息,请查看相关文章。

  5. 单击场景设置图标并启用存储未完成的执行。

![](https://static.xiaobot.net/file/2024-11-28/720345/3fdbd408ce2ea7b9e9cec14f441a3263.png)
  6. 右键单击出错模块和中断错误处理程序之间的连接。选择添加模块。Make 在出错模块和中断错误处理程序之间插入一个灰色的模块占位符。

  7. 点击灰色模块占位符并搜索 Gmail 应用程序。将"发送电子邮件"模块插入模块占位符。

  8. 设置发送电子邮件模块。在内容字段中添加您希望在发生错误时收到的通知消息。使用创建系统变量获取元数据。例如:

![](https://static.xiaobot.net/file/2024-11-28/720345/5144adeba471209220bce5f8c55d6c4a.png)

你在你的场景中设置了错误处理,以创建不完整的执行并在发生错误时通过电子邮件通知你。

包含错误处理的示例场景如下:

![](https://static.xiaobot.net/file/2024-11-28/720345/bdf2665bfdd930ea8f9edbce3e682531.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/1675b15c88a4e5161bcd2b6c52a5e238.png)