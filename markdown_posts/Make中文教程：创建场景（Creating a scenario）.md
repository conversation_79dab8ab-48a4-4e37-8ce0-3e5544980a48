---
title: "Make中文教程：创建场景（Creating a scenario）"
created_at: "2024-08-27T10:09:36.000000Z"
uuid: "63e41ddd-e41b-43e0-9e6e-bf2a31c729d3"
tags: ['Make基础教程']
---

# Make中文教程：创建场景（Creating a scenario）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于创建场景的教程。

  1. **教程简介** ：本教程详细介绍了如何在Make.com中创建和配置一个Scenario（场景），包括应用程序选择、Scenario构建、测试及激活。例子展示了如何将收件箱的新邮件自动添加到Google表格中。

  2. **学习目标** ：学习如何在Make.com中创建和管理场景，掌握自动化任务的基本操作步骤，包括应用选择、模块配置、Scenario测试与激活。

  3. **学习内容** ：主要包括四个步骤：选择应用程序、构建Scenario、测试Scenario以及激活Scenario。此外，示例详细分解了Email和Google Sheets模块的添加与配置过程。

  4. **概念理解** ：Scenario是一系列模块的组合，用于自动化不同应用间的数据传输和处理。激活与安排是使Scenario按计划自动运行的关键步骤。

  5. **任务练习** ：尝试创建一个新的Scenario，实现将收件箱中的新邮件自动添加至Google表格，确保正确配置Email和Google Sheets模块，并测试Scenario的正常运行。

本教程翻译自Make.com官方教程，可点击如下链接访问：<https://www.make.com/en/help/scenarios/creating-a-scenario>


## **什么是 Make 自动化？**

想象一下，Make就像是一个聪明的助手，可以帮你完成各种重复性的工作。就像搭积木一样，你可以把不同的应用程序连接起来，让它们自动协同工作。


## **核心概念：场景（Scenario）**

场景就像是你写给助手的一份"工作指南"，告诉它该做什么、怎么做。每个场景都是由一系列模块（modules）组成的工作流程。


### **创建场景的四个简单步骤：**

  1. 选择需要的应用程序（比如 Email、Google表格等）

  2. 设计你的工作流程

  3. 进行测试，确保一切正常

  4. 启动自动化流程


## **实战案例：邮件自动记录器**

让我们通过一个简单的例子来学习：**自动将新收到的邮件记录到 Google 表格中** 。

这就像是给自己配了一个永不疲倦的秘书，可以24小时监控收件箱，并把新邮件的信息整整齐齐地记录在表格里。


### **第一步：选择应用程序**

  1. 登录 Make 账户，点击 "Create a new scenario"（创建新场景）按钮

![](https://static.xiaobot.net/file/2024-11-19/720345/549f562889d1bd6315e158728bb176ea.png)![](https://static.xiaobot.net/file/2024-11-19/720345/91799c33908e2f7d2bb843ccdef72795.png)

  2. 点击加号，在搜索框中输入 "Email"

![](https://static.xiaobot.net/file/2024-11-19/720345/ef3f4c3d04097ffb0da3ffbe17735b73.png)


### **第二步：搭建工作流程**

**A. 设置邮件监控**

  1. 添加 "Email > Watch email"（监控邮件）模块

  2. 连接你的邮箱账户

  3. 选择要监控的文件夹（比如收件箱）

  4. 设置监控条件（可以指定特定发件人或关键词）

![](https://static.xiaobot.net/file/2024-11-19/720345/6c833ad8ad5c611f770b63733d22257e.png)

  5. 选择开始监控的时间点

![](https://static.xiaobot.net/file/2024-11-19/720345/9106e7761408b69092e34cd4d79141f0.png)

**B. 设置 Google 表格记录**

  1. 点击邮件模块右侧的加号，添加 "Google Sheets > Add a row"（添加行）模块

![](https://static.xiaobot.net/file/2024-11-19/720345/68666d81128664a5ffb03350f100d57c.png)

  2. 连接你的 Google 账户

  3. 选择目标表格和工作表

![](https://static.xiaobot.net/file/2024-11-19/720345/b1e18339ff89e2b089b0edc54ed40b29.png)

  4. 设置数据映射，决定邮件中的哪些信息要记录到表格的哪些列中

![](https://static.xiaobot.net/file/2024-11-19/720345/56d2b0d0f78cc3b292754b48301536f1.png)

提示：就像搭建一座桥梁，这些设置会把邮件信息精准地"搬运"到表格中的指定位置。


## **第三步：测试你的自动化流程**

就像试驾新车一样，在正式使用前，我们需要确保一切都能正常运转。


### **如何测试：**

  1. 点击 "Run once"（运行一次）按钮

![](https://static.xiaobot.net/file/2024-11-19/720345/16f60cd76aab34c77bac2f37de89c34e.png)

  2. 观察运行结果：

  - 每个模块上方会显示处理的数据信息（我们称之为"数据包"）

  - 点击数字可以查看详细的处理记录

![](https://static.xiaobot.net/file/2024-11-19/720345/bba3df179a312d6049115a8b2815aa93.png)

想象这个过程就像查看快递包裹的轨迹，你可以清楚地看到数据在各个环节的流转情况。


## **第四步：启动自动化**

确认一切正常后，就可以正式启动你的自动化助手了！


### **设置运行计划：**

  1. 点击时钟图标设置运行频率

![](https://static.xiaobot.net/file/2024-11-19/720345/948186d91cddf09287b6fc32d3d28db5.png)

  2. 选择合适的运行间隔

![](https://static.xiaobot.net/file/2024-11-19/720345/395160f037628ca91cd3dbf0d15bf298.png)

  3. 退出编辑器并激活场景

![](https://static.xiaobot.net/file/2024-11-19/720345/f22407042032e560ea1517284af279c9.png)


## **重要概念解析**


### **1\. 场景状态**

就像电灯的开关，场景可以处于：

  - 激活状态：正在工作

  - 停用状态：暂时休息


### **2\. 场景命名**

给你的自动化流程起个好记的名字：

![](https://static.xiaobot.net/file/2024-11-19/720345/897cc626d9a76e7573260ce9bc41a2df.png)


### **3\. 数据包设置**

  - 默认情况下，每个模块一次处理2个数据包

  - 可以根据需要调整处理数量


### **4\. 场景蓝图**

把你设计好的工作流程保存为"蓝图"，就像是把制作流程保存成菜谱一样：

  - 可以分享给他人使用

  - 包含所有模块设置和流程

  - 不包含个人账号信息（出于安全考虑）


## **小贴士**

  1. 刚开始时建议从简单的自动化任务开始

  2. 多利用测试功能，确保流程准确无误

  3. 定期检查自动化流程的运行情况

  4. 如果遇到问题，可以查看错误提示来排查原因


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-19/720345/d72b684b5e63f5c91ad20b783586bd39.png)