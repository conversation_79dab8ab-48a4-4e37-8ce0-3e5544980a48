---
title: "Make可调用免费API 1"
created_at: "2024-08-24T05:11:22.000000Z"
uuid: "19544559-034d-431a-8584-4a80c013a2e8"
tags: ['API']
---

# Make可调用免费API 1

**标签**: API

**前言**

Make.com的自动化流程通过API的集成可以实现更广泛的操作。API能够将不同的系统、应用和服务连接起来，使工作流具备跨平台的数据交互能力，从而打破单一平台的局限性，实现复杂的自动化操作。

在自媒体内容制作的过程中，借助自动化工作流可以大幅提升工作效率。风轻寒云API提供了丰富的接口资源，适用于开发者和内容创作者优化工作流。这些API可以通过Make.com的HTTP模块调用，满足特定领域的内容获取需求。翔宇工作流在这为大家介绍这些API的功能，并探讨如何在自媒体内容制作中有效利用这些工具。

风轻寒云API网址：

<https://api.liuzhuai.com/>

**特别感谢风轻寒云免费提供的服务** ，这些API在提升工作效率的过程中发挥了重要作用。然而，需要注意的是，第三方服务可能存在不稳定的情况，使用过程中需考虑这一因素，以确保内容创作的顺利进行。

**API分类与功能介绍**

风轻寒云API共提供26个接口，涵盖社交工具、信息查询、内容生成等多个领域。以下是这些API的功能介绍：

1\. **社交工具类API**

\- **QQ在线状态查询**

功能：查询指定QQ好友的在线状态，判断其是否通过电脑登录QQ。

\- **获取QQ昵称和头像**

功能：通过QQ号获取用户的昵称和头像，便于展示用户资料。

\- **获取QQ头像直链**

功能：获取QQ头像的直链地址，便于在不同平台展示或使用。

\- **QQ强制对话**

功能：强制弹出QQ对话窗口，兼容各种设备，适用于紧急通知或消息推送。

2\. **信息查询类API**

\- **网站TDK描述查询**

功能：查询指定网站的标题（Title）、关键词（Keywords）和描述（Description），有助于SEO优化。

\- **查询目标网站favicon.ico**

功能：获取指定网站的favicon.ico图标，用于网站分析或展示。

\- **域名备案查询**

功能：查询域名备案信息，确保网站运营合法合规。

\- **网站公安备案查询**

功能：查询网站的公安备案情况，保障网站安全性。

\- **IP地理位置查询**

功能：通过IP地址查询地理位置信息，适用于用户访客分析或区域限制。

\- **手机号归属地查询**

功能：查询手机号的归属地信息，便于用户数据统计与分析。

3\. **内容生成类API**

\- **网易云解析**

功能：在线解析网易云音乐链接，便于音乐的分享与播放。

\- **随机语录**

功能：每天随机生成一条语录，增添内容的趣味性。

\- **古风唯美诗词**

功能：随机输出古风唯美诗词，适合文艺内容创作。

\- **舔狗日记**

功能：随机生成一条舔狗日记，用于社交媒体或娱乐用途。

\- **在人间凑数日子**

功能：输出选自网友的经典日记内容，适用于感悟和分享。

\- **历史今天接口**

功能：获取历史上今天发生的重要事件或纪念日内容。

\- **60秒读懂世界**

功能：快速获取每日的黄历及历史上的重要事件，帮助用户了解每日资讯。

4\. **视觉与娱乐类API**

\- **运动刷步**

功能：支持支付宝、微信、QQ等平台的运动步数修改，满足个性化需求。

\- **QQ头像加密**

功能：对QQ头像进行加密处理，并生成分享链接，保护用户隐私。

\- **举牌大人**

功能：生成举牌图片，用于各种场合的庆祝或趣味展示。

\- **随机兽耳酱图片**

功能：随机生成带有兽耳的卡通角色图片，适合二次元文化爱好者。

\- **mc酱动漫**

功能：生成mc酱风格的动漫人物图片，用于创作或社交媒体展示。

\- **二次元动漫**

功能：生成二次元动漫图片，满足ACG文化爱好者的需求。

\- **美女横屏壁纸**

功能：提供横屏的美女壁纸，适用于手机或桌面壁纸设置。

\- **美女竖屏壁纸**

功能：提供竖屏的美女壁纸，适用于不同设备的个性化展示。

**实例应用：自动化生成小红书图文内容**

风轻寒云API的功能不仅丰富，还能与Make.com的自动化工作流无缝结合，特别是在自媒体内容创作方面提供了强大的支持。以下是如何在小红书的“每日励志”赛道或“古诗词”赛道中利用这些API进行内容自动化生成的示例：

1\. **获取内容**

\- 每日励志赛道：使用“随机语录”API，每天自动生成一条励志语录，作为内容的核心文本。

\- 古诗词赛道：使用“古风唯美诗词”API，自动获取一段古风诗词，丰富文艺内容的创作。

2\. **生成图片**

利用Midjourney或Flux等文生图API，根据获取的语录或诗词内容生成提示词，创建与文字相匹配的图片。这个步骤可以通过Make.com集成文生图API进行自动化处理，确保图片与内容风格一致。

3\. **图文组合**

将生成的文字内容与图片通过HTML转图片模块进行组合。Make.com可以将文字和图片组合成最终的图文形式，并生成统一的图像文件，方便后续的发布或存档。

**调用实例**

**随机语录API请求方式**

\- **Method** : GET

\- **请求地址** : <https://api.liuzhuai.com/ancient/api.php>

**Make.com中HTTP模块的使用方法**

1\. **创建HTTP请求**

在Make.com中，使用HTTP模块来调用随机语录API。

\- **Method** : GET

\- **URL** : <https://api.liuzhuai.com/ancient/api.php>

注意打开Parse response，以便解析返回数据。

2\. **处理响应**

配置HTTP模块的输出，以接收API返回的数据。你可以将返回的数据直接用于下一步的处理，如生成图片的提示词或将其嵌入到HTML模板中。

示例JSON蓝图文件下载：

<https://pan.quark.cn/s/304edfaef2e4>

通过这种方式，你可以在Make.com中轻松实现自动化内容生成，将风轻寒云API与自动化工作流结合，极大提高自媒体内容创作的效率。未来翔宇工作流也会发布相关的自动化工作流，以满足相关自媒体内容制作的自动化需求。