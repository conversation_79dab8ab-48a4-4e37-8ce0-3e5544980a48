---
title: "Make中文教程：Inoreader"
created_at: "2024-09-01T03:19:21.000000Z"
uuid: "01475e6f-f47f-46e1-a955-0f5c1268a2e0"
tags: ['Make基础教程']
---

# Make中文教程：Inoreader

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Inoreader 入门指南的教程。

**教程简介** ：本教程介绍如何使用Make.com中的Inoreader模块，包括监控、创建、更新、列出和删除Inoreader中的文章和RSS订阅内容，以及如何连接Inoreader账户。

**学习目标** ：通过本教程，用户将学习如何在Make.com中集成Inoreader，更有效地管理和监控Inoreader中的文章和订阅内容，提升效率和生产力。

**学习内容** ：

  1. 连接Inoreader账户到Make.com

  2. 监控、新建、列出和更新文章

  3. 创建和管理RSS订阅

  4. 使用Webhook触发器

  5. 执行API调用

**概念理解** ：

  - **情景（Scenario）** ：在Make.com中，一个情景是由一系列操作和自动化流程组成的工作流。

  - **模块** ：模块是特定功能的操作单元，比如监控文章或添加订阅。

  - **Webhook** ：Webhook是通过HTTP请求，在特定事件发生时触发操作的机制。

**任务练习** ：

  1. 创建一个情景，连接您的Inoreader账户并监控某个特定文件夹中的文章。

  2. 使用Inoreader模块添加一个新的RSS订阅，并为其设置特定标签。

  3. 设置一个Webhook触发器，当新文章通过规则过滤时自动执行特定操作。


# **Inoreader**

Inoreader 模块允许您观看、创建、更新、列出和删除您 Inoreader 账户中的文章和订阅。


## **开始使用 Inoreader**

 先决条件

  -  Inoreader 账户

要将 Inoreader 与 Make 一起使用,需要拥有 Inoreader 账户。您可以在 inoreader.com 上创建 Inoreader 账户。

![](https://static.xiaobot.net/file/2024-11-26/720345/2a5c38c4e911e60645a10681f6b2f8ab.png)


## **连接 Inoreader 制作**

1\. 转到"制作"并打开 Inoreader 模块的"创建连接"对话框。

![](https://static.xiaobot.net/file/2024-11-26/720345/a58e9fb234d4febd266d2e6c880d0f7b.gif)

2.输入您的 Inoreader 帐户凭据,并单击"授权"按钮确认访问。

![](https://static.xiaobot.net/file/2024-11-26/720345/1f0fa514c88a0a0104b15bb20b6030bc.png)

连接已建立。


## **  文章**


### **  手表文章**

当发布指定类型的文章时触发。

所需权限：读取

![](https://static.xiaobot.net/file/2024-11-26/720345/8f4e0908a96781e83702668c3644dd41.png)


### **  文章列表**

返回一个文章列表。

所需权限：读取

![](https://static.xiaobot.net/file/2024-11-26/720345/f9905179007a91f1e9ebb8e2d52b5fb2.png)


### **  分配标签**

为一篇或多篇文章分配标签。

所需权限：读取、写入

![](https://static.xiaobot.net/file/2024-11-26/720345/7357e521e530b048653b0ff6eb48be1a.png)


### **  保存网页**

根据 URL 保存网页。

所需权限：读取、写入

![](https://static.xiaobot.net/file/2024-11-26/720345/9e4f4347955ae625c447c3e6aa162c3f.png)


## **  RSS 订阅源**


### **  观看订阅**

当有新订阅添加时触发。

所需权限：读取

![](https://static.xiaobot.net/file/2024-11-26/720345/56739b12e65ac74c8d676f843eae9eaa.png)


### **  添加订阅**

通过其 URL 为 RSS 源添加订阅。

所需权限：读取、写入

![](https://static.xiaobot.net/file/2024-11-26/720345/fa707c97c4963478d6a6cabc589c8f45.png)


### **  订阅列表**

返回所有订阅的列表。

所需权限：读取

![](https://static.xiaobot.net/file/2024-11-26/720345/a7434f630ba82dea67006a53691a33bf.png)


## **其他**


### **  观察 Webhook 触发器**

当触发器 Webhook 规则被处理时触发。

所需权限：读取

![](https://static.xiaobot.net/file/2024-11-26/720345/7c743209bf0136cbe25a5c2ffb51df52.png)

1\. 打开观察 Webhook 触发器模块,建立连接,单击保存,并将 URL 地址复制到剪贴板。

![](https://static.xiaobot.net/file/2024-11-26/720345/414703df22737129185b825e77562db1.png)

2\. 登录您的 Inoreader 帐户。点击您的个人资料图标 > 偏好设置 > 规则、过滤器、高亮显示 > 新建规则。为该规则输入名称，选择您希望接收触发器的事件，选择触发 Webhook，然后单击保存。

![](https://static.xiaobot.net/file/2024-11-26/720345/042aa1f21bb23945669d38e61967314b.gif)

您已成功启用 Webhook。


### **  发起 API 请求**

执行任意授权的 AP 调用。

所需权限：读取、写入

![](https://static.xiaobot.net/file/2024-11-26/720345/ea64c4f4e3d85f5aa622747ac3895496.png)


### **使用示例 - 列出订阅**

以下 API 调用将返回您 Inoreader 账户中的所有订阅:

  /0/subscription/list

 方法： GET

![](https://static.xiaobot.net/file/2024-11-26/720345/e08b7730b5bf76421dc181828c187a64.png)

搜索结果可以在模块的输出中找到,位于 Bundle > Body > subscriptions。在我们的示例中,返回了 5 个订阅。

![](https://static.xiaobot.net/file/2024-11-26/720345/52eacc1960df7e4c699b951e41dd9ef3.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-20/720345/59bc66577c248e48a0d907a11dcb7aaf.png)