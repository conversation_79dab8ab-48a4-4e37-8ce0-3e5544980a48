---
title: "Make16.小红书图文笔记批量制作，Make帮你实现图片创作自动化"
created_at: "2024-09-22T08:22:01.000000Z"
uuid: "93681d59-288a-4c82-b39f-1a477b6bda0c"
tags: ['资源包']
---

# Make16.小红书图文笔记批量制作，Make帮你实现图片创作自动化

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/xiaohongshu-bulk-content-creation-make/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

2024年9月22日：[工作流下载](<https://pan.quark.cn/s/27bd03717fb2>)

**3\. Notion 数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[数据库模板](<https://xiaoyuzaici.notion.site/e94f5500b6a44ab0a70c87826d10461a?v=1d3fc4c42063486a8b55f29116e119ba&pvs=4>)

**4\. 视频配套PPT**

[PPT](<https://gamma.app/docs/16Make-s6ybpmtnvy4nz57>)

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！

**5\. 配套模版：**

模板样式查看：

[链接](<https://kdocs.cn/l/cflLkkbd5p8w>)

工作流下载：

[链接](<https://pan.quark.cn/s/85ca04eb6f7d>)


# **视频介绍**

利用自动化工具 Make，结合网页结构化数据提取、网页转图片工具，以及大模型，实现小红书图文笔记的批量制作，提升内容创作效率。

**主要内容：**

  1. **结构化数据的意义**

  - 结构化数据是自动化的基础，因为自动化前提是流程化和规范化，而这些都建立在结构化数据之上。

  - 结构化数据便于存储、利用和批量处理，例如将网页中大量财经词汇及其解释提取出来，进行独立输出和自成体系的批量生成卡片。

  1. "自动化的基础，就是结构化…流程化之后，再往前导，其实就是规范化…再往前走，其实规范化来自于什么？来自于结构化。"

  2. **网页结构化提取方法**

  - 传统方法：利用正则表达式提取网页数据。

  - 找特征：分析目标数据在网页中的位置和格式，找出唯一标识目标数据的特征。

  - 搭表达式：根据特征构建正则表达式。

  - 测表达式：利用相关网站测试正则表达式效果。

  - "找特征搭表达式，测表达式，我们三个一样的步骤。"

  - 新型方法：使用 Firecrawl 的 SmartCrawl 功能，利用大模型进行结构化提取。

  - 定义提示词：告诉 Firecrawl 需要提取的内容，例如 "我想在这一个网页数据中提取财经词汇和财经解释这两项内容，你给我输出"。

  - 抓取内容：Firecrawl 根据提示词抓取网页数据并结构化输出。

  - "我只需要给到它提示词即可…那么整个工作，其实 firecrawl 就可以很好的把这个数据提取出来，并把这些数据筛选出来，按照我们所需要的格式输出出来。"

  1. **网页转图片工具**

  - HTML/CSS to image 工具：将结构化数据转化成图片。

  - 特点：支持 Make 和 Zapier 集成，生成高保真图片，渲染效果与谷歌 Chrome 浏览器一致，确保高质量输出。

  - 额度：每月免费使用 50 张图片，足够基础使用。

  1. "它就支持 make，zapier 的这个集成…而且它的渲染也是非常漂亮的，这就是它重要的一个特点。"

  2. **Make 工作流实操演示**

  - 创建工作流，设置 Notion 知识库，添加触发器输入采集网址。

  - 利用 Jina API 采集网页数据，使用正则表达式筛选有效内容。

  - 使用 Firecrawl 或正则表达式提取结构化数据。

  - 调用 ChatGPT，根据提示词生成名词解释、示例和新闻等内容，以 JSON 格式输出。

  - 利用 HTML/CSS to image 工具生成图片，并上传至 Cloudinary 图床保存。

  - 将图片链接和笔记内容保存到 Notion 知识库。

  1. **Make 使用技巧**

  - 利用小报童提供的快捷工具，复制粘贴代码快速创建 HTTP 调用模块，避免手动输入错误。

  - 使用 Create JSON 模块构建 JSON 数据结构，避免因空格、换行符等问题导致 HTTP 调用出错。

  - 利用 Router 模块实现工作流的灵活跳转，例如通过设置条件将不需要执行的模块跳过。

  - 通过设置模块的执行顺序，保证工作流逻辑正确。

  - 注意限制 ChatGPT 输出的字数，避免生成的图片文本溢出。

**重要结论:**

  - 通过自动化工具和 AI 技术，可以高效地批量制作小红书图文笔记，提升内容创作效率。

  - 结构化数据是自动化的关键，要善于利用结构化数据提升工作流的效率和稳定性。

  - 不断学习和掌握新的工具和技巧，例如正则表达式、Firecrawl、HTML/CSS to image 等，可以更好地利用 AI 扩展自身能力边界。