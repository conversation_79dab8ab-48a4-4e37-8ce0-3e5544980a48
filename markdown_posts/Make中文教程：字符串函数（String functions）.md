---
title: "Make中文教程：字符串函数（String functions）"
created_at: "2024-09-19T08:01:20.000000Z"
uuid: "94e5fae1-755a-47e6-a266-6d1968489586"
tags: ['Make基础教程']
---

# Make中文教程：字符串函数（String functions）

**标签**: Make基础教程

# **Make平台字符串函数详解指南**

翔宇工作流今天为大家带来关于字符串函数的教程。

教程简介：

本教程介绍了Make.com中各种字符串处理函数，包括文本转换、编码解码、查找替换等多种操作。通过这些函数，你可以轻松处理字符串数据，满足不同场景下的文本处理需求。

学习目标：

主要目标是掌握各种字符串处理函数的用法，能够在实际运用中灵活应用这些函数来操作和转换字符串，提高文本处理效率。

学习内容：

教程涵盖了字符串移除非ASCII字符、Base64编码、大小写转换、字符串包含验证、URL编码解码、HTML转义与移除、字符串查找与替换、哈希计算、字符串分割、去除空格等操作。


## **1\. 文本清理函数族**


### ascii \- 文本净化专家

**功能** ：清除非标准英文字符

基本用法：

    {{ascii("ěMščařžkýáeíé")}}

结果：Make

去除变音符号：

    {{ascii("ěščřž "; true)}}

结果：escrz


### trim \- 空格清理工具

**功能** ：删除文本前后的空白字符

    {{trim("  Hello World  ")}}

结果：Hello World


## **2\. 大小写转换系列**


### capitalize \- 首字母大写

    {{capitalize("make.com")}}

结果：Make.com


### upper \- 全大写转换

    {{upper("Hello")}}

结果：HELLO


### lower \- 全小写转换

    {{lower("HELLO")}}

结果：hello


### startcase \- 标题格式化

    {{startcase("hello WORLD")}}

结果：Hello World


## **3\. 编码转换工具集**


### base64 \- Base64编码

编码：

    {{base64("Make")}}

结果：TWFrZQ==

解码：

    {{toString(toBinary("TWFrZQ==", "base64"))}}

结果：Make


### **URL编码解码**

编码：

    {{encodeURL("Hello World")}}

结果：Hello%20World

解码：

    {{decodeURL("Hello%20World")}}

结果：Hello World


## **4\. 文本查找与验证**


### contains \- 内容检查

检查是否包含"Hello"：

    {{contains("Hello World", "Hello")}}

结果：true

检查是否包含"Bye"：

    {{contains("Hello World", "Bye")}}

结果：false


### indexOf \- 位置查找

基本查找：

    {{indexOf("Make", "k")}}

结果：2

指定位置开始查找：

    {{indexOf("We love Make", "e", 7)}}

结果：11

查找不存在的字符：

    {{indexOf("We love Make", "x")}}

结果：-1


## **5\. HTML处理**


### escapeHTML \- HTML转义

    {{escapeHTML("**Hello** ")}}

结果：**Hello**


### stripHTML \- HTML清理

    {{stripHTML("**Hello** ")}}

结果：Hello


## **6\. 实用组合示例**

清理并规范化文本：

    {{trim(lower(stripHTML("
    
      HELLO WORLD  
    
    ")))}}

结果：hello world

条件处理文本：

    {{if(contains(text, "important"), upper(text), text)}}

结果：根据条件返回原文本或大写文本

安全处理用户输入：

    {{escapeHTML(trim(ascii(userInput)))}}

结果：清理后的安全文本

> 🌟 使用提示：
> 
>   - 每个函数都可以单独复制使用
> 
>   - 函数可以根据需要组合使用
> 
>   - 注意检查括号匹配和参数正确性
> 
> 


## **7\. 哈希函数系列**


### md5 \- MD5哈希计算

    {{md5("Make")}}

结果：529a05ca6c1263aab080ec4f20754411


### sha1 \- SHA1哈希计算

基础使用：

    {{sha1("Make")}}

结果：a94431ee22f05f141107f9355ed3127d0f0c4d5a

指定编码：

    {{sha1("Make", "base64")}}

结果：qUQx7iLwXxQRB/k1XtMSfQ8MTVo=


### sha256 \- SHA256哈希计算

    {{sha256("Make")}}

结果：ccdd25d4230fb997a7ee1166f8afabd157248eb812ea55ec7c3d1b7c8af7fa11


### sha512 \- SHA512哈希计算

    {{sha512("Make")}}

结果：e8000cd0fb8fae18caa8daa677269b0034380d3ec549e0113a0722d8b8dc05b0f7037f33f32fa09f906b2f1d7c43f2689a55d79aadf6bf09dd93f79407424d34


## **8\. 文本处理进阶**


### replace \- 文本替换

基础替换：

    {{replace("Hello World", "Hello", "Hi")}}

结果：Hi World

使用正则表达式：

    {{replace("Phone: 123456789", "/(\d{3})(\d{3})(\d{3})/", "$1-$2-$3")}}

结果：Phone: 123-456-789


### split \- 文本分割

    {{split("John, George, Paul", ", ")}}

结果：\["John", "George", "Paul"\]


### substring \- 截取子串

从开始位置截取：

    {{substring("Hello", 0, 3)}}

结果：Hel

从中间位置截取：

    {{substring("Hello", 1, 3)}}

结果：el


### replaceEmojiCharacters \- 表情符号处理

    {{replaceEmojiCharacters("Hello 😊 World"; emptystring)}}

结果：Hello World


## **9\. 二进制数据处理**


### toBinary \- 转换为二进制

基本转换：

    {{toBinary("Make")}}

结果：4d 61 6b 65

指定编码转换：

    {{toBinary("TWFrZQ==", "base64")}}

结果：原始二进制数据


### toString \- 转换为字符串

    {{toString(binaryData)}}

结果：转换后的文本


## **10\. 实用组合案例**


### **电话号码格式化**

    {{replace(substring(phoneNumber, 0, 9), "/(\d{3})(\d{3})(\d{3})/", "+$1 $2 $3")}}

结果：将 "123456789" 转换为 "+123 456 789"


### **安全的文件名生成**

    {{lower(replace(ascii(originalName), "/[^a-z0-9]/g", "-"))}}

结果：将 "File Name\! \(2023\)" 转换为 "file-name-2023"


### **URL参数清理**

    {{encodeURL(trim(lower(originalUrl)))}}

结果：清理并编码的URL


## **使用技巧**


### **1\. 数据验证组合**

    {{if(length(trim(input)) > 0, "有效输入", "输入为空")}}


### **2\. 安全文本处理**

    {{escapeHTML(trim(stripHTML(userInput)))}}


### **3\. 多步格式转换**

    {{base64(trim(upper(text)))}}


## **常见应用场景**

  1. **用户数据处理**

     - 输入验证

     - 格式标准化

     - 安全过滤

  2. **API数据处理**

     - 请求参数编码

     - 响应数据解码

     - 错误信息清理

  3. **文件处理**

     - 文件名清理

     - 内容编码

     - 格式转换

  4. **多语言处理**

     - 字符转换

     - 编码规范化

     - 特殊字符处理

> 🔔 提示：
> 
>   - 所有函数都支持直接复制使用
> 
>   - 注意检查函数参数的正确性
> 
>   - 可以根据实际需求组合使用不同函数
> 
> 


### 翔宇工作流：一个专注AI与自动化的频道

翔宇工作流希望通过本教程，让您在Make.com上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-19/720345/d0f000c9e4c56b0460af403d5fd6a6a8.png)