---
title: "Make 低成本调用阿里DeepSeek R1手把手教程（免费百万 Token）"
created_at: "2025-02-11T11:47:10.000000Z"
uuid: "abf4627a-c32f-427e-8689-4321995fff80"
tags: ['API']
---

# Make 低成本调用阿里DeepSeek R1手把手教程（免费百万 Token）

**标签**: API

在视频24.**AI写作革命！DeepSeek自动生成10万+爆文工作流教程** 中，翔宇介绍了使用Together AI平台稳定调用DeepSeek R1的方案，但该平台每百万Token收费7美元，成本较高。DeepSeek的中文写作能力出众，如果希望稳定运行，现在选择逐步丰富。今天为大家推荐两个更具性价比的替代平台：阿里云百炼大模型服务平台和302 AI，并详细介绍使用教程。

其中两种方法的工作里已经更新到视频 24 的资源包中，具体参见链接。

<https://xiaobot.net/post/cd6503e5-19b8-497a-94f9-d9007c4c8467>

![](https://static.xiaobot.net/file/2025-02-11/720345/e10c9771855ef83c71728a3467f4c703.png)


# **阿里云百炼大模型服务平台介绍**


### **平台注册与开通**

平台介绍网址：

<https://www.aliyun.com/product/bailian>

![](https://static.xiaobot.net/file/2025-02-11/720345/75f46ae28cdeb60af57e266ea3cbf356.png)

点击上图中的新人免费体验查看详情。

阿里云百炼大模型服务平台现推出DeepSeek新人专享福利，注册即送100万免费Token，有效期至2025年7月26日。


### **模型优势**

DeepSeek-R1模型在后训练阶段大规模采用强化学习技术，在仅有极少标注数据的情况下，显著提升了模型推理能力，尤其在数学、代码、自然语言推理等任务上表现卓越。


### **平台特性**

平台采用OpenAI标准接口，支持智能负载均衡与自动扩缩容，确保服务稳定性。模型调用费用极具竞争力，输入仅需¥0.004/千Token，输出¥0.016/千Token。


### **调用限制**

为确保调用公平性，平台设置了基于账号维度的限流机制，每分钟支持15000次调用和120万Token消耗。


### **使用须知**

开发者需注意，如需在多个业务空间使用模型功能，需先进行模型授权，以保障计费及模型调用安全可控。平台还提供详细的API文档和技术支持，是开发者调用大模型的最佳选择。


### **1.开通大模型**

  1. 访问阿里云百炼大模型服务平台

[https://bailian.console.aliyun.com/](<https://bailian.console.aliyun.com/\)>)

  2. 点击"立即开通"按钮

![](https://static.xiaobot.net/file/2025-02-11/720345/1cc1a16a29efd1f6a71653bddc23731d.png)![](https://static.xiaobot.net/file/2025-02-11/720345/c3882644e2a7d3a293caf59aade9590f.png)

  3. 完成实名认证流程

  4. 进入控制台，即可获取100万免费Token额度


### **2\. API Key配置**

  1. 登录平台后，点击右上角用户头像

![](https://static.xiaobot.net/file/2025-02-11/720345/361ce73c62a82337a754b3aa7cce84b4.png)
  2. 选择"API-KEY"选项

  3. 创建新的API-KEY并妥善保存

![](https://static.xiaobot.net/file/2025-02-11/720345/44bf65b4d80de39ff682541dd92b7c06.png)


## **3.Make 中快捷调用阿里云百炼大模型的详细步骤**

**第一步：获取调用模板**

  1. 访问金山文档快捷调用工具： 【金山文档 | WPS云文档】 翔宇工作流《Make基础教程》 [*https://kdocs.cn/l/cn96iMt0JeM2*](<https://kdocs.cn/l/cn96iMt0JeM2>)

  2. 在左侧导航栏找到"HTTP 模块条件查询"

![](https://static.xiaobot.net/file/2025-02-11/720345/bd268f4b6e65229858f69b833bcb6520.png)
  3. 在模型选项中选择"阿里云百炼大模型"

![](https://static.xiaobot.net/file/2025-02-11/720345/ad831a41b96e13b06f0c17b917ebbd8d.png)
  4. 从支持的模块列表中选择"对话"模块

![](https://static.xiaobot.net/file/2025-02-11/720345/4a0fbf6a36b5e123feb5632799ed3889.png)
  5. 复制该模块的代码模板

![](https://static.xiaobot.net/file/2025-02-11/720345/0aca5e05f31d425d388e3fdb2f866438.png)

**第二步：配置Make模块**

  1. 在Make中将复制的代码粘贴到画布

![](https://static.xiaobot.net/file/2025-02-11/720345/7ae996be2d9da30f156465a4c673fb15.png)
  2. 新建Create JSON模块

![](https://static.xiaobot.net/file/2025-02-11/720345/3a59b3e2a0e4735c007753f7835a3001.png)
  3. 在Data Structure中选择"Add"添加新数据结构

![](https://static.xiaobot.net/file/2025-02-11/720345/8b644cee1dd34aacbdd5ed3df1fa0eec.png)
  4. 将数据结构重命名为"阿里百炼大模型"

![](https://static.xiaobot.net/file/2025-02-11/720345/b04f7b1fa54a0a2f4b1427c8af30dfef.png)
  5. 在Specification中点击"Generate"生成模板

  6. 返回金山文档，复制对应模块的请求体

![](https://static.xiaobot.net/file/2025-02-11/720345/134514310db7bf33d53177c687abc483.png)
  7. 讲该请求体粘贴到 gengerate 的文本框中，点击gengerate

![](https://static.xiaobot.net/file/2025-02-11/720345/bc80adaf2ea2777aa3d59f549798f3e3.png)

**第三步：参数设置与调用**

  1. 在JSON模块的Model字段中输入正确的模型名称、对话内容

![](https://static.xiaobot.net/file/2025-02-11/720345/eff85ab09486f6afc3e7bb8a3827d025.png)
  2. 在HTTP模块中：

     - 将JSON模块的输出映射到请求体

![](https://static.xiaobot.net/file/2025-02-11/720345/373ce025717592461ffcd72a76e58794.png)

     - 替换API Key为您的实际密钥

![](https://static.xiaobot.net/file/2025-02-11/720345/4a37f59ac709db04272fa91ec2a395f7.png)
  3. 点击"运行"按钮进行测试

**注意事项：**

  - 确保API Key具有足够的调用权限

  - 建议设置调用限额提醒

  - 首次调用建议使用测试数据进行验证


### 302 AI平台使用教程

  - 访问302 AI官网：[ *https://www.302.ai/*](<https://www.302.ai/>)

  - 注册账号

  - 点击 API KEY即可生成自己的密钥

![](https://static.xiaobot.net/file/2025-02-11/720345/25bfffc5448a60b30e06195889981c37.png)

  - Make 中的调用方法大致过程和阿里云的方法类似，只不过在金山文档中搜索 302定位到具体的模块进行使用，这里就不做赘述。

![](https://static.xiaobot.net/file/2025-02-11/720345/d8af88a1eaf72510fed9abc270be565b.png)