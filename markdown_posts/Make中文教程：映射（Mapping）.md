---
title: "Make中文教程：映射（Mapping）"
created_at: "2024-09-01T09:56:55.000000Z"
uuid: "59bc66ba-61e2-43b5-baf5-273721387f04"
tags: ['Make基础教程']
---

# Make中文教程：映射（Mapping）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于映射的教程。

  1. **教程简介**  ：本教程介绍了在Make.com中执行数据映射的原理和方法。通过不同案例展示了如何在应用间进行数据信息传递，如从电子邮件映射到Slack消息和从表单数据映射到Jira问题。

  2. **学习目标**  ：学习如何在Make.com中使用映射将数据从一个应用程序传递到另一个，实现自动化数据处理。本教程帮助用户理解和掌握数据映射的基本概念和实际操作。

  3. **学习内容**  ：

     - 映射的定义和重要性

     - 数据包（Bundle）的结构和种类

     - 获取并查看数据包内容的方法

     - 在场景（Scenario）中添加和设置模块

     - 将数据映射到目标模块

  4. **概念理解**  ：

     - **映射（Mapping）**  ：将数据从一个应用模块传递到另一个的过程。

     - **数据包（Bundle）**  ：包含数据信息的集合，可包括数组或集合类型数据。

     - **源模块（Source Module）**  ：提供数据供映射的开始模块。

     - **目标模块**  ：接收映射数据的终点模块。

  5. **任务练习**  ：

     - 创建一个新的场景，添加“Email > Watch emails”模块获取电子邮件信息。

     - 获取数据包内容，并将其映射到“Slack > Create a message”模块中，为特定用户发送消息。

     - 通过运行模块验证数据映射是否成功，确保电子邮件内容正确显示在Slack消息中。


## **什么是映射？**

映射就像是给数据画一张路线图，告诉数据该如何从A点到达B点。想象你是一位快递员，映射就是你的配送清单，告诉你每个包裹该送到哪里。 也可以把映射想象成微信群里的消息转发：

  - 从A群收到重要通知

  - 需要转发到B群

  - 映射就是这个"转发"动作


### **实际应用场景**

  1. 邮件自动转发到Slack

     - 就像把信件内容复制到即时消息

  2. 表单自动创建Jira工单

     - 相当于把客户填写的内容自动誊抄到工作记录本

  3. Facebook leads自动同步到数据库

     - 如同把名片信息同时录入到多个联系人系统


## **什么可以被映射？**


### **Bundle（数据包）概念**

像收快递：

  - 快递盒子 = 数据包\(Bundle\)

  - 包裹内容 = 具体数据

  - 快递单号 = 数据标识


### **数据组织方式**

  1. Array（数组）

     - 就像餐厅菜单：

       - 只包含同类信息（全是菜品）

       - 有序排列

       - 例如：一组日期、一列名字

  2. Collection（集合）

     - 像搬家纸箱：

       - 可以装各种不同物品

       - 混合类型信息

       - 例如：既有日期又有描述文字


### **获取Bundle的步骤**

  1. 运行模块获取数据包

  2. 查看可用信息

  3. 确定要映射的内容

就像在开箱前，需要先知道箱子里装了什么，这样才能决定如何分类整理。


## **Bundle详解**

以Slack消息创建模块为例：

![](https://static.xiaobot.net/file/2024-11-26/720345/326bdf06098eaca663774b4703031618.png)


### **输入\(Input\)数据**

想象成寄出的包裹：

  - Text: 消息内容

  - User: 接收人ID


### **输出\(Output\)数据**

相当于快递签收回执：

  - Message: 发送的消息内容

  - Channel: 消息发送的频道ID


## **目标映射设置**


### **实例：邮件转Slack消息**

  1. 配置Slack模块：

![](https://static.xiaobot.net/file/2024-11-26/720345/9f18ad39bc4c8989bb665cec4d344ad7.png)
  2. 选择发送目标：

     - 选择频道类型

     - 指定接收用户

     - 设置消息内容


## **实用技巧**


### **1\. 查找源模块**

  - 悬停在映射数据上，源模块会闪烁提示

  - 就像追踪包裹的来源地


### **2\. 处理即时触发器无数据**

  - 需要等待真实数据触发

  - 例如：表单提交后才能获取数据

![](https://static.xiaobot.net/file/2024-11-26/720345/547ecb57e0a987a258a60b4b6e83d55e.png)


### **3\. 轮询触发器设置**

选择起始数据方式：

  1. 指定日期开始

     - 如：某日期后的邮件

  2. 选择第一条数据

     - 适用于有现存数据的情况

这就像设定包裹的收取时间，确保不会遗漏重要信息。


## **常见问题解决**

  1. 数据不显示

     - 检查源模块配置

     - 确认数据存在

  2. 映射失败

     - 验证数据格式匹配

     - 检查必填字段

记住：良好的映射设置就像精确的物流系统，确保每条数据都能准确送达目的地。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-26/720345/bf5268c58c7ce9755fc32b0a2056e98d.png)