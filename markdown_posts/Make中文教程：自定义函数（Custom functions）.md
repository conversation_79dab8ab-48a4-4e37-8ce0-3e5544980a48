---
title: "Make中文教程：自定义函数（Custom functions）"
created_at: "2024-09-19T08:23:52.000000Z"
uuid: "6ca9b633-5b29-48c9-af31-a4caa670f83e"
tags: ['Make基础教程']
---

# Make中文教程：自定义函数（Custom functions）

**标签**: Make基础教程

# **Make平台自定义函数完全指南**


## **重要提示**

  - 自定义函数功能仅适用于企业版计划用户

  - Make平台提供了管理和使用自定义函数的基础设施和系统支持，但客服团队不提供编写和调试自定义函数的技术支持


## **基本概念介绍**


### **什么是函数？**

在 Make 平台中，函数用于转换数据，特别是在将数据从一个模块映射到另一个模块时。想象函数就像一个数据加工厂，输入原材料（数据），经过加工后输出成品（转换后的数据）。


### **Make 提供两种函数：**

  1. 内置函数：平台预先定义好的常用函数

  2. 自定义函数：用户使用 JavaScript 自己编写的函数

> 小贴士：如果你是编程新手，可以把自定义函数理解为制作一个专属于你的数据处理"配方"，这个配方可以反复使用。


## **创建和管理自定义函数**


### **访问方式**

在左侧菜单中点击"Functions"（函数）即可进入函数管理界面。


### **权限说明**

  - 团队成员：可以查看和使用自定义函数

  - 团队管理员：可以创建和编辑自定义函数

![](https://static.xiaobot.net/file/2024-11-19/720345/e548a2c991cdd76f1f67571f0af46b0d.png)


### **创建步骤**

  1. 点击左侧菜单的"Functions"

  2. 点击"Add a function"（添加函数）

  3. 填写函数名称和描述

> 重要：函数名称有特殊要求

     - 只能包含字母和数字

     - 不能以数字开头

     - 不能使用 JavaScript 保留字（如 if、for、while 等）

     - 注意：保存后无法修改函数名称！

  4. 点击保存，这会创建函数头和空白函数体

  5. 编写函数代码，使用 return 关键字定义返回值

  6. 完成后点击保存

创建完成后，你的自定义函数就可以在场景设计器中使用了。系统会用特殊图标标记自定义函数，方便与内置函数区分。

![](https://static.xiaobot.net/file/2024-11-19/720345/57cb2c5826a25a49529e1e840453b930.png)


## **函数结构说明**

每个自定义函数都必须包含三个部分：

  1. 函数头：包含函数名和参数

  2. 函数体：用花括号 \{\} 包围的代码

  3. return 语句：必须有且仅有一个

> 新手提示：可以把函数想象成一个工厂，函数头是工厂的名字和原料清单，函数体是加工流程，return 是最终的产品输出。


## **技术细节**


### **支持的编程语言**

  - 支持 JavaScript ES6

  - 不支持第三方库

  - 代码在 Make 后端运行，不支持浏览器特有的功能


### **使用限制**

  - 单个函数运行时间不能超过 300 毫秒

  - 代码长度不超过 5000 字符

  - 只能写同步代码，不支持异步

  - 不能发送 HTTP 请求

  - 不能在函数内调用其他自定义函数

  - 不能使用递归


### **版本历史管理**

每次保存都会创建新的历史记录，你可以：

  - 比较不同版本

  - 恢复到之前的版本

![](https://static.xiaobot.net/file/2024-11-19/720345/45e53c63d5d2ca2cd054590d07e5fe1b.png)


## **实用示例**


### **示例1：Hello World**

最简单的函数示例：

    function helloWorld() {
        return "Hello world!";
    }


### **示例2：计算月份工作日数量**

    function numberOfWorkingDays(month, year) {
      let counter = 0;
      let date = new Date(year, month - 1, 1);
      const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      while (date.getTime() < endDate.getTime()) {
        const weekDay = date.getDay();
        if (weekDay !== 0 && weekDay !== 6) {
          counter += 1;
        }
        date = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
      }
      return counter;
    }

> 说明：这个函数可以计算指定年月的工作日数量（不包含节假日）


### **示例3：计算两个日期之间的天数**

    function numberOfDays(start, end) {
      const startDate = new Date(start);
      const endDate = new Date(end);
      return Math.abs((startDate.getTime() - endDate.getTime()) / (1000 * 60 * 60 * 24));
    }


### **示例4：随机问候语**

    function randomGreeting(greetings) {
      const index = Math.floor(Math.random() * greetings.length);
      return greetings[index];
    }

> 使用场景：当你需要从一组问候语中随机选择一个，比如做客服机器人时


## **调试与删除**


### **调试方法**

  - 使用代码编辑器下方的调试控制台测试函数


### **删除函数注意事项**

  1. 删除前先检查哪些场景在使用该函数

  2. 删除后，使用该函数的场景会执行失败

  3. 错误信息会显示：Failed to map '': Function '' not found\!


## **高级技巧**


### **在自定义函数中调用内置函数**

可以通过 iml 对象使用 Make 的内置函数，例如：

    function useBuiltInFunction(text){
        return iml.length(text);
    }


### **处理日期时区**

自定义函数在处理日期时会使用你的组织设置的时区，确保数据处理的准确性。

![](https://static.xiaobot.net/file/2024-11-19/720345/f24f5f664ab7985e3b85527ab9d50585.png)