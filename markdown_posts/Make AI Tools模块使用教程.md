---
title: "Make AI Tools模块使用教程"
created_at: "2025-02-06T11:40:05.000000Z"
uuid: "e2235352-7695-4380-8b18-9b88bbc07db4"
tags: ['Make基础教程']
---

# Make AI Tools模块使用教程

**标签**: Make基础教程

在自动化工作流程构建过程中,Make平台用户过去需要通过集成第三方API服务来实现基于大语言模型的智能化功能。这种方式不仅增加了系统集成成本,还需要解决HTTP调用、JSON数据格式等技术对接问题。为解决这一痛点,Make平台现已推出原生AI Tools模块,该模块原生集成了情感分析、智能问答、文本分类、语言识别等核心AI能力,显著降低了AI技术在自动化流程中的应用门槛。本文将深入分析这一重要功能模块的功能,探讨其如何有效提升工作流程的智能化水平。

![](https://static.xiaobot.net/file/2025-02-06/720345/0602969b4546415cca8175cd2ff9052d.png)


## **功能概览：全方位解析AI工具核心能力**


### **基础AI功能：专业引导，高效应用**

Make AI Tools为用户提供了一套完整的基础AI功能体系，通过标准化的接口设计，确保功能的易用性与专业性：

  - **情感分析** ：基于大模型，该模块可对文本进行情感评估，输出积极、中性或消极的量化结果。该功能可用于客户情感监测、舆情分析等专业场景。

  - **智能问答** ：采用先进的大语言模型，通过结构化prompt实现精准的问答交互。

  - **文本分类** ：提供可定制的多维度分类框架，支持层级分类体系构建。系统可根据预设标签进行自动分类，实现高效的信息分流管理。

![](https://static.xiaobot.net/file/2025-02-06/720345/f9012ba704889b1aceb9d735f37a6315.png)

  - **语言识别** ：集成多语言识别引擎，支持200+种语言的精确识别。系统可自动检测文本语种，为后续处理提供语言标识。

  - **信息提取** ：运用命名实体识别\(NER\)技术，精准提取文本中的结构化信息。可应用于简历解析、合同要素提取等专业场景。

![](https://static.xiaobot.net/file/2025-02-06/720345/5183d06db4827f3cb1a397d549313861.png)

  - **文本标准化** ：提供全面的文本规范化处理能力，包括格式统一、符号标准化、数据规范化等，确保输出文本的专业性。

  - **文本摘要** ：采用抽取式与生成式相结合的摘要算法，在保持语义完整性的基础上，实现高质量的文本浓缩。

  - **文本翻译** ：基于神经网络翻译模型，支持多语言双向互译，保证翻译结果的准确性与流畅性。

以上功能均采用模块化设计，提供标准API接口，支持灵活调用与组合使用。系统配备完整的技术文档与最佳实践指南，助力用户快速实现业务需求。


### **高级AI功能**

![](https://static.xiaobot.net/file/2025-02-06/720345/936e279c5b3918e5f8a954feb76d3e77.png)

  - **文本分块** ：将大段文本分割为更易管理的小块（chunks），特别适合构建RAG（检索增强生成）系统。通过智能分割算法，系统能够：

    - 根据Token 进行自动划分段落

    - 支持自定义块大小设置

    - 支持块重叠保留更多信息


## **工具测试与体验计划**

目前，该工具已面向Premium Tier 1等级用户开放测试使用。


## **总结与展望**

Make AI Tools原生模块的推出为自动化流程带来了新的可能性。通过集成多种AI功能，该模块可以在以下场景中发挥作用：

  - 多语言翻译：支持多种语言之间的文本转换

  - 文本摘要：自动生成文章重点内容概述

  - 智能分类：根据预设规则对信息进行分类整理

  - 情感分析：分析文本情感倾向，辅助决策

这些功能简化了操作流程，极大降低一些操作的复杂性，从而节约 Make 操作数。虽然目前该工具需要订阅成为Premium Tier 1放可使用，随着技术的发展，我们期待Make平台能够继续完善AI Tools模块的功能，为用户提供更多实用的自动化解决方案。