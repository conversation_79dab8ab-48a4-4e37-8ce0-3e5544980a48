---
title: "Make中文教程：数据存储（Data store）"
created_at: "2024-09-19T12:38:53.000000Z"
uuid: "def7f5c1-2bb9-418f-a21f-024426652789"
tags: ['Make基础教程']
---

# Make中文教程：数据存储（Data store）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于数据存储的教程。

  1. **教程简介**  ：本教程介绍如何在Make.com中创建和管理数据库。主要内容包括数据库的创建、数据结构设定、记录的添加和操作，以及数据库结构的更新和数据备份等。

  2. **学习目标**  ：学习如何在Make.com中使用数据库模块，掌握创建和管理数据库的基本技能，能够有效地存储和操作数据，并了解数据库结构的维护和更新方法。

  3. **学习内容**  ：本教程涵盖以下内容：数据库的创建、数据结构设定、记录的添加与更新、记录的检索与删除、数据库的备份和恢复、数据库结构的更新以及解决空间不足等常见问题。

  4. **概念理解**  ：数据库模块相当于一个简单数据库，可以通过各种操作如添加、替换、更新、检索等管理数据。数据结构决定了表（数据库）的列和数据类型。操作数据库时需要注意字段名称和类型的唯一性和相关性。

  5. **任务练习**  ：创建一个名为“联系人”的数据库，添加一个包含姓名（文本类型）和电话号码（数字类型）的数据结构。然后，向数据库添加几条记录，尝试检索、更新和删除某条记录，最后备份数据库中的数据。


# **Make数据存储使用指南**

数据存储允许您保存场景数据或在不同场景及运行之间传输数据。您可以使用数据存储在场景执行期间存储应用程序数据。数据存储的功能类似于一个简单的数据库。

数据存储应用程序的模块允许您在Make数据存储中添加、替换、更新、检索、删除、搜索或统计记录。

您可以在Make的数据存储部分查看和管理您的数据存储及其包含的数据。


## **在Make中创建数据存储**

  1. 点击左侧菜单中的"数据存储"。

![](https://static.xiaobot.net/file/2024-11-18/720345/4343e88f7eabee3aca82d44a7a3c0fec.png)
  2. 点击"添加数据存储"。

![](https://static.xiaobot.net/file/2024-11-18/720345/605ec49a1cb55334f102f5d230c7e73b.png)

  3. 为新数据存储输入设置。

![](https://static.xiaobot.net/file/2024-11-18/720345/26004eb5b2ba7bbec16db498c7b6e6cb.png)


## **管理数据存储的数据结构**


### **设置数据结构**

打开“添加数据结构”窗口以设置数据结构。

![](https://static.xiaobot.net/file/2024-11-18/720345/ed83dc9ee2312fc199e27988bcd8eea9.png)

您可以在创建或编辑数据存储时，通过单击“添加”按钮来访问此对话框：

![](https://static.xiaobot.net/file/2024-11-18/720345/cda3a4b5ede3a8775bc7717ef14bd0fd.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/160e0386b191012111d4f5539f21b1a3.png)


## **更新数据存储结构**

更新数据存储结构时要格外小心。在更新结构之前请备份数据存储中的数据。更改数据存储的结构可能会导致意外结果。

更新数据存储结构时，请记住以下几点：

  - 数据结构字段名称是数据存储列的唯一标识符。当您重命名数据结构字段时，Make将无法检索原始数据存储列中的数据，因为它们使用了不同的列标识符。

  - 您可以随时更新数据结构字段标签，而不会产生上述影响。

  - 对数据存储结构的更改仅适用于您放入数据存储的新数据。Make不会更改或验证原始数据以适应更新后的结构。

更新数据存储的数据结构的最佳方法是创建临时字段，其中包含数据的副本。更新临时字段中的数据，并确保其符合最终数据结构。

**示例数据存储：**

在具有4列的数据存储中：

  - **Key** : 类型为number

  - **productId** : 类型为number

  - **productName** : 类型为text

  - **dateAdded** : 类型为date

![](https://static.xiaobot.net/file/2024-11-18/720345/31396d2b18c6cd8d1e7b8df99c7c821c.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/5ef44dcf944bc667fc36f547c7e0f9c7.png)


### **重命名数据存储结构字段**

您可以随时更新数据存储结构字段的**标签** 。

如果要更改数据存储结构字段的**名称** ，请按以下步骤操作：

  1. 备份数据存储中的数据。

  2. 在数据存储中创建一个新名称的字段。

  3. 将原始列中的所有数据复制到新列。

  4. 将原始列中的所有数据更新为空字段。此步骤可防止原始列中的数据与新列中的数据一起存储。

您已经将原始数据存储列中的数据放入新的数据存储列。此外，您还有一个数据存储备份来检查更新是否成功。


### **更改数据存储结构字段的类型**

  1. 备份数据存储中的数据。

  2. 创建临时字段用于更新后的数据类型，或者跳过此步骤并直接更新字段类型。

  3. 使用转换函数将数据存储列中的所有值更新为新类型。例如，要将text转换为date，请使用parseDate函数。

您已经更新了数据存储列的类型。您已将数据存储列中的所有数据转换为新的数据类型。


## **操作**


### **添加/替换记录**

在数据存储中添加或替换记录。

![](https://static.xiaobot.net/file/2024-11-18/720345/55cb31e7212b9a5fc308676a22f4c368.png)


### **更新记录**

更新所选数据存储中的记录。

![](https://static.xiaobot.net/file/2024-11-18/720345/6fbd509fca88f1d10ff77b84488291d6.png)


### **获取记录**

从所选数据存储中检索记录。

![](https://static.xiaobot.net/file/2024-11-18/720345/22e147f283207671219b2413150ae856.png)


### **检查记录是否存在**

如果记录存在于指定的数据存储中，则返回值true，如果记录不存在于数据存储中，则返回false。

![](https://static.xiaobot.net/file/2024-11-18/720345/0fd0486d6ec3f667bfa2f872ce89deb7.png)


### **删除一条记录**

从所选的数据存储中删除特定记录。

![](https://static.xiaobot.net/file/2024-11-18/720345/80106c69e67f1c6de60b2557cdcf0990.png)


### **删除所有记录**

从所选的数据存储中删除所有记录。


### **查找记录**

根据过滤设置执行记录搜索。

![](https://static.xiaobot.net/file/2024-11-18/720345/a9133fe5dade228fde24b800d2f0e478.png)


### **统计记录**

返回所选数据存储中的记录数量。


## **管理数据存储中的记录**

Make允许您查看、更新和删除数据存储中的记录。

要管理数据存储中的记录，请点击左侧菜单中的"数据存储"，然后点击数据存储旁边的"浏览"。

![](https://static.xiaobot.net/file/2024-11-18/720345/c213eedc98d87fd7a9f44a3ae9ec07cd.png)

这将显示数据存储记录的编辑界面。

![](https://static.xiaobot.net/file/2024-11-18/720345/a0c60285f4806238627287c81d92d98e.png)


### **添加和编辑记录**

点击"添加"向数据存储添加新记录。您可以通过多次点击"添加"来添加多条记录。新插入的记录以绿色高亮显示。

点击现有字段可以更改它。更改的记录以黄色高亮显示。

![](https://static.xiaobot.net/file/2024-11-18/720345/0dd0bac158b57e57c0971950a0ee5595.png)

点击"保存"将所有更改保存到数据存储。点击"放弃更改"可以放弃您所做的任何更改，包括添加的记录和编辑的记录。


### **删除记录**

要从数据存储中删除记录，首先通过选择记录旁边的复选框来选择要删除的记录。然后，点击"删除"图标。

![](https://static.xiaobot.net/file/2024-11-18/720345/fc57041542e8c955054a621503ac993a.png)


### **删除记录**

当您需要在数据存储中进行大规模更改时，比如删除大量记录或更改数据存储结构，您应该首先备份您的数据。

有两种选项可用于备份数据存储中的数据，使用 Make：

  -  创建一个 Make 场景

  -  使用 Make API。

在接下来的步骤中，我们将重点介绍如何使用 Make 场景备份您的数据

  1. 克隆原始数据存储的数据结构，并清晰命名以表明它是用于备份数据存储的。

  2. 创建一个带有备份数据结构的数据存储。命名数据存储，清晰显示它是您数据的备份。

  3. 创建一个备份数据存储的场景。命名清晰显示其用途。

  4. 将“数据存储”>“搜索记录”模块添加到场景中。在“数据存储”字段中，选择包含您想要备份数据的数据存储。

  5. 将数据存储添加到场景中的添加/替换记录模块。

     1. 选择要备份数据的数据存储库。

     2. 将第一个模块的所有字段映射到第二个模块，包括 Key 字段。

  6.  执行该场景。

备份数据存储现在已经包含了原始数据存储中的所有数据。


## **故障排除**


### **从您的数据存储中恢复丢失的数据**

数据存储中丢失的值无法通过自动化流程恢复。不过，您可以采用手动方法来解决这个问题。

定位并恢复丢失的数据，请按照以下步骤进行：

  - 打开存放丢失项目的具体工作流场景。

  - 查看执行历史记录，找出数据存储中插入项目的情况。

  - 将识别的缺失数据复制到您的数据存储中。


### **更新数据存储结构**

在更新数据存储的数据结构之前，请先备份您的数据。

更新数据存储的数据结构时会遇到两个挑战：

  - 当您重命名数据结构中的字段时，该字段中的数据将变得无法访问。

  - 当您更改数据结构中字段的类型时，原始数据保持原始数据类型，而新数据则具有新的类型。

**重命名数据存储结构中的字段**

当您在数据存储的结构中重命名字段时，数据存储中的数据将变得无法访问。

在更新数据结构字段之前：

![](https://static.xiaobot.net/file/2024-11-18/720345/c688ab8d0de129e4eab368653d64ea85.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/bc534e6f8eeead3cc110e1f79d536bcb.png)

数据结构字段更新后：

![](https://static.xiaobot.net/file/2024-11-18/720345/8aa2efb2388a3c1b11f76672d7bd85b5.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/0e4f6f72fe18ed85dfaf0e40a14cda67.png)

如果您想再次访问数据，请向数据存储结构添加一个带有原始字段名称的字段。

![](https://static.xiaobot.net/file/2024-11-18/720345/84d37cf2554df4033b89442a82a4ce01.png)

![](https://static.xiaobot.net/file/2024-11-18/720345/e8f175839ea628fd0362696dd67f638c.png)

当您再次访问原始字段中的数据时，您可以按照步骤更新数据存储结构。


### **硬盘空间不足错误**

您收到“您的空间已用完”的消息是因为您当前有一个数据存储已分配了您分配的数据存储空间。

请编辑您现有的任何数据存储以释放空间。

  1.  点击“编辑”按钮。

  2. 缩小数据存储空间。

![](https://static.xiaobot.net/file/2024-11-18/720345/bd4206051c412accbe9fbacf5918d240.png)

您现在可以添加一个新的数据存储。


### **  注意**

在创建新数据存储时，请确保不要将所有空间都分配给一个记录，除非您确实需要。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/97db82c67ea979aa1ba41e9222849106.png)