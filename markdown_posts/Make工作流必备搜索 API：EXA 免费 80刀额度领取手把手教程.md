---
title: "Make工作流必备搜索 API：EXA 免费 80刀额度领取手把手教程"
created_at: "2025-03-03T03:16:06.000000Z"
uuid: "9d334e30-590c-4084-879a-41b86aa6823a"
tags: ['API']
---

# Make工作流必备搜索 API：EXA 免费 80刀额度领取手把手教程

**标签**: API

感谢网名为缘《囍》来自有机的分享（如果有什么和 Make 工作流相关的优惠都可以告知我，分享给大家），现在可以免费领取 EXA 的 80刀额度，现在翔宇写一篇手把手教程来实现这一过程。EXA 相当于大模型的搜索引擎，能够通过关键词直接抓取各类素材，翔宇很多工作流的搜索工具都是使用的 EXA，同时 EXA 具备 Make 原生模块，使用简单方便，推荐各位都注册申请获取一下这个免费额度，在很长的时间内都能免费使用，当然多注册个账号可以翻倍这个额度，方便未来使用。

1.首先点击EXA 官网：<https://exa.ai/>

![](https://static.xiaobot.net/file/2025-03-03/720345/c2b236b83db8bfb393af8be52ead702f.png)

2.点击右上角API DASHBOARD，使用谷歌账号直接登录。

![](https://static.xiaobot.net/file/2025-03-03/720345/62dbd6c692f2b35499a00e3c380dd1f6.png)

3.点击 Billing 来到账单页面。

![](https://static.xiaobot.net/file/2025-03-03/720345/dfef4bb077b29856f2b2131b4d629515.png)

4.直接输入EXA50ELEVEN，然后点击兑换即可添加 50 美金

![](https://static.xiaobot.net/file/2025-03-03/720345/1d53c651b10ab3e91fec0903680c9981.png)

5.然后点击右上角头像，在弹出的对话框中点击 Onborading Checklist。

![](https://static.xiaobot.net/file/2025-03-03/720345/b70c913f312a1a771ec70772f45e699b.png)

6.然后根据新手任务操作，点击 start

![](https://static.xiaobot.net/file/2025-03-03/720345/d355b0d8100005db0204ba6caefa26b9.png)

7.在搜索框中输入 make 关键词，然后在分类中选择 company，然后点击运行，完成第一步测试。

![](https://static.xiaobot.net/file/2025-03-03/720345/56f0475963960832a516e808ca151d9c.png)

8.点击如图位置，点开新手任务，继续点击 start

![](https://static.xiaobot.net/file/2025-03-03/720345/4bc43b5b7ea161ce4363388f1906feaa.png)![](https://static.xiaobot.net/file/2025-03-03/720345/bc1e261540a52def2d1017e8fb033d6c.png)

9.输入一个自己其它邮箱，点击添加用户。

![](https://static.xiaobot.net/file/2025-03-03/720345/2c58f44456140b04e976932059dee0ac.png)

10.点击如图位置，继续点击 start

![](https://static.xiaobot.net/file/2025-03-03/720345/6134396d19375a886f711346e1a7de8d.png)

11.任选一项，完成调研。

![](https://static.xiaobot.net/file/2025-03-03/720345/dd2a37464273e89adf204c96d15c9d12.png)

12.回到账单页面，点击左侧菜单 Billing 页面，查看余额。

我申请到了 79.99 刀，按价格算，能够搜索16000 次，未来的很长一段时间，获取素材方面实现了免费，每个人申请的金额并不相同。

![](https://static.xiaobot.net/file/2025-03-03/720345/c21b7cdeb75d4b4b26c406d366aaf901.png)