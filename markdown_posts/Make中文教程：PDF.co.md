---
title: "Make中文教程：PDF.co"
created_at: "2024-09-01T03:01:24.000000Z"
uuid: "0f6092a1-4ca6-4f03-a2ea-72ce08259459"
tags: ['Make基础教程']
---

# Make中文教程：PDF.co

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于PDF.co的教程。

**教程简介** ：本教程介绍了如何在Make平台上使用PDF.co模块，实现文档和图片的互相转换及其他PDF相关操作。内容包括如何建立连接、获取API密钥和执行各种转换与操作。

**学习目标** ：了解如何通过Make平台连接和使用PDF.co模块，掌握文档与图片的转换、PDF操作及条形码操作，学会调用PDF.co API进行高级操作。

**学习内容** ：

  1. 在Make中建立与PDF.co的连接。

  2. 从PDF转换为其他格式或从其他格式转换为PDF。

  3. 执行PDF合并、拆分、文本添加等操作。

  4. 生成和读取条形码。

  5. 上传文件，调用API，发送带附件的电子邮件等操作。

**概念理解** ：

  1. **API密钥** ：用于验证用户身份和权限的密钥。

  2. **Scenario** ：Make中的自动化工作流，包含一系列模块和操作。

  3. **PDF操作** ：包括合并、拆分、添加文本等对PDF文件的处理。

  4. **条形码操作** ：生成或读取条形码，用于编码和解码信息。

**任务练习** ：

  1. 注册PDF.co账户并获取API密钥。

  2. 在Make中建立与PDF.co的连接。

  3. 创建一个Scenario，添加PDF.co模块，尝试将一个图像文件转换为PDF。

  4. 实现PDF文本添加功能，将一段文本添加到已有PDF中。


# **PDF.co**

使用 Make 中的 PDF.co 模块,您可以将文档和图像转换为 PDF,反之亦然,在您的 PDF.co 账户中。

要使用 PDF.co 模块,您必须拥有一个 PDF.co 账户。您可以在 app.pdf.co/signup 上创建一个账户。

请参考 PDF.co API 文档以获取可用的端点列表。


## **  连接 PDF.co 去制作**

要建立连接,您必须:

  1. 在 PDF.co 中获取您的 API 密钥。

  2. 建立在 Make 中的连接。


### **在 PDF.co 获取你的 API 密钥**

要从您的 PDF.co 帐户获取 API 密钥:

  1. 登录您的 PDF.co 账户。

  2. 单击您的 API 密钥并将 API 密钥复制到您的剪贴板上。

![](https://static.xiaobot.net/file/2024-11-26/720345/4752d5e908ec2a159a39faeae58a54ef.gif)

您将在 Make 中的 API 密钥字段中使用此值。

**在 Make 中与 PDF.co 建立连接**

建立在 Make 中的连接

  1. 登录您的 Make 账户,将 PDF.co 模块添加到您的场景中,然后单击创建连接。

  2. 在"连接名称"字段中输入连接的名称。

  3. 在 API 密钥字段中输入上方复制的 API 密钥。

  4. 可选: 单击显示高级设置,然后输入您专用服务器的 URL。有关更多信息,请参阅 PDF.co 文件上传文档。

如果需要,请在创建您的自定义应用程序时使用以下重定向 URI: https://www.integromat.com/oauth/cb/pdf-co 。

  5.  点击保存。

  6. 如果提示,请验证您的账户并确认访问权限。

您已成功建立连接。您现在可以编辑您的方案并添加更多 PDF.co 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤进行操作。


## **  构建 PDF.co 场景**

连接应用程序后,您可以执行以下操作:

**  从 PDF 转换**

  -  从 PDF 转换

  - 将 PDF 转换为图像

**  转换为 PDF**

  -  转换为 PDF

  - 将图像转换为 PDF

  -  将 HTML 转换为 PDF

**PDF**

  -  合并 PDF

  -  拆分 PDF

  - 添加文本、图像和表单字段到 PDF

  - 获取 PDF 文档信息

  -  解析文档

  -  文件分类器

  -  填写 PDF 表单

  - 添加密码和安全性到 PDF 文件中

  - 从 PDF 中移除密码和安全性

  - 压缩和优化 PDF

  -  在 PDF 中搜索文本

  - 在 PDF 中搜索和替换文本

  - 在 PDF 中搜索及删除找到的文本

  - 在 PDF 中搜索和替换图像

**  条形码**

  -  生成条形码

  -  扫描条形码

**  其他**

  -  上传文件

  -  生成 pdfco API 请求

  - 发送带有附件的电子邮件

  -  工作检查


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-01/720345/036766bbd013218fb62a8384d110778f.png)