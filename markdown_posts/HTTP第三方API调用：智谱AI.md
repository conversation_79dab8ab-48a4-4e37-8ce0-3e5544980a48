---
title: "HTTP第三方API调用：智谱AI"
created_at: "2024-09-29T16:07:14.000000Z"
uuid: "18ae22f8-edc0-4749-baec-84ca7fd5c190"
tags: ['快捷工具']
---

# HTTP第三方API调用：智谱AI

**标签**: 快捷工具

# 模型介绍：

智谱AI是一款功能强大的人工智能平台，提供了多种高性能模型，涵盖文本处理、图像生成、视频理解等多种应用场景。其中，智谱AI提供免费的API，尤其适合用于自动化工作流的应用，如Make等平台。针对基础的素材汇总、分析和新闻生成等任务，智谱AI的免费调用模型（如GLM-4-Flash）**可以实现0成本的高效处理，极大地提升性价比** 。


# **使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 Make.com 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

[HTTP第三方API调用：智谱AI](<https://kdocs.cn/l/cl3gRZAOaFak>)


# 对话

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://open.bigmodel.cn/api/paas/v4/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Content-Type","value":"application/json"},{"name":"Authorization","value":"Bearer <你的apikey>"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"model\": \"glm-4\",\"messages\": [{\"role\": \"user\",\"content\": \"你好\"}]}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}


# **通用搜索\(类似博查和EXA对网络信息检索获取返回数据\)：**

    {
      "subflows": [
        {
          "flow": [
            {
              "id": 8,
              "module": "http:ActionSendData",
              "version": 3,
              "parameters": {
                "handleErrors": false,
                "useNewZLibDeCompress": true
              },
              "mapper": {
                "url": "https://open.bigmodel.cn/api/paas/v4/tools",
                "serializeUrl": false,
                "method": "post",
                "headers": [
                  {
                    "name": "Authorization",
                    "value": "YOUR API KEY"
                  }
                ],
                "qs": [],
                "bodyType": "raw",
                "parseResponse": true,
                "authUser": "",
                "authPass": "",
                "timeout": 300,
                "shareCookies": false,
                "ca": "",
                "rejectUnauthorized": true,
                "followRedirect": true,
                "useQuerystring": false,
                "gzip": true,
                "useMtls": false,
                "contentType": "application/json",
                "data": "{\"request_id\":\"{{uuid}}\",\"tool\":\"web-search-pro\",\"stream\":false,\"messages\":[{\"role\":\"user\",\"content\":\"中国队奥运会拿了多少奖牌\"}]}",
                "followAllRedirects": false
              },
              "metadata": {
                "designer": {
                  "x": 210,
                  "y": 265,
                  "name": "HTTP"
                },
                "restore": {
                  "expect": {
                    "method": {
                      "mode": "chose",
                      "label": "POST"
                    },
                    "headers": {
                      "mode": "chose",
                      "items": [
                        null,
                        null
                      ]
                    },
                    "qs": {
                      "mode": "chose",
                      "items": [
                        null
                      ]
                    },
                    "bodyType": {
                      "label": "Raw"
                    },
                    "contentType": {
                      "label": "JSON (application/json)"
                    }
                  }
                },
                "parameters": [
                  {
                    "name": "handleErrors",
                    "type": "boolean",
                    "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                    "required": true
                  },
                  {
                    "name": "useNewZLibDeCompress",
                    "type": "hidden"
                  }
                ],
                "expect": [
                  {
                    "name": "url",
                    "type": "url",
                    "label": "URL",
                    "required": true
                  },
                  {
                    "name": "serializeUrl",
                    "type": "boolean",
                    "label": "Serialize URL",
                    "required": true
                  },
                  {
                    "name": "method",
                    "type": "select",
                    "label": "Method",
                    "required": true,
                    "validate": {
                      "enum": [
                        "get",
                        "head",
                        "post",
                        "put",
                        "patch",
                        "delete",
                        "options"
                      ]
                    }
                  },
                  {
                    "name": "headers",
                    "type": "array",
                    "label": "Headers",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "qs",
                    "type": "array",
                    "label": "Query String",
                    "spec": [
                      {
                        "name": "name",
                        "label": "Name",
                        "type": "text",
                        "required": true
                      },
                      {
                        "name": "value",
                        "label": "Value",
                        "type": "text"
                      }
                    ]
                  },
                  {
                    "name": "bodyType",
                    "type": "select",
                    "label": "Body type",
                    "validate": {
                      "enum": [
                        "raw",
                        "x_www_form_urlencoded",
                        "multipart_form_data"
                      ]
                    }
                  },
                  {
                    "name": "parseResponse",
                    "type": "boolean",
                    "label": "Parse response",
                    "required": true
                  },
                  {
                    "name": "authUser",
                    "type": "text",
                    "label": "User name"
                  },
                  {
                    "name": "authPass",
                    "type": "password",
                    "label": "Password"
                  },
                  {
                    "name": "timeout",
                    "type": "uinteger",
                    "label": "Timeout",
                    "validate": {
                      "max": 300,
                      "min": 1
                    }
                  },
                  {
                    "name": "shareCookies",
                    "type": "boolean",
                    "label": "Share cookies with other HTTP modules",
                    "required": true
                  },
                  {
                    "name": "ca",
                    "type": "cert",
                    "label": "Self-signed certificate"
                  },
                  {
                    "name": "rejectUnauthorized",
                    "type": "boolean",
                    "label": "Reject connections that are using unverified (self-signed) certificates",
                    "required": true
                  },
                  {
                    "name": "followRedirect",
                    "type": "boolean",
                    "label": "Follow redirect",
                    "required": true
                  },
                  {
                    "name": "useQuerystring",
                    "type": "boolean",
                    "label": "Disable serialization of multiple same query string keys as arrays",
                    "required": true
                  },
                  {
                    "name": "gzip",
                    "type": "boolean",
                    "label": "Request compressed content",
                    "required": true
                  },
                  {
                    "name": "useMtls",
                    "type": "boolean",
                    "label": "Use Mutual TLS",
                    "required": true
                  },
                  {
                    "name": "contentType",
                    "type": "select",
                    "label": "Content type",
                    "validate": {
                      "enum": [
                        "text/plain",
                        "application/json",
                        "application/xml",
                        "text/xml",
                        "text/html",
                        "custom"
                      ]
                    }
                  },
                  {
                    "name": "data",
                    "type": "buffer",
                    "label": "Request content"
                  },
                  {
                    "name": "followAllRedirects",
                    "type": "boolean",
                    "label": "Follow all redirect",
                    "required": true
                  }
                ]
              }
            }
          ]
        }
      ],
      "metadata": {
        "version": 1
      }
    }

控制台：

<https://bigmodel.cn/console/overview>

价格随时变化，可参考以下网址：

<https://bigmodel.cn/pricing>


# 模型列表：

智谱AI推出了一系列性能强大的智能模型，适用于多种复杂任务处理，同时提供了灵活的定价方案，方便用户根据需求选择合适的模型。以下是部分热门模型的功能和定价介绍：

1\. **GLM-4-Plus**

\- **简介** ：高智能旗舰模型，适用于长文本和复杂任务处理，性能全面提升。

\- **上下文** ：128K

\- **单价** ：0.05元 / 千tokens

\- **Batch API 定价** ：0.025元 / 千tokens

2\. **GLM-4-0520**

\- **简介** ：高智能模型，擅长处理复杂且多样化的任务。

\- **上下文** ：128K

\- **单价** ：0.1元 / 千tokens

\- **Batch API 定价** ：0.05元 / 千tokens

3\. **GLM-4-AirX**

\- **简介** ：极速推理模型，具备高效推理性能，适合快速处理任务。

\- **上下文** ：8K

\- **单价** ：0.01元 / 千tokens

4\. **GLM-4-Air**

\- **简介** ：高性价比模型，性能与成本的最佳平衡。

\- **上下文** ：128K

\- **单价** ：0.001元 / 千tokens

\- **Batch API 定价** ：0.0005元 / 千tokens

5\. **GLM-4-Long**

\- **简介** ：专为超长文本处理设计，支持1M字符的上下文，适合大规模文本分析。

\- **上下文** ：1M

\- **单价** ：0.001元 / 千tokens

6\. **GLM-4-FlashX**

\- **简介** ：高速低价版本，具备超快推理速度，适合高效任务处理。

\- **上下文** ：128K

\- **单价** ：0.0001元 / 千tokens

7\. **GLM-4-Flash**

\- **简介** ：智谱AI的免费调用模型，零成本使用，适合初级用户和测试使用。

\- **上下文** ：128K

\- **单价** ：免费

\- **Batch API 定价** ：免费

8\. **GLM-4V-Plus**

\- **简介** ：具备视频和多图片理解能力，适用于多媒体内容分析。

\- **上下文** ：128K

9\. **CogView-3-Plus**

\- **简介** ：图片生成模型，能够根据文字描述生成高质量图像，支持多种图片尺寸。

\- **上下文** ：128K

10\. **CogVideoX**

\- **简介** ：支持视频生成，通过输入文本或图片轻松生成高分辨率视频（1440x960）。

\- **上下文** ：128K

11\. **GLM-4-AllTools**

\- **简介** ：具备自主规划和执行复杂任务的能力，支持128K上下文。

\- **上下文** ：128K

12\. **GLM-4-Assistant**

\- **简介** ：智能体模型，只需指定智能体ID即可应用于多种业务场景。

\- **上下文** ：128K

13\. **GLM-4-9B**

\- **简介** ：开源模型，适合处理复杂推理任务，支持8K上下文。

\- **上下文** ：8K

14\. **GLM-4V**

\- **简介** ：具备图像理解与推理能力，适用于视觉任务处理。

\- **上下文** ：128K

15\. **CogView-3**

\- **简介** ：图片生成模型，支持根据文字描述生成1024x1024分辨率图像。

\- **上下文** ：128K

16\. **CharGLM-3**

\- **简介** ：拟人模型，适用于情感陪伴和虚拟角色，支持4K上下文。

\- **上下文** ：4K

17\. **Web-Search-Pro**

\- **简介** ：专业版联网搜索模型，增强意图识别能力，并支持流式输出搜索结果。

\- **上下文** ：128K

18\. **CodeGeeX-4**

\- **简介** ：代码生成模型，具备代码补全、翻译和智能问答能力，支持128K上下文。

\- **上下文** ：128K

19\. **Embedding-3**

\- **简介** ：支持自定义向量维度，最高支持2048向量维度，适合文本向量化任务。

\- **上下文** ：128K

20\. **Embedding-2**

\- **简介** ：向量模型，适用于文本向量化任务，支持1024向量维度。

\- **上下文** ：128K

21\. **ChatGLM3-6B**

\- **简介** ：开源模型，具备60亿参数，支持微调训练，可用于开发者的专业应用。

\- **上下文** ：8K

智谱AI提供广泛的模型类型，帮助用户在性能和成本之间找到最佳平衡，满足不同任务需求。

![](https://static.xiaobot.net/file/2024-09-30/720345/94f7e0f42931d7adc85bcad471227e06.png)