---
title: "n8n 数据映射超详细入门教程"
created_at: "2025-05-03T14:57:15.000000Z"
uuid: "85a322c1-bdde-4807-837c-4c5fe7d04908"
tags: ['n8n 基础教程']
---

# n8n 数据映射超详细入门教程

**标签**: n8n 基础教程

翔宇在实践 n8n 的过程中，深刻体会到"数据映射"对工作流成功的关键作用——它就像血管一样，高效地将各节点间的"数据血液"精准传递。本教程是翔宇专为零基础新手打造的指南，从最基础的 Fixed（固定值）与 Expression（表达式）概念入手，循序渐进地解析各种映射技巧、实用案例与常见陷阱，并在适当时机与 Make.com 的映射机制进行对比。

通过阅读本教程，翔宇希望各位小伙伴不仅能在 n8n 界面上快速上手操作，更能借鉴翔宇的实战经验，自信地构建出既高效又易于维护的自动化工作流。**当然，由于翔宇的实践经验有一定局限性，本教程可能存在一些疏漏或不准确之处。恳请各位读者理解，也欢迎指正和补充，以便共同进步。**

![](https://static.xiaobot.net/file/2025-05-03/720345/1e06269ae026cf499d9f9e53f31be7b8.png)


## **前言：为什么需要了解数据映射？**

想象一下你在玩一个传话游戏，你需要准确地把 A 同学的话告诉 B 同学。在 n8n 这个自动化工具里，“数据映射”就扮演着类似的角色。

n8n 能帮你把不同的 App 或服务连接起来，自动完成一些重复性的工作（比如收到邮件自动存附件到网盘）。它很灵活，你可以像搭积木一样拖拽模块（叫“节点”）来设计流程，也可以写点简单的代码来实现复杂功能。

在这个过程中，“数据映射”是核心，它决定了**上一个节点处理完的数据，要怎样准确地传递给下一个节点使用** 。比如，你从网站表单收到了一个用户填写的名字，你想在下一步发送欢迎邮件时，邮件里能自动填上这个名字，这就需要数据映射。

如果映射搞错了，就像传话传错了，整个自动化流程可能就乱套了，数据丢失或者跑出奇怪的结果。所以，就算你是 n8n 新手，弄懂数据映射也是非常重要的第一步。

n8n 里设置节点参数（可以理解为节点的配置项）主要有两种方式：

  1. **Fixed \(固定值\)** ：直接写死一个值。

  2. **Expression \(表达式\)** ：用一种特殊的“公式”动态地从其他地方取值。

这篇教程会用最简单直白的方式，带你彻底搞懂这两种方式，让你轻松掌握 n8n 的数据流转。

![](https://static.xiaobot.net/file/2025-05-03/720345/a3693582d69ec1f3c6ce3cdd8325c71a.png)


## **核心映射方式：Fixed \(固定值\) vs. Expression \(表达式\)**

在 n8n 节点的配置界面，你会经常看到参数旁边有个可以在 "Fixed" 和 "Expression" 之间切换的小按钮。理解这两者的区别很关键：


### **1\. Fixed \(固定值\)**

  - **是什么** ：直接在输入框里填写一个固定的、不会变的值。

  - **何时使用** ：当这个参数的值在每次流程运行时都应该是**一样** 的。

  - **举个例子** ：

    - 你要设置一个“发送邮件”节点，收件人地址总是同一个老板的邮箱。这时就在“收件人”字段选择 "Fixed" 模式，直接填入老板的邮箱地址 <EMAIL>。

    - 连接数据库时，数据库的地址、用户名通常也是固定的。

  - **优点** ：简单直接，容易理解，不易出错。

  - **缺点** ：不够灵活，不能根据前面的数据变化。


### **2\. Expression \(表达式\)**

  - **是什么** ：使用一对双大括号 \{\{ \}\} 包裹起来的“代码片段”或“公式”，用来动态计算参数的值。

  - **何时使用** ：当参数的值需要**根据前面节点的输出数据** 或者其他**动态信息** 来决定时。

  - **举个例子** ：

    - 还是“发送邮件”节点，但这次你想在邮件正文里称呼收件人的名字。这个名字是从上一步的“Webhook 触发器”（比如接收表单提交）里获取的。这时，“邮件正文”字段就要用 "Expression" 模式，写类似 你好，\{\{ $node\("Webhook"\).json.body.name \}\}！ 这样的表达式。这里的 \{\{ ... \}\} 部分会自动被替换成 Webhook 收到的实际名字。

  - **优点** ：非常灵活，能让工作流根据实时数据做出反应。

  - **缺点** ：需要学习一点语法规则（别担心，很简单）。

**小结** ：

  - **固定不变的值，用 Fixed。**

  - **需要动态变化的值（来自其他节点数据），用 Expression。**


## **深入理解 Expression 表达式**

好了，既然 Expression 这么重要又灵活，我们就来详细解析一下它常用的几种用法。记住，它们都写在 \{\{ \}\} 里面。


### **1\. Node 表达式：引用其他节点的数据**

![](https://static.xiaobot.net/file/2025-05-03/720345/87abc9a9631682900887c3708d56398b.jpeg)

  - **作用** ：这是最常用的，用来获取**之前某个特定节点** 输出结果中的数据。

  - **写法** ：基本格式是 \{\{ $\("节点名称"\).item.json.字段名 \}\} 或者 \{\{ $node\("节点名称"\).json.字段名 \}\} \(旧写法，下面会解释区别\)。

    - $\("节点名称"\) 或 $node\("节点名称"\)：指定你想从哪个节点获取数据，把 "节点名称" 换成你工作流里实际的节点名字（注意大小写）。

    - .item \(新写法\)：这是 n8n 比较智能的地方，它会尝试找到和当前正在处理的数据“配对”的那条来源数据。

    - .json：表示你要获取的是那个节点输出的 JSON 格式数据（n8n 内部数据主要是 JSON）。

    - .字段名：你想从 JSON 数据里提取的具体信息，可以用 . 或者 \["字段名"\] 的方式一层层往下查找。

  - **真实数据示例** ：假设你有一个名为 "获取用户信息" \(Fetch User Info\) 的 HTTP Request 节点，它请求 API 后返回了如下 JSON 数据：
        
        // "获取用户信息" 节点输出的数据 (Item 0)[{
            "id": 123,
            "name": "张三",
            "email": "[email protected]",
            "address": {
              "street": "人民路 100号",
              "city": "上海"
            }}]​

现在，你想在下一个“发送消息” \(Send Message\) 节点的内容中，使用这个用户的名字和邮箱。你可以在“消息内容”字段这样写（使用 Expression 模式）：
        
        用户名：{{ $("获取用户信息").item.json.name }}，邮箱：{{ $("获取用户信息").item.json.email }}

当工作流运行到这一步时，上面的表达式会被替换成实际值，最终发送的消息内容就是：
        
        用户名：张三，邮箱：[email protected]

如果想获取嵌套的城市信息，可以这样写：
        
        {{ $("获取用户信息").item.json.address.city }}

结果就是：上海

  - **注意事项** ：

    - **节点名称要正确** ：必须和你工作流里显示的名字完全一致。修改了节点名字，表达式也要跟着改。

    - **.json不能省略** ：通常都需要加上 .json 才能访问里面的数据。

    - **数据配对 \(.item\)** ：新版 n8n \(推荐用 $\("..."\)\) 会智能匹配数据。如果它无法确定数据来源（比如经过了合并或拆分），可能会报错（后面会讲如何解决）。

    - **执行顺序** ：只能引用**已经执行完毕** 的节点的数据。

    - **新旧语法** ：

      - **推荐** ：$\("节点名"\).item.json... （利用了 Item Linking 智能配对）

      - **旧/兼容** ：$node\("节点名"\).json... （它更像是按数据项的顺序来取，如果数据量不匹配可能出错）。虽然还能用，但建议新项目用 $\("..."\) 的写法。你在网上看到旧教程可能会用 $node\["节点名"\] 这种更老的写法，也应该换成新的。


### **2\. Item 表达式：指定获取第几条数据**

  - **作用** ：有时候，一个节点可能输出了**一个列表** （多条数据），而你只想明确地获取**其中固定某一条** （比如总是获取第一条），或者你想在处理某条数据时，去引用**另一条固定索引** 的数据。

  - **写法** ：用 $item\(索引\) 开头。索引是从 0 开始的（0 代表第一条，1 代表第二条...）。

    - \{\{ $item\(0\).$node\("节点名"\).json.字段 \}\}：强制获取 "节点名" 输出的**第一条** 数据中的某字段。

  - **应用场景** ：

    - **共享配置** ：一个节点获取了 API 密钥（只有一条数据），后面的节点需要处理一个用户列表（多条数据），且每次处理都需要用同一个 API 密钥。

    - **处理数组特定位置** ：一个节点返回了 \["苹果", "香蕉", "橙子"\]，你想在下一步分别处理。

  - **真实数据示例** ：

    - 读取配置 节点输出（只有一条数据，索引为 0）：
          
          [{
              "apiKey": "mysecretkey12345",
              "apiUrl": "[https://api.example.com](https://api.example.com)"}]​

    - 处理订单 节点的输入（有多条订单数据，比如正在处理第 2 条，索引为 1）：
          
          [{"orderId": "A001", "amount": 100},{"orderId": "A002", "amount": 200}, // <-- 当前正在处理这条{"orderId": "A003", "amount": 150}]​

    - 在 处理订单 节点中，需要为每条订单调用 API，API Key 始终是 读取配置 输出的那一个。表达式应写为：
          
          {{ $item(0).$node("读取配置").json.apiKey }}

即使当前正在处理第 2 条订单 \(索引 1\)，这个表达式也会强制去获取 读取配置 节点输出的**第 1 条**  \(索引 0\) 数据里的 apiKey。结果始终是：mysecretkey12345。

  - **注意事项** ：

    - 索引从 0 开始。

    - $item\(索引\) 要放在表达式最前面。

    - 新版 n8n 提供了更易读的替代方法：

      - $\("节点名"\).first\(\).json... \(相当于 $item\(0\)\)

      - $\("节点名"\).last\(\).json... \(获取最后一条\)

      - $\("节点名"\).all\(\)\[索引\].json... \(获取所有数据组成的数组，再按下标取\)建议优先使用这些新方法，语义更清晰。

    - 旧的 $item\("0"\) \(带引号\) 写法已过时，不要使用。


### **3\. JSONPath、JMESPath 与 JavaScript 表达式：灵活处理数据**

n8n 的表达式非常强大，不仅能取值，还能对数据进行筛选、计算和转换。

  - **JMESPath 查询 \($jmespath\)** ：

    - **作用** ：当需要从复杂的 JSON 结构里精准地挑选数据，或者对数组进行过滤和变换时，用它特别方便。它是一种专门查询 JSON 的语言。

    - **写法** ：\{\{ $jmespath\("查询语句", 数据源\) \}\}

    - **真实数据示例** ：假设 $json \(当前节点的输入数据\) 是：
          
          {"department": "Sales","employees": [
              {"name": "Alice", "age": 30, "active": true},
              {"name": "Bob", "age": 25, "active": false},
              {"name": "Charlie", "age": 35, "active": true}]}​

      - 提取所有员工的名字：
            
            {{ $jmespath("employees[*].name", $json) }}

结果：\["Alice", "Bob", "Charlie"\] \(一个名字数组\)

      - 只提取**在职 \(active\) 员工** 的名字：
            
            {{ $jmespath("employees[?active==`true`].name", $json) }}

结果：\["Alice", "Charlie"\]

      - 获取第一个员工的年龄：
            
            {{ $jmespath("employees[0].age", $json) }}

结果：30

  - **内置辅助函数 \(类似 Lodash\)** ：n8n 提供了一些方便的小函数。

    - $ifEmpty\(值, 默认值\)：如果值是空的 \(null, undefined, ''\), 就返回默认值。

      - 示例：\{\{ $ifEmpty\($json.description, "暂无描述"\) \}\}

      - 如果 $json.description 是空字符串 ""，结果就是 "暂无描述"。

    - $if\(条件, 为真时的值, 为假时的值\)：类似 Excel 的 IF 函数。

      - 示例：\{\{ $if\($json.score >= 60, "及格", "不及格"\) \}\}

      - 如果 $json.score 是 75，结果就是 "及格"。

    - 还有 $max\(...\), $min\(...\) 等。

  - **原生 JavaScript \(JS\)** ：Expression 本质上是在执行 JS 代码，所以可以直接使用 JS 语法。

    - **字符串拼接** ：

      - 输入数据：\{"firstName": "Li", "lastName": "Lei"\}

      - 表达式：\{\{ $json.firstName + ' ' + $json.lastName \}\}

      - 结果："Li Lei"

    - **数学计算** ：

      - 输入数据：\{"price": 19.9, "quantity": 3\}

      - 表达式：\{\{ $json.price \* $json.quantity \}\}

      - 结果：59.7

    - **调用 JS 方法** ：

      - 输入数据：\{"message": " hello world "\}

      - 表达式：\{\{ $json.message.trim\(\).toUpperCase\(\) \}\} \(去除首尾空格并转大写\)

      - 结果："HELLO WORLD"

  - **如何选择？**

    - 简单取值、加减乘除、字符串处理：直接用 JS。

    - 需要判断、给默认值：用 $if, $ifEmpty 更清晰。

    - 从复杂 JSON 或数组里筛选/提取：优先考虑 $jmespath。

    - 逻辑太复杂（比如好几行代码）：不要硬塞在表达式里，考虑用专门的 Function \(Code\) 节点写 JS 代码更方便维护。


### **4\. 变量与参数映射：不止是节点数据**

表达式不仅能获取节点数据，还能访问一些全局信息和特殊变量。

  - **工作流和执行信息** ：

    - $workflow.name：当前工作流的名字。

    - $execution.id：本次运行的唯一 ID。

    - 用途：可以在发送通知或记录日志时，包含这些信息，方便追溯是哪个流程、哪次运行产生的。

      - 示例：【\{\{ $workflow.name \}\}】工作流执行完毕，ID: \{\{ $execution.id \}\}

  - **环境变量 \($env\)** ：

    - 可以获取 n8n 运行环境设置的变量。

    - 写法：\{\{ $env\("变量名"\) \}\}

    - 用途：存放 API 密钥等敏感信息，不在工作流里直接写死，更安全。

      - 示例：\{\{ $env\("MY\_SECRET\_API\_KEY"\) \}\}

  - **全局变量 \($vars\)** ：

    - 可以在 n8n 设置里定义一些全局通用的变量。

    - 写法：\{\{ $vars.变量名 \}\}

    - 用途：存储多个工作流都可能用到的值，如公司名、通用邮箱地址。

      - 示例：\{\{ $vars.companyName \}\}

  - **日期与时间 \($now,  $today\)**：

    - $now：获取当前的完整日期和时间 \(是一个特殊对象，可以调用方法\)。

    - $today：只获取当前日期 \(时间部分为零\)。

    - 常用操作：

      - \{\{ $now.toISO\(\) \}\}：输出标准 ISO 格式时间字符串，如 "2025-05-03T14:26:56.000Z"。

      - \{\{ $now.toFormat\("yyyy-MM-dd HH:mm:ss"\) \}\}：按指定格式输出，如 "2025-05-03 07:26:56" \(注意时区\)。

      - \{\{ $now.plus\(\{days: 1\}\).toISODate\(\) \}\}：获取明天的日期，如 "2025-05-04"。

  - **特殊节点变量** ：

    - **HTTP Request 节点** ：如果开启了自动分页 \(Pagination\)，可以用 $http.pagination 获取分页信息，比如用 \{\{ $http.pagination.nextPage \}\} 设置下一页的请求 URL。

    - **Function \(Code\) 节点** ：在这个节点里写 JS 代码时，可以直接用 $json, $node\(...\) 等变量，**不需要** 外面再套 \{\{ \}\}。这是 Function 节点的特殊之处。


### **5\. 社区插件与自定义表达式**

n8n 有活跃的社区，贡献了很多额外的节点（社区插件）。

  - **使用社区插件** ：这些插件提供了更多功能，但你从它们那里获取数据的方式，还是用我们上面讲的 Node 表达式，比如 \{\{ $\("社区插件节点名"\).item.json.输出的字段 \}\}。

  - **兼容性问题** ：有些老的社区插件可能没完全适配 n8n 最新的数据配对 \(Item Linking\) 机制。如果你用 $\("插件名"\).item... 获取数据时报错说找不到匹配项，可以试试：

    - 改用 $\("插件名"\).first\(\).json... \(只取第一条\)

    - 改用 $\("插件名"\).all\(\)\[0\].json... \(按索引取第一条\)

    - 联系插件作者更新。

  - **自定义表达式** ：n8n 本身不支持用户随意添加全局的自定义函数到表达式里。复杂的自定义逻辑，还是建议放到 Function 节点里实现。


## **常见报错与解决方法**

写表达式时，难免会遇到一些问题。这里列举几个常见的报错和处理方法：

  1. **错误信息："Referenced node is unexecuted" / "Can’t get data for expression"**

     - **含义** ：你想引用的那个节点，在当前流程路径下还没执行，所以获取不到它的数据。

     - **处理方法** ：

       - 检查节点连接顺序，确保引用的节点确实会先执行。

       - 如果是 IF/Switch 分支导致的，要么确保引用的节点在所有分支都会执行，要么用 $if 或 $\("节点名"\).isExecuted \(判断节点是否执行过\) 来处理可能不存在数据的情况。

       - 在编辑器里预览时看到这个错误，有时是正常的，因为还未实际运行，尝试执行一下工作流再看。

  2. **错误信息："Multiple matching items for expression" / "Can’t determine which item to use"**

     - **含义** ：你用了 .item 来获取数据，但 n8n 发现当前数据可能对应到来源节点的多条数据，它不知道该用哪一条。常见于数据经过合并 \(Merge\)、循环 \(Loop\) 或汇总 \(Aggregate\) 之后。

     - **处理方法** ：

       - **最常用** ：不再使用 .item，明确指定你要哪条。改用：

         - .first\(\)：获取第一条。

         - .last\(\)：获取最后一条。

         - .all\(\)\[索引\]：获取所有数据组成的数组，再指定索引取。

       - 检查工作流逻辑，看能否避免产生这种数据来源歧义。

  3. **错误信息："The ‘JSON Output’ in item X contains invalid JSON"**

     - **含义** ：你可能在一个需要填写标准 JSON 格式的字段里（比如 Set 节点的 JSON 模式），填写的表达式结果不是一个合法的 JSON。

     - **处理方法** ：

       - 确保你的表达式生成的是完整的、格式正确的 JSON。

       - 注意字符串值需要用引号包裹。如果表达式结果是对象，可能需要用 JSON.stringify\(\) 转换一下。

       - 用 JSON 校验工具检查一下生成的内容。

  4. **错误信息："Invalid syntax"**

     - **含义** ：你的表达式语法有错，比如拼写错误、符号用错、括号未配对等。

     - **处理方法** ：

       - 仔细检查 \{\{ \}\} 里面的内容。n8n 的表达式编辑器通常会标红错误的地方。

       - 对照教程里的例子，看看是不是格式写错了。

       - 复杂的表达式可以先拆分成几部分测试。

**排错小技巧** ：

  - 善用 n8n 的**节点输出预览** 功能，看看每个节点实际输出了什么样的数据结构。

  - 先写简单的表达式，测试成功了再逐步增加复杂逻辑。

  - 不确定时，查阅 n8n 官方文档 \(Expressions 部分\)。


## **表达式功能的版本演进（了解即可）**

n8n 一直在发展，它的表达式功能也经历了一些变化。了解这些可以帮助你看懂旧教程或者处理旧的工作流。

  - **关键变化：Item Linking \(成对项\)**

    - 大约在 0.2xx 版本后引入，1.0 版本成为核心。

    - 目标是让数据在复杂流程（如分支合并）中也能准确匹配来源。

    - 引入了新的推荐语法：$\("节点名"\) 配合 .item, .first\(\), .last\(\), .all\(\)。

  - **旧语法 \(不推荐\)** ：

    - $node\("节点名"\) 或 $node\["节点名"\]：按索引匹配，容易出错。

    - $item\("0"\) \(带引号的索引\)：已废弃。

    - .data 属性：现在统一用 .json。

  - **严格错误处理 \(v1.0+\)** ：

    - 以前表达式写错了，可能只是返回空值，流程继续运行。

    - 现在表达式出错会直接导致节点失败，流程中断（除非你设置了错误处理流程）。这更安全，但也要求你写表达式时更仔细。

**总之，现在编写新的工作流，尽量使用  $\("节点名"\).first\(\)... 或 $\("节点名"\).all\(\)\[...\] 这类新语法，避免使用方括号的 $node\["..."\] 和带引号的 $item\("..."\)。**


## **附录：常用表达式速查 & FAQ**


### **常用表达式速查表**

![](https://static.xiaobot.net/file/2025-05-03/720345/660324d3145905342666aacdc216c5ad.jpeg)


### **常见问题 \(FAQ\)**

  - **问：什么时候用 Expression，什么时候用 Function 节点？**

    - **答：**  简单的的数据传递、转换（比如取个值、拼个字符串、做个简单判断），用 Expression 更方便快捷。如果逻辑复杂（比如需要循环、多步计算、调用外部 API 等），或者一段逻辑需要在多处复用，那么用 Function 节点写 JS 代码会更清晰、易于维护。

  - **问：$\("节点"\)  和 $node\("节点"\) 到底有什么区别？我该用哪个？**

    - **答：**  $\("节点"\) 是新语法，推荐使用！它背后有智能的数据配对逻辑。通常配合 .first\(\), .last\(\), .all\(\) 或 .item 使用。$node\("节点"\) 是旧语法，它更像是直接按数据项的顺序（索引）去取，在复杂流程里容易出错。虽然还能用，但新项目请用 $\("..."\)。

  - **问：表达式里能用像 Lodash 或 moment.js 这样的外部 JS 库吗？**

    - **答：**  不能直接用。n8n 的表达式环境是沙盒化的，只内置了 Luxon \(处理日期时间，通过 $now 使用\) 和 JMESPath \(处理 JSON 查询，通过 $jmespath 使用\) 以及一些辅助函数。如果你需要用 Lodash 等库的复杂功能，应该在 Function 节点里写代码（可能需要配置 n8n 允许引入外部模块），然后把结果输出给后续节点用。

  - **问：为什么有时候我在表达式编辑器里看不到预览结果？**

    - **答：**  几个可能原因：1）引用的节点还没执行过，没有数据。2）表达式依赖于运行时的据配对（比如用了 .item），编辑器无法静态确定。3）表达式本身有错。对于第 2 种情况，即使预览是空的，实际运行时通常是正常的。最可靠的还是看实际执行的结果。

  - **问：如果一个字段可能不存在，表达式怎么处理才不会报错？**

    - **答：**  可以用默认值技巧：

      - \{\{ $json.maybeField || "默认值" \}\} \(JS 的或运算符\)

      - \{\{ $json.user?.address?.street || "地址未知" \}\} \(JS 的可选链操作符 ?.，推荐\)

      - \{\{ $ifEmpty\($json.maybeField, "默认值"\) \}\} \(n8n 内置函数\)推荐用可选链 ?. 或 $ifEmpty，语义更清晰。

  - **问：n8n 和 Make \(原 Integromat\) 的数据映射有什么不同？**

    - **答：**  Make 主要是点击式映射，在界面上拖拽数据“胶囊”到目标字段。它也有函数，但更像是填表格。n8n 主要是写表达式 \{\{ ... \}\}，更接近写代码。

      - **Make 优点** ：对非技术人员非常友好，直观。

      - **n8n 优点** ：极度灵活，几乎可以用 JS 做任何数据操作，对开发者友好。

      - **Make 缺点** ：复杂映射可能需要嵌套很多模块或函数，不一定比 n8n 简单。

      - **n8n 缺点** ：需要学习一点表达式语法，对完全不懂代码的人有门槛。总的来说，Make 更“可视化”，n8n 更“脚本化”。如果你需要精细控制数据处理，n8n 的表达式能力更强。

  - **问：能不能动态决定引用哪个节点的数据？比如根据条件用节点 A 或节点 B 的输出？**

    - **答：**  不能直接在 $\("..."\) 或 $node\("..."\) 的括号里用变量来动态指定节点名，比如 $\($json.sourceNodeName\) 是不行的。节点名必须是写死的字符串。实现这种需求，通常做法是：

      - 用 IF 或 Switch 节点把流程分开，各自处理后或合并。

      - 在 Function 节点里用 JS 的 if/else 语句来判断条件，然后从不同的 $node\("A"\)... 或 $node\("B"\)... 里取值，最后输出统一的结果。

  - **问：表达式能引用后面步骤节点的数据吗？**

    - **答：**  不能。n8n 的数据是按流程顺序流动的，一个节点只能引用它前面已经执行过的节点的数据，无法“预知未来”。如果你的逻辑需要后面步骤的结果，那很可能需要重新设计流程的顺序。


### 结语

至此，翔宇带领大家全面梳理了 n8n 中的数据映射体系：固定值的简易直观、表达式的灵活多样，以及从 Node、Item 到 JMESPath、全局变量的深度拆解；还覆盖了常见报错排查与版本演进脉络。掌握这些要点后，读者将不再因字段路径或索引混乱而停滞，而能像翔宇一样，以数据驱动的思维，从容应对各类复杂场景。愿这篇超详细教程，成为你在 n8n 自动化之路上的可靠参考，助力更多高效创新的工作流落地！

![](https://static.xiaobot.net/file/2025-05-03/720345/9b73a6a472772542b36671a8aed51252.png)