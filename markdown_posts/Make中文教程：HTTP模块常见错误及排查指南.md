---
title: "Make中文教程：HTTP模块常见错误及排查指南"
created_at: "2024-08-23T13:57:33.000000Z"
uuid: "b2dc546e-3d1d-47b9-b3c4-0ff48e1c32e7"
tags: ['Make基础教程']
---

# Make中文教程：HTTP模块常见错误及排查指南

**标签**: Make基础教程

我是翔宇工作流的小宇，今天将为大家分享在使用Make.com的HTTP模块时可能遇到的常见错误及其解决方法。通过以下建议，你将能够更顺畅地使用Make.com来处理API调用。


# 1\. URL错误

**错误类型1：** URL拼写错误

解决方法：

仔细检查URL，确保拼写正确，避免因输入错误导致请求失败。处理长URL或复杂路径时，特别要注意每个字符的准确性。

**错误类型2：** URL格式不正确

解决方法：

确保URL格式符合标准，包括协议（如http或https）、域名、路径和参数。检查是否使用了正确的协议，避免遗漏://或路径中的斜杠。可以将URL复制到浏览器中验证格式是否正确。

**错误类型3：** 资源不存在

解决方法：

对照API文档，确认资源路径是否正确。可以通过在浏览器中直接访问该URL来验证资源是否存在。如资源确实不存在，检查是否访问了正确的环境（如生产环境或测试环境）。

**错误示例代码：**

Error: 404 Not Found

\{“code”:5,“error”:“url.not\_found”,“message”:“没找到对象”,“method”:“POST”,“scode”:“0x5”,“status”:false,“ua”:“Make/production”,“url”:”/v1/chat/completion”\}

解决方案：

1\. 检查并修改API端点，确保URL路径与API文档一致。如API文档更新，请及时调整请求URL。

2\. 确保使用正确的HTTP方法（如POST或GET），避免因方法错误导致请求失败。POST和GET的使用要与具体提供服务的API文档规定的方法一致。


# 2\. 认证错误

**错误类型1：** API密钥错误

解决方法：

确保使用的API密钥正确无误，且没有过期并具备必要的访问权限。尝试重新生成API密钥或通过API管理平台检查密钥的状态，确认其是否有效。

**错误类型2：** 用户名或密码错误

解决方法：

仔细核对输入的用户名和密码，确保正确无误并具备访问API的权限。确保没有多余的空格或错误的字符编码，特别是在自动化脚本中更要注意。

**错误类型3：** OAuth Token过期

解决方法：

重新获取有效的OAuth Token，并确保在HTTP请求中正确使用该Token。检查Token的有效期，并在Token即将过期时提前刷新。

**错误示例代码：**

Error: 401 Unauthorized

\{“error”:\{“message”:“Invalid Authentication”,“type”:“invalid\_authentication\_error”\}\}

解决方案：

1\. 确保API密钥正确，检查API提供方是否有足够的授权Token，并且API密钥未被撤销或过期。

2\. 确认是否已授予相关服务的访问权限，且权限范围足够。

3\. 检查API请求中是否存在不合理的空格或拼写错误，尤其是复制粘贴时可能引入的空格。

4\. 确保API密钥前添加了正确的前缀Bearer（如API文档中所示），并对照API文档进行调整。


# 3\. 请求参数错误

**错误类型1：** 参数名称错误

解决方法：

对照API文档，确认所有请求参数名称拼写正确，避免因名称错误导致请求失败。确保参数名称大小写与API文档保持一致。

**错误类型2：** 参数类型错误

解决方法：

检查并确保每个请求参数的类型与API文档要求一致。例如，字符串、数字或布尔值应与预期类型匹配。若文档要求整数类型，确保不要传递小数或字符串。

**错误类型3：** 参数值错误

解决方法：

确保请求参数的值在API文档规定的有效范围内，并且值的格式正确。特别是在传递枚举值时，确保值与文档中的选项完全匹配。

**错误示例代码：**

Error: 401 Unauthorized

\{“error”:\{“message”:“Incorrect API key provided”,“type”:“incorrect\_api\_key\_error”\}\}

解决方案：

1\. 仔细检查API请求中的参数名称和类型，确保与API文档一致。修改错误的参数名称或类型，并重新发送请求。如果API文档更新，确保参数设置与最新文档保持一致。

2\. 检查是否在Headers和Query String中存在混用，正确设置即可解决。


# 4\. 服务器端错误

**错误类型1：** 服务器内部错误

解决方法：

查看服务器日志或联系服务器管理员，获取详细错误信息，了解问题根源。可能是服务器代码或配置问题，特别是大规模请求时容易出现。

**错误类型2：** 服务不可用

解决方法：

检查服务器运行状态。如果服务暂时不可用，稍后重试请求。可以通过服务状态页面或第三方监控工具检查服务可用性。

**错误类型3：** 请求超时

解决方法：

考虑增加请求超时时间，或优化请求内容，减少不必要的数据传输，以提高请求响应速度。你可以在请求配置中增加超时时间，或将请求拆分为多个小请求。

错误示例代码：

遇到一些图片生成的模块涉及长时间等待，可能会出现工作流停滞在HTTP模块不进行下一步运行。

解决方案：

1\. 使用Sleep模块手动添加睡眠时间，为工作流运行预留充足时间。

2\. 在HTTP的高级设置中将Timeout设置为300，为模块运行预留足够时间。


# 5\. 其他常见错误

**错误类型1：** HTTP状态码错误

解决方法：

了解HTTP状态码的含义，并根据具体的错误码对请求进行调整。例如，4xx错误通常与客户端请求有关，5xx错误则多为服务器问题。

**错误类型2：** 编码错误

解决方法：

确保请求和响应的编码一致，特别是在处理特殊字符时，注意正确的编码方式，避免出现乱码或解析错误。

**错误类型3：** 返回数据聚集在一起，无法映射利用

解决方法：

HTTP模块的Parse response未打开，没有将返回的JSON数据进行解析，打开即可映射返回数据。


# 6\. JSON解析错误

**错误类型1：** JSON格式错误

解决方法：

使用JSON格式验证工具，确保请求的JSON格式正确，没有语法错误或不必要的符号。

**错误类型2：** JSON数据类型不匹配

解决方法：

确认JSON字段的数据类型与API文档中的要求一致，例如字符串、数字或布尔值。

**错误类型3：** JSON字段缺失或冗余

解决方法：

对照API文档，添加缺失的字段或删除多余的字段，确保JSON结构与API预期一致。

**错误示例代码：**

Error: 400 Bad Request

\{“error”:\{“message”:“Invalid request: invalid character ‘\n’ in string literal”,“type”:“invalid\_request\_error”\}\}

解决方案：

1\. 在JSON的请求内容中，可能存在不正确的换行符和影响JSON结构的特殊符号。需要特别注意的是，有些换行符可能肉眼无法察觉。此外，在添加映射数据时，也可能会出现不合理的换行符和符号。为了处理这些问题，可以使用文件解析器来删除这些不需要的符号和换行符。具体的删除公式是 \[”\{\}\\\n\]，你可以参考视频《7-Kimi API实操办公自动化：Make工作流自动总结PDF文档》中介绍的方法来解决这些问题。

2\. 你可以使用 <https://jsonformatter.org/> 这个网站对JSON格式进行检查和处理，以便发现其中不正确的内容和符号。此外，这个网站还可以帮助你对JSON进行格式化，使其更加美观，便于阅读。

解决问题的通用步骤

1\. 查看错误信息：仔细阅读错误信息，从中获取线索。

2\. 检查配置：检查HTTP模块的配置，包括URL、方法、请求头和请求体等。

3\. 对照API文档：确认请求的格式和参数是否符合API文档的要求。

4\. 调试：逐步调试你的流程，定位问题所在。

5\. 搜索解决方案：在社区或论坛上搜索类似问题的解决方法。

常见调试技巧

• **运行日志分析：** 根据Make运行记录逐一检查请求和响应的数据。

• **缩小范围：** 逐步简化请求，定位问题所在。

注意

• **细心：** 计算机代码是一个精细的工作，任何一个细小的错误都会造成错误，细心检查可解决90%的错误。

• **及时保存：** 在进行任何运行之前，建议先保存你的工作流，如果遇到工作流停滞未保存会造成前期设置丢失。

• **寻求帮助：** 如果问题仍然无法解决，可以寻求提供API服务的社区或官方支持。

这些错误代码是以Kimi API请求进行示例，不同服务的错误可能有所不同，但排查方法类似。

希望以上内容能够帮助你在工作流中更好地解决Make.com HTTP模块中常见的错误，顺利完成API调用的任务。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/d6c354528e9cd448b3b033c07785267b.png)