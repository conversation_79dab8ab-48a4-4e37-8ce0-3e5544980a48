---
title: "Make14.爆款提示词工厂：Make自动化生成高质量写作提示词"
created_at: "2024-08-26T05:08:49.000000Z"
uuid: "6fbea6cc-58b9-43a5-b20f-6c301226c143"
tags: ['资源包']
---

# Make14.爆款提示词工厂：Make自动化生成高质量写作提示词

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/14-make-high-quality-prompt-factory/>


# 视频资源

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载（推荐下载最新版本使用）**

**2024年8月26日版本** （修改https://segment.jina.ai/网址）

[工作流下载](<https://pan.quark.cn/s/a866ce641478>)

**2024年9月5日版本** （添加文本解析器解决openrouter模块报错）

[工作流下载](<https://pan.quark.cn/s/7e26cc6c2f80>)

**2024年10月8日版本** （添加Create JSON模块解决可能得报错）

[工作流下载](<https://pan.quark.cn/s/0b5d1c3eba27>)

提取码：iHJ7

由于模板无法导入数据结构，现将2024年10月8日版本用到的数据结构都保存在以下金山文档之中直接复制粘贴即可。使用方法参照视频16介绍的流程，在JSON模块中的Create JSON模块中新建选择Generate，将数据结构粘贴进去生成结构保存即可。

[JSON数据结构](<https://kdocs.cn/l/crpBLhlQRioQ>)

**3\. Notion数据库模板**

点击下方链接即可获取Notion 数据库模板复制到自己的工作空间：

[爆款文章分析](<https://xiaoyuzaici.notion.site/61196bef5f5e400da0145ddd98c741b7?v=d552cc8618f04d1e8999260bb98deb8c&pvs=4>)

[爆款文章提示词](<https://xiaoyuzaici.notion.site/d1f359e691e54e4eb07336dd80c0bf4b?v=7b143eebf39942298b4250953910b409&pvs=4>)

[爆款文章写作](<https://xiaoyuzaici.notion.site/51b54a208e6345538714ba192210c547?v=cad30db61e5c4f2dafd9091c140fbe1d&pvs=4>)


# **可能运行报错与解决方案：**

**1.运行Token计算模块（**[**https://segment.jina.ai/**](<https://segment.jina.ai/>)**）报错（模块37）：**

Error: 400 Bad Request

\!DOCTYPE html

html lang="en"

解决方案：

使用Create JSON模块新建JSON数据机构进行映射使用参照视频16介绍的流程。（10月8日版本方案）

**2.EXA模块出现如下错误：**

Error: 400 Bad Request

\{"requestId":"66d9ab1100000","error":"Validation error: String must contain at least 1 character\(s\) at \"query\""\}

代表在爆款文章写作数据库中没有素材开始，则无法输入检索信息。

解决方法：直接选择一个主题将状态选项选到开始。

**3.Openrouter生成文章的模块也出现JSON错误：**

提示词存在空行等影响JSON结构的问题。

输入素材的内容存在影响JSON结构的问题。

解决方案：

使用Create JSON模块新建JSON数据机构进行映射使用参照视频16介绍的流程。（10月8日版本方案）

**4.如果出现运行完成运行，并未在正确的位置生成数据：**

可能是由于第三行生成文章的工作流出现顺序颠倒，解决方案讲下面标注的两个模块顺序放置正确，第一个是主题，第二个是提示词。

![](https://static.xiaobot.net/file/2024-09-07/720345/4ec6b9b841d2ca80debef5d02e460262.png)


# **相关资源：**

**爆款写作理论（如需复制请访问如下金山文档）：**

[爆款写作理论](<https://kdocs.cn/l/ct3M0UvlrRgg>)

**视频配套PPT：**

<https://gamma.app/docs/Make-fdje7ns8sda8gfk>

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭


# **视频介绍**

本文介绍如何使用Make \(make.com\) 自动化生成爆款文章写作提示词，并结合API抓取热点信息及素材，最终实现爆款文章的自动生成。

**主要内容:**

**工作流介绍:**

  - 本工作流通过分析爆款文章的写作风格，利用Make大模型多轮对话的能力生成高质量的写作提示词。

  - 基于生成的提示词，通过API抓取新闻热点，获取写作素材，最终自动生成爆款文章。

  - 工作流包含33个模块，分为三部分：

  - 生成爆款写作提示词。

  - 通过API抓取热点信息。

  - 基于提示词和热点信息抓取素材并生成文章。

**Notion数据库:**

  - 需要创建三个Notion数据库：

  - 存储爆款文章，用于分析写作风格。

  - 存储生成的写作提示词。

  - 存储API抓取的热点信息和生成的爆款文章。

**高质量写作提示词生成背后的逻辑:**

  - 分析爆款文章写作风格:从标题、开头、结尾、结构、语言风格、修辞手法等15个方面分析爆款文章的写作特点。

  - "首先就是标题，爆款文章它是怎样对标题进行开头的？那它适用的什么方式？是直接式，例如这个2024年全球经济展望，还是问题式，为什么要关注气候变化，还是我们的比喻式，创新生命，企业的生命线，还是我们悬念式的，呼吁式的，拯救题就从我做起，还是悬念式的，现在爆款最多的就是悬念式的，说他在黑夜里看到了什么，等等还是风向式的，还是数字式的，描述的引用的，对比的幽默的，所以说，我们就总结一下，爆款文章现在用的比较多的，他是哪种标题，我怎么样来通过这种爆款文章，来总结出他的一个标题的一个方法，那这是其中的一个点。"

  - 反向生成写作指令:根据分析出的写作风格，反推生成相应的写作指令，例如标题设计、开头写作、结构编排等。

  - 多指令汇总:将多个基础指令汇总，整合相似内容，合并重复的，确保表达一致性，并融入一些独特的元素和案例，最终生成完整且高质量的写作提示词。

**Make工作流实操演示:**

  - 视频中详细演示了使用Make搭建工作流的步骤，包括模块选择、参数设置、连接等，并对过程中遇到的错误进行了解释和解决。

  - 演示了如何使用Make的多轮对话能力，实现更丰富的上下文内容，从而提升生成效果。

**Make工作流特点:**

  - 专业性:相比简单的写作流程，Make工作流更加专业，能够更好地保证文章质量。

  - "就说我们工作流的质量，是由工作流的专业程度来决定的，尽管我们工作流程，会成倍的增加我们的工作量，但是它的效果是非常好的。"

  - 清晰的提示词:高质量的提示词是工作流的关键，需要清晰定义目标效果和实现流程。

  - "那么这个提示词，就是说就很重要了，你需要定义它一个达到的效果，它里面实现的一个流程，所以说，我们就是说从好的效果需求出发，需要我们流程必须专业，那么这些流程专业最终落脚到哪，就会落脚到我们指令去清晰，所以说，我们需要生成这样一个高质量的，一个提示词，这是我们进行每一个工作流，都首先需要完成的一个工作点。"

  - 可单独使用:工作流的每个部分（写作、热点抓取、指令生成）都可以拆开，形成三个独立的工作流。

**总结:**

本文详细介绍了使用Make自动化生成爆款文章写作提示词的方法，并结合API抓取热点信息及素材，最终实现爆款文章的自动生成。通过学习本文，读者可以了解Make工作流的特点和优势，以及如何利用Make提升写作效率和文章质量。