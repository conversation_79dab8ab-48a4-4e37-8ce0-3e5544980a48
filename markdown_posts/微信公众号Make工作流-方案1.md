---
title: "微信公众号Make工作流-方案1"
created_at: "2024-09-29T12:02:59.000000Z"
uuid: "36df6fc9-c215-499c-b371-c571026f5663"
tags: ['会员独家']
---

# 微信公众号Make工作流-方案1

**标签**: 会员独家

今天，翔宇工作流通过一个简单的示例演示了如何使用 Make 实现微信公众号文章的采集与获取。由于自媒体运营涉及多个环节的配合，我倾向于将复杂的工作流拆分，以提升工作流的运行效率，避免单个模块的错误导致整体工作流的资源浪费。因此，建议大家将复杂工作流模块化处理。比如，自媒体创作流程可以拆分为：文章采集、选题筛选、内容加工、文章发布等多个独立的工作流。这其实是对传统媒体选、编、发流程的复刻。未来，翔宇工作流会发布更多相关流程在小报童平台上，供大家参考。

获取微信公众号订阅地址的方法如下：

<https://xiaobot.net/post/d7e45c3b-c381-429b-be3e-6738b28849f5>


# **工作流简介：**

通过“**今天看啥”** 平台，付费之后在平台上获取RSS订阅源之后实现文章的高效采集与保存。所以使用的前提是去今天看啥注册付费后、选择自己希望采集的公众号，然后获取相应的RSS订阅地址。

工作流下载地址：

微信采集工作流1-今天看啥方案.json

链接：<https://pan.quark.cn/s/a5ec619e705b>

Notion数据库：

[https://xiaoyuzaici.notion.site/110017a905fd8091a9eef638020fecdf?v=110017a905fd81eb8092000ce64af00e&pvs;=4](<https://xiaoyuzaici.notion.site/110017a905fd8091a9eef638020fecdf?v=110017a905fd81eb8092000ce64af00e&pvs=4>)


# **工作流使用说明：**

1\. 该工作流通过 RSS 订阅源来更新文章。如果需要聚合多个微信公众号的内容，可以在 RSS 阅读器（如 Inoreader 和 Feedly）中分别订阅不同的公众号链接，并通过文件夹将多个订阅源合并为一个分类。在 Make 中，使用 Inoreader 或 Feedly 模块来获取并保存这些聚合的内容。

2\. 由于“今天看啥”输出的 RSS 订阅源包含 XML 格式的全文数据，利用 HTML 转 Markdown 模块可以将文章完整保存到 Notion，方便后续的内容加工和使用。

3\. Notion Search Objects 模块用于检索链接。如果返回的数据为空，表示该文章尚未保存过，可以进行去重后的保存。

4\. 该工作流还可以集成大模型进行进一步筛选，包括自动剔除广告、提取文章主题关键词、统计字数、分析内容等。用户可以根据自身需求灵活设置和调整。

5.由于是日常运行的工作流，所以该工作流推荐简洁一些，不要有过多的模块，以节省操作数。

通过模块化的设计，这套工作流不仅提高了效率，还确保了各个环节的独立性和灵活性，避免了单点故障对整体流程的影响。

**注意事项：**

由于第三方服务可能存在不稳定性，可能随时失效，建议会员采取小额充值、按需使用的策略，避免大额充值带来的资金浪费。此外，务必注意保护个人信用卡信息，防范网络诈骗或数据泄露风险。

另外，由于属于第三方服务，出现的任何使用问题与不稳定情况，可到相关网址进行解决方案的获取。

本文分享的所有数据获取方法和工具推荐仅供学习、交流和了解数据获取途径之用，禁止用于任何商业用途。会员在使用这些工具时应严格遵守相关法律法规，确保使用行为的合法性。

![](https://static.xiaobot.net/file/2024-09-29/720345/e1bf6ad4ebad782bbb053a7a777bf4bd.png)