---
title: "Make中文教程：如何恢复一个之前的场景版本（How to restore a previous scenario version）"
created_at: "2024-09-21T15:08:04.000000Z"
uuid: "91b66074-7cd2-4e09-90fd-79eae33a0914"
tags: ['Make基础教程']
---

# Make中文教程：如何恢复一个之前的场景版本（How to restore a previous scenario version）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于如何恢复旧版场景的教程。

**教程简介** ：本教程介绍了如何在Make.com中恢复之前版本的Scenario，通过详细步骤指导用户选择和恢复历史版本。

**学习目标** ：掌握如何在Make.com中找到并恢复之前的Scenario版本，确保在 Scenario 出现问题时能够快速回滚到稳定版本。

**学习内容** ：

  - 找到Scenario编辑器

  - 访问历史版本菜单

  - 选择并恢复特定版本

  - 确认和手动保存恢复后的版本

**概念理解** ：在Make.com中，Scenario指的是创建和管理自动化流程的环境。恢复历史版本可帮助用户回滚到之前的工作状态，确保流程稳定。

**任务练习** ：打开一个Scenario，按照教程步骤进入历史版本菜单并尝试选择恢复某个版本，然后手动保存恢复后的版本。


# **如何恢复一个之前的场景版本**

  1.  打开您的场景。

  2. 在场景编辑器底部点击有三个点的图标。

  3. 从菜单中选择"上一版本"。

![](https://static.xiaobot.net/file/2024-11-23/720345/27e40fea23155a9c1f32209b1074c468.png)
  4. 单击"选择版本"占位符以打开以前版本的列表。

  5. 选择您想要恢复的版本。

![](https://static.xiaobot.net/file/2024-11-23/720345/7ed88299fec7142540cc574b8a80277d.png)
  6. 点击"确定"按钮以确认:

![](https://static.xiaobot.net/file/2024-11-23/720345/fce0bb64b24ab0253444e45aeaf57504.png)
  7. 所选场景版本将被恢复。

![](https://static.xiaobot.net/file/2024-11-23/720345/2a7480245596d9ac517142a44b41d10b.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-23/720345/ce4c75f0bd2da03e6e2f8004bab77399.png)