---
title: "Make中文教程：数据结构（Data structures）"
created_at: "2024-09-19T12:41:16.000000Z"
uuid: "c138f529-258d-42fe-a054-97a756a5ab10"
tags: ['Make基础教程']
---

# Make中文教程：数据结构（Data structures）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于数据结构的教程。

**教程简介** ：本教程介绍了数据结构在Make.com中的作用，包括如何定义和使用数据结构文档。通过使用数据结构生成器，可以自动生成数据结构并加以修改。

**学习目标** ：了解数据结构的定义和作用，学会使用数据结构生成器，根据数据示例自动生成并修改数据结构，提升对数据格式的理解和管理能力。

**学习内容** ：

  1. 什么是数据结构及其重要性。

  2. 如何添加新的数据存储和创建数据结构。

  3. 使用数据结构生成器自动生成数据结构。

  4. 手动修改生成的数据结构。

**概念理解** ： 数据结构是描述传输到Make.com的数据格式的文档，常用于序列化/解析JSON、XML、CSV等格式。数据结构生成器是一种工具，通过数据示例自动创建数据结构文档。

**任务练习** ：

  1. 添加一个新的数据存储并创建一个数据结构。

  2. 使用数据结构生成器，提供一个JSON数据示例并生成相应的数据结构。

  3. 手动修改生成的数据结构，确保其满足使用需求。

我来帮您翻译这段内容,用简单易懂的语言解释:

我来帮您翻译这段内容,用简单易懂的语言解释:


# **数据结构**

数据结构是用于详细描述数据格式的,它告诉 **Make**  平台数据应该如何传输。通过这个文档,**场景编辑器** 就能够知道每个模块会返回或接收什么样的数据。数据结构文档最常用于处理 JSON、XML、CSV 等数据格式的序列化和解析。


# **数据结构入门指南 🔰**


## **什么是数据结构？**

数据结构就像是一个说明书，它告诉 **Make**  平台：

  - 数据应该长什么样

  - 数据包含哪些内容

  - 数据如何组织

简单来说，它就像是给数据定规则，这样系统就知道怎么正确处理这些数据了。


## **主要用途 🎯**

数据结构最常用来处理以下几种格式的数据：

  - JSON 格式

  - XML 格式

  - CSV 表格数据

  - 其他常见数据格式


## **创建方式 ✍️**


### **方法一：手动创建**

当您[添加新的数据存储](<https://www.make.com/en/help/tools/data-store#setting-up-the-data-structure>)时，可以自己定义数据结构。


### **方法二：使用生成器（推荐新手使用）👍**

  1. 准备一个数据样本

  2. 把样本输入到生成器中

  3. 系统自动生成数据结构

  4. 如果需要，您还可以手动调整生成的结果


## **小贴士 💡**

  - 不用担心不会创建，系统提供的生成器可以帮您自动完成大部分工作

  - 生成后的结构是可以修改的，您可以根据需求调整


## **图示说明**

下面是数据结构生成器的界面示例：

![](https://static.xiaobot.net/file/2024-11-18/720345/71140f25d2d143b4959d507552ce4285.png)


## **小结 📝**

  - 数据结构就是数据的规则说明书

  - 可以手动创建，也可以用生成器自动创建

  - 对于新手来说，建议先使用生成器，再根据需要修改

  - 这样可以既省时间，又能学习数据结构的写法


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/f8a20222356ebe3afef6ceaea3eb6fc3.png)