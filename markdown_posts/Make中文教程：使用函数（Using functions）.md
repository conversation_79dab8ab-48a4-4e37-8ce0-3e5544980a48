---
title: "Make中文教程：使用函数（Using functions）"
created_at: "2024-09-19T07:46:10.000000Z"
uuid: "4c23ecfc-5344-4df3-988b-9ceab7b71a17"
tags: ['Make基础教程']
---

# Make中文教程：使用函数（Using functions）

**标签**: Make基础教程

# **Make平台函数使用完全指南**

**翔宇工作流** 今天为大家带来关于使用映射函数的教程。

**教程简介** ：

欢迎来到翔宇工作流的教程！今天我们将向大家介绍如何在 Make.com 中使用各种映射函数。这些函数与 Excel 或编程语言中的函数类似，能够帮助你在自动化工作流中进行复杂的数据运算和转换。

**学习目标** ：

通过本教程，您将学会如何在 Make.com 中使用函数来处理数据。您将掌握如何编写复杂的公式，理解常见函数类型，并学会如何进行数据格式的转换。

**学习内容** ：

  - 如何将函数插入到字段中

  - 使用 Make.com 提供的常用函数

  - 通过具体示例（如 substring\(\) 函数）学习函数使用方法

  - 使用 Google Sheets 函数进行数据处理

**概念理解** ：

  - **映射数据** ：将一个模块的输出数据传递到其他模块。

  - **函数** ：用于数据处理和转换的工具，类似于编程语言中的函数。

  - **数据映射字段** ：模块中用于输入数据的区域，可以在其中插入函数。

  - **模块** ：Make.com 中独立的功能单元，如 Google Sheets 或 Twitter 模块。

**任务练习** ：

  1. 尝试将一个日期格式从"YYYY-MM-DD"转换为"DD/MM/YYYY"格式。

  2. 使用substring\(\)函数将文本限制为指定字符数，例如将一段文字裁剪为前100个字符。

  3. 通过Google Sheets模块创建一个新电子表格，并使用SUM函数计算两数之和。


## **函数是什么？**

想象函数就像一个神奇的数据加工厂：你输入一些原始数据，函数就会按照特定规则将其转换成你需要的格式。如果你用过Excel，那理解起来就更容易了 —— Make平台的函数与Excel函数非常相似！

> 小贴士：函数就像是一个个小帮手，可以帮你：
> 
>   - 把文字转换成大写
> 
>   - 裁剪文本长度
> 
>   - 调整日期格式
> 
>   - 还有很多其他神奇的转换！
> 
> 


## **如何在字段中使用函数？**


### **打开"函数工具箱"**

当你点击一个字段时，会弹出映射面板，这就是你的"函数工具箱"：

![](https://static.xiaobot.net/file/2024-11-20/720345/35e7ebb201f46c160cb27cdc284753ff.png)


### **工具箱里都有什么？**

  - **第一个标签页** ：显示可以从其他模块映射的项目

  - **其他标签页** ：各种实用函数

    1. 常用函数（General functions）

    2. 数学函数（Math functions）

    3. 文本处理函数（Text and binary functions）

    4. 日期时间函数（Date and time）

    5. 数组处理函数（Array functions）

    6. 自定义函数（Custom functions）

    7. 自定义和系统变量（Custom and system variables）

> 使用技巧：可以直接点击或拖拽函数到字段中使用


## **实战案例：Twitter文字长度控制**


### **场景描述**

想象你需要发送推文，但是内容可能超过Twitter的280字符限制。这时候就需要一个"自动裁剪机"！


### **解决方案**

使用 substring\(\) 函数来限制文本长度：

![](https://static.xiaobot.net/file/2024-11-20/720345/09099cf209d4f4754cc9f97da6951936.png)


## **进阶技巧：借用Google Sheets的强大功能**


### **为什么要用Google Sheets？**

如果你发现Make平台没有你需要的特定函数，但Google Sheets有，你可以"借用"Google Sheets的计算能力！


### **操作步骤**

  1. **准备工作**

     - 在Google Sheets创建一个新的空白表格

     - 在Make打开你的场景

  2. **设置更新单元格**

     - 添加 Google Sheets > Update a cell 模块

     - 选择刚创建的表格

     - 在Value字段输入你的公式

![](https://static.xiaobot.net/file/2024-11-20/720345/4252dcf5e2f40d36050896f46ee04815.png)

  3. **获取计算结果**

     - 添加 Google Sheets > Get a cell 模块

     - 配置相同的单元格ID

![](https://static.xiaobot.net/file/2024-11-20/720345/53b94af88d296f042b35001abf322004.png)


## **使用建议**


### **1\. 选择合适的函数**

  - 优先使用Make内置函数（处理速度更快）

  - 复杂运算才考虑使用Google Sheets


### **2\. 性能优化**

  - 避免过多嵌套函数

  - 注意数据类型的转换


### **3\. 调试技巧**

  - 使用简单数据测试函数

  - 分步骤验证复杂公式


## **常见问题解答**

  1. Q: 函数执行结果不符合预期？ A: 检查函数参数顺序和数据类型

  2. Q: Google Sheets函数很慢？ A: 考虑使用Make内置函数替代

  3. Q: 字符串处理出现乱码？ A: 检查文本编码格式


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-20/720345/5aea372c22127456f9356ec68cfe2790.png)