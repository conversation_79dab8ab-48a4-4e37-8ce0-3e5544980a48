---
title: "Make中文教程：Make中的错误类型（Types of errors in Make）"
created_at: "2024-08-30T01:52:03.000000Z"
uuid: "ef4415dc-2e7d-477a-a88e-7e8f3dbff588"
tags: ['Make基础教程']
---

# Make中文教程：Make中的错误类型（Types of errors in Make）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Make错误类型的教程。

**教程简介** ：本教程介绍了Make.com中不同类型的错误及其处理方法，包括AccountValidationError、BundleValidationError、ConnectionError等，帮助用户理解和管理这些错误。

**学习目标** ：学习识别和处理不同类型的错误，确保Make.com场景的稳定运行，提升自动化流程的可靠性和效率。

**学习内容** ：

  1. 各类错误类型及其触发条件。

  2. 错误的HTTP状态代码关联。

  3. 错误处理方法和解决方案。

**概念理解** ：

  1. **AccountValidationError** ：因凭据问题导致的错误。

  2. **ConnectionError** ：第三方应用不可用时的连接错误。

  3. **DataError** ：模块发送的数据未通过第三方验证。

  4. **RateLimitError** ：API请求超过速率限制。

**任务练习** ：

  1. 识别一个场景中的ConnectionError并尝试修复。

  2. 创建一个导致DataError的错误场景，并使用错误处理器解决。

  3. 针对一个频繁出现的RateLimitError，尝试不同策略进行优化并记录结果。


# **Make 中的错误类型**

在一个场景中,可以有多个模块和项目映射。 它们共同创造了你的场景。 这些元素中的每一个都可以创造一个意外事件 -- 一个错误。 根据它们的起源和原因,将错误分类。

例如,当应用程序由于第三方维护或停机而无法使用,而 Make 无法连接到应用程序服务器时,就会出现 ConnectionError 。

当模块返回错误时，Make 会用"注意"标志突出显示该模块。要检查错误类型，请单击模块上方的思维泡泡。

![](https://static.xiaobot.net/file/2024-12-02/720345/6bb80e0ffed7cecaf723040e21d3fe7b.png)![](https://static.xiaobot.net/file/2024-12-02/720345/fdd45cda467424dcf7b4ba2c91ae96b3.png)


## **Account Validation Error**

当 Make 无法在第三方应用程序中验证您时,一个模块会输出 AccountValidationError 。例如,当您在应用程序中更改凭据或凭据过期且您未在连接中更新它们时,应用程序模块将输出 AccountValidationError 。

AccountValidationError 也出现在 HTTP 状态码 401 和 403 中。

如果模块输出 AccountValidationError ，该情况会以错误结束。当您的情况以连续错误次数结束时，Make 会禁用对您情况的调度。

![](https://static.xiaobot.net/file/2024-12-02/720345/a319e01dc2309e32b1f3d57808eb37f4.png)

如要解决 AccountValidationError ，请检查应用程序中的凭据和 Make 中的连接。如有需要，为应用程序创建新的连接。如果经常出现 AccountValidationError ，请联系我们的支持。


## **Bundle Validation Error**

当进入模块的 bundle 不通过验证时,模块会输出 BundleValidationError 。验证意味着在处理模块中的 bundle 之前,Make 会检查模块映射中的数据类型是否匹配,以及模块必填字段是否有缺失值。

BundleValidationError 也会出现在 HTTP 状态码 400 和 404 中。

例如，当你将文本映射到需要日期的模块设置字段时，或者将空值映射到模块设置中的必填字段时，你会得到 BundleValidationError 。

如果模块输出 BundleValidationError 且没有错误处理,该场景将以错误结束。当您的场景以连续错误次数过多而以错误结束时,Make 会禁用该场景的调度。

处理此错误的最佳方式是在模块设置中查看您的映射。

关于如何处理场景中缺失数据的提示,请查看关于如何修复缺失数据错误的文章。


## **Connection Error**

当第三方应用程序不可用时,模块会输出 ConnectionError 。例如,第三方服务可能由于维护而离线。此错误使用 HTTP 502、503 和 504 状态代码。

如果模块输出 ConnectionError 而没有错误处理,该场景将以错误结束。Make 会重复该场景运行,并增加时间延迟。

要修复 ConnectionError ，请查看关于修复 ConnectionErrors 的文章。


## **Data Error**

当模块发送的数据未通过第三方的验证时，该模块会输出 DataError 。例如，当您尝试使用 Twitter >创建推特模块创建一条超过 280 个字符的推特时，创建推特模块会输出 DataError ，因为推特的最大长度为 280 个字符。

另一种情况是当您将不正确的数据类型映射到函数时会出现 DataError 。例如,当您将 text 数据类型的数据映射到 parseDate 函数时。

如果模块输出 DataError 且没有错误处理,该场景将以错误结束。当您的场景以连续错误次数过多而以错误结束时,Make 会禁用该场景的调度。

要修复 DataError 错误,请检查您的映射并确定错误发生的原因。如果无法通过不同的映射来修复错误,您可以使用恢复和忽略错误处理程序。


## **Data Size Limit Exceeded Error**

当您耗尽数据传输配额时,一个模块会输出 DataSizeLimitExceededError 。您的数据传输限制是从操作限制计算得出的。您可以在定价计划概览中检查限制计算。

如果模块输出 DataSizeLimitExceededError 且没有错误处理,该场景将以错误结束。因为 DataSizeLimitExceededError 是致命错误,Make 立即禁用场景调度,无论连续错误的数量。

要修复 DataSizeLimitExceededError ，请考虑购买额外的操作或升级您组织的订阅计划。


## **Duplicate Data Error**

当您向不允许重复的模块发送相同的数据时，该模块会输出 DuplicateDataError 。例如，当您尝试在应用程序中创建新用户时，用户的电子邮件地址必须是唯一的，但该电子邮件地址已被使用。该模块会输出 DuplicateDataError ，因为该电子邮件地址已被另一个用户注册。

如果模块输出 DuplicateDataError 且没有错误处理,该场景将以错误结束。当您的场景以连续错误次数过多而以错误结束时,Make 会禁用该场景的调度。

如果在您的场景中出现 DuplicateDataError 错误,您应该检查一下场景设计。例如,如果您正在使用数据库,可以先通过搜索模块检查数据库记录是否存在。或者对于推文示例,您可以使用忽略错误的处理程序来忽略该错误。


## **Incomplete Data Error**

当模块只能从第三方应用程序获取部分数据时，模块会输出 IncompleteDataError 。

例如,当您正在将新照片上传到 Google Photos 时,您有一个同时下载照片的场景。尝试下载您正在上传的照片。照片文件将不完整,Google Photos 模块将输出 IncompleteDataError。

如果模块输出 IncompleteDataError 且没有错误处理,该场景将以错误结束。将场景暂停 20 分钟,然后重新运行该场景,直到场景成功或达到连续错误的数量。

如果你想要处理 IncompleteDataError ，你可以使用 Break 错误处理程序。


## **Inconsistency Error**

在场景回滚过程中发生错误时，模块会输出 InconsistencyError 。当您对同时运行两个场景的数据存储进行更改时，可能会发生此错误。如果一个场景遇到错误并尝试在回滚阶段撤消更改,但另一个场景已完成更改,则无法安全撤消更改,您会从第一个场景的数据存储模块获得 InconsistencyError 。

例如,想象两个人在同一个文件的同一部分进行修改。其中一个人先保存了修改,另一个人的修改会发生什么?

如果模块输出 InconsistencyError 且没有错误处理,该场景将以错误结束。因为 InconsistencyError 是致命错误,Make 立即禁用场景调度,无论连续错误的数量。

要修复 InconsistencyError ，请检查您的数据并在必要时修复数据。如果您经常遇到 InconsistencyError ，请检查使用数据库的场景。


## **Max File Size Exceeded Error**

当您尝试处理超出最大文件大小的文件时，模块会输出 MaxFileSizeExceededError 。最大文件大小根据您的组织订阅而有所不同。您可以在定价计划概览中查看最大文件大小。

例如,如果您在使用"Google Drive > 移动文件"模块在拥有核心计划的组织中移动大于 100MB 的文件,您将收到 MaxFileSizeExceededError 。

如果模块输出 MaxFileSizeExceededError 且没有错误处理,该场景将以错误结束。当您的场景以连续错误次数过多而以错误结束时,Make 会禁用该场景的调度。

要修复 MaxFileSizeError ，您需要将文件缩小（压缩或分割文件）或升级您的组织订阅计划。


## **Module Timeout Error**

当模块请求或处理数据超过 40 秒时,模块会输出 ModuleTimeoutError 。例如,在从应用程序检索大量数据或应用程序可用性降低时,可能会出现此错误。

如果模块输出 ModuleTimeoutError 而没有错误处理,该场景将以错误结束。Make 会重复该场景运行,并增加时间延迟。

要修复搜索模块中的 ModuleTimeoutError ，请将模块设置中的限制值设置为较低的数字。要应对可用性降低的情况,请查看有关修复 ConnectionError 的文章。


## **Operations Limit Exceeded Error**

当您用完操作次数时,模块将输出 OperationsLimitExceededError 。您的操作次数限制由您所在组织的订阅决定。您可以在定价计划概览中查看您的操作次数限制。

如果模块输出 OperationsLimitExceededError ，这个场景将以错误结束。因为 OperationsLimitExceededError 是一个致命错误，Make 立即禁用场景调度,不管连续错误的数量有多少。

要修复 OperationsLimitExceededError ，请考虑购买额外的操作或升级您组织的订阅计划。


## **Rate Limit Error**

当您在一段时间内向应用程序 API 发出太多请求时，模块会输出 RateLimitError 。此错误使用 HTTP 429 状态码,并遵循第三方的速率限制规则。

例如,当您达到基于您的订阅的每分钟 API 调用次数限制时,Make 应用程序模块会输出 RateLimitError 。

如果一个模块输出 RateLimitError 且没有错误处理,该场景会以错误结束。请暂停该场景 20 分钟,然后重新运行该场景直到成功。

要修复 RateLimitError ，请查看关于修复速率限制错误的文章。


## **Runtime Error**

当第三方应用程序报告的错误不符合任何其他错误类型的标准时,该模块会输出 RuntimeError 。例如,当你用完了与 OpenAI 创建完成模块相关的所有令牌时,你会收到 RuntimeError 。

如果模块输出 RuntimeError 且没有错误处理,该场景将以错误结束。当您的场景以连续错误次数过多而以错误结束时,Make 会禁用该场景的调度。

没有一般的规则可以修复 RuntimeError 。检查场景历史中的错误消息,或尝试使用 Make DevTool 重现错误。

对错误处理的概述可以帮助您制定错误处理策略。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-02/720345/345aa2c35ec491371a9a268027f7904c.png)