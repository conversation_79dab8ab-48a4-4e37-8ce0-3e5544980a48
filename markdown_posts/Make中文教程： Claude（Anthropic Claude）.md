---
title: "Make中文教程： Claude（Anthropic Claude）"
created_at: "2024-12-05T12:42:14.000000Z"
uuid: "ecae07b7-5c1f-4a34-82b7-01f5a5c93fee"
tags: ['Make基础教程']
---

# Make中文教程： Claude（Anthropic Claude）

**标签**: Make基础教程

使用 Anthropic Claude 模块在 Make 中,您可以在 Anthropic Claude 账户中创建消息。

要开始使用 Anthropic Claude,请在 anthropic.com/earlyaccess 申请使用其 API。更多信息,请参见 Anthropic Claude 入门指南。

请参阅 Anthropic Claude API 文档了解可用的端点。


## 将 Anthropic Claude 连接到 Make

要建立连接,您必须:

  1. 在 Anthropic 的 Claude 中获取您的 API 密钥。

  2. 建立在 Make 中的连接。


### **在 Anthropic Claude 上获取您的 API 密钥**

从您的 Anthropic Claude 账户获取 API 密钥:

  1. 登录您的 Anthropic Claude 控制台。

  2. 在右上角,点击您的个人资料图标 > API 密钥。

  3. 点击创建密钥按钮。

  4. 输入密钥名称并点击创建密钥。

  5. 复制您的密钥并将其存储在安全的地方,因为您将无法再次查看它。

您将在 Make 中的 API 密钥字段中使用此值。

![](https://static.xiaobot.net/file/2024-12-05/720345/f0c28f0a4e5c762236a8c75aef9bff9d.png)


### **在 Make 中与 Anthropic Claude 建立连接**

建立在 Make 中的连接

  1. 登录您的 Make 账户,添加一个 Anthropic Claude 模块到您的场景,然后点击创建连接。

  2. 在"连接名称"字段中输入连接的名称。

  3. 在 API 密钥字段中输入在第 5 步中复制的 API 密钥。

  4.  点击保存。

您已成功建立连接。您现在可以编辑您的场景并添加更多 Anthropic Claude 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤进行操作。


## **构建 Anthropic Claude 情景**

连接应用程序后,您可以执行以下操作:

**  行动**

  -  创建提示

  -  发起 API 请求


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-05/720345/f0ab0f386799e223106c19e920a6e210.png)