---
title: "HTTP第三方API调用：硅基流动"
created_at: "2024-10-03T03:55:32.000000Z"
uuid: "6cf2fc75-4197-4058-b643-ab9c7c7353e5"
tags: ['快捷工具']
---

# HTTP第三方API调用：硅基流动

**标签**: 快捷工具

**使用方法：**

**1\. 复制代码块：**

请将下面代码块中的内容完整复制。

**2\. 粘贴代码块：**

• 进入 Make.com 的工作流设计界面。

• 右键点击空白区域，选择 “Paste”。

• 系统将自动根据代码块中的内容创建对应的模块。

**3\. 修改 API 密钥与请求内容：**

• 将 API 密钥替换为您的实际密钥。

• 根据您的需求，修改请求内容（request content）中的内容，以确保 API 请求的正确性和有效性。

**4\. 完成调用：**

修改完成后，您可以运行工作流以调用 API 并处理响应。

**注意：**

• 确保 API 密钥和请求内容中的信息准确无误，否则可能导致 API 请求失败或返回错误响应。

• 如果请求内容（request content）中的 JSON（JavaScript Object Notation）格式不正确，建议将其复制并粘贴到网站 <https://jsonformatter.org/点击“Format> / Beautify” 按钮以优化 JSON 格式（format）。这样可以使内容更易于阅读和修改。

**如需复制，请访问以下文档**

[HTTP第三方API调用：硅基流动](<https://kdocs.cn/l/cu9fJwmO8iRf>)

[注册地址](<https://cloud.siliconflow.cn/i/FxZfAIU7>)

对话：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://api.siliconflow.cn/v1/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Authorization","value":"Bearer sk-vjjiixlwsqpjumlabrgkrbpoimozlipkzdxclguibnha"},{"name":"Content-Type","value":"application/json"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"model\":\"meta-llama/Meta-Llama-3.1-8B-Instruct\",\"messages\":[{\"role\":\"user\",\"content\":\"世界有几个大洲\"}]}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}

对话带有JSON结构化输出：

    {"subflows":[{"flow":[{"id":8,"module":"http:ActionSendData","version":3,"parameters":{"handleErrors":false,"useNewZLibDeCompress":true},"mapper":{"url":"https://api.siliconflow.cn/v1/chat/completions","serializeUrl":false,"method":"post","headers":[{"name":"Authorization","value":"Bearer sk-kuonvjjiixlwsqpjumlabrgkrbpoimozlipkzdxclguibnha"},{"name":"Content-Type","value":"application/json"}],"qs":[],"bodyType":"raw","parseResponse":true,"authUser":"","authPass":"","timeout":"","shareCookies":false,"ca":"","rejectUnauthorized":true,"followRedirect":true,"useQuerystring":false,"gzip":true,"useMtls":false,"contentType":"application/json","data":"{\"model\":\"meta-llama/Meta-Llama-3.1-8B-Instruct\",\"messages\":[{\"role\":\"user\",\"content\":\"世界有几个大洲，请以json结构输出，数据条目为大洲名称\"}],\"response_format\":{\"type\":\"json_object\"}}","followAllRedirects":false},"metadata":{"designer":{"x":210,"y":265,"name":"HTTP"},"restore":{"expect":{"method":{"mode":"chose","label":"POST"},"headers":{"mode":"chose","items":[null,null]},"qs":{"mode":"chose","items":[null]},"bodyType":{"label":"Raw"},"contentType":{"label":"JSON (application/json)"}}},"parameters":[{"name":"handleErrors","type":"boolean","label":"Evaluate all states as errors (except for 2xx and 3xx )","required":true},{"name":"useNewZLibDeCompress","type":"hidden"}],"expect":[{"name":"url","type":"url","label":"URL","required":true},{"name":"serializeUrl","type":"boolean","label":"Serialize URL","required":true},{"name":"method","type":"select","label":"Method","required":true,"validate":{"enum":["get","head","post","put","patch","delete","options"]}},{"name":"headers","type":"array","label":"Headers","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"qs","type":"array","label":"Query String","spec":[{"name":"name","label":"Name","type":"text","required":true},{"name":"value","label":"Value","type":"text"}]},{"name":"bodyType","type":"select","label":"Body type","validate":{"enum":["raw","x_www_form_urlencoded","multipart_form_data"]}},{"name":"parseResponse","type":"boolean","label":"Parse response","required":true},{"name":"authUser","type":"text","label":"User name"},{"name":"authPass","type":"password","label":"Password"},{"name":"timeout","type":"uinteger","label":"Timeout","validate":{"max":300,"min":1}},{"name":"shareCookies","type":"boolean","label":"Share cookies with other HTTP modules","required":true},{"name":"ca","type":"cert","label":"Self-signed certificate"},{"name":"rejectUnauthorized","type":"boolean","label":"Reject connections that are using unverified (self-signed) certificates","required":true},{"name":"followRedirect","type":"boolean","label":"Follow redirect","required":true},{"name":"useQuerystring","type":"boolean","label":"Disable serialization of multiple same query string keys as arrays","required":true},{"name":"gzip","type":"boolean","label":"Request compressed content","required":true},{"name":"useMtls","type":"boolean","label":"Use Mutual TLS","required":true},{"name":"contentType","type":"select","label":"Content type","validate":{"enum":["text/plain","application/json","application/xml","text/xml","text/html","custom"]}},{"name":"data","type":"buffer","label":"Request content"},{"name":"followAllRedirects","type":"boolean","label":"Follow all redirect","required":true}]}}]}],"metadata":{"version":1}}