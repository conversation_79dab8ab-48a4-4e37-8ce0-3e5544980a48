---
title: "Make中文教程：路由器（Router）"
created_at: "2024-08-27T13:51:04.000000Z"
uuid: "cf22e4de-074d-4d1c-9e71-650d46bbac38"
tags: ['Make基础教程']
---

# Make中文教程：路由器（Router）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于路由器的教程。

  1. **教程简介**  ：本教程介绍了在Make.com平台中使用路由器模块的方法，包含如何添加路由器、排列路由顺序、设置后备路由以及如何基于条件分配数据流。

  2. **学习目标**  ：掌握在Make.com中通过路由器模块实现场景流程分支的方法，熟悉条件筛选、路由顺序设置与后备路由配置，提升数据流控制能力。

  3. **学习内容**  ：

     - 路由器的添加方法

     - 路由顺序的设置

     - 后备路由的配置

     - 用例示例的讲解

  4. **概念理解**  ：

     - **路由器**  ：使场景中的数据流依据设置的条件分支成不同模块链条。

     - **后备路由**  ：处理所有不满足前述条件的数据，确保所有数据都有相应处理路径。

  5. **任务练习**  ：创建一个天气通知场景并添加三个路由：通过条件筛选发送不同天气通知，最后设置一个后备路由处理未满足条件的情况。


## **设置路由处理顺序**

路由的处理顺序直接影响工作流的执行结果。以下是设置步骤：

  1. 点击包含路由的Router模块

  2. 右键选择"Order routes"\(排序路由\)

![](https://static.xiaobot.net/file/2024-11-18/720345/a7adabb19bc34c4b1e3def11b62fdbd7.png)

  3. 使用箭头按钮调整顺序

  4. 可选：勾选"Auto-align arranges with set order"自动排列模块位置

  5. 点击"Apply"\(应用\)

注意：路由按顺序处理，不能并行执行。


## **设置后备路由\(Fallback Route\)**

后备路由就像是一个"安全网"或"兜底方案"，它主要有这些特点： 执行顺序：总是最后执行，不管你把它放在路由列表的什么位置 使用场景：

  - 处理不符合其他所有路由条件的数据

  - 捕获异常情况

  - 确保每条数据都有处理去向 具体的设置方法如下：

  1. 点击想要设置为后备路由的分支

![](https://static.xiaobot.net/file/2024-11-18/720345/757137b6d5463a033acaac2e936d875d.png)

  2. 选择"Yes"

  3. 点击"Save"\(保存\)

后备路由会显示特殊箭头图标：

![](https://static.xiaobot.net/file/2024-11-18/720345/63a5abccb5a74024852ec81127c0ed7f.png)


## **选择整个分支**

需要同时管理多个模块时：

  1. 点击路由菜单

  2. 选择"Select whole branch"\(选择整个分支\)

![](https://static.xiaobot.net/file/2024-11-18/720345/bdc0878e64e1b5b2bab98d02dde80d6f.png)


## **实际案例：天气提醒系统**

这个例子展示如何根据天气情况发送不同的Slack提醒：

场景概览：

![](https://static.xiaobot.net/file/2024-11-18/720345/7acca7ac2db644227ce86576b7f8f082.png)

路由顺序设置：

![](https://static.xiaobot.net/file/2024-11-18/720345/8411bb0048c60ab1b1ea10ad331ca1a1.png)

具体过滤条件：

  1. 炎热天气路由：

![](https://static.xiaobot.net/file/2024-11-18/720345/696bee94a0b14fe91b093a608dea5169.png)

  2. 寒冷天气路由：

![](https://static.xiaobot.net/file/2024-11-18/720345/e0ab8cec54a206b366f3d95da89bbf8f.png)

  3. 后备路由：

![](https://static.xiaobot.net/file/2024-11-18/720345/b90e9e783975e6fa477e868d3519491c.png)

工作流程：

  1. Weather模块获取明天天气数据

  2. Router根据温度条件分流：

     - 温度过高 → 发送"wear shorts"\(穿短裤\)

     - 温度过低 → 发送"wear a jacket"\(穿外套\)

     - 其他情况 → 发送"better stay at home"\(最好待在家\)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！ **翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-18/720345/c34197ba8ab504ed7caac6ffef014d06.png)