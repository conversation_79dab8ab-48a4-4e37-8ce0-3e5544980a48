---
title: "Make25.全网首个多模态 Make工作流，轻松从文本、音频或视频生成爆款口播稿！"
created_at: "2025-02-25T02:28:59.000000Z"
uuid: "bed3c2fd-c4c7-42b5-bce9-ae20ae00164c"
tags: ['资源包']
---

# Make25.全网首个多模态 Make工作流，轻松从文本、音频或视频生成爆款口播稿！

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/25-first-multimodal-make-workflow-generate-popular-scripts/>


# **视频资源**


## **1\. 使用教程和简要过程：**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

  - 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

  - 配置各模块的账户和API密钥

  - 链接和配置Notion数据库

  - 保存并运行工作流


## **2\. Make 工作流模版文件下载：**


### [**下载链接（点击）**](<https://pan.quark.cn/s/284d5c62a440>)

提取码：mcQt

其中 JSON 的数据结构请参看如下网址：

<https://www.xiaobot.net/post/008e7fa9-1cd4-4769-b2c1-4597f846afe6?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

该工作流有 2 个版本，口播稿创作的大模型分别是利用火山引擎和 openrouter 平台，根据自己的需求选择，可以首先使用火山引擎进行测试和使用。


## **3.数据库模板：**

点击下方链接查看数据库模板：

[https://xiaoyuzaici.notion.site/191017a905fd80558c9af462f381ef70?v=191017a905fd81e1a3f4000cfbcf78de&pvs;=4](<https://xiaoyuzaici.notion.site/191017a905fd80558c9af462f381ef70?v=191017a905fd81e1a3f4000cfbcf78de&pvs=4>)


## **4.PPT：**

<https://link.excalidraw.com/p/readonly/Q7z4EYvBL0RlJfMad5my>

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！

![](https://static.xiaobot.net/file/2025-02-25/720345/dbd33afc4078d27aa8fd2d28330ad488.webp)