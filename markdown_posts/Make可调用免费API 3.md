---
title: "Make可调用免费API 3"
created_at: "2024-09-24T03:35:52.000000Z"
uuid: "76eba93b-3055-4ef6-bf8a-804f61362835"
tags: ['API']
---

# Make可调用免费API 3

**标签**: API

Make.com的自动化流程通过API的集成可以实现更广泛的操作。API能够将不同的系统、应用和服务连接起来，使工作流具备跨平台的数据交互能力，从而打破单一平台的局限性，实现复杂的自动化操作。

在自媒体制作中，自动化工作流显著提升效率。OoyonghongoO 的 Dick API 提供了丰富的接口资源，通过 Make.com 的 HTTP 模块即可调用，优化内容制作。翔宇工作流在这里为大家介绍这些 API 的功能，并探讨其高效应用。

感谢 OoyonghongoO 的 Dick API 提供的免费服务。由于免费API的特性，可能会出现访问不稳定或因不可抗力导致下架等情况。如遇API下架，可及时寻找其他替代方案。请随时关注API状态，并根据实际情况调整使用策略。

OoyonghongoO 的 Dick API 网址：

<https://api.vore.top/>


# **一. API 分类与功能介绍**

OoyonghongoO 的 Dick API 接口提供了多种服务，涵盖查询类、生成类和实用类等多个领域。以下是这些 API 的具体功能介绍：


## **查询类 API**

1\. **IP 归属地查询 API 接口**

查询 IP 归属地（自动区分 IPv4 & IPv6），调用次数：15.5亿

2\. **IPv4 归属地查询 API 接口**

查询 IPv4 地址归属地，调用次数：3.48亿

3\. **IPv6 归属地查询 API 接口**

查询 IPv6 地址归属地，调用次数：3.48亿

4\. **手机号归属地查询 API 接口**

输入手机号，返回归属地信息，调用次数：1235.3万

5\. **网站信息获取 API 接口**

获取网站首页标题及 favicon 图片，调用次数：1083.1万

6\. **WHOIS 查询 API 接口**

查询域名 WHOIS 详细信息，调用次数：11.47万

7\. **百度热搜榜 API 接口**

返回更加简洁明了的百度热搜排行榜，调用次数：5.95万

8\. **ICP 备案信息查询 API 接口**

查询域名ICP备案信息，调用次数：4.92万

9\. **新浪图片来源解析 API 接口**

通过图片链接反查发布者微博主页，调用次数：119

10\. **Alexa 排名查询 API 接口**

查询对应网址 Alexa 排名，调用次数：162


## **生成类 API**

1\. **二维码生成 API 接口**

快速将 URL 或文字生成二维码，调用次数：233.07万

2\. **随机二次元背景图 API 接口**

随机输出二次元背景图，调用次数：171.35万

3\. **验证码生成 API 接口**

生成字符画格式验证码，有效抗打码，调用次数：1141

4\. **UUID 生成 API 接口**

生成通用唯一识别码，调用次数：184

5\. **文字转图片 API 接口**

将文字转为工整的图片，方便发送/保存，调用次数：266

6\. **Markdown 渲染 API 接口**

在线渲染 Markdown 为 HTML 单页，调用次数：264

7\. **占位图生成 API 接口**

生成空白内容占位图，调用次数：140


## **实用类 API**

1\. **天气预报 API 接口**

查询全球天气预报信息，调用次数：47.59万

2\. **浏览计数娘 API 接口**

召唤可爱的计数娘统计浏览次数，调用次数：16.99万

3\. **Bing 每日图 API 接口**

获取 Bing 每日图并输出，调用次数：16.04万

4\. **IP 签名卡 API 接口**

根据访问者 IP 生成签名卡，调用次数：3.65万

5\. **QQToken 二维码获取 API 接口**

通过二维码获取 QQToken，调用次数：3.49万

6\. **历史上的今天 API 接口**

查看历史上的今天发生的事件，调用次数：3.03万

7\. **新冠疫情数据 API 接口**

获取国家卫健委实时疫情数据，调用次数：2081

8\. **TTS 文字转语音 API 接口**

将文本转换为音频输出，调用次数：1757

9\. **我的世界服务器信息 API 接口**

查询 Minecraft 服务器当前状态，调用次数：1073

10\. **万年历 API 接口**

提供万年历服务，调用次数：1055

11\. **结巴分词 API 接口**

使用“结巴 Jieba”中文分词组件分割文本，调用次数：312

12\. **身份证年龄核验 API 接口**

自动升位/解析身份证，仅成年核验，调用次数：230

13\. **姓名合法校验 API 接口**

校验姓氏是否符合中国姓氏构成，调用次数：211

14\. **汉字转拼音 API 接口**

汉字转拼音（支持多音字），调用次数：179


## **娱乐类 API**

1\. **一言 API 接口**

内容来自一言数据库，调用次数：4.36万

2\. **Bing 每日图 API 接口**

获取 Bing 每日壁纸，调用次数：16.04万

3\. **随机二次元背景图 API 接口**

随机输出二次元背景图，调用次数：171.35万

4\. **B站视频解析 API 接口**

通过 AV/BV 号解析 bilibili 视频，调用次数：2385

5\. **B站封面提取 API 接口**

通过 AV/BV 号解析 bilibili 视频封面，调用次数：1050

6\. **网易云音乐解析 API 接口**

解析网易云音乐，返回 MP3 直链，调用次数：431

7\. **蓝奏直链解析 API 接口**

解析蓝奏云分享链接为下载直链，调用次数：320

8\. **B站综合信息 API 接口**

返回 B站电脑版的视频原始 JSON，调用次数：130

9\. **成语词典 API 接口**

查询成语拼音、释义、出处、例句，调用次数：696

10\. **QQ 强制聊天 API 接口**

调用 QQ 客户端发起强制聊天，调用次数：678

11\. **狗屁不通文章生成器 API 接口**

在线生成一个狗屁不通的文章，调用次数：486

12\. **火星文转换 API 接口**

将文字转变为焱暒妏输出，调用次数：851


## **其他类 API**

1\. **垃圾分类 API 接口**

输入垃圾名称，返回垃圾类型，调用次数：131

2\. **垃圾话生成器 API 接口**

瞎喷垃圾话的垃圾接口，调用次数：1836

3\. **腾讯网址安全检测 API 接口**

对接【腾讯安全】检测网址是否安全，调用次数：5136

4\. **QQToken 二维码获取 API 接口**

通过二维码获取 QQToken，调用次数：3.49万

5\. **身份证穷举 API 接口**

根据条件穷举指定身份证，调用次数：2.09万

6\. **访客欢迎信息 API 接口**

通过访客信息，并输出欢迎语，调用次数：1.7万


# 二. 应用场景介绍分析


## **示例应用1：自动化每日热点资讯汇总与发布工作流**

1\. **实时热点获取**

使用 **微博热搜榜 API 接口** ，定时（例如每日早晨）调用该接口，获取最新的微博热搜榜单，获取当前最热门的话题和关键词。

2\. **关键词提取与分析**

对获取的热搜关键词进行分词处理，提取核心主题和相关联的关键词，分析热点趋势。

3\. **内容生成**

通过大模型结合提取的关键词，自动生成相关文章内容，包括简短的引言和扩展的讨论内容。

4\. **自动发布**

将生成的内容通过 Make 的微信公众号集成模块，自动发布到指定的微信公众号或博客平台，确保每日热点资讯的及时更新与发布。


## **示例应用2：自动化多平台壁纸内容生成与分发工作流**

1\. **壁纸图片获取**

通过 **随机二次元背景图 API 接口** 和 **Bing每日图 API 接口** ，定时调用这些接口，获取每日更新的高质量壁纸图片，涵盖二次元和自然风光等多种风格。

2\. **图片处理与优化**

在壁纸图片上添加每日一句励志语句或热门关键词，增强图片的互动性与分享性。

3\. **上传与存储**

利用 Make 的图床模块，将处理后的壁纸图片上传至在线存储平台（如 Imgur 或自建图床），并获取图片的直链。

4\. **自动分发**

通过 Make 的社交媒体集成模块，将生成的壁纸内容自动发布到多个平台，如微信公众号、Instagram 和 X，覆盖更广泛的受众群体。

这些 Make 工作流示例展示了如何充分利用 OoyonghongoO 的 Dick API 接口，实现内容自媒体创作的自动化与高效化。通过合理配置和组合不同的 API 服务，内容创作者可以显著提升工作效率，增强内容的多样性与吸引力，满足用户不断变化的需求。