---
title: "Make中文教程：Apify"
created_at: "2024-09-01T03:09:20.000000Z"
uuid: "cc85a02e-6949-43e1-8899-060887e0d2b9"
tags: ['Make基础教程']
---

# Make中文教程：Apify

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Apify使用指南的教程。

  1. **教程简介** ：Apify使用指南介绍了如何将Apify账户连接到Make.com，以及如何通过Make.com监控和运行Apify中的演员和任务，获取数据集项目。

  2. **学习目标** ：本教程的主要目标是帮助用户学会将Apify账户与Make.com集成，并掌握在Make.com中使用Apify的基本操作，包括监控任务、运行任务和获取数据集项目。

  3. **学习内容** ：用户将学习如何获取并配置API令牌，将Apify连接到Make.com，设置触发器来监控演员和任务运行，以及使用动作来执行任务和获取数据。

  4. **概念理解** ：API令牌是访问Apify账户数据和服务的密钥，它允许Make.com与Apify进行安全通信。监控演员和任务运行的触发器功能用来实时跟踪Apify中的活动。

  5. **任务练习** ：创建一个Apify账户并生成API令牌，将其连接至Make.com。设置一个触发器来监视特定任务的运行，并使用动作来运行该任务或获取相关数据集项目。


# **Apify**

Apify 模块允许您监控和运行执行器和任务,或在您的 Apify 帐户中获取数据集项目。

要开始使用 Apify，请在 my.apify.com 上创建一个帐户。


## **  连接 Apify 到制作**

要将您的 Apify 帐户连接到 Make，您需要从 Apify 帐户获取 API 令牌。

  1. 登录您的 Apify 账户。

  2. 点击设置 > 集成。

![](https://static.xiaobot.net/file/2024-11-26/720345/5fbe26e1ff532cd2028c5cb77f908eb1.png)
  3.  点击 + 添加令牌。

  4. 在添加身份验证令牌弹出窗口中,输入令牌的名称,然后单击保存。

![](https://static.xiaobot.net/file/2024-11-26/720345/db711255c9a84f6b518e8dcbc54fd00d.png)
  5. 点击复制按钮复制令牌。

![](https://static.xiaobot.net/file/2024-11-26/720345/6cbba47fc4aa3371942b0eb5ee4399fd.png)
  6. 进入"创建连接"对话框,打开所需的 Apify 模块。

  7. 在 API 令牌字段中输入在步骤 5 中复制的值,然后点击保存。

您已成功连接 Apify 应用程序,现在可以构建场景


## **触发器**


### **查看角色运行**

在所选角色的运行结束时触发。

![](https://static.xiaobot.net/file/2024-11-26/720345/5341237615bab3ec944d0638ab1b384e.png)


### **  监控任务运行**

当所选任务运行完成时触发。

![](https://static.xiaobot.net/file/2024-11-26/720345/3d4e1d47eadf502ddd3e40a54a9a9681.png)


## **  行动**


### **  运行任务**

执行所选的角色任务。

![](https://static.xiaobot.net/file/2024-11-26/720345/9601d6a1b79cd0a0f767887286c9e9a5.png)![](https://static.xiaobot.net/file/2024-11-26/720345/0c423973306d7817c3c32ab339518f95.png)


### **  运行一名角色**

 运行所选角色。

![](https://static.xiaobot.net/file/2024-11-26/720345/7a7450b8e377313fdb1e74dc735e94dd.png)![](https://static.xiaobot.net/file/2024-11-26/720345/aa7e334c6a2a12f59bc1dec8ed4002e5.png)


## **  搜索**


### **  获取数据集项目**

从数据集中检索项目。

![](https://static.xiaobot.net/file/2024-11-26/720345/19c163775ea3f2cb83a1841fc0ed7f51.png)

有关模块功能的更多详细信息,请参阅 APIFY 文档。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-26/720345/24889bc39de96aa91343dcfd4e2bd12a.png)