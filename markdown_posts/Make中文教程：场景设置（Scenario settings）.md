---
title: "Make中文教程：场景设置（Scenario settings）"
created_at: "2024-09-21T08:44:39.000000Z"
uuid: "c45b5744-c215-47ef-acfe-573c0d1987e3"
tags: ['Make基础教程']
---

# Make中文教程：场景设置（Scenario settings）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于场景设置的教程。

**教程简介** ： 本教程介绍了Make.com中的Scenario设置，包括顺序处理、数据保密、未完成执行存储、数据丢失、自动提交、最后提交触发器、最大循环次数及连续错误次数的配置方法和应用场景。

**学习目标** ： 本教程旨在帮助用户了解并设置Make.com中的Scenario高级选项，以优化自动化流程，减少错误发生，确保数据处理的有序性和安全性。

**学习内容** ：

  1. 顺序处理和Webhooks的配置

  2. 数据保密及其启用方法

  3. 存储未完成执行设置

  4. 数据丢失设置及其适用场景

  5. 自动提交与事务性处理

  6. 最后提交触发器设置

  7. 最大循环次数及示例

  8. 连续错误次数设置

**概念理解** ：

  1. **顺序处理** ：确保Scenario按顺序处理未完成的执行。

  2. **数据保密** ：限制Scenario执行后数据的存储。

  3. **未完成执行存储** ：在发生错误时保存数据，供问题解决后继续执行。

  4. **数据丢失** ：在无法保存数据包时防止Scenario中断。

  5. **自动提交** ：控制每个模块操作后的数据提交方式。

  6. **最后提交触发器** ：决定提交阶段跳过触发器的顺序。

  7. **最大循环次数** ：设置Scenario执行时的循环次数上限。

  8. **连续错误次数** ：定义Scenario停用前的最大连续错误次数。

**任务练习** ：

  1. 启用顺序处理设置，观察Scenario执行顺序的变化。

  2. 尝试启用和禁用数据保密，比较执行后的数据可见性。

  3. 配置未完成执行的存储设置，并引入错误，观察Scenario的暂停行为。

  4. 实验启用数据丢失设置，了解其对连续执行任务的影响。

  5. 修改自动提交设置，比较即时提交与延迟提交的区别。

  6. 设置最大循环次数为不同的值，测试其对数据处理效率的影响。

  7. 定义连续错误次数设置，并故意引入错误，观察Scenario的停用行为。


# 场景设置

在场景编辑器中点击齿轮图标将打开场景设置面板。在此您可以设置各种高级设置。

![](https://static.xiaobot.net/file/2024-11-24/720345/b65b82fe1c2f6e93cbef48de692c688c.png)


## **1 - 顺序处理**

您可以允许"Make"存储有关未完成方案执行的信息。

顺序处理设置决定了 Make 如何处理循环执行中的不完整场景。"不完整执行"文件夹必须包含场景数据。

  - 如果启用,Make 会暂停执行场景,直到您解决所有未完成的执行。这个选项可以确保 Make 总是按顺序解决未完成的执行。

  - 如果禁用,该方案将继续按照其计划运行,不管是否存在错误。

![](https://static.xiaobot.net/file/2024-11-24/720345/6e4b5eefd0abfbf3c4b8e57ab8e9efac.png)


## **2 - 数据是保密的**

在场景执行完毕后,您可以显示有关模块处理数据的信息。这是默认的行为。

如果您不想存储此信息,请启用数据机密设置。

![](https://static.xiaobot.net/file/2024-11-24/720345/77cb5c009969faa80f9287bf61520bed.png)


## **允许存储不完整的执行**

这个设置决定了如果一个场景运行遇到错误会发生什么。您可以选择 Make 如何处理数据。

  - 如果启用,该方案会暂停并移到"未完成执行"文件夹。这样您就可以解决问题并从方案停止的地方继续执行。

  - 如果禁用,该场景将停止运行并开始回滚阶段。

您可以手动或自动解决每一个未完成的执行。

![](https://static.xiaobot.net/file/2024-11-24/720345/5e076261a927874fa31a08e30f2a6a5a.png)


## **启用数据丢失**

创建可能无法将数据包保存到未完成执行的队列中\(例如由于空间不足\)。启用此设置后,创建不会保存丢失的数据。这是为了防止场景执行中断。

这个选项非常适合于持续执行是最高优先级的场景。传入的数据不太重要。

场景模块可能遇到超过最大允许大小的文件。在这种情况下,Make 会按照启用数据丢失设置进行处理,并显示警告消息。

![](https://static.xiaobot.net/file/2024-11-24/720345/63283b730a6adbf7889c4d4601b8a75b.png)


## **  5 - 自动提交**

此设置适用于交易,并定义了处理场景的方式。此设置默认启用。

  - 如果启用,每个模块的提交阶段会立即在操作阶段之后开始。数据会立即提交,在出错的情况下无法恢复。

  - 如果禁用,则在执行所有模块的操作之前不会发生提交。

![](https://static.xiaobot.net/file/2024-11-24/720345/d46d248e6f63f855df246b27135e80be.png)


## **6 - 提交触发器最后**

此设置定义了成功的场景操作阶段之后的模块提交顺序。此设置默认启用。

  - 如果启用,提交阶段将跳过触发器并最后处理该模块。

  - 如果禁用，提交阶段会按默认顺序进行。


## **7 - 最大循环次数**

此设置定义了在方案执行期间允许的最大循环次数。

设置更多周期可在您希望防止与第三方服务的连接中断时有用。这也可确保在场景运行中处理所有记录。

如果您手动执行该场景,点击"仅运行一次"按钮,设置将被忽略,只会执行一个循环。


### **  示例**

比特网盘 > 监视文件从文件夹检索添加的文件,以及 Google 表格 > 添加一行模块将文件名和文件大小插入表格的新行。

![](https://static.xiaobot.net/file/2024-11-24/720345/64aa44e2d38e2d65dc1b5acdea2b5d08.png)

  1. **最大循环次数设置为 1（默认）**

![](https://static.xiaobot.net/file/2024-11-24/720345/1bee512da179f7bcf1bef923c170ef66.png)

在 Dropbox > 监视文件模块中,限制字段设置为 10 。

![](https://static.xiaobot.net/file/2024-11-24/720345/7fb0c3508659942d83cc5e7e156b394e.png)

如果向 Dropbox 文件夹添加 100 个文件,并将限制字段设置为 10,则在一次运行场景后会有 90 个文件未被处理。下一次计划执行的场景中将处理下一个 10 个文件。

  2. **最大循环次数设置为 10**

![](https://static.xiaobot.net/file/2024-11-24/720345/7468a564a75d82bc634434a915ea6225.png)

正如前所述，Dropbox>监视文件模块中的"限制"字段设置为 10 。

如果将 100 个文件添加到 Dropbox 文件夹中,并将"限制"字段选项设置为 10,那么在第一个周期内将处理 10 个文件,在第二个周期内处理下一 10 个文件,在第三个周期内处理下一 10 个文件,依此类推,直至所有文件都被处理完。

所有文件都在同一个场景中处理。

您可以在方案运行详情中查看已经运行的循环。


## **8 - 连续错误次数**

此设置定义在方案停用前的最大连续执行尝试次数（尽管在错误处理概述中列出了例外情况）。

![](https://static.xiaobot.net/file/2024-11-24/720345/868bcbf7ffe409d290512c7465a21076.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/8071a2a7c7309db2246d6cff7469687c.png)