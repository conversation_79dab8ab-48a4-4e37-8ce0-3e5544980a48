---
title: "Make中文教程：微信公众号（WeChat Official Account）"
created_at: "2024-08-28T02:46:57.000000Z"
uuid: "2d77f7a6-ce1d-447e-ad63-0df63b660190"
tags: ['Make基础教程']
---

# Make中文教程：微信公众号（WeChat Official Account）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于微信公众号简介的教程。

  1. **教程简介** ：该教程介绍了如何使用Make.com的微信模块管理微信公众号。主要内容包括创建微信公众号、连接公众号到Make.com、配置Webhook以及使用模块进行消息管理。

  2. **学习目标** ：学习如何通过Make.com管理微信公众号的不同功能，具体包括消息管理、资源管理、用户管理、生成二维码和API调用。掌握如何利用Make.com连接和操作微信公众平台。

  3. **学习内容** ：

     - 注册和创建微信公众号

     - 获取微信App ID和App Secret

     - 将微信公众号连接到Make.com

     - 配置Webhook，监控和管理消息

     - 使用发送消息模块进行测试

  4. **概念理解** ：

     - **App ID和App Secret** ：是微信公众平台提供的身份认证信息，用于连接微信API和第三方应用。

     - **Webhook** ：是一种在特定事件发生时发送HTTP请求的方式，用于与其他应用集成。

     - **Token** ：用来验证Webhook的请求安全性的字符串。

  5. **任务练习** ：

     - 任务1：创建一个微信公众号，并获取App ID和App Secret。

     - 任务2：在Make.com中添加微信公众账号模块并配置连接。

     - 任务3：配置Webhook并使用发送消息模块进行一次消息发送测试。


# **微信公众号 \(WeChat Official Account\)**

通过 **Make** 的微信模块（WeChat modules），你可以管理所有的消息、资源、标签和用户，生成二维码，并在你的微信公众号中进行API调用。


## **开始**

首先需要创建一个微信公众号。请前往微信公众平台进行公众号创建。


## **将微信连接到Make**

为了建立连接，你需要从你的微信公众号获取App ID和App Secret。

  1. 登录你的微信公众号账号。

     1. 访问 [微信公众平台](<https://mp.weixin.qq.com/?lang=en_US>)。

     2. 使用微信应用内的扫描器扫描二维码（其他条码扫描应用程序不起作用）。

     3. 在手机上点击**接受** ，网页会自动重定向。

     4. 进入微信公众号管理界面，首先点击左侧复制与开发、而后点击基本配置，来到开发信息界面，复制**开发者ID** 去make.com微信模块粘贴到**App ID** 栏目**。**

![](https://static.xiaobot.net/file/2024-08-28/720345/39f1aa0d10ec323a7f53de51a1ffe1c5.png)

     5. 点击开发者密码出点击启用，扫码身份确认，进行密码验证获取开发者密码，去make.com微信模块粘贴到**App Secret** 栏目**。**

![](https://static.xiaobot.net/file/2024-08-28/720345/14f011968e76a957ffebd66fddd4f5c9.png)
     6. 点击<https://www.make.com/en/help/connections/allowing-connections-to-and-from-make-ip-addresses?_gl=1*vgxfbb*_gcl_au*MTU5NzU3Nzc0Ni4xNzE3OTQzOTQ1*_ga*Njc5NzI4NDIwLjE3MDk4ODAxOTA.*_ga_MY0CJTCDSF*MTcyNDc2NTYxOC4yNzIuMS4xNzI0NzY2MDEzLjU5LjAuMA> 复制所有IP地址。

![](https://static.xiaobot.net/file/2024-08-28/720345/af3a737f5737b726acd78f7ad394d1b4.png)
     7. 来到微信开发者信息界面，点击IP白名单的查看按钮，将刚复制的所有ip地址粘贴保存。

![](https://static.xiaobot.net/file/2024-08-28/720345/b780c109e0fefba81ad0cbfe9e74c002.png)![](https://static.xiaobot.net/file/2024-08-28/720345/ad2a12d5a496dd110b39210b9463e5e6.png)
     8. 回到make.com的微信模块保存信息，即可完成链接。

  2. 现在你应该可以在**测试号信息** 下看到你的**App ID** 和**App Secret** 。将它们复制到剪贴板。

![](https://static.xiaobot.net/file/2025-05-09/720345/6471bd70e917363644079f727cab8a3b.png)
  3. 前往你的**Make** **场景** 并选择你想要使用的微信公众账号模块。

  4. 在**连接** 下，点击**添加** 。

  5. 为你的新连接命名。

  6. 在下面的字段中粘贴App ID和App Secret。

  7. 点击**保存** 。

你已经成功建立了连接。


## **Webhook 配置（鉴于微信更新规则，该配置项无需设置）**

**监控消息** 模块允许你监控传入消息并触发一个**场景** 。

  1. 前往你的**Make** **场景** 并选择**监控消息** 模块，为的简便并把所有的Message Type和Envent Type都打开，如果有更精细的场景可以根据场景选择打开。

![](https://static.xiaobot.net/file/2024-08-28/720345/0b54b9d6ea4a14e0df0997c3cf475e76.png)
  2. 你可以在 Token 中输入任何随机字符串，但记得将其写下来。我输入的是xiangyugongzuoliu，注意自己变一下。这个Token是用于Webhook验证的。

![](https://static.xiaobot.net/file/2024-08-28/720345/005152561c5388bdbadaf10584adf5e0.png)
  3. 点击**保存** 并将Webhook地址复制到剪贴板。

![](https://static.xiaobot.net/file/2024-08-28/720345/12115832eed459d1789e48e526c926f9.jpeg)
  4. 保存场景并刷新页面。

  5. 返回到你的 [微信公众号](<https://mp.weixin.qq.com/?lang=en_US>)，并在**接口配置信息** 标题下点击**修改** 。

![](https://static.xiaobot.net/file/2024-08-28/720345/e0499e36415e68698e315903ed4a0769.jpeg)

  6. 粘贴Webhook URL和Token，然后点击**提交** 。如果出现错误消息，请确保你粘贴了正确的Webhook地址和Token。

![](https://static.xiaobot.net/file/2024-08-28/720345/bdd8b28ae19351191d7c5a01b6d3bdda.jpeg)

  7. 为了进行测试，在**make.com** 的**监控消息** 模块保存并开启运行，来到微信公众号中发送一条消息如果成功make.com中可以收到信息内容。

  8. 如果希望测试发送消息可以尝试添加一个**发送消息** 模块并进行如下配置。

![](https://static.xiaobot.net/file/2024-08-28/720345/efe23ec05751596a2fffcec5ad9b44f2.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-08-28/720345/1dd41dd88547ad0755d9d258b44cc579.png)