---
title: "Make+n8n实战教程目录"
created_at: "2024-10-07T12:01:50.000000Z"
uuid: "7e523204-a124-4236-b4ba-3064628d7343"
tags: ['Make高级应用']
---

# Make+n8n实战教程目录

**标签**: Make高级应用

1.从 RSS 到 Notion：如何利用make实现自动更新知识库 <https://www.xiaobot.net/post/2e535cdf-3142-444c-98ea-792bc69dfa9e>

2.从 Apify到 Notion：如何利用make实现自动化Youtube视频采集 <https://xiaobot.net/post/31c7d443-34e4-4363-903d-d07f00b4f4ea>

3.Jina Reader api实操：如何利用make自动采集OpenAI官网新闻 <https://xiaobot.net/post/174d3f29-0bb1-4c45-94ca-389f63ac9040>

4.小红书自动化：如何利用Make制作个人自媒体中心，批量生成爆款笔记 <https://xiaobot.net/post/c48a3630-a770-448e-a906-da3a5b13d7bc>

5.高效Notion自动化实操：使用Make实现数据库自动关联、分类、整理，告别手动整理 <https://xiaobot.net/post/86e6a93a-3920-4663-bbe9-adb86aa8b134>

6.播客自动化实操：用Make自动制作每日新闻播客

<https://xiaobot.net/post/ca80bd41-96e7-47f5-b2ff-7ec0f0f4b1f4>

7.Kimi API实操办公自动化：Make工作流自动总结PDF文档 <https://xiaobot.net/post/8b2da730-ca2f-4012-b0f1-0f9f191a4b74>

8.零基础教程：如何使用Notion和Make搭建自动化电子图书馆 <https://xiaobot.net/post/086cfd74-e51a-40e8-9c0b-107ecdce6726>

9.微信公众号图文混排文章自动化实战：利用Make 批量制作 <https://xiaobot.net/post/8ddc6dcd-e8cc-411e-8df1-0b8eff851bfe>

10.科研自动化实操：用Make工具批量自动进行文献总结 <https://xiaobot.net/post/9f1660f1-3dcb-4794-a8bf-f2ce1ee9e3d0>

11.写作自动化实操：用Make和OpenRouter批量制作万字长文 <https://xiaobot.net/post/4c1f0037-04e6-4aa0-89cf-661a72ec3542>

12.小红书头像赛道自动化实操：用Make和Replicate制作人物卡通头像 <https://xiaobot.net/post/aff1991d-5ef0-47b9-94fe-e8dc1d55e2dd>

13.完整教程：Make和EXA AI全自动撰写微信公众号图文及小红书笔记 <https://xiaobot.net/post/f3dff274-89ce-476a-af52-502de7c92955>

14.爆款提示词工厂：Make自动化生成高质量写作提示词 <https://xiaobot.net/post/6fbea6cc-58b9-43a5-b20f-6c301226c143>

15.Firecrawl爬虫实操：Make和Firecrawl全自动撰写博客文章引流 <https://xiaobot.net/post/b6354771-82cf-4f73-9589-dfac08bcef3d>

16.小红书图文笔记批量制作，Make帮你实现图片创作自动化 <https://xiaobot.net/post/93681d59-288a-4c82-b39f-1a477b6bda0c>

17.PDF翻译自动化：利用Make打造反思翻译工作流

<https://xiaobot.net/post/5366f2cf-1071-455c-a7ea-868220167b39>

18.跨境电商必备：Make与RAG打造文章自动化创作工作流 <https://xiaobot.net/post/3b24449f-c32d-4b65-be29-b8c0b452cc18>

19.3分钟速成：利用Make和Flux打造图文并茂的儿童绘本 <https://xiaobot.net/post/efbea7de-c182-4c23-b26d-92efc3f56111>

20.Make工作流改造方法论：一个工作流如何变成1000个工作流？ <https://xiaobot.net/post/a4d271ac-65c0-40ca-acfa-e8e8967c9ea9>

21\. 写推文不再难！128字浓缩千字精华，轻松实现推特运营自动化！ <https://xiaobot.net/post/fde4c2ca-e5c5-4442-b449-be91061d6bf9>

22.小红书与Instagram自动化运营：一键生成真实感美女图片与视频 <https://xiaobot.net/post/14252912-0e24-4a95-850d-51d1c0f0678e>

24.AI写作革命！DeepSeek自动生成10万+爆文工作流教程 <https://xiaobot.net/post/cd6503e5-19b8-497a-94f9-d9007c4c8467>

23.提示词生成新方法！用Make自动化生成，只有今天能学到的独家技巧！ <https://xiaobot.net/post/8c7cc322-dff4-4d66-bdd4-1a85b570d146>

25.全网首个多模态 Make工作流，轻松从文本、音频或视频生成爆款口播稿！ <https://xiaobot.net/post/bed3c2fd-c4c7-42b5-bce9-ae20ae00164c>

26.偷偷赚美元？微信公众号深度改写+ Medium创收玩法曝光！

<https://xiaobot.net/post/150bbddf-8dbd-4898-b5db-21c947998710>

27.DeepSeek 新V3 到底多猛？小绿书、小红书爆款图文笔记一键批量生成！

<https://xiaobot.net/post/d3666e81-aa64-4ae3-8b0f-d20f012e0851>

28.只需输入字幕！Youtube标题、描述、封面全自动生成，Make工作流效率逆天！

<https://xiaobot.net/post/0ebefa66-2e75-4fd4-8143-233de9f38bdb>

29.n8n自动化赚钱全流程拆解：小白也能做的副业系统

<https://xiaobot.net/post/61a0fd41-3664-4700-af94-a0307abc619a>

30.n8n RAG 全自动知识库副业工作流，轻松月入过万？

<https://xiaobot.net/post/fa4d3750-3420-486b-8f95-f6273b874987>

31.效率翻10倍！n8n+MCP实操指南：极速接入上百AI大模型秒出风格化图片

<https://xiaobot.net/post/d8d66816-1061-4699-8887-6aab14029834>

32.SEO 自动化揭秘：n8n 手把手教程，3 步搞定跨境电商关键词挖掘

<https://xiaobot.net/post/531bafb6-84ee-4d35-87ea-140d03787a95>

33.不剪辑不拍摄！这个 n8n 自动化工作流批量生成万种风格抖音 TikTok 爆款短视频

<https://xiaobot.net/post/05f930a5-a9ed-4219-9a26-9d2404151652>

Make中文教程：5分钟搞定第一个工作流

<https://xiaobot.net/post/92ec0b28-79d9-499e-b29f-04bcf9848889>

会员必看：Make基础教程小报童使用指南（重要）

<https://xiaobot.net/post/7c407ce2-2043-40fe-a72d-2c08d97a4c6d>

Make 学习过程中的常见问题汇总

<https://xiaobot.net/post/fa3c50e7-7488-4843-b51b-c248f2de60c3>

会员必看：Make工作流JSON蓝图导入教程（会员独家） <https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039>

AI 提示词模版1000+（会员独家）

<https://xiaobot.net/post/764e665c-ac49-47a0-aabd-81e873b15ea0>

全网AI公开教程与资源汇总（会员独家）

<https://xiaobot.net/post/c76a17f9-b768-44e5-a8aa-df207fa7a6b4>

抖音自动化采集工作流（会员独家）

<https://xiaobot.net/post/03d8dedf-c0ca-42ca-9f52-93e613e604a8>

小红书自动化采集工作流（会员独家）

<https://xiaobot.net/post/02648d80-5a88-4c73-9fd6-6d3680a30e96>

微信公众号采集工作流-方案1（会员独家）

<https://xiaobot.net/post/36df6fc9-c215-499c-b371-c571026f5663>

微信公众号采集工作流-方案2（会员独家）

<https://xiaobot.net/post/a1c777f7-4098-4849-9934-6793ce3a2241>

微信公众号RSS订阅指南 （会员独家）

<https://xiaobot.net/post/d7e45c3b-c381-429b-be3e-6738b28849f5>

RSS订阅工具汇总（会员独家）

<https://xiaobot.net/post/3fb1a892-cc7b-4775-943c-9aa1d414897b>

为什么自动化工作流需要Follow？（会员独家）

<https://xiaobot.net/post/80797a10-7626-4487-8288-eac9fe120276>

会员必看：Make基础教程快捷工具合集（会员独家） <https://xiaobot.net/post/008e7fa9-1cd4-4769-b2c1-4597f846afe6>

免费用Make的方法来啦！（会员独家）

<https://xiaobot.net/post/face36c3-4310-459f-824a-489541375786>

免费推文自动化采集工作流（会员专享）

<https://xiaobot.net/post/7397f304-2a96-409d-8dd9-99552e8d834e>

会员必看：Make中文教程合集（会员独家）

<https://xiaobot.net/post/bf05b784-09b7-47c7-a55f-b5d5b624c812>

会员必看：Make基础教程buymeacoffee使用指南

微信爆款文章采集自动化工作流（会员独家）

<https://xiaobot.net/post/97ce0ea0-96e6-44cd-b0df-d56568fa1313>

免费Instagram自动化采集工作流（会员独家）

<https://xiaobot.net/post/bbbc4483-31ca-4308-91ab-010e9cbb2b13>

自媒体热点追踪工具汇总

<https://xiaobot.net/post/4e2a9a24-9d5d-4cb4-bfa7-73a5c236ed93>

Make中文教程：场景设置（Scenario settings）

<https://xiaobot.net/post/63e41ddd-e41b-43e0-9e6e-bf2a31c729d3>

Make中文教程：场景模板（Scenario templates）

<https://xiaobot.net/post/fe077b5c-f718-4658-bf84-e76f2099e864>

Make中文教程：场景调度（Scheduling a scenario） <https://www.xiaobot.net/post/40e20b1e-b69e-4a1a-b739-cbf06d7a3e5c>

Make中文教程：场景编辑器（Scenario editor） <https://www.xiaobot.net/post/ccb6690f-c14f-4b43-8184-7add50a8967b>

Make中文教程：场景详情（Scenario detail）

<https://www.xiaobot.net/post/8ea25079-8474-4c03-bdc6-5db7f076d9f0>

Make中文教程：场景设置（Scenario settings）

<https://www.xiaobot.net/post/c45b5744-c215-47ef-acfe-573c0d1987e3>

Make中文教程：自定义场景属性（Custom scenario properties） <https://www.xiaobot.net/post/e68ad2a5-0a4b-4787-a078-8417fff9dc10>

Make中文教程：操作（Operations）

<https://www.xiaobot.net/post/e705f627-6857-4cf8-84e2-e3b8d9265caa>

Make中文教程：场景输入（Scenario inputs）

<https://www.xiaobot.net/post/fedcf2cd-6afd-4085-90ef-26de5e558453>

Make中文教程：活跃和非活跃场景（Active and inactive scenarios） <https://www.xiaobot.net/post/fe700235-d409-4147-8595-fc81f53f5364>

Make中文教程：如何恢复一个之前的场景版本（How to restore a previous scenario version）

<https://www.xiaobot.net/post/91b66074-7cd2-4e09-90fd-79eae33a0914>

Make中文教程：场景执行历史（Scenario execution history） <https://www.xiaobot.net/post/6c271a10-03a8-4b52-a926-34c12c9bd085>

Make中文教程：场景执行流程（Scenario execution flow） <https://www.xiaobot.net/post/4e0d02d3-31a6-43c4-8f25-24a16988ceba>

Make中文教程：场景执行、循环和阶段（Scenario execution, cycles, and phases） <https://www.xiaobot.net/post/6b16b4b4-d172-49a0-bd51-8795d4fb71b2>

Make中文教程：未完成执行（Incomplete executions） <https://www.xiaobot.net/post/da9d39c4-0005-4f06-b05e-b1764ac467dd>

Make中文教程：过滤（Filtering）

<https://www.xiaobot.net/post/81f0e174-7cec-4708-92ec-569cc483de27>

Make中文教程：键盘快捷键（Keyboard shortcuts） <https://www.xiaobot.net/post/************************************>

Make中文教程：子场景（Subscenarios）

<https://www.xiaobot.net/post/026bcba6-6940-4ad9-a0ab-bdcfe7d1656b>

Make中文教程：映射（Mapping）

<https://xiaobot.net/post/59bc66ba-61e2-43b5-baf5-273721387f04>

Make中文教程：数组映射（Mapping arrays）

<https://xiaobot.net/post/e79d9707-4a10-439a-8df9-2c5dc712f361>

Make中文教程：项目数据类型（Item data types）

<https://xiaobot.net/post/12d34904-9c2e-4d51-9247-7d8644c544f8>

Make中文教程：类型强制转换（Type coercion）

<https://xiaobot.net/post/98b3a55d-43b3-4e14-a2ee-ca85e4c4384f>

Make中文教程：文件处理（Working with files）

<https://xiaobot.net/post/23d9e293-c4d8-4f7a-a04e-9b2115ea444f>

Make中文教程：模块类型（Types of modules）

<https://xiaobot.net/post/a60dc728-6404-4ad3-b117-96a607de6a3a>

Make中文教程：聚合器（Converger）

<https://xiaobot.net/post/94801611-12fd-4249-bdfd-a5e13acae829>

Make中文教程：路由器（Router）

<https://xiaobot.net/post/cf22e4de-074d-4d1c-9e71-650d46bbac38>

Make中文教程：迭代器（Iterator）

<https://xiaobot.net/post/8e7b4c95-b220-44b4-ad3c-24e8af9f5013>

Make中文教程：聚合器（Aggregator）

<https://xiaobot.net/post/dfc0b5be-6f38-4d27-84ee-7d1e28b3ceca>

Make中文教程：精准控制数据流（Selecting the first bundle） <https://xiaobot.net/post/59a2dfb8-d4d7-4c76-bfef-2b81e4cb0187>

Make中文教程：模块设置（Module settings）

<https://xiaobot.net/post/6d3b37f4-7b16-4536-b216-f56796456c5b>

Make中文教程：应用生命周期（App lifecycle）

<https://xiaobot.net/post/7937d6b3-c1ff-41f7-8c55-ddb2964f2a39>

Make中文教程：术语表详解（Glossary）-重要

<https://xiaobot.net/post/2b5991a7-fc95-41f6-b35b-dd45d4bef6f1>

Make中文教程：如何利用Text Parser与正则表达式进行文本处理 <https://xiaobot.net/post/61198455-9ad2-4f25-88c1-4e972b238735>

Make中文教程：替换旧模块为新模块（Replacing Legacy Modules with New Modules） <https://xiaobot.net/post/cbef1790-46c0-4f9f-bcab-481d0f79cf1a>

Make中文教程：替换Google Sheets模块（Replacing Google Sheets legacy Modules with New Modules）

<https://xiaobot.net/post/2e22c214-ccd9-4046-a2e0-304dc8e63212>

Make中文教程：Make工具升级Gmail（Upgrading Gmail and Email App Versions Using Make DevTool）

<https://xiaobot.net/post/a0fa3c3a-7a7e-4d65-b49c-894dbc328947>

Make AI Tools模块使用教程

<https://xiaobot.net/post/e2235352-7695-4380-8b18-9b88bbc07db4>

Make中文教程：常用函数（General functions）

<https://xiaobot.net/post/a6be0aba-a083-4986-a18c-8c57123cea88>

Make中文教程：使用函数（Using functions）

<https://xiaobot.net/post/4c23ecfc-5344-4df3-988b-9ceab7b71a17>

Make中文教程：数学函数（Math variables）

<https://xiaobot.net/post/1aeb2ada-e81f-4914-9e15-807658afe2ed>

Make中文教程：字符串函数（String functions）

<https://xiaobot.net/post/94e5fae1-755a-47e6-a266-6d1968489586>

Make中文教程：日期和时间函数\(Date and time functions\) <https://xiaobot.net/post/4b5f09c2-5caa-4d53-ae52-8743cb6a9a82>

Make中文教程：数组函数（Array functions）

<https://xiaobot.net/post/95a668ff-b73c-4b7b-b018-ff6386474c3a>

Make中文教程：自定义函数（Custom functions）

<https://xiaobot.net/post/6ca9b633-5b29-48c9-af31-a4caa670f83e>

Make中文教程：变量（Variables）

<https://xiaobot.net/post/38103885-d879-4e38-97e7-37d915b268c4>

Make中文教程：数学函数（Math variables）

<https://xiaobot.net/post/fe78bec9-691b-492c-9a42-df526ba222f9>

Make中文教程：证书和密钥\(Certificates and keys\)

<https://xiaobot.net/post/aea31564-dcdc-4d39-9024-94d55ebbb772>

Make中文教程：日期时间解析符号（Tokens for date/time parsing） <https://xiaobot.net/post/dd05ed5b-10d0-4fb3-ae5c-e6e014831429>

Make中文教程：日期时间格式符号（Tokens for date/time formatting） <https://xiaobot.net/post/b4c1b904-0efc-4049-abb2-8665ad97e5a8>

Make中文教程：工具（Tools）

<https://www.xiaobot.net/post/0d864ebb-61e6-456c-8958-7ea3477cabc6>

Make中文教程：网络钩子（Webhooks）

<https://www.xiaobot.net/post/874d0617-7c5f-4358-8778-3622422d0f05>

Make中文教程：文本解析器（Text Parser）

<https://xiaobot.net/post/528c34b5-2e20-432d-911f-f5fc26ac1ed3>

Make中文教程：流程控制（Flow control）

<https://xiaobot.net/post/28c6187f-a083-4abe-a2a3-12b5780de08b>

Make中文教程：数据结构（Data structures）

<https://xiaobot.net/post/c138f529-258d-42fe-a054-97a756a5ab10>

Make中文教程：数据存储（Data store）

<https://xiaobot.net/post/def7f5c1-2bb9-418f-a21f-024426652789>

Make中文教程：Make开发工具（Make DevTool） <https://www.xiaobot.net/post/8221e7d2-0991-44f8-8b09-e56cf2be289a>

Make中文教程：JSON

<https://www.xiaobot.net/post/caa604d3-ec6d-419f-9390-c3b16df594a3>

Make中文教程：XML

<https://www.xiaobot.net/post/0d60a7f1-8dac-47ee-8e50-01ce78bfec2b>

Make中文教程：Make 中错误处理的概述（Overview of error handling in Make） <https://www.xiaobot.net/post/3b737e14-af82-4055-aaea-1f78de767282>

Make中文教程：Make 中错误和警告的介绍（Introduction to errors and warnings in Make） <https://www.xiaobot.net/post/0414bbf6-0caf-4522-9dfd-a71a37288348>

Make中文教程：Make中的错误类型（Types of errors in Make） <https://www.xiaobot.net/post/ef4415dc-2e7d-477a-a88e-7e8f3dbff588>

Make中文教程：Make中的警告类型（Types of warnings in Make） <https://www.xiaobot.net/post/167ee104-0aa3-4e8b-945d-0cf97b001c27>

Make中文教程：修复缺失数据错误（Fixing missing data errors） <https://xiaobot.net/post/8dfad570-fa10-4d03-968c-91598ff48df9>

Make中文教程：回滚错误处理器（Rollback Error Handler） <https://www.xiaobot.net/post/5bef1368-7cd1-4a0a-9133-b76e57c30312>

Make中文教程：恢复错误处理程序（Resume Error Handler） <https://www.xiaobot.net/post/35a47495-eb66-4203-9d4b-88f10e917413>

Make中文教程：忽略错误处理器（Ignore Error Handler） <https://www.xiaobot.net/post/0a8e69dc-39c3-488f-b726-cb8c28d83171>

Make中文教程： 提交错误处理程序（Commit Error Handler） <https://www.xiaobot.net/post/76e48380-16a9-4389-bfbd-757f8a367d24>

Make中文教程：中断错误处理器（Break error handler） <https://www.xiaobot.net/post/7f3fc119-c059-4149-8c2e-0493ef63a72e>

Make中文教程：修复连接错误（Fixing connection errors） <https://www.xiaobot.net/post/41bc923e-3634-4352-91d5-b8f5590d6d6f>

Make中文教程：修复速率限制错误（Fixing rate limit errors） <https://www.xiaobot.net/post/b3812a9d-8937-4ec4-bc1a-85f9a0b3bb23>

Make中文教程：丢弃操作（Throw）

<https://www.xiaobot.net/post/f734bd26-de1c-432b-bfbe-eb3c7cec2c0b>

Make中文教程：指数退避（Exponential Backoff） <https://www.xiaobot.net/post/92158494-8d2f-4c2b-afc8-4804b47d40e6>

Make中文教程：OpenAI模块常见错误及排查指南 <https://www.xiaobot.net/post/6644c74c-970c-43bf-a6e1-ad8ebaa532aa>

Make中文教程：HTTP模块常见错误及排查指南 <https://www.xiaobot.net/post/b2dc546e-3d1d-47b9-b3c4-0ff48e1c32e7>

Make中文教程：Notion模块常见错误及排查指南 <https://www.xiaobot.net/post/5dcf39c4-a0c2-4cc5-9391-77a91ceac939>

Make中文教程：Notion <https://www.xiaobot.net/post/59e0ee29-fbfb-4547-9c69-8d49b2e8cf95>

Make中文教程：微信公众号（WeChat Official Account） <https://www.xiaobot.net/post/2d77f7a6-ce1d-447e-ad63-0df63b660190>

Make中文教程：HTTP <https://www.xiaobot.net/post/724ee1e8-49bd-4e2a-9a80-8340f77b0424>

Make中文教程：OpenAI \(ChatGPT、Whisper、DALL-E\) <https://www.xiaobot.net/post/2fe22e2e-cb71-49d2-8a70-e910d20287d6>

Make中文教程：谷歌文档（Google Docs）

<https://www.xiaobot.net/post/9210a3f5-35e0-4417-a7b6-03e13b18d458>

Make中文教程：

[PDF.co](<http://PDF.co>) <https://www.xiaobot.net/post/0f6092a1-4ca6-4f03-a2ea-72ce08259459>

Make中文教程：PDF4me

<https://www.xiaobot.net/post/06054fad-16b5-4841-9bde-8e9fbf4c795d>

Make中文教程：HTML/CSS 转图像（HTML/CSS to Image） <https://www.xiaobot.net/post/6e2e1cb8-2d9a-472a-a659-4667fd0c6bec>

Make中文教程：Inoreader

<https://www.xiaobot.net/post/01475e6f-f47f-46e1-a955-0f5c1268a2e0>

Make中文教程：图像（Image）

<https://www.xiaobot.net/post/a80faa63-6ae5-4fbc-885e-7408dd47c280>

Make中文教程：电子邮件（Email）

<https://www.xiaobot.net/post/3bb62417-18ea-4dad-8889-a3d9f23d563e>

Make中文教程：谷歌云盘（Google Drive）

<https://xiaobot.net/post/d7558027-6ab2-4060-b0e5-0b6c1a580cd1>

Make中文教程：X模块授权

<https://www.xiaobot.net/post/ccd27632-3a2c-4838-b165-8a622bae00bd>

Make中文教程： 人工智能 Claude（Anthropic Claude） <https://www.xiaobot.net/post/ecae07b7-5c1f-4a34-82b7-01f5a5c93fee>

Make中文教程： ElevenLabs

<https://www.xiaobot.net/post/46e40c58-7a50-4201-a728-e446a231b0d2>

Make中文教程：Hugging Face

<https://xiaobot.net/post/ee464dd2-e9a3-4967-b0d5-722f82ad84ad>

Make中文教程：使用自定义OAuth客户端连接到Google服务（Connecting to Google services using custom OAuth client）

<https://xiaobot.net/post/2ac9bd72-cecf-4b03-b9c2-879091cf494f>

Make中文教程：Pinecone

<https://xiaobot.net/post/2cc0815e-d9af-4412-a44f-f377d30c676a>

Make中文教程：Instagram

<https://xiaobot.net/post/9978f033-8af0-4886-80d4-7ee3a130983c>

Make中文教程：Apify

<https://www.xiaobot.net/post/cc85a02e-6949-43e1-8899-060887e0d2b9>

Make中文教程：Cloudinary

<https://www.xiaobot.net/post/4c985f41-3e51-4ce3-91b2-febe2347755c>

HTTP第三方API调用：OpenRouter

<https://xiaobot.net/post/11058111-b8d0-4134-b62d-e7db5a721b76>

HTTP第三方API调用：EXA AI

<https://xiaobot.net/post/08d3b581-7ef8-4930-a27e-bec1aaa7a3b0>

HTTP第三方API调用：Jina

<https://xiaobot.net/post/0bcf3c73-817e-4f19-966d-09f7891eb097>

HTTP第三方API调用：Kimi

<https://xiaobot.net/post/ad7268ab-1d84-442c-b2a4-af9ed1561ce8>

HTTP第三方API调用：博查

<https://xiaobot.net/post/3eca3296-f550-4773-a55a-2ab40651f816>

HTTP第三方API调用：DeepSeek

<https://xiaobot.net/post/4bfefd3f-16f6-42be-9ad5-bc11610c2f5f>

HTTP第三方API调用：

[302.AI](<http://302.AI>) <https://xiaobot.net/post/e166755d-c49f-4b61-abc5-72e4739c5bc9>

HTTP第三方API调用：Firecrawl

<https://xiaobot.net/post/c38bd38a-e4a0-4277-8923-1db9f4d9fdae>

HTTP第三方API调用：Deepbricks

<https://xiaobot.net/post/fd269c61-2bbc-4c17-ab46-45d86f99f431>

HTTP第三方API调用：Replicate

<https://xiaobot.net/post/00f2a006-cd75-4b44-877e-97a41e9240fc>

HTTP第三方API调用：智谱AI

<https://xiaobot.net/post/18ae22f8-edc0-4749-baec-84ca7fd5c190>

HTTP第三方API调用：Google Gemini

<https://xiaobot.net/post/4a896e91-3121-45e1-b625-e7e9f379d01d>

HTTP第三方API调用：硅基流动

<https://xiaobot.net/post/6cf2fc75-4197-4058-b643-ab9c7c7353e5>

HTTP第三方API调用：Anthropic Claude官方

<https://xiaobot.net/post/cf65ccbf-0dc1-4f2d-a99a-607dfde558e0>

HTTP第三方API调用：[Fal.ai](<http://Fal.ai>) <https://xiaobot.net/post/efd6a3ef-ccd4-4c7e-b45c-97989f98a490>

HTTP第三方API调用：阿里通义千问（千万tokens免费额度） <https://www.xiaobot.net/post/bc73f863-d636-4519-9ca4-b7c949d0567e>

HTTP第三方API调用：豆包大模型（50万免费Token） <https://www.xiaobot.net/post/e0907337-32e5-4890-bcec-84f2b3fbb1e5>

HTTP第三方API调用：MINIMAX（15元免费额度） <https://www.xiaobot.net/post/8edac204-a4b6-46d4-ae2c-92129799fba6>

科研相关提示词集合

<https://www.xiaobot.net/post/2586a279-66fe-4f0d-a0db-4934943ba398>

OpenAI提示词优化工具内置系统提示词

<https://www.xiaobot.net/post/7019f508-603c-4817-a95b-fc9aaa6d7fe8>

英文提示词示例合集

<https://www.xiaobot.net/post/1cec4f93-eec2-4afe-a861-7c89921d809e>

提示词生成方法

<https://www.xiaobot.net/post/7b720417-fcb6-42c8-83fc-2536109cdd68>

写作相关提示词

<https://www.xiaobot.net/post/bfd29479-69cb-4caa-be46-5c9ccd7ebacf>

格式整理提示词

<https://www.xiaobot.net/post/b1a8f97d-685e-4bd6-bb74-f26159d34316>

如何通过 Prompt 获取并整理所有 LLM 系统提示词 <https://www.xiaobot.net/post/bdc9f626-f2e7-4931-a467-00388e81aa2e>

Make可调用免费API 1

<https://xiaobot.net/post/19544559-034d-431a-8584-4a80c013a2e8>

Make可调用免费API 2

<https://xiaobot.net/post/0ef447b6-545c-4a82-9977-8fba18d728cb>

Make可调用免费API 3

<https://xiaobot.net/post/76eba93b-3055-4ef6-bf8a-804f61362835>

两款免费AI大模型API，轻松集成到Make！

<https://xiaobot.net/post/aca03f5f-b237-4e30-ba83-a54fde0b1afd>

每月25美元免费额度的大模型API来了！

<https://xiaobot.net/post/ce9457a5-9ce9-4c7f-8afb-9e00eb0084dd>

300 个免费 API 合集来啦，Make 工作流超强帮手！

<https://xiaobot.net/post/c16a7e4b-4326-4172-a368-120bf0b51121>

OpenRouter原生模块登录Make，支持结构化输出

<https://xiaobot.net/post/cfed3d6b-5795-4bec-9fd9-57c4096d988d>

全新大模型上线！免费平替OpenAI，支持Make和结构化输出！ <https://xiaobot.net/post/17cb50a4-b256-46cf-90aa-c874f34b980a>

OpenAI API 手把手充值教程

<https://xiaobot.net/post/d4199829-cc22-41b2-aee9-7658cd378d06>

Mistral 免费25美元 Firecrawl 免费 100 美元领取方法手把手教程 <https://xiaobot.net/post/abf4627a-c32f-427e-8689-4321995fff80>

Make调用xAI Gork手把手教程，充值 5 美元，每月 150 美元免费额度！ <https://xiaobot.net/post/0004527c-2a6c-412b-b623-a7e7ede12736>

火山引擎调用 DeepSeek R1 大模型手把手教程

<https://xiaobot.net/post/c706c6a9-4729-4c24-8f7f-96b8ebf2b14f>

Make 低成本调用阿里云 DeepSeek R1 的手把手教程（免费百万 Token） <https://xiaobot.net/post/abf4627a-c32f-427e-8689-4321995fff80>

Make工作流必备搜索 API：EXA 免费 80刀额度领取手把手教程 <https://xiaobot.net/post/9d334e30-590c-4084-879a-41b86aa6823a>

Make工作流图床必备：Cloudflare R2 存储桶手把手使用教程 <https://xiaobot.net/post/7b22c05d-79b3-4a94-a24f-156d13dc94c6>


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在 **Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2025-06-11/720345/f32a0884a9a442a5f065d3bb6d6fe2cc.png)