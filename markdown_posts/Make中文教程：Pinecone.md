---
title: "Make中文教程：Pinecone"
created_at: "2024-12-05T13:51:30.000000Z"
uuid: "2cc0815e-d9af-4412-a44f-f377d30c676a"
tags: ['Make基础教程']
---

# Make中文教程：Pinecone

**标签**: Make基础教程

使用 Pinecone 模块在 Make 中,您可以增加、更新、检索、查询和删除向量,并发出 API 调用。

要开始使用 Pinecone,在 pinecone.io 创建一个账户。

请参考 Pinecone API 文档以了解可用的端点列表。


## **将 Pinecone 连接到 Make**

建立连接:

  1. 登录您的 Pinecone 账户。

  2. 在左侧边栏上单击"API 密钥"按钮,然后单击"创建 API 密钥"按钮。

  3. 为 API 密钥输入一个名称,然后单击创建密钥。

  4. 单击"复制密钥值"按钮并将 API 密钥保存在安全的地方。

  5. 登录您的 Make 账户,将 Pinecone 模块添加到您的方案中,然后单击创建连接。

  6. 在"连接名称"字段中输入连接的名称。

  7. 在索引名称字段中输入您的索引名称。您可以通过导航到左侧边栏上的"索引"选项卡来找到您的索引名称。如果您的索引名称是example-123.svc.exampleEnvironment.pinecone.io，请输入 example-123.svc.exampleEnvironment 。

  8. 在"API 密钥"字段中粘贴在步骤 4 中复制的 API 密钥。

  9.  点击保存。

  10. 如果提示,请验证您的账户并确认访问权限。

您已成功建立连接。您现在可以编辑您的场景并添加更多 Pinecone 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤操作。


## **构建Pinecone场景**

**  向量**

  -  将向量插入或更新

  -  更新向量

  -  获得一个向量

  -  查询向量

  -  删除向量

**  其他**

  -  发起 API 请求


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注翔宇工作流！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-12-05/720345/3b5b980c878908e96bb71b26e4421034.png)