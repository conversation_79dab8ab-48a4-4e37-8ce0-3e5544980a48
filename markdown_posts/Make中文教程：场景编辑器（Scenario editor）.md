---
title: "Make中文教程：场景编辑器（Scenario editor）"
created_at: "2024-09-21T08:29:16.000000Z"
uuid: "ccb6690f-c14f-4b43-8184-7add50a8967b"
tags: ['Make基础教程']
---

# Make中文教程：场景编辑器（Scenario editor）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于场景编辑器的教程。

**教程简介** ：本教程介绍了Make.com场景编辑器的使用方法，包括如何创建和编辑场景，添加和管理模块，设置调度，查看日志以及使用工具和控件来优化流程。

**学习目标** ：学习如何在Make.com场景编辑器中创建和编辑自动化流程，掌握各个功能模块、调度设置、路由配置以及日志查看和错误处理的具体操作。

**学习内容** ：

  1. 创建和编辑场景

  2. 添加和移除模块

  3. 调度设置及场景运行

  4. 路由设置与过滤器应用

  5. 日志记录与错误处理

  6. 使用工具和收藏功能优化流程

**概念理解** ：

  - **模块** ：自动化流程中的单个步骤，连接不同应用。

  - **调度设置** ：定义场景运行的时间和频率。

  - **路由** ：两个模块之间的连接，用于数据传输。

  - **日志** ：记录场景执行的历史和详细信息。

**任务练习** ：

  1. 创建一个新场景并添加两个模块，连接它们以完成一个简单的自动化任务。

  2. 设置调度，使场景每隔15分钟运行一次。

  3. 使用日志功能查看场景运行历史并记录操作数量。


# **场景编辑器**

Make 的可视化场景编辑器使您能够创建和编辑场景。

转到"情景"并单击"创建新情景"打开情景编辑器。

![](https://static.xiaobot.net/file/2024-11-24/720345/1b3284815ab7e8dcc02d6544d63b1704.png)

选择"编辑"编辑现有场景,或点击现有场景页面的任意位置打开场景编辑器。


## **  模块**

在 Make 中, 模块\(Modules\)代表自动化过程中的单个步骤。有不同类型的模块。

您可以添加无数个模块到您的场景中。操作的数量取决于您的订阅情况。

为了让 Make 与给定应用程序进行通信,你需要创建一个连接。

根据您选择的模块,右键单击该模块可打开以下选项:

  - **  设置**

  - **  仅运行此模块**

  - **  添加模块**

  - **  添加错误处理程序**

  - **  重命名**

  - **  关闭**

  - **  复制模块**

  - **  添加备注**

  - **  删除模块**


## **  日程设置**

时间表设置允许您设置运行场景的频率。默认情况下,场景每 15 分钟运行一次。点击时钟来设置场景时间表。您也可以通过单击"时间表设置"在工具栏上访问时间表设置。

![](https://static.xiaobot.net/file/2024-11-24/720345/be8818a5dbdfc041c13b89879d198e0e.png)

时钟代表了一个预定的触发器,您场景的起点。场景也可以以即时触发器开始,这在视觉上表示为闪电。了解不同类型的触发器。


## **  建立路线**

路径是两个模块之间的连接,通过虚线的形式进行视觉表示。

![](https://static.xiaobot.net/file/2024-11-24/720345/87fea90de9e36fa757c8a3f894b2ebef.png)

点击将两个模块连接的路径下方的扳手\( \)以显示以下选项:

  - 设置过滤器 - 根据特定标准对过滤器包进行分组。您还可以通过单击连接两个模块的路由来访问此选项。

  - 取消链接 - 当您取消链接一个模块时,它将不会被执行,除非它是一个触发器。

  - **  添加路由器**

  - **  添加模块**

  - **  添加备注**


## **添加或删除模块**

要添加一个模块,请单击加号创建一个空模块。会弹出一个包含应用程序/服务列表的菜单,您可以从中选择添加另一个模块到您的方案中。

![](https://static.xiaobot.net/file/2024-11-24/720345/0f098ce5b02f64c1cbc035d6f1edc75d.png)

要删除一个模块,右击要删除的模块,然后选择"删除模块"。


## **  场景工具栏**

情境工具栏包含帮助您管理和设置情境的工具,如"运行一次"按钮或计划设置。情境工具栏位于情境编辑器的底部。

情境工具栏有两种形式:

  -  倒塌了:

![](https://static.xiaobot.net/file/2024-11-24/720345/d14dc1fdf281ab47b8f557d0f0ac5844.png)
  -  扩展: 翻译文本:

![](https://static.xiaobot.net/file/2024-11-24/720345/acf9607c4813d9e8471c0851e600921b.png)![](https://static.xiaobot.net/file/2024-11-24/720345/da0eeeca278d9ef33a819da8777bc5e0.png)


### **  1 - 运行一次**

一旦您配置好您的场景,请单击"立即运行"来测试您的场景。在您的场景执行之后,您可以查看操作的数量,以及日志中的更多详细信息。

![](https://static.xiaobot.net/file/2024-11-24/720345/16b34a5e0a96799f0f9c9417ba25212c.png)


### **2 - 激活和安排**

如果一切正常运行,您可以激活该方案。点击调度设置旁边的开关即可激活或取消激活您的方案。当您的方案处于活动状态时,开关为绿色。

![](https://static.xiaobot.net/file/2024-11-24/720345/aa0188bfb829ae18bc5d5f4b971bd0a1.png)![](https://static.xiaobot.net/file/2024-11-24/720345/2231abda299974cb0454e9b1c5b37467.png)

了解主动和被动场景之间的差异。

激活开关旁边是日程设置。在您激活方案后，Make 会根据设定的日程运行您的方案。默认情况下,方案每 15 分钟运行一次。

如果您的方案不如预期运行,请查看我们的错误处理部分。


### **  3 - 情景工具**

场景工具是一组功能，可让您在场景编辑器中管理和检查场景。场景工具显示在场景工具栏上，以操作图标的形式呈现，如保存图标用于保存场景或设置图标用于访问场景设置。

**  保存**

当您完成编辑场景时,点击保存图标以保存当前场景。

![](https://static.xiaobot.net/file/2024-11-24/720345/a1f94646ddef0a8c5c5e905d098d0125.png)![](https://static.xiaobot.net/file/2024-11-24/720345/d3ff1065f189f5b00982bd298a1184fa.png)![](https://static.xiaobot.net/file/2024-11-24/720345/c6062191a36b50b22bfc2761c1bc84d2.png)

**  场景输入**

点击"情景输入"按钮以打开情景输入面板。在情景输入面板中，您可以设置场景的输入数据。

![](https://static.xiaobot.net/file/2024-11-24/720345/a295343c0d81d73e4dd41476128760c7.png)

**  场景设置**

点击齿轮图标打开场景设置。

**  笔记**

点击"便笺"图标以显示或隐藏已添加到您方案中模块的便笺。

要向模块或者连接两个模块的路径（路线）添加新注释,右击要添加注释的模块或者点击相应的路线,然后选择"添加注释"。在文本框中写下您的注释。

你可以添加无限数量的笔记。

![](https://static.xiaobot.net/file/2024-11-24/720345/25a36ed52f324fc9abb7e8cf9e8c1579.png)

**  以前的版本**

允许您恢复场景的先前版本。了解如何恢复先前的场景版本。

**  自动对齐**

![](https://static.xiaobot.net/file/2024-11-24/720345/46e8ee760c7cef09e9a5bcd73624e56a.png)

点击魔法棒图标,即可自动排列场景中的模块。

![](https://static.xiaobot.net/file/2024-11-24/720345/59e19ea82dae15ce4141c59bd189428c.png)![](https://static.xiaobot.net/file/2024-11-24/720345/dd34a1c7ca20c89df41e830a87a53909.png)


### **4 - 更多场景工具**

点击三个点以打开以下菜单:

  - 说明流程: 允许您查看模块之间的数据流。例如,您可以在运行方案之前检查数据流,这样就可以在运行之前看到路由器、迭代器或聚合器的效果。

  - 导出蓝图:允许您将场景蓝图以 JSON 格式保存到您的设备。

  - 导入蓝图:允许您从设备上以 JSON 格式加载场景蓝图。

![](https://static.xiaobot.net/file/2024-11-24/720345/0322988fea961f2862671b7e347839f3.png)


### **  5 - 数据应用程序**

在您的 Make 场景中,您通常需要修改或处理数据。以下是经过 Make 验证和开发的应用程序选择,提供了一系列模块来帮助您在场景中处理数据:

  - Flow Control  

  -  工具

  -  文本解析器


### **6 - 常用应用程序**

情景工具栏包含最常用于您所打开场景的应用程序列表。您可以使用这些常用应用程序作为快捷方式,而不必在向场景添加应用程序模块时搜索应用程序。

当您点击应用程序图标时，您会弹出一个窗口,其中列出了该应用程序的各个模块。选择应用程序模块后,该模块将被添加到您的方案中。

您也可以通过点击列表末尾的加号图标直接添加应用程序到列表中。

![](https://static.xiaobot.net/file/2024-11-24/720345/08fc08e22d3698ededc869fbfca9435c.png)


### **  7 - 日志**

日志记录了一次场景运行中发生的每一个事件。您可以通过单击场景工具栏中的日志图标来打开场景日志:

![](https://static.xiaobot.net/file/2024-11-24/720345/ae5f979355b07a5bcda7b42624b5f5d1.png)


### **展开和折叠工具栏**

场景工具栏上的最后一个按钮可用于切换工具栏的折叠和展开形式。


## **  手术次数**

操作是模块执行的任务。一旦场景被执行,Make 会在每个模块的右上角显示白色气泡中的操作次数。点击气泡可查看模块操作的结果。

![](https://static.xiaobot.net/file/2024-11-24/720345/3c3cc0f8bfcb3336c3e9ec55e8cee428.png)

根据您场景中模块执行的操作总数向您收费。更多信息请查看如何计算操作数量。


## **  退出编辑**

点击左上角的左箭头关闭场景编辑器,返回到场景详情页面。

![](https://static.xiaobot.net/file/2024-11-24/720345/ee3bd9350efa0c88c7c4079904620d4f.png)


## **  场景名称**

要重命名场景,请单击左上角的场景名称并进行编辑。按 Enter 键或单击编辑字段外部以保存场景的新名称,然后单击保存图标以保存新名称。

![](https://static.xiaobot.net/file/2024-11-24/720345/bb48a32ff6a3294dccd9ff615dde91e0.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/84f9b4009481caa2c8ac30277d42c467.png)