---
title: "Make中文教程：忽略错误处理器（Ignore Error Handler）"
created_at: "2024-08-30T05:38:46.000000Z"
uuid: "0a8e69dc-39c3-488f-b726-cb8c28d83171"
tags: ['Make基础教程']
---

# Make中文教程：忽略错误处理器（Ignore Error Handler）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于忽略错误处理的教程。

  1. **教程简介** ：本教程介绍如何使用Make.com中的忽略错误处理器（Ignore error handler）来忽略错误并确保场景（scenario）在出现错误时继续运行。

  2. **学习目标** ：了解如何应用忽略错误处理器，从而在流程中遇到错误时，不停止流程的运行，并移除导致错误的数据包。

  3. **学习内容** ：教程涵盖如何在Make.com场景中添加忽略错误处理器、忽略错误数据包处理逻辑，以及实践示例以测试和展示错误处理器效果。

  4. **概念理解** ：忽略错误处理器（Ignore error handler）是一个模块，当场景中的某个模块发生错误时，它会移除导致错误的数据包，并让场景继续处理剩余的数据包。

  5. **任务练习** ：创建一个包含多个模块的场景，在其中一个模块中故意制造一个错误，接着添加忽略错误处理器，观察并记录场景在处理错误后的运行情况。


# **忽略错误处理器**

忽略错误处理程序会忽略错误并从场景流中移除该包。场景运行将继续执行下一个包。

当您知道您的场景中可能存在不正确的数据,并且这些数据不会影响您的流程时,您可以使用忽略错误处理程序。忽略错误处理程序可以防止在出现错误时关闭场景,即使出现错误,也会将场景运行标记为成功。

例如:这个演示场景包含五个模块。该场景对于测试和展示错误处理程序的效果很有用:

  1. JSON - 解析 JSON 数据可提供由三个记录 ID 组成的测试数据数组。

  2. 迭代器将数组拆分为单独的捆绑包。

  3. 数据存储 - 更新记录：更新数据存储中的数据。

  4. 数据存储 - 更新记录：本模块再次更新数据。这次模块的工作方式有所不同。在模块映射中，有一个映射明确地创建了一个错误：

![](https://static.xiaobot.net/file/2024-11-28/720345/cdde7bb88e901a1efc57ef2ff2b4cd08.png)

映射将 null 值插入必需的密钥字段中,这总是会创建 BundleValidationError 。

  5. Slack - 发送消息：发送一条消息到私密测试频道。

这就是示例场景的样子:

![](https://static.xiaobot.net/file/2024-11-28/720345/f468c1e3b6cb9fcbda89690629327107.png)

当我们运行示例场景时,我们将得到 BundleValidationError :

![](https://static.xiaobot.net/file/2024-11-28/720345/1557856eec5d758a4292e63f3e8015b1.png)![](https://static.xiaobot.net/file/2024-11-28/720345/63fb3850d5c51e292b5b63b5a576a50c.png)

如果我们将"忽略错误处理程序"添加到"更新记录"模块,则"忽略错误处理程序"将从场景流中删除该包。该包不会进入第五个（发送消息）模块。第三个包通过该场景运行。

![](https://static.xiaobot.net/file/2024-11-28/720345/d974a0b6989845c756016c9f6bfd078b.png)

关于错误处理策略的更多信息请查看错误处理概述。


## **即使出现错误,也保持方案运行**

使用"忽略错误处理程序"，您可以从场景流程中删除导致错误的捆绑包,并处理场景中其余的捆绑包。此外,Make 将继续按计划运行您的场景,而不会因为出错而禁用调度。

以下是一个示例:在更新记录模块中输出错误

![](https://static.xiaobot.net/file/2024-11-28/720345/ef1b2695133aefb5a2c338a91164b130.png)![](https://static.xiaobot.net/file/2024-11-28/720345/4052d5077f17809de09373b21fc4735b.png)

忽略错误并继续运行您的场景,请按以下步骤操作:

  1. 右键单击导致错误的模块。在菜单中选择添加错误处理程序。

  2. 选择忽略错误处理程序。

  3.  保存您的场景。

您的场景会继续运行,忽略错误。当数据存储模块出现错误时,忽略错误处理程序会将该包从场景流中删除。

![](https://static.xiaobot.net/file/2024-11-28/720345/6496f6368b1e9524a0dc20a7c9d0c848.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/487dba09e100423534afe67141780d3e.png)