---
title: "Make20.Make工作流改造方法论：一个工作流如何变成1000个工作流？"
created_at: "2024-11-12T08:00:19.000000Z"
uuid: "a4d271ac-65c0-40ca-acfa-e8e8967c9ea9"
tags: ['资源包']
---

# Make20.Make工作流改造方法论：一个工作流如何变成1000个工作流？

**标签**: 资源包

# **视频链接**

<https://xiangyugongzuoliu.com/20-make-workflow-scaling-methodology/>


# **视频资源**

**1\. 使用教程和简要过程**

详细的模版导入教程可以参考以下链接：

<https://xiaobot.net/post/03a57a85-017d-4433-8b69-85ac73779039?refer=887f6c59-e879-48e4-a8fb-ef07f8dac6b3>

简要过程：

1\. 登录Make账户，创建新场景并导入翔宇工作流的蓝图文件

2\. 配置各模块的账户和API密钥

3\. 链接和配置Notion数据库

4\. 保存并运行工作流

**2\. Make 工作流模版文件下载**

**（如遇问题请随时访问该页面检查是否有新版本更新）** ：

[工作流下载](<https://pan.quark.cn/s/288b6889769c>)

提取码：ZF36

**3.数据库模板**

点击下方链接查看数据库模板：

[https://xiaoyuzaici.notion.site/128017a905fd802aa7f8e5d4722b310a?v=128017a905fd81cea537000c450a45fa&pvs;=4](<https://xiaoyuzaici.notion.site/128017a905fd802aa7f8e5d4722b310a?v=128017a905fd81cea537000c450a45fa&pvs=4>)

**4.PPT:**

[**https://miro.com/app/board/uXjVK14yTgg=/?share\_link\_id=760826879387**](<https://gamma.app/docs/20Make1000-czhmgqvem9xgyp4>)

通过这些工具和资源，你可以快速完成翔宇工作流视频中的工作流搭建！

![](https://static.xiaobot.net/file/2025-01-23/720345/8df9922497593e4274bc85f983122397.png)