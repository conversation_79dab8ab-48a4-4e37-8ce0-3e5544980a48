---
title: "Make中文教程：操作（Operations）"
created_at: "2024-09-21T12:40:44.000000Z"
uuid: "e705f627-6857-4cf8-84e2-e3b8d9265caa"
tags: ['Make基础教程']
---

# Make中文教程：操作（Operations）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于操作流程的教程。 **教程简介** ： 本教程介绍了Make.com中操作（operations）的使用和计费方式。用户将学习如何管理操作次数，以优化成本和防止场景中断。

**学习目标** ： 理解操作在Make.com中的重要性，学会如何监控和管理操作次数，以避免超过每月限制，从而保证自动化流程的连续运行。

**学习内容** ：

  - 操作的定义和用例

  - 不同模块如何使用操作

  - 监控操作使用情况

  - 优化操作使用的方法

  - 管理组织和团队的操作使用情况

**概念理解** ： 操作（operations）指模块执行动作时消耗的资源。在Make.com中，操作数量直接影响服务成本，通过监控和管理操作次数，用户可以有效控制预算和避免流程中断。

**任务练习** ： 创建一个读取Google表格数据并发送至Gmail的场景。监控操作次数，尝试通过优化模块使用和限制数据处理量来减少操作消耗。


# **操作**

每次模块执行一个操作时,都会使用一个操作。例如,向您的 Google 表格添加一个行或从您的 Gmail 帐户获取数据,每个操作都会使用一个操作。

了解您的操作使用情况很重要,因为 Make 根据您的场景中模块执行的总操作数进行收费。一个场景中处理的更多模块和软件包会导致操作数量更高。

如果您超出每月操作限制,您的场景将被暂停,直到下一次使用重置。要立即恢复您的场景,您可以购买额外的操作,或者您可以启用额外操作的自动购买功能,以防止您的场景在将来暂停。您可以在下面了解更多关于可用选项的信息。

![](https://static.xiaobot.net/file/2024-11-24/720345/6920baf1a747cca8b215ae7e10621c94.png)


## **如何在 Make 中使用模块的操作**

每次场景中的模块执行一个操作时,它就会使用一个操作。如果一个模块输出多个信息包（比如一个列表模块返回多条记录），它会使得下一个模块针对每个信息包分别运行一次,分别处理每条记录。一个模块的总操作数等于它执行的操作次数。

所有类型的模块根据它们在场景中的位置和功能,可使用从一个到多个操作

  - 触发模块始终使用一个操作，不管是否接收到任何数据。

  - 搜索模块使用一个操作运行,但可以输出多个包。例如,Google Sheets 搜索行模块将运行一次并使用一个操作,但它可以在一次运行后返回关于多个行的信息。每一行都是一个单独的数据包。

  - 操作模块\(添加、更新、删除等\)使用尽可能多的操作来处理所有输入数据。如果 Google 表格搜索行模块返回 10 行数据,则下一个删除行模块将运行 10 次来删除所有 10 行,并使用 10 次操作。

  - 聚合器模块会将多个捆绑包合并为一个。聚合器会为每个聚合使用一个操作。如果聚合器从您的 Google 表格记录中创建一个数组,它将只使用一个操作。

  - 迭代器模块与聚合器模块相反。迭代器接受一个数组包并将其拆分为多个输出包,因此它们对整个数组使用一个操作,但它们之后的模块将收到多个输入。

当 Make 不使用操作时,有几个模块例外:

  - 错误处理模块\(回滚、终止、恢复、提交、忽略\)

  - 路由器模块和过滤器


## **从所选艺人处添加热门歌曲到您在 Spotify 上的喜欢歌曲**

在这个情况下,Make 获取您选择的艺术家的前 10 首歌曲列表,并将其添加到您喜欢的歌曲列表中。

当某个场景执行完毕后，Make 会在模块上方的小白圈中显示每个模块执行的操作数。

![](https://static.xiaobot.net/file/2024-11-24/720345/77177a7125123ec893c5ca1581b31643.png)

在这个例子中，第一个 Spotify 模块返回了 10 首歌曲并进行了 1 次操作来获取信息。每首歌曲都被作为一个信息包返回，所以下一个模块需要处理 10 个信息包。

![](https://static.xiaobot.net/file/2024-11-24/720345/ba5f3f27b99f3a088209dd71f8841be0.png)

第二个 Spotify 模块必须将 10 首歌曲中的每一首都添加到播放列表中,需要执行该操作 10 次。因此,该模块处理了 10 个捆绑包并使用了 10 个操作。

![](https://static.xiaobot.net/file/2024-11-24/720345/e4b91c2ab530285e1ad148a9ad4d20c9.png)

此方案共需执行 11 项操作。

![](https://static.xiaobot.net/file/2024-11-24/720345/fbab418c9cdb214743ed9adeb07cca28.png)


## **检查场景的总操作次数**

在上面的例子中,您可以看到如何在一次运行期间统计场景中的操作次数。但是知道您的场景总共使用了多少操作也很重要。

为您的场景进行调度会导致它每次运行时都会消耗操作。例如,如果您的 Spotify 场景被设置为每小时运行一次,那么一天内就会消耗 264 个操作\(11x24\)。如果您将其设置为每 5 分钟运行一次,它一天内就会消耗 3168 个操作\(11x24x12\)。这就是为什么您需要小心谨慎地调度您的场景。您可以通过减少某些场景的运行频率来降低所消耗的操作数量。

每个场景运行至少使用一次操作,因为触发器模块需要检查更新。即使没有检索到新数据,这些检查运行仍然会增加您的总操作次数,因为触发器根据计划每次检查都会使用一次操作。您可以在场景的历史选项卡中查看所有检查运行。如果您看不到它们,请确保隐藏检查运行开关已关闭。

![](https://static.xiaobot.net/file/2024-11-24/720345/c3f5d45b51c19be7f7af635126b32afd.png)

你也可以检查你的方案总共消耗了多少操作,而无需进行任何计算。你可以轻松地在方案列表或方案历史中找到这些信息。


### **从情景列表**

当您打开场景列表时，您可以在每个场景的名称下看到它消耗了多少操作。如果您将鼠标悬停在数字上，您可以看到计数是从何时开始的\(使用期的开始\)。

![](https://static.xiaobot.net/file/2024-11-24/720345/a8c063c3551f03a002858ce9b19d9c6d.png)

对于年度订阅,标签显示重置日期,但数据代表最近 60 天内的累计使用情况。


### **从场景历史**

要打开场景执行历史记录,请从场景列表中单击该场景。在右侧的"历史记录"下,您可以查看每个场景运行所消耗的操作次数。

![](https://static.xiaobot.net/file/2024-11-24/720345/57e6d07d733ab96dfc28468a2ee937f2.png)

同样地，您可以切换到历史记录选项卡，在表格格式中查看每次运行的消耗操作数。

![](https://static.xiaobot.net/file/2024-11-24/720345/85f176b05e7417ebc86df12d0c06cb73.png)![](https://static.xiaobot.net/file/2024-11-24/720345/b737507e48ee09e27defca17b1644002.png)


## **查看组织或团队的总操作数量**

了解每个场景所需的操作数量固然重要,但同时也需要了解您的组织以及组织内每个团队所使用的操作数量。这些信息可以从组织和团队仪表板中轻松获取。

要打开仪表板,请在左侧导航中单击"组织"或"团队"。

从组织仪表板中,您也可以查看您根据计划拥有的限制,当前期间使用了多少操作,并检查在需要时购买额外操作的选项。


## **如果你快要用完操作**

当您的操作用尽时，您的情景将暂停,直到添加更多操作或下一个结算周期开始。传入的 webhook 将根据您可用的 webhook 存储进行排队,并在您的情景恢复新的操作时得到处理。此外,轮询数据的情景将自上一次成功运行以来搜索可用记录。

![](https://static.xiaobot.net/file/2024-11-24/720345/e37a480f6e27968d3c51c0f508a17c19.png)

当您达到已购买操作的 90%和 95%时,我们将通知您。这样,您就可以确保有足够的操作来满足您的需求,或采取行动防止您的场景暂停。

如果您在下一个账单周期前用尽了操作次数,您可以这样做:

  - 在本周期结束前升级您的订阅。

  - 在固定的订阅价格下,以 1,000 个或 10,000 个的批量购买额外的操作次数。额外的操作次数与预购的操作次数使用方式相同,并在一个月内过期\(针对月度订阅\)或在一年内过期\(针对 Pro & Teams 年度订阅计划\)。

  - 开启自动购买功能：一旦您使用完 10,000 个额外操作,自动购买将再次开启 10,000 个操作。自动购买的限制为订阅中的操作次数。例如,如果您的计划有 80,000 个操作,自动购买最多可发生 8 次。我们会根据您的计划收取 10,000 个操作的费用,加上 30%的附加费。您可以在此了解更多相关信息。


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-24/720345/d80e35d3207638998dd09bfd5cec660b.png)