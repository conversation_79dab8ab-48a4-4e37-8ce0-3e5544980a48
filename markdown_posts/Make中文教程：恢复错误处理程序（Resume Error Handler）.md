---
title: "Make中文教程：恢复错误处理程序（Resume Error Handler）"
created_at: "2024-08-30T05:42:52.000000Z"
uuid: "35a47495-eb66-4203-9d4b-88f10e917413"
tags: ['Make基础教程']
---

# Make中文教程：恢复错误处理程序（Resume Error Handler）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于恢复错误处理的教程。

**教程简介** ： 本教程介绍了如何使用Make.com中的Resume错误处理程序，通过替代输出处理模块错误，从而确保流程继续进行。提供了一个包含多个模块的场景示例，演示了Resume错误处理程序的具体应用。

**学习目标** ： 学习如何在Make.com上设置和使用Resume错误处理程序。掌握通过定义替代输出来应对流程中出现的错误，确保其余流程正常进行。

**学习内容** ：

  1. Resume错误处理程序的定义和作用。

  2. 如何在错误处理程序中设置替代输出。

  3. 使用Resume错误处理程序替换导致错误的数据包。

  4. 通过实际场景示例了解错误处理的过程。

**概念理解** ： Resume错误处理程序：用于在模块发生错误时，用预定义的替代输出替换原错误输出，以确保流程可以继续进行。通过替代映射，可以修复或标记数据包以便后续处理。

**任务练习** ： 设置一个包含多个模块的测试场景，故意引入一个错误模块并添加Resume错误处理程序。定义一个替代输出，确保场景在发生错误时仍能继续执行其余部分。


# **恢复错误处理程序**

简历错误处理程序在发生错误时用替代输出替换模块输出。您在简历错误处理程序设置中定义替代输出。场景的其余部分使用替代输出。Make 会正常处理场景流中其余的捆绑包。

例如:这个演示场景包含五个模块。该场景对于测试和展示错误处理程序的效果很有用:

  1. JSON - 解析 JSON 数据可提供由三个记录 ID 组成的测试数据数组。

  2. 迭代器将数组拆分为单独的捆绑包。

  3. 数据存储 - 更新记录：更新数据存储中的数据。

  4. 数据存储 - 更新记录：本模块再次更新数据。这次模块的工作方式有所不同。在模块映射中，有一个映射明确地创建了一个错误：

![](https://static.xiaobot.net/file/2024-11-28/720345/882b0625bfe3d2b5b3104d9312322f56.png)

映射将 null 值插入必需的密钥字段中,这总是会创建 BundleValidationError 。

  5. Slack - 发送消息：发送一条消息到私密测试频道。

这就是示例场景的样子:

![](https://static.xiaobot.net/file/2024-11-28/720345/4e8c029df18b57deaf9098958a3d6599.png)

当我们运行示例场景时,我们将得到 BundleValidationError :

![](https://static.xiaobot.net/file/2024-11-28/720345/49e14917585a07b8cb4be6fcdcccf6e8.png)![](https://static.xiaobot.net/file/2024-11-28/720345/7c4d0227903a39c35ff9b966afe92c43.png)

如果我们将恢复错误处理程序添加到更新记录模块中,恢复错误处理程序将使用替代映射替换该捆绑包。当模块输出错误时,Make 将使用替代捆绑包。第三个捆绑包通过该场景运行:

![](https://static.xiaobot.net/file/2024-11-28/720345/c3a3a02766010727236b2c5168b51531.png)

当你有一个修复捆包并允许数据捆包在场景流程中继续的替代映射时,你可以使用 Resume 错误处理程序。你也可以使用 Resume 错误处理程序添加一个占位符,将数据捆包标记以便于稍后检查。

关于错误处理策略的更多信息请查看错误处理概述。


## **当包导致错误时更改包**

使用简历错误处理程序,您可以用自定义数据替换出错的捆绑包。自定义数据继续剩余的场景流程,而不是出错的捆绑包。

在简历错误处理程序设置中,您可以获得与处理模块设置相同的字段。在错误处理程序字段中,您可以提供 Make 使用的自定义数据,而不是导致错误的捆绑包。

以下是一个示例:在更新记录模块中输出错误

![](https://static.xiaobot.net/file/2024-11-28/720345/7aeb01109e79ea019a9e7ae9b774edd0.png)![](https://static.xiaobot.net/file/2024-11-28/720345/cac2d38877dbad0096098bb3f6534f24.png)

要更改自定义数据的错误捆绑包并在场景的其余部分使用它们,请按照以下步骤操作:

  1. 右键单击导致错误的模块。在菜单中选择添加错误处理程序。

  2. 选择简历错误处理程序。

  3. 使用您的自定义映射填写简历处理程序设置。

  4.  保存您的场景。

您已将恢复错误处理程序添加到您的场景中。当 Data store 模块发生错误时,恢复错误处理程序会将捆绑包替换为您的自定义映射。

![](https://static.xiaobot.net/file/2024-11-28/720345/208e8673cc33336c6b61730e247e4749.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-11-28/720345/daf1464c2e4116f8fcf88688c5aa4c38.png)