---
title: "Make中文教程：谷歌文档（Google Docs）"
created_at: "2024-09-01T02:54:41.000000Z"
uuid: "9210a3f5-35e0-4417-a7b6-03e13b18d458"
tags: ['Make基础教程']
---

# Make中文教程：谷歌文档（Google Docs）

**标签**: Make基础教程

**翔宇工作流** 今天为大家带来关于Google文档的教程。

  1. **教程简介** ：本教程介绍如何在Make.com中使用Google Docs模块，涵盖连接、监控、创建、编辑和删除文档等操作。提供从设置Google Cloud Console项目到使用具体功能的详细步骤。

  2. **学习目标** ：掌握如何使用Make.com中Google Docs模块实现文档的监控、创建和编辑，理解如何配置Google Cloud Console项目并管理OAuth凭据，提升自动化处理文档的效率。

  3. **学习内容** ：

     - 连接Google Docs和Make.com

     - 创建和配置Google Cloud Console项目

     - 监控和列出文档

     - 获取文档内容

     - 创建和编辑文档，包括从模板创建

     - 向文档添加段落和图片

  4. **概念理解** ：

     - **Google Docs模块** ：用于在Make.com中操作Google文档的工具。

     - **OAuth凭据** ：授权Make.com访问您的Google帐户所需的安全机制。

     - **场景（Scenario）** ：Make.com中的自动化流程，包含多个模块和操作。

  5. **任务练习** ：

     1. 根据教程，连接您的Google Docs和Make.com账户。

     2. 创建一个新的Google文档并插入一个段落。

     3. 配置一个场景，监控特定文件夹中的文档变化，并发送通知。

     4. 从模板创建一个文档，替换模板中的标签和图片。


# **谷歌文档**

使用 Make 中的 Google Docs 模块,您可以监控、创建、编辑、检索、下载和删除您 Google Docs 帐户中的文档。

要使用 Google Docs 模块,你必须有一个 Google 帐户。你可以在 accounts.google.com 上创建一个帐户。

请参考 Google Docs API 文档以了解可用的终端节点列表。

![](https://static.xiaobot.net/file/2024-11-26/720345/5265d9b198c5cdee0d73d9c7f67af0fb.png)


## **连接 Google Docs 来制作**

建立在 Make 中的连接

  1. 登录您的 Make 账户,将 Google Docs 模块添加到您的场景中,然后单击创建连接。

  2. 在"连接名称"字段中输入连接的名称。

  3. 可选:打开"显示高级设置"开关,输入您的 Google Cloud Console 项目客户端凭据。有关更多信息,请参见下面的"创建和配置 Google Cloud Console 项目以用于 Google Docs"部分。

  4. 点击使用谷歌登录。

  5. 如果提示,请验证您的账户并确认访问权限。

您已成功建立连接。您现在可以编辑您的场景并添加更多 Google Docs 模块。如果您的连接在任何时候需要重新授权,请按照此处的连接更新步骤进行操作。


### **为 Google 文档创建并配置 Google 云控制台项目**

要使用您自己的客户端凭据连接到 Make,您可以在 Google Cloud 控制台中创建和配置一个项目。

**为 Google 文档创建一个 Google 云控制台项目**

创建一个 Google Cloud 控制台项目:

  1. 使用您的谷歌账号登录谷歌云控制台。

  2. 在顶部菜单中,单击"选择项目">"新建项目"。

  3. 输入项目名称并选择项目位置。

  4.  点击创建。

  5. 在顶部菜单中,检查您新创建的项目是否在"选择项目"下拉列表中被选中。如果没有,请选择您刚刚创建的项目。

**为 Google 文档启用 API**

要启用所需的 API:

  1. 打开左侧导航菜单，并转到 APIs & Services > 库。

  2. 搜索并启用以下 API:Google 文档 API、Google 云端硬盘 API。

**配置您的 Google Docs OAuth 同意屏幕**

配置您的 OAuth 同意屏幕:

  1. 在左侧边栏中,单击 OAuth 同意屏幕。

  2. 在"用户类型"下选择"外部"。

有关用户类型的更多信息,请参阅 Google 的"验证要求例外"文档。

  3.  点击创建。

  4. 填写所需的字段,输入您的信息。

  5. 在"授权域"部分，添加 make.com 和 integromat.com 。

  6.  点击保存并继续。

  7. 在 Scopes 页面中,点击添加或删除 Scopes,添加以下 Scopes,然后点击更新。

     - https://www.googleapis.com/auth/userinfo.email

     - https://www.googleapis.com/auth/drive

     - https://www.googleapis.com/auth/documents

     - https://www.googleapis.com/auth/documents.readonly

  8.  点击保存并继续。

  9. 如果您的项目将保持测试发布状态,请在"测试用户"页面添加测试用户电子邮件,然后单击"保存并继续"。

**创建您的 Google Docs 客户端凭据**

创建您的客户凭证:

  1. 在左侧边栏中,单击凭证。

  2. 点击+创建凭据>OAuth 客户端 ID。

  3. 在应用程序类型下拉菜单中,选择 Web 应用程序。

  4. 更新您的 OAuth 客户端的名称。这将帮助您在控制台中识别它。

  5. 在授权重定向 URI 部分,单击 + 添加 URI,输入以下重定向 URI: https://www.integromat.com/oauth/cb/google 。

  6. 请复制您的客户端 ID 和客户端密钥值,并将它们存储在一个安全的地方。

您将在 Make 中的"客户端 ID"和"客户端密钥"字段中使用这些值。


## **构建 Google 文档场景**

连接应用程序后,您可以执行以下操作:


### **  文件**

![](https://static.xiaobot.net/file/2024-11-26/720345/aa79ae82e160ddef5979b39578bf5b10.png)

**  文档列表**

检索文档列表。

![](https://static.xiaobot.net/file/2024-11-26/720345/af19b8e0dfbcf203206033c3dda64c55.png)

**获取文档内容**

获取文档内容。

![](https://static.xiaobot.net/file/2024-11-26/720345/cee6be415ea2e12bafe12626a7735b86.png)

**  创建文档**

以 HTML 格式添加内容创建新的 Google 文档。

![](https://static.xiaobot.net/file/2024-11-26/720345/85b97e13dce1299370ea4b390f8895aa.png)

**根据模板创建文档**

创建现有模板文档的副本,并替换任何标签,例如 \{\{\!notfound:name\}\} 。此模块允许通过新图像的 URL 替换图像。

![](https://static.xiaobot.net/file/2024-11-26/720345/ce193d5a6ba97768c82a0b5cb4827266.png)

**插入段落到文档中**

向现有文档插入或附加新段落。

![](https://static.xiaobot.net/file/2024-11-26/720345/28fe139c4db4653371c40c4ca32793fa.png)

**将图像插入文档**

添加一个带有文档 URL 的新图像。

注意:图像最大尺寸为 50MB。图像分辨率不得超过 25 百万像素。仅支持 PNG、JPEG 或 GIF 格式。

![](https://static.xiaobot.net/file/2024-11-26/720345/8ddaccfe926bd78f9a10e2eeceafaedd.png)

**用新图像替换图像**

用一个新图像替换现有图像,该新图像具有文档中的 URL。为了填充原始图像的边界,新图像可能会被缩放或剪裁。

![](https://static.xiaobot.net/file/2024-11-26/720345/203a94d91b5b1018196264f7faa9d11d.png)

**更改文档中的文本**

替换文档中的旧文本为新文本。

![](https://static.xiaobot.net/file/2024-11-26/720345/929267761e2131a808a73734b83c6958.png)

**下载文件**

下载文档到所需格式。

![](https://static.xiaobot.net/file/2024-11-26/720345/974f2bbdb9bbf220e2b967826191c0c6.png)

**  删除文档**

 删除文档。

![](https://static.xiaobot.net/file/2024-11-26/720345/b8798b7541ccf09f5ceedc7b27737c43.png)


### **  其他**

**  发起 API 请求**

执行任意授权 API 调用。

![](https://static.xiaobot.net/file/2024-11-26/720345/29fb3d2f0ee10030b6f6c99ce56bc520.png)

**使所有链接都可点击**

使所有文档中的链接可点击。

![](https://static.xiaobot.net/file/2024-11-26/720345/bd407932f751bc839b00df6a62720688.png)


# **翔宇工作流：一个专注AI与自动化的频道**

**翔宇工作流** 希望通过本教程，让您在**Make.com** 上更轻松地创建和管理自动化工作流。更多自动化内容，敬请关注**翔宇工作流** ！**翔宇出品，请勿转载！**

![](https://static.xiaobot.net/file/2024-09-20/720345/da49230c7b9b6f4e11f659222303aa96.png)