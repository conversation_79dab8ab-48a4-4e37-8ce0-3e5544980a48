import java.io.*;
import java.util.*;

/**
 * 配置加载器 - 从JSON文件加载动态策略配置
 * 
 * 支持功能：
 * 1. 从JSON文件加载配置
 * 2. 支持任意数量的策略配置
 * 3. 配置验证和默认值处理
 * 4. 配置热加载支持
 */
public class ConfigLoader {
    
    /**
     * 从JSON文件加载配置
     */
    public static DynamicStrategyConfig loadFromFile(String configFilePath) {
        try {
            System.out.println("正在加载配置文件: " + configFilePath);
            
            // 读取JSON文件内容
            String jsonContent = readFileContent(configFilePath);
            
            // 解析JSON配置
            DynamicStrategyConfig config = parseJsonConfig(jsonContent);
            
            // 验证配置
            validateConfig(config);
            
            System.out.println("配置加载成功: " + config.getScenId());
            System.out.println("策略数量: " + config.getStrategies().size());
            System.out.println("策略列表: " + config.getStrategies().keySet());
            
            return config;
            
        } catch (Exception e) {
            System.err.println("配置加载失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to load config from: " + configFilePath, e);
        }
    }
    
    /**
     * 读取文件内容
     */
    private static String readFileContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 解析JSON配置（简化版JSON解析）
     * 注：实际项目中建议使用Jackson或Gson等专业JSON库
     */
    private static DynamicStrategyConfig parseJsonConfig(String jsonContent) {
        // 移除空白字符和换行
        jsonContent = jsonContent.replaceAll("\\s+", " ").trim();
        
        DynamicStrategyConfig config = new DynamicStrategyConfig();
        
        // 解析基础配置
        config.setScenId(extractStringValue(jsonContent, "scenId"));
        config.setAbId(extractStringValue(jsonContent, "abId"));
        config.setSwitchOn(extractBooleanValue(jsonContent, "switchOn"));
        config.setType(extractStringValue(jsonContent, "type"));
        config.setBizName(extractStringValue(jsonContent, "bizName"));
        config.setMaxQPS(extractIntValue(jsonContent, "maxQPS"));
        config.setSafetyBuffer(extractDoubleValue(jsonContent, "safetyBuffer"));
        config.setAdjustmentStep(extractDoubleValue(jsonContent, "adjustmentStep"));
        config.setMaxRT(extractIntValue(jsonContent, "maxRT"));
        
        // 解析策略配置
        Map<String, StrategyInfo> strategies = parseStrategies(jsonContent);
        config.setStrategies(strategies);
        
        // 解析目标权重
        Map<String, Double> targetWeights = parseTargetWeights(jsonContent);
        config.setTargetWeights(targetWeights);
        
        // 解析紧急配置
        EmergencyConfig emergencyConfig = parseEmergencyConfig(jsonContent);
        config.setEmergencyConfig(emergencyConfig);
        
        return config;
    }
    
    /**
     * 解析策略配置
     */
    private static Map<String, StrategyInfo> parseStrategies(String jsonContent) {
        Map<String, StrategyInfo> strategies = new HashMap<>();

        // 提取strategies部分 - 改进的正则表达式
        String pattern = "\"strategies\"\\s*:\\s*\\{([^}]+)\\}";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.DOTALL);
        java.util.regex.Matcher m = p.matcher(jsonContent);

        if (!m.find()) {
            System.err.println("无法找到strategies配置");
            return strategies;
        }

        String strategiesSection = m.group(1);
        System.out.println("解析strategies部分: " + strategiesSection);

        // 解析每个策略 - 使用更简单的方法
        // 查找所有策略名称
        java.util.regex.Pattern strategyPattern = java.util.regex.Pattern.compile("\"(\\w+)\"\\s*:\\s*\\{([^}]+)\\}");
        java.util.regex.Matcher strategyMatcher = strategyPattern.matcher(strategiesSection);

        while (strategyMatcher.find()) {
            String strategyName = strategyMatcher.group(1);
            String strategyConfig = strategyMatcher.group(2);

            System.out.println("解析策略: " + strategyName + " -> " + strategyConfig);

            int batchNum = extractIntValue(strategyConfig, "batchNum");
            int count = extractIntValue(strategyConfig, "count");

            strategies.put(strategyName, new StrategyInfo(batchNum, count));
        }

        return strategies;
    }
    
    /**
     * 解析目标权重
     */
    private static Map<String, Double> parseTargetWeights(String jsonContent) {
        Map<String, Double> weights = new HashMap<>();
        
        String weightsSection = extractObjectValue(jsonContent, "targetWeights");
        String[] weightPairs = weightsSection.split(",");
        
        for (String pair : weightPairs) {
            if (pair.trim().isEmpty()) continue;
            
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("\"", "");
                double value = Double.parseDouble(keyValue[1].trim());
                weights.put(key, value);
            }
        }
        
        return weights;
    }
    
    /**
     * 解析紧急配置
     */
    private static EmergencyConfig parseEmergencyConfig(String jsonContent) {
        String emergencySection = extractObjectValue(jsonContent, "emergencyConfig");
        
        String condition = extractStringValue(emergencySection, "condition");
        Map<String, Double> weights = parseWeightsFromSection(emergencySection, "weights");
        
        return new EmergencyConfig(condition, weights);
    }
    
    /**
     * 从指定section解析权重
     */
    private static Map<String, Double> parseWeightsFromSection(String section, String weightsKey) {
        Map<String, Double> weights = new HashMap<>();
        
        String weightsSection = extractObjectValue(section, weightsKey);
        String[] weightPairs = weightsSection.split(",");
        
        for (String pair : weightPairs) {
            if (pair.trim().isEmpty()) continue;
            
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("\"", "");
                double value = Double.parseDouble(keyValue[1].trim());
                weights.put(key, value);
            }
        }
        
        return weights;
    }
    
    // 辅助方法：提取字符串值
    private static String extractStringValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        return m.find() ? m.group(1) : "";
    }
    
    // 辅助方法：提取布尔值
    private static boolean extractBooleanValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*(true|false)";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        return m.find() ? Boolean.parseBoolean(m.group(1)) : false;
    }
    
    // 辅助方法：提取整数值
    private static int extractIntValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*(\\d+)";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        return m.find() ? Integer.parseInt(m.group(1)) : 0;
    }
    
    // 辅助方法：提取双精度值
    private static double extractDoubleValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*([0-9.]+)";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        return m.find() ? Double.parseDouble(m.group(1)) : 0.0;
    }
    
    // 辅助方法：提取对象值
    private static String extractObjectValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\\{([^}]+)\\}";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        return m.find() ? m.group(1) : "";
    }
    
    // 辅助方法：提取引号中的键名
    private static String extractQuotedKey(String text) {
        java.util.regex.Pattern p = java.util.regex.Pattern.compile("\"(\\w+)\"\\s*:");
        java.util.regex.Matcher m = p.matcher(text);
        return m.find() ? m.group(1) : null;
    }
    
    /**
     * 验证配置
     */
    private static void validateConfig(DynamicStrategyConfig config) {
        // 验证基础配置
        if (config.getScenId() == null || config.getScenId().isEmpty()) {
            throw new IllegalArgumentException("scenId不能为空");
        }
        
        if (config.getMaxQPS() <= 0) {
            throw new IllegalArgumentException("maxQPS必须大于0");
        }
        
        if (config.getSafetyBuffer() <= 0 || config.getSafetyBuffer() > 1) {
            throw new IllegalArgumentException("safetyBuffer必须在(0,1]范围内");
        }
        
        // 验证策略配置
        if (config.getStrategies() == null || config.getStrategies().isEmpty()) {
            throw new IllegalArgumentException("strategies不能为空");
        }
        
        if (config.getStrategies().size() < 2) {
            throw new IllegalArgumentException("至少需要2个策略");
        }
        
        // 验证目标权重
        if (config.getTargetWeights() == null || config.getTargetWeights().isEmpty()) {
            throw new IllegalArgumentException("targetWeights不能为空");
        }
        
        // 验证权重总和
        double totalWeight = config.getTargetWeights().values().stream().mapToDouble(Double::doubleValue).sum();
        if (Math.abs(totalWeight - 1.0) > 0.001) {
            throw new IllegalArgumentException("目标权重总和必须等于1.0，当前总和: " + totalWeight);
        }
        
        // 验证策略一致性
        Set<String> strategyNames = config.getStrategies().keySet();
        Set<String> weightNames = config.getTargetWeights().keySet();
        
        if (!strategyNames.equals(weightNames)) {
            throw new IllegalArgumentException("策略名称与权重配置不一致");
        }
        
        System.out.println("✅ 配置验证通过");
    }
}
