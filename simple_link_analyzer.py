#!/usr/bin/env python3
"""
简化版内部链接分析器
不依赖复杂的第三方库，专注于核心功能
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import difflib

@dataclass
class LinkInfo:
    """链接信息数据类"""
    target_uuid: str
    target_title: str
    link_type: str
    confidence: float
    context: str
    position: int

@dataclass
class Article:
    """文章信息数据类"""
    uuid: str
    title: str
    file_path: str
    content: str
    tags: List[str]
    created_at: str
    outbound_links: List[LinkInfo]
    inbound_links: List[str]

class SimpleLinkAnalyzer:
    """简化版链接分析器"""
    
    def __init__(self, markdown_dir: str):
        self.markdown_dir = Path(markdown_dir)
        self.articles: Dict[str, Article] = {}
        self.title_to_uuid: Dict[str, str] = {}
        self.external_domains = {
            'netlify.app', 'pan.quark.cn', 'notion.site', 'github.com',
            'youtube.com', 'youtu.be', 'twitter.com', 'x.com'
        }
        
    def load_articles(self) -> None:
        """加载所有 Markdown 文件"""
        print("正在加载文章...")
        
        for md_file in self.markdown_dir.glob("*.md"):
            if md_file.name.endswith('.json'):
                continue
                
            try:
                article = self._parse_markdown_file(md_file)
                if article and article.uuid:
                    self.articles[article.uuid] = article
                    self.title_to_uuid[article.title] = article.uuid
                    
            except Exception as e:
                print(f"解析文件 {md_file} 时出错: {e}")
        
        print(f"成功加载 {len(self.articles)} 篇文章")
    
    def _parse_markdown_file(self, file_path: Path) -> Optional[Article]:
        """解析单个 Markdown 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析 YAML 前置元数据
            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    frontmatter_text = parts[1].strip()
                    main_content = parts[2].strip()
                    
                    # 简单解析 YAML（避免依赖 PyYAML）
                    frontmatter = self._parse_simple_yaml(frontmatter_text)
                    
                    return Article(
                        uuid=frontmatter.get('uuid', ''),
                        title=frontmatter.get('title', ''),
                        file_path=str(file_path),
                        content=main_content,
                        tags=frontmatter.get('tags', []),
                        created_at=frontmatter.get('created_at', ''),
                        outbound_links=[],
                        inbound_links=[]
                    )
        except Exception as e:
            print(f"解析文件 {file_path} 出错: {e}")
        
        return None
    
    def _parse_simple_yaml(self, yaml_text: str) -> Dict:
        """简单的 YAML 解析器"""
        result = {}
        
        for line in yaml_text.split('\n'):
            line = line.strip()
            if ':' in line and not line.startswith('#'):
                key, value = line.split(':', 1)
                key = key.strip().strip('"\'')
                value = value.strip().strip('"\'')
                
                # 处理列表
                if value.startswith('[') and value.endswith(']'):
                    # 简单列表解析
                    items = value[1:-1].split(',')
                    result[key] = [item.strip().strip('\'"') for item in items if item.strip()]
                else:
                    result[key] = value
        
        return result
    
    def analyze_links(self) -> Dict:
        """分析所有文章的链接关系"""
        print("正在分析链接关系...")
        
        # 为每篇文章提取链接
        for uuid, article in self.articles.items():
            print(f"分析: {article.title[:50]}...")
            links = self._extract_links(article)
            article.outbound_links = links
            
            # 更新被引用文章的入站链接
            for link in links:
                if link.target_uuid in self.articles:
                    self.articles[link.target_uuid].inbound_links.append(uuid)
        
        # 构建关系数据
        relationships = []
        for uuid, article in self.articles.items():
            for link in article.outbound_links:
                relationships.append({
                    'source_uuid': uuid,
                    'source_title': article.title,
                    'target_uuid': link.target_uuid,
                    'target_title': link.target_title,
                    'link_type': link.link_type,
                    'confidence': link.confidence,
                    'context': link.context
                })
        
        # 计算统计信息
        stats = self._calculate_statistics()
        
        return {
            'articles': {uuid: self._article_to_dict(article) 
                        for uuid, article in self.articles.items()},
            'relationships': relationships,
            'statistics': stats
        }
    
    def _extract_links(self, article: Article) -> List[LinkInfo]:
        """提取文章中的内部链接"""
        links = []
        content = article.content
        
        # 1. 精确标题匹配
        for title, target_uuid in self.title_to_uuid.items():
            if target_uuid == article.uuid:  # 跳过自引用
                continue
                
            # 查找标题在内容中的位置
            for match in re.finditer(re.escape(title), content, re.IGNORECASE):
                if not self._is_in_external_link(content, match.start(), match.end()):
                    context = self._extract_context(content, match.start(), match.end())
                    
                    links.append(LinkInfo(
                        target_uuid=target_uuid,
                        target_title=title,
                        link_type='exact_match',
                        confidence=1.0,
                        context=context,
                        position=match.start()
                    ))
        
        # 2. Markdown 链接解析
        markdown_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        for match in re.finditer(markdown_pattern, content):
            link_text = match.group(1)
            link_url = match.group(2)
            
            # 检查是否为外部链接
            if self._is_external_link(link_url):
                continue
            
            # 尝试匹配链接文本到文章标题
            best_match = self._find_best_title_match(link_text)
            if best_match:
                target_uuid = self.title_to_uuid.get(best_match)
                if target_uuid and target_uuid != article.uuid:
                    context = self._extract_context(content, match.start(), match.end())
                    
                    links.append(LinkInfo(
                        target_uuid=target_uuid,
                        target_title=best_match,
                        link_type='markdown_link',
                        confidence=0.9,
                        context=context,
                        position=match.start()
                    ))
        
        # 3. 模糊匹配（使用 difflib）
        for title, target_uuid in self.title_to_uuid.items():
            if target_uuid == article.uuid or len(title) < 5:
                continue
            
            # 使用 difflib 进行模糊匹配
            similarity = difflib.SequenceMatcher(None, title.lower(), content.lower()).ratio()
            if similarity > 0.3:  # 较低的阈值，因为是全文匹配
                # 寻找部分匹配
                words = title.split()
                if len(words) >= 2:
                    # 检查是否有连续的词出现
                    for i in range(len(words) - 1):
                        phrase = ' '.join(words[i:i+2])
                        if len(phrase) > 4 and phrase.lower() in content.lower():
                            pos = content.lower().find(phrase.lower())
                            if pos != -1 and not self._is_in_external_link(content, pos, pos + len(phrase)):
                                context = self._extract_context(content, pos, pos + len(phrase))
                                
                                links.append(LinkInfo(
                                    target_uuid=target_uuid,
                                    target_title=title,
                                    link_type='fuzzy_match',
                                    confidence=0.6,
                                    context=context,
                                    position=pos
                                ))
                                break
        
        # 去重
        return self._deduplicate_links(links)
    
    def _is_external_link(self, url: str) -> bool:
        """判断是否为外部链接"""
        if url.startswith(('http://', 'https://')):
            for domain in self.external_domains:
                if domain in url:
                    return True
        return url.startswith(('mailto:', 'tel:', 'ftp://'))
    
    def _is_in_external_link(self, content: str, start: int, end: int) -> bool:
        """检查文本是否在外部链接中"""
        before = content[max(0, start-50):start]
        after = content[end:min(len(content), end+50)]
        
        if '](http' in before + after or '<a href=' in before:
            return True
        return False
    
    def _find_best_title_match(self, text: str) -> Optional[str]:
        """找到最佳的标题匹配"""
        best_match = None
        best_ratio = 0
        
        for title in self.title_to_uuid.keys():
            ratio = difflib.SequenceMatcher(None, text.lower(), title.lower()).ratio()
            if ratio > best_ratio and ratio > 0.8:  # 80% 相似度阈值
                best_ratio = ratio
                best_match = title
        
        return best_match
    
    def _extract_context(self, content: str, start: int, end: int, context_length: int = 100) -> str:
        """提取链接周围的上下文"""
        context_start = max(0, start - context_length)
        context_end = min(len(content), end + context_length)
        context = content[context_start:context_end].strip()
        
        # 清理上下文
        context = re.sub(r'\s+', ' ', context)
        return context
    
    def _deduplicate_links(self, links: List[LinkInfo]) -> List[LinkInfo]:
        """去重链接"""
        seen = set()
        unique_links = []
        
        # 按置信度排序
        links.sort(key=lambda x: (-x.confidence, x.position))
        
        for link in links:
            key = (link.target_uuid, link.position // 50)
            if key not in seen:
                seen.add(key)
                unique_links.append(link)
        
        return unique_links
    
    def _article_to_dict(self, article: Article) -> Dict:
        """将文章对象转换为字典"""
        return {
            'uuid': article.uuid,
            'title': article.title,
            'file_path': article.file_path,
            'tags': article.tags,
            'created_at': article.created_at,
            'outbound_count': len(article.outbound_links),
            'inbound_count': len(set(article.inbound_links)),
            'outbound_links': [asdict(link) for link in article.outbound_links],
            'inbound_links': list(set(article.inbound_links))
        }
    
    def _calculate_statistics(self) -> Dict:
        """计算统计信息"""
        total_articles = len(self.articles)
        total_links = sum(len(article.outbound_links) for article in self.articles.values())
        
        outbound_counts = [len(article.outbound_links) for article in self.articles.values()]
        inbound_counts = [len(set(article.inbound_links)) for article in self.articles.values()]
        
        # 最受欢迎的文章
        most_referenced = sorted(
            self.articles.items(),
            key=lambda x: len(set(x[1].inbound_links)),
            reverse=True
        )[:10]
        
        # 引用最多的文章
        most_referencing = sorted(
            self.articles.items(),
            key=lambda x: len(x[1].outbound_links),
            reverse=True
        )[:10]
        
        return {
            'total_articles': total_articles,
            'total_links': total_links,
            'avg_outbound_links': sum(outbound_counts) / total_articles if total_articles > 0 else 0,
            'avg_inbound_links': sum(inbound_counts) / total_articles if total_articles > 0 else 0,
            'max_outbound_links': max(outbound_counts) if outbound_counts else 0,
            'max_inbound_links': max(inbound_counts) if inbound_counts else 0,
            'most_referenced_articles': [
                {'uuid': uuid, 'title': article.title, 'inbound_count': len(set(article.inbound_links))}
                for uuid, article in most_referenced
            ],
            'most_referencing_articles': [
                {'uuid': uuid, 'title': article.title, 'outbound_count': len(article.outbound_links)}
                for uuid, article in most_referencing
            ]
        }
    
    def generate_report(self, output_dir: str = "link_analysis_results") -> None:
        """生成分析报告"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 分析链接
        data = self.analyze_links()
        
        # 保存 JSON 数据
        with open(f"{output_dir}/relationships.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 生成 Markdown 报告
        self._generate_markdown_report(data, f"{output_dir}/analysis_report.md")
        
        # 生成简单的网络数据
        self._generate_network_data(data, f"{output_dir}/network_data.json")
        
        print(f"分析完成！结果保存在: {output_dir}/")
        print(f"- relationships.json: 完整关系数据")
        print(f"- analysis_report.md: 分析报告")
        print(f"- network_data.json: 网络图数据")
    
    def _generate_markdown_report(self, data: Dict, output_path: str) -> None:
        """生成 Markdown 报告"""
        stats = data['statistics']
        
        report = f"""# 翔宇工作流内部链接分析报告

## 📊 总体统计

- **文章总数**: {stats['total_articles']} 篇
- **内部链接总数**: {stats['total_links']} 个
- **平均出链数**: {stats['avg_outbound_links']:.2f}
- **平均入链数**: {stats['avg_inbound_links']:.2f}
- **最大出链数**: {stats['max_outbound_links']}
- **最大入链数**: {stats['max_inbound_links']}

## 🏆 最受欢迎文章 (被引用最多)

"""
        
        for i, article in enumerate(stats['most_referenced_articles'], 1):
            report += f"{i}. **{article['title']}** (被引用 {article['inbound_count']} 次)\n"
        
        report += f"""

## 📝 引用最多文章 (引用他人最多)

"""
        
        for i, article in enumerate(stats['most_referencing_articles'], 1):
            report += f"{i}. **{article['title']}** (引用 {article['outbound_count']} 篇)\n"
        
        # 链接类型统计
        link_types = Counter(rel['link_type'] for rel in data['relationships'])
        report += f"""

## 🔗 链接类型分布

"""
        for link_type, count in link_types.most_common():
            type_names = {
                'exact_match': '精确匹配',
                'markdown_link': 'Markdown链接',
                'fuzzy_match': '模糊匹配'
            }
            report += f"- **{type_names.get(link_type, link_type)}**: {count} 个\n"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
    
    def _generate_network_data(self, data: Dict, output_path: str) -> None:
        """生成网络图数据"""
        nodes = []
        edges = []
        
        for uuid, article in data['articles'].items():
            nodes.append({
                'id': uuid,
                'label': article['title'],
                'size': article['inbound_count'] * 2 + 5,
                'inbound_count': article['inbound_count'],
                'outbound_count': article['outbound_count']
            })
        
        for rel in data['relationships']:
            edges.append({
                'from': rel['source_uuid'],
                'to': rel['target_uuid'],
                'weight': rel['confidence'],
                'type': rel['link_type']
            })
        
        network_data = {
            'nodes': nodes,
            'edges': edges
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(network_data, f, ensure_ascii=False, indent=2)


def main():
    """主函数"""
    print("🔗 翔宇工作流内部链接分析器 (简化版)")
    print("=" * 50)
    
    markdown_dir = "markdown_posts"
    
    if not os.path.exists(markdown_dir):
        print(f"❌ 找不到目录: {markdown_dir}")
        print("请确保已经运行了 HTML to Markdown 转换器")
        return
    
    try:
        analyzer = SimpleLinkAnalyzer(markdown_dir)
        analyzer.load_articles()
        
        if not analyzer.articles:
            print("❌ 没有找到任何文章")
            return
        
        analyzer.generate_report()
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
