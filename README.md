# 动态策略调整系统演示

## 项目简介

这是一个基于Java实现的动态策略调整系统，能够根据当前QPS负载和系统容量约束，智能调整A、B两个策略的权重分配，确保系统在安全容量范围内运行。

## 核心功能

- **实时QPS监控**：监控当前系统QPS负载
- **智能权重计算**：基于容量约束计算最优权重分配
- **动态策略调整**：自动调整策略权重以满足容量要求
- **紧急保护机制**：在极端情况下启用紧急保护权重

## 算法原理

### 权重计算公式
```
目标QPS = 当前QPS × (A权重×1 + B权重×3)
安全容量 = 最大QPS × 安全缓冲系数(0.9)
```

### 调整策略
1. **目标权重验证**：检查目标QPS是否超过安全容量
2. **容量优化算法**：逐步减少B策略权重，增加A策略权重
3. **紧急保护机制**：当无法找到可行解时，使用紧急权重配置

## 文件结构

```
├── DynamicStrategyConfig.java    # 动态策略配置类
├── StrategyInfo.java            # 策略信息类
├── EmergencyConfig.java         # 紧急配置类
├── WeightCalculationResult.java # 权重计算结果类
├── AdjustmentStep.java          # 调整步骤类
├── WeightCalculationService.java # 权重计算服务（核心算法）
├── DynamicStrategyDemo.java     # 演示程序主类
└── README.md                    # 项目说明文档
```

## 运行演示

### 编译和运行
```bash
# 编译所有Java文件
javac *.java

# 运行演示程序
java DynamicStrategyDemo
```

### 演示场景

#### 🔵 场景1：目标权重可达成（正常负载）
- **当前QPS**: 300
- **目标QPS**: 840
- **安全容量**: 900
- **结果**: 直接使用目标权重 A=10%, B=90%

#### 🟡 场景2：需要容量优化（中等负载）
- **当前QPS**: 400
- **目标QPS**: 1120（超出安全容量）
- **结果**: 通过权重优化，调整为 A=46%, B=54%

#### 🔴 场景3：启用紧急保护（高负载）
- **当前QPS**: 900
- **目标QPS**: 2520（严重超出安全容量）
- **结果**: 尝试所有调整策略后仍无法满足约束，启用紧急权重 A=90%, B=10%

#### 🚨 场景4：真正的紧急保护（极高负载）
- **当前QPS**: 950
- **目标QPS**: 2660（极度超出安全容量）
- **结果**: 启用紧急保护权重，资源利用率126.7%

## 配置参数

### 基础配置
- **maxQPS**: 1000（系统最大QPS承载能力）
- **safetyBuffer**: 0.9（安全缓冲系数）
- **adjustmentStep**: 0.1（权重调整步长）

### 策略配置
- **A策略**: batchNum=1, count=150（轻量级策略）
- **B策略**: batchNum=3, count=150（重量级策略）

### 权重配置
- **目标权重**: A=10%, B=90%（理想分配）
- **紧急权重**: A=90%, B=10%（保护性配置）

## 核心算法特点

1. **容量优先**：始终确保系统在安全容量范围内运行
2. **智能调整**：优先减少高资源消耗策略的权重
3. **兜底保护**：提供紧急保护机制确保系统可用性
4. **详细日志**：记录完整的调整过程和决策依据

## 扩展说明

这个演示程序专注于核心算法实现，在实际生产环境中可以扩展为：
- 集成Spring Boot框架
- 添加定时任务调度
- 接入真实的QPS监控数据
- 实现配置推送到目标系统
- 添加监控指标和告警机制
