import java.util.*;

/**
 * 权重计算服务 - 核心算法实现
 */
public class WeightCalculationService {

    /**
     * 计算最优权重分配 - 核心算法入口
     *
     * 算法流程：
     * 1. 计算安全容量（maxQPS × safetyBuffer）
     * 2. 验证目标权重可行性（目标QPS是否超出安全容量）
     * 3. 如果可行，直接使用目标权重
     * 4. 如果不可行，启动容量优化算法
     * 5. 如果优化失败，启用紧急保护权重
     *
     * @param currentQps 当前系统QPS负载
     * @param config 动态策略配置（包含目标权重、容量限制等）
     * @return 权重计算结果（包含最终权重、状态、调整步骤等）
     */
    public WeightCalculationResult calculateOptimalWeights(double currentQps, DynamicStrategyConfig config) {
        long startTime = System.currentTimeMillis();
        List<AdjustmentStep> steps = new ArrayList<>();

        try {
            System.out.println("\n=== 开始权重计算 ===");
            System.out.println("当前QPS: " + currentQps);
            System.out.println("最大QPS: " + config.getMaxQPS());
            System.out.println("安全缓冲: " + config.getSafetyBuffer());

            // 步骤1：计算安全容量
            // 安全容量 = 最大QPS × 安全缓冲系数，为系统预留安全余量
            double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
            System.out.println("安全容量: " + safeMaxQps);

            // 步骤2：验证目标权重可行性
            // 计算目标权重下的资源消耗倍数
            double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
            // 计算目标权重下的预期QPS
            double targetQps = currentQps * targetMultiplier;

            System.out.println("目标权重: " + config.getTargetWeights());
            System.out.println("目标倍数: " + String.format("%.2f", targetMultiplier));
            System.out.println("目标QPS: " + String.format("%.0f", targetQps));
            
            // 步骤3：目标权重可行性判断
            if (targetQps <= safeMaxQps) {
                // 情况1：目标QPS在安全容量范围内，可以直接使用目标权重
                System.out.println("✅ 目标QPS在安全范围内，直接使用目标权重");
                return buildSuccessResult(config.getTargetWeights(), targetQps, safeMaxQps,
                    CalculationStatus.TARGET_ACHIEVED, "目标权重可直接使用", steps, startTime);
            }

            // 情况2：目标QPS超出安全容量，需要进行权重优化
            System.out.println("❌ 目标QPS超出安全容量，启动容量优化算法");

            // 步骤4：启动容量优化算法
            // 通过逐步减少高资源消耗策略（B策略）的权重来降低总体资源消耗
            Map<String, Double> optimizedWeights = optimizeWeights(currentQps, safeMaxQps, config, steps);

            if (optimizedWeights != null) {
                // 情况2a：找到可行的优化权重方案
                double optimizedQps = currentQps * calculateMultiplier(optimizedWeights, config);
                System.out.println("✅ 找到优化权重: " + optimizedWeights);
                return buildSuccessResult(optimizedWeights, optimizedQps, safeMaxQps,
                    CalculationStatus.CAPACITY_OPTIMIZED, "通过权重优化满足容量约束", steps, startTime);
            }

            // 情况2b：无法找到可行的优化方案，启用紧急保护机制
            System.out.println("⚠️ 无法找到可行的优化方案，启用紧急保护权重");
            Map<String, Double> emergencyWeights = config.getEmergencyConfig().getWeights();
            double emergencyQps = currentQps * calculateMultiplier(emergencyWeights, config);

            return buildSuccessResult(emergencyWeights, emergencyQps, safeMaxQps,
                CalculationStatus.EMERGENCY_PROTECTION, "启用紧急保护权重", steps, startTime);
                
        } catch (Exception e) {
            System.err.println("权重计算失败: " + e.getMessage());
            return buildFailureResult(e.getMessage(), startTime);
        }
    }
    
    /**
     * 容量优化算法 - 通过逐步调整权重来满足容量约束
     *
     * 优化策略：
     * 1. 优先减少B策略权重（因为B策略资源消耗更大，batchNum=3）
     * 2. 相应增加A策略权重（保持总权重=1.0）
     * 3. 按照预定义的缩放因子逐步尝试：80% → 60% → 40% → 20% → 10%
     * 4. 每次调整后验证是否满足容量约束
     *
     * @param currentQps 当前QPS
     * @param safeMaxQps 安全容量上限
     * @param config 动态策略配置
     * @param steps 调整步骤记录列表
     * @return 优化后的权重配置，如果无法找到可行解则返回null
     */
    private Map<String, Double> optimizeWeights(double currentQps, double safeMaxQps,
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {

        // 计算最大允许的权重倍数
        // 最大倍数 = 安全容量 / 当前QPS，确保调整后的QPS不超过安全容量
        double maxMultiplier = safeMaxQps / currentQps;
        System.out.println("最大允许倍数: " + String.format("%.2f", maxMultiplier));

        Map<String, Double> currentWeights = new HashMap<>(config.getTargetWeights());

        // 定义B策略权重的缩放因子数组
        // 从80%开始逐步降低到10%，每次尝试都会减少B策略的权重占比
        double[] scaleFactors = {0.8, 0.6, 0.4, 0.2, 0.1};

        System.out.println("\n--- 开始权重调整优化 ---");
        for (double scaleFactor : scaleFactors) {
            // 根据当前缩放因子调整权重
            Map<String, Double> adjustedWeights = adjustWeights(currentWeights, scaleFactor);
            // 计算调整后的权重倍数
            double multiplier = calculateMultiplier(adjustedWeights, config);
            // 计算调整后的预期QPS
            double expectedQps = currentQps * multiplier;
            // 判断是否满足容量约束
            boolean feasible = multiplier <= maxMultiplier;
            
            AdjustmentStep step = new AdjustmentStep(
                "将B策略权重缩放至 " + String.format("%.1f", scaleFactor * 100) + "%",
                new HashMap<>(adjustedWeights),
                multiplier,
                expectedQps,
                feasible
            );
            steps.add(step);
            
            System.out.println("尝试调整: " + step.getStepDescription());
            System.out.println("  调整后权重: " + adjustedWeights);
            System.out.println("  预期倍数: " + String.format("%.2f", multiplier));
            System.out.println("  预期QPS: " + String.format("%.0f", expectedQps));
            System.out.println("  是否可行: " + (feasible ? "✅" : "❌"));
            
            if (feasible) {
                System.out.println("找到可行解!");
                return adjustedWeights;
            }
        }
        
        return null; // 无可行解
    }
    
    /**
     * 权重调整核心算法 - 减少B策略权重，增加A策略权重
     *
     * 调整原理：
     * 1. B策略资源消耗高（batchNum=3），减少其权重可显著降低总体资源消耗
     * 2. A策略资源消耗低（batchNum=1），增加其权重对总体资源消耗影响较小
     * 3. 保持总权重=1.0的约束条件
     *
     * 计算公式：
     * - 新B权重 = 原B权重 × 缩放因子
     * - 权重差值 = 原B权重 - 新B权重
     * - 新A权重 = 原A权重 + 权重差值
     *
     * @param originalWeights 原始权重配置
     * @param bScaleFactor B策略的缩放因子（0.8, 0.6, 0.4, 0.2, 0.1）
     * @return 调整后的权重配置
     */
    private Map<String, Double> adjustWeights(Map<String, Double> originalWeights, double bScaleFactor) {
        Map<String, Double> adjustedWeights = new HashMap<>();

        // 计算B策略的新权重
        double originalBWeight = originalWeights.get("B");
        double newBWeight = originalBWeight * bScaleFactor;
        // 计算权重减少量
        double weightDiff = originalBWeight - newBWeight;

        // 设置B策略的新权重（减少）
        adjustedWeights.put("B", newBWeight);

        // 设置A策略的新权重（增加），确保总权重=1.0
        double originalAWeight = originalWeights.get("A");
        double newAWeight = originalAWeight + weightDiff;
        adjustedWeights.put("A", newAWeight);

        return adjustedWeights;
    }
    
    /**
     * 权重倍数计算 - 核心资源消耗计算公式
     *
     * 计算原理：
     * 权重倍数反映了不同策略组合下的总体资源消耗水平
     * 每个策略的资源消耗 = 权重 × batchNum
     *
     * 具体计算：
     * - A策略：权重 × 1（batchNum=1，轻量级策略）
     * - B策略：权重 × 3（batchNum=3，重量级策略）
     * - 总倍数 = A策略消耗 + B策略消耗
     *
     * 示例：
     * - 目标权重（A=0.1, B=0.9）：倍数 = 0.1×1 + 0.9×3 = 2.8
     * - 紧急权重（A=0.9, B=0.1）：倍数 = 0.9×1 + 0.1×3 = 1.2
     *
     * @param weights 权重配置映射
     * @param config 动态策略配置（包含各策略的batchNum）
     * @return 权重倍数（资源消耗系数）
     */
    private double calculateMultiplier(Map<String, Double> weights, DynamicStrategyConfig config) {
        double multiplier = 0.0;

        // 遍历所有策略，累加各策略的资源消耗
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            String strategyName = entry.getKey();           // 策略名称（A或B）
            double weight = entry.getValue();               // 策略权重
            int batchNum = config.getStrategies().get(strategyName).getBatchNum(); // 策略资源消耗系数

            // 累加当前策略的资源消耗：权重 × 资源消耗系数
            multiplier += weight * batchNum;
        }

        return multiplier;
    }
    
    /**
     * 构建成功结果对象 - 封装计算结果和关键指标
     *
     * 结果对象包含：
     * 1. 计算状态（TARGET_ACHIEVED/CAPACITY_OPTIMIZED/EMERGENCY_PROTECTION）
     * 2. 最终权重分配
     * 3. 预期QPS和资源利用率
     * 4. 调整原因和执行耗时
     * 5. 详细的调整步骤记录
     *
     * @param finalWeights 最终确定的权重配置
     * @param expectedQps 预期QPS（当前QPS × 权重倍数）
     * @param safeMaxQps 安全容量上限
     * @param status 计算状态枚举
     * @param reason 调整原因描述
     * @param steps 调整步骤记录列表
     * @param startTime 计算开始时间戳
     * @return 完整的权重计算结果对象
     */
    private WeightCalculationResult buildSuccessResult(Map<String, Double> finalWeights,
            double expectedQps, double safeMaxQps, CalculationStatus status, String reason,
            List<AdjustmentStep> steps, long startTime) {

        // 计算总执行耗时
        long calculationTime = System.currentTimeMillis() - startTime;

        // 计算资源利用率：预期QPS / 安全容量
        // 利用率 < 100% 表示在安全范围内
        // 利用率 > 100% 表示超出安全容量（通常出现在紧急保护场景）
        double resourceUtilization = expectedQps / safeMaxQps;

        // 构建并返回结果对象
        return new WeightCalculationResult(status, finalWeights, expectedQps,
            resourceUtilization, reason, calculationTime, steps);
    }
    
    /**
     * 构建失败结果
     */
    private WeightCalculationResult buildFailureResult(String errorMessage, long startTime) {
        long calculationTime = System.currentTimeMillis() - startTime;
        return new WeightCalculationResult(CalculationStatus.CALCULATION_FAILED, 
            new HashMap<>(), 0, 0, errorMessage, calculationTime, new ArrayList<>());
    }
}
