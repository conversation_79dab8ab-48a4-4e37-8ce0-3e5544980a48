#!/usr/bin/env python3
import json
import re
from html import unescape

def extract_netlify_links(json_file):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = []
    
    for post in data['posts']:
        content = post.get('content', '')
        title = post.get('title', '')
        
        if 'netlify.app' in content:
            # 使用正则表达式匹配包含 netlify.app 的链接
            # 匹配 <a> 标签
            link_pattern = r'<a[^>]*href="([^"]*netlify\.app[^"]*)"[^>]*>(.*?)</a>'
            matches = re.findall(link_pattern, content, re.DOTALL | re.IGNORECASE)
            
            for url, link_text in matches:
                # 清理链接文本，移除HTML标签
                clean_text = re.sub(r'<[^>]+>', '', link_text).strip()
                clean_text = unescape(clean_text)  # 解码HTML实体
                
                results.append({
                    'title': title,
                    'url': url,
                    'link_text': clean_text
                })
    
    return results

def print_table(results):
    if not results:
        print("未找到包含 netlify.app 的链接")
        return
    
    # 计算列宽
    max_title_len = max(len(r['title']) for r in results)
    max_url_len = max(len(r['url']) for r in results)
    max_text_len = max(len(r['link_text']) for r in results)
    
    # 设置最小宽度
    title_width = max(max_title_len, 20)
    url_width = max(max_url_len, 30)
    text_width = max(max_text_len, 20)
    
    # 打印表头
    print(f"{'文章标题':<{title_width}} | {'链接地址':<{url_width}} | {'链接文本':<{text_width}}")
    print("-" * (title_width + url_width + text_width + 6))
    
    # 打印数据行
    for result in results:
        title = result['title'][:title_width-3] + "..." if len(result['title']) > title_width else result['title']
        url = result['url'][:url_width-3] + "..." if len(result['url']) > url_width else result['url']
        text = result['link_text'][:text_width-3] + "..." if len(result['link_text']) > text_width else result['link_text']
        
        print(f"{title:<{title_width}} | {url:<{url_width}} | {text:<{text_width}}")

if __name__ == "__main__":
    results = extract_netlify_links('xiaobot.json')
    print_table(results)
    
    print(f"\n总共找到 {len(results)} 个包含 netlify.app 的链接")
