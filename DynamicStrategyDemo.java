import java.util.*;

/**
 * 动态策略调整演示程序
 * 通过main方法模拟三种不同场景的权重计算效果
 */
public class DynamicStrategyDemo {
    
    public static void main(String[] args) {
        DynamicStrategyDemo demo = new DynamicStrategyDemo();
        WeightCalculationService calculationService = new WeightCalculationService();
        
        // 创建测试配置
        DynamicStrategyConfig config = demo.createTestConfig();
        
        System.out.println("==========================================");
        System.out.println("        动态策略调整演示程序");
        System.out.println("==========================================");
        System.out.println("配置信息: " + config);
        
        // 场景1：目标权重可达成（正常负载）
        System.out.println("\n\n🔵 场景1：目标权重可达成（正常负载）");
        System.out.println("==========================================");
        demo.testScenario1(calculationService, config);
        
        // 场景2：需要容量优化（中等负载）
        System.out.println("\n\n🟡 场景2：需要容量优化（中等负载）");
        System.out.println("==========================================");
        demo.testScenario2(calculationService, config);
        
        // 场景3：启用紧急保护（高负载）
        System.out.println("\n\n🔴 场景3：启用紧急保护（高负载）");
        System.out.println("==========================================");
        demo.testScenario3(calculationService, config);

        // 场景4：真正的紧急保护（极高负载）
        System.out.println("\n\n🚨 场景4：真正的紧急保护（极高负载）");
        System.out.println("==========================================");
        demo.testScenario4(calculationService, config);

        System.out.println("\n\n==========================================");
        System.out.println("           演示程序结束");
        System.out.println("==========================================");
    }
    
    /**
     * 创建测试配置
     */
    private DynamicStrategyConfig createTestConfig() {
        // 策略信息
        Map<String, StrategyInfo> strategies = new HashMap<>();
        strategies.put("A", new StrategyInfo(1, 150));  // A策略：batchNum=1
        strategies.put("B", new StrategyInfo(3, 150));  // B策略：batchNum=3
        
        // 目标权重
        Map<String, Double> targetWeights = new HashMap<>();
        targetWeights.put("A", 0.1);  // A=10%
        targetWeights.put("B", 0.9);  // B=90%
        
        // 紧急配置权重
        Map<String, Double> emergencyWeights = new HashMap<>();
        emergencyWeights.put("A", 0.9);  // A=90%
        emergencyWeights.put("B", 0.1);  // B=10%
        
        EmergencyConfig emergencyConfig = new EmergencyConfig("高负载保护", emergencyWeights);
        
        return new DynamicStrategyConfig(
            "31854",                    // scenId
            "XX,YY | *",               // abId
            true,                      // switchOn
            "RTP",                     // type
            "refined_operation_rebo_rank_model_time_division_v3_1_1", // bizName
            1000,                      // maxQPS
            0.9,                       // safetyBuffer
            0.1,                       // adjustmentStep
            50,                        // maxRT
            strategies,                // strategies
            targetWeights,             // targetWeights
            emergencyConfig            // emergencyConfig
        );
    }
    
    /**
     * 场景1：目标权重可达成
     * 当前QPS=300，目标QPS=840，安全容量=900
     */
    private void testScenario1(WeightCalculationService service, DynamicStrategyConfig config) {
        double currentQps = 300;
        
        System.out.println("输入参数:");
        System.out.println("- 当前QPS: " + currentQps);
        System.out.println("- 最大QPS: " + config.getMaxQPS());
        System.out.println("- 安全容量: " + (config.getMaxQPS() * config.getSafetyBuffer()));
        System.out.println("- 目标权重: " + config.getTargetWeights());
        
        WeightCalculationResult result = service.calculateOptimalWeights(currentQps, config);
        
        System.out.println("\n计算结果:");
        System.out.println(result);
        
        if (result.getAdjustmentSteps() != null && !result.getAdjustmentSteps().isEmpty()) {
            System.out.println("\n调整步骤:");
            for (AdjustmentStep step : result.getAdjustmentSteps()) {
                System.out.println("- " + step);
            }
        }
    }
    
    /**
     * 场景2：需要容量优化
     * 当前QPS=400，目标QPS=1120，需要调整权重
     */
    private void testScenario2(WeightCalculationService service, DynamicStrategyConfig config) {
        double currentQps = 400;
        
        System.out.println("输入参数:");
        System.out.println("- 当前QPS: " + currentQps);
        System.out.println("- 最大QPS: " + config.getMaxQPS());
        System.out.println("- 安全容量: " + (config.getMaxQPS() * config.getSafetyBuffer()));
        System.out.println("- 目标权重: " + config.getTargetWeights());
        
        WeightCalculationResult result = service.calculateOptimalWeights(currentQps, config);
        
        System.out.println("\n计算结果:");
        System.out.println(result);
        
        if (result.getAdjustmentSteps() != null && !result.getAdjustmentSteps().isEmpty()) {
            System.out.println("\n调整步骤:");
            for (AdjustmentStep step : result.getAdjustmentSteps()) {
                System.out.println("- " + step);
            }
        }
    }
    
    /**
     * 场景3：启用紧急保护
     * 当前QPS=800，即使调整权重也无法满足容量约束
     */
    private void testScenario3(WeightCalculationService service, DynamicStrategyConfig config) {
        double currentQps = 900;
        
        System.out.println("输入参数:");
        System.out.println("- 当前QPS: " + currentQps);
        System.out.println("- 最大QPS: " + config.getMaxQPS());
        System.out.println("- 安全容量: " + (config.getMaxQPS() * config.getSafetyBuffer()));
        System.out.println("- 目标权重: " + config.getTargetWeights());
        System.out.println("- 紧急权重: " + config.getEmergencyConfig().getWeights());
        
        WeightCalculationResult result = service.calculateOptimalWeights(currentQps, config);
        
        System.out.println("\n计算结果:");
        System.out.println(result);
        
        if (result.getAdjustmentSteps() != null && !result.getAdjustmentSteps().isEmpty()) {
            System.out.println("\n调整步骤:");
            for (AdjustmentStep step : result.getAdjustmentSteps()) {
                System.out.println("- " + step);
            }
        }
    }

    /**
     * 场景4：真正的紧急保护
     * 当前QPS=950，即使最小权重调整也无法满足容量约束
     */
    private void testScenario4(WeightCalculationService service, DynamicStrategyConfig config) {
        double currentQps = 950;

        System.out.println("输入参数:");
        System.out.println("- 当前QPS: " + currentQps);
        System.out.println("- 最大QPS: " + config.getMaxQPS());
        System.out.println("- 安全容量: " + (config.getMaxQPS() * config.getSafetyBuffer()));
        System.out.println("- 目标权重: " + config.getTargetWeights());
        System.out.println("- 紧急权重: " + config.getEmergencyConfig().getWeights());

        WeightCalculationResult result = service.calculateOptimalWeights(currentQps, config);

        System.out.println("\n计算结果:");
        System.out.println(result);

        if (result.getAdjustmentSteps() != null && !result.getAdjustmentSteps().isEmpty()) {
            System.out.println("\n调整步骤:");
            for (AdjustmentStep step : result.getAdjustmentSteps()) {
                System.out.println("- " + step);
            }
        }
    }
}
