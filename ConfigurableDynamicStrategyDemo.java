import java.util.*;

/**
 * 可配置的动态策略演示程序
 * 
 * 支持功能：
 * 1. 从JSON配置文件加载策略配置
 * 2. 支持任意数量策略的权重计算
 * 3. 模拟不同QPS场景的测试
 * 4. 详细的结果分析和对比
 */
public class ConfigurableDynamicStrategyDemo {
    
    public static void main(String[] args) {
        ConfigurableDynamicStrategyDemo demo = new ConfigurableDynamicStrategyDemo();
        
        // 检查命令行参数
        String configFile = "strategy-config.json"; // 默认配置文件
        if (args.length > 0) {
            configFile = args[0];
        }
        
        System.out.println("==========================================");
        System.out.println("     可配置动态策略调整演示程序");
        System.out.println("==========================================");
        System.out.println("使用配置文件: " + configFile);
        
        try {
            // 加载配置
            DynamicStrategyConfig config = ConfigLoader.loadFromFile(configFile);
            
            // 创建服务实例
            UniversalWeightCalculationService service = new UniversalWeightCalculationService();
            
            // 显示配置信息
            demo.displayConfigInfo(config);
            
            // 运行测试场景
            demo.runTestScenarios(service, config);
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
            
            System.out.println("\n使用说明:");
            System.out.println("java ConfigurableDynamicStrategyDemo [配置文件路径]");
            System.out.println("示例:");
            System.out.println("  java ConfigurableDynamicStrategyDemo strategy-config.json");
            System.out.println("  java ConfigurableDynamicStrategyDemo strategy-config-abc.json");
            System.out.println("  java ConfigurableDynamicStrategyDemo strategy-config-abcd.json");
        }
    }
    
    /**
     * 显示配置信息
     */
    private void displayConfigInfo(DynamicStrategyConfig config) {
        System.out.println("\n📋 配置信息:");
        System.out.println("==========================================");
        System.out.println("场景ID: " + config.getScenId());
        System.out.println("业务名称: " + config.getBizName());
        System.out.println("最大QPS: " + config.getMaxQPS());
        System.out.println("安全缓冲: " + config.getSafetyBuffer());
        System.out.println("调整步长: " + config.getAdjustmentStep());
        
        System.out.println("\n📊 策略配置:");
        for (Map.Entry<String, StrategyInfo> entry : config.getStrategies().entrySet()) {
            String name = entry.getKey();
            StrategyInfo info = entry.getValue();
            double weight = config.getTargetWeights().get(name);
            System.out.printf("  %s: batchNum=%d, count=%d, 目标权重=%.1f%%\n", 
                name, info.getBatchNum(), info.getCount(), weight * 100);
        }
        
        System.out.println("\n🚨 紧急配置:");
        System.out.println("  触发条件: " + config.getEmergencyConfig().getCondition());
        System.out.print("  紧急权重: ");
        config.getEmergencyConfig().getWeights().forEach((k, v) -> 
            System.out.printf("%s=%.1f%% ", k, v * 100));
        System.out.println();
    }
    
    /**
     * 运行测试场景
     */
    private void runTestScenarios(UniversalWeightCalculationService service, DynamicStrategyConfig config) {
        // 计算目标权重倍数，用于确定测试QPS
        double targetMultiplier = calculateTargetMultiplier(config);
        double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
        
        // 定义测试场景
        TestScenario[] scenarios = {
            new TestScenario("正常负载", safeMaxQps / targetMultiplier * 0.8, "目标权重应该可直接使用"),
            new TestScenario("中等负载", safeMaxQps / targetMultiplier * 1.2, "需要权重优化调整"),
            new TestScenario("高负载", safeMaxQps / targetMultiplier * 1.8, "可能需要紧急保护"),
            new TestScenario("极高负载", safeMaxQps / targetMultiplier * 2.5, "必须启用紧急保护")
        };
        
        List<WeightCalculationResult> results = new ArrayList<>();
        
        // 执行测试场景
        for (int i = 0; i < scenarios.length; i++) {
            TestScenario scenario = scenarios[i];
            
            System.out.println("\n" + getScenarioIcon(i) + " 场景" + (i + 1) + "：" + scenario.name);
            System.out.println("==========================================");
            System.out.println("测试QPS: " + String.format("%.0f", scenario.qps));
            System.out.println("预期结果: " + scenario.expectedResult);
            
            WeightCalculationResult result = service.calculateOptimalWeights(scenario.qps, config);
            results.add(result);
            
            printResult(result);
        }
        
        // 结果汇总分析
        System.out.println("\n📈 结果汇总分析:");
        System.out.println("==========================================");
        printResultSummary(scenarios, results);
    }
    
    /**
     * 计算目标权重倍数
     */
    private double calculateTargetMultiplier(DynamicStrategyConfig config) {
        double multiplier = 0.0;
        for (Map.Entry<String, Double> entry : config.getTargetWeights().entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            multiplier += weight * batchNum;
        }
        return multiplier;
    }
    
    /**
     * 获取场景图标
     */
    private String getScenarioIcon(int index) {
        String[] icons = {"🔵", "🟡", "🔴", "🚨"};
        return icons[index];
    }
    
    /**
     * 打印单个结果
     */
    private void printResult(WeightCalculationResult result) {
        System.out.println("\n计算结果:");
        System.out.println("  状态: " + result.getStatus());
        System.out.println("  最终权重: " + formatWeights(result.getFinalWeights()));
        System.out.println("  预期QPS: " + String.format("%.0f", result.getExpectedQps()));
        System.out.println("  资源利用率: " + String.format("%.1f%%", result.getResourceUtilization() * 100));
        System.out.println("  计算耗时: " + result.getCalculationTimeMs() + "ms");
        System.out.println("  调整步骤数: " + (result.getAdjustmentSteps() != null ? result.getAdjustmentSteps().size() : 0));
        
        if (result.getAdjustmentSteps() != null && !result.getAdjustmentSteps().isEmpty()) {
            System.out.println("  关键调整步骤:");
            List<AdjustmentStep> steps = result.getAdjustmentSteps();
            // 只显示前3个和最后1个步骤
            for (int i = 0; i < Math.min(3, steps.size()); i++) {
                AdjustmentStep step = steps.get(i);
                System.out.printf("    %d. %s (可行: %s)\n", 
                    i + 1, step.getStepDescription(), step.isFeasible() ? "✅" : "❌");
            }
            if (steps.size() > 3) {
                System.out.println("    ... (省略中间步骤)");
                AdjustmentStep lastStep = steps.get(steps.size() - 1);
                System.out.printf("    %d. %s (可行: %s)\n", 
                    steps.size(), lastStep.getStepDescription(), lastStep.isFeasible() ? "✅" : "❌");
            }
        }
    }
    
    /**
     * 打印结果汇总
     */
    private void printResultSummary(TestScenario[] scenarios, List<WeightCalculationResult> results) {
        System.out.println("┌──────────┬─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐");
        System.out.println("│   场景   │      状态       │   预期QPS   │ 资源利用率  │   耗时(ms)  │ 调整步骤数  │");
        System.out.println("├──────────┼─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤");
        
        for (int i = 0; i < scenarios.length; i++) {
            TestScenario scenario = scenarios[i];
            WeightCalculationResult result = results.get(i);
            
            System.out.printf("│ 场景%-4d │ %-15s │ %-11.0f │ %-10.1f%% │ %-11d │ %-11d │\n",
                i + 1,
                result.getStatus().toString(),
                result.getExpectedQps(),
                result.getResourceUtilization() * 100,
                result.getCalculationTimeMs(),
                result.getAdjustmentSteps() != null ? result.getAdjustmentSteps().size() : 0
            );
        }
        
        System.out.println("└──────────┴─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘");
        
        // 统计分析
        long totalTime = results.stream().mapToLong(WeightCalculationResult::getCalculationTimeMs).sum();
        double avgUtilization = results.stream().mapToDouble(WeightCalculationResult::getResourceUtilization).average().orElse(0);
        
        System.out.println("\n📊 统计分析:");
        System.out.println("  总计算耗时: " + totalTime + "ms");
        System.out.println("  平均资源利用率: " + String.format("%.1f%%", avgUtilization * 100));
        System.out.println("  成功优化场景: " + results.stream()
            .mapToInt(r -> r.getStatus() != CalculationStatus.CALCULATION_FAILED ? 1 : 0).sum() + "/" + results.size());
    }
    
    /**
     * 格式化权重显示
     */
    private String formatWeights(Map<String, Double> weights) {
        if (weights == null || weights.isEmpty()) return "{}";
        
        return weights.entrySet().stream()
            .map(e -> e.getKey() + "=" + String.format("%.2f", e.getValue()))
            .reduce((a, b) -> a + ", " + b)
            .map(s -> "{" + s + "}")
            .orElse("{}");
    }
    
    /**
     * 测试场景类
     */
    private static class TestScenario {
        String name;
        double qps;
        String expectedResult;
        
        TestScenario(String name, double qps, String expectedResult) {
            this.name = name;
            this.qps = qps;
            this.expectedResult = expectedResult;
        }
    }
}
