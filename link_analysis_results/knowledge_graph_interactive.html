
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翔宇工作流知识图谱</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .stats {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            margin: 5px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        #network {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .controls {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 翔宇工作流知识图谱</h1>
        <p>基于内部链接分析的文章关系网络</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-number">232</div>
            <div class="stat-label">文章总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">151</div>
            <div class="stat-label">链接总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">135</div>
            <div class="stat-label">被引用文章</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">7</div>
            <div class="stat-label">引用他人文章</div>
        </div>
    </div>
    
    <div id="network"></div>
    
    <div class="controls">
        <h3>📊 图表说明</h3>
        <p><strong>节点大小</strong>：表示文章被引用的次数（越大表示越受欢迎）</p>
        <p><strong>连线粗细</strong>：表示链接的置信度（越粗表示关联性越强）</p>
        <p><strong>颜色分类</strong>：不同颜色代表不同的文章标签类型</p>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FF6B6B;"></div>
                <span>Make基础教程</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4ECDC4;"></div>
                <span>n8n基础教程</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #45B7D1;"></div>
                <span>资源包</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #96CEB4;"></div>
                <span>API</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FFEAA7;"></div>
                <span>自动化赚钱</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #DDA0DD;"></div>
                <span>快捷工具</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #98D8C8;"></div>
                <span>会员独家</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #F7DC6F;"></div>
                <span>AI教程与资源</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #95A5A6;"></div>
                <span>其他</span>
            </div>
        </div>
    </div>

    <script>
        // 准备数据
        const nodes = new vis.DataSet([{"id": "bed3c2fd-c4c7-42b5-bce9-ae20ae00164c", "label": "Make25.全网首个多模态 Make工作流，轻松从文本、音...", "title": "Make25.全网首个多模态 Make工作流，轻松从文本、音频或视频生成爆款口播稿！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "02648d80-5a88-4c73-9fd6-6d3680a30e96", "label": "小红书自动化采集Make工作流", "title": "小红书自动化采集Make工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "b3812a9d-8937-4ec4-bc1a-85f9a0b3bb23", "label": "Make中文教程：修复速率限制错误（Fixing rate ...", "title": "Make中文教程：修复速率限制错误（Fixing rate limit errors）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "1aeb2ada-e81f-4914-9e15-807658afe2ed", "label": "Make中文教程：数学函数（Math functions）", "title": "Make中文教程：数学函数（Math functions）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "7019f508-603c-4817-a95b-fc9aaa6d7fe8", "label": "OpenAI提示词优化工具内置系统提示词", "title": "OpenAI提示词优化工具内置系统提示词\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "36df6fc9-c215-499c-b371-c571026f5663", "label": "微信公众号Make工作流-方案1", "title": "微信公众号Make工作流-方案1\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "7b720417-fcb6-42c8-83fc-2536109cdd68", "label": "提示词生成方法", "title": "提示词生成方法\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "46e40c58-7a50-4201-a728-e446a231b0d2", "label": "Make中文教程： ElevenLabs", "title": "Make中文教程： ElevenLabs\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "b1a8f97d-685e-4bd6-bb74-f26159d34316", "label": "格式整理提示词", "title": "格式整理提示词\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "070be0ae-1c0f-46a8-8177-4086fcaaf091", "label": "福利来啦 | 手把手教你领取 $10 大模型免费额度", "title": "福利来啦 | 手把手教你领取 $10 大模型免费额度\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "88bbaa4d-058e-416f-b139-30cd5c299cbc", "label": "n8n RAG 系统实战手册：手把手教你应对大文件、OCR、...", "title": "n8n RAG 系统实战手册：手把手教你应对大文件、OCR、标准化处理\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "cef6aa8c-2b6f-44c9-8292-f3e78a51edfc", "label": "Make可调用免费API 4", "title": "Make可调用免费API 4\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "94e5fae1-755a-47e6-a266-6d1968489586", "label": "Make中文教程：字符串函数（String function...", "title": "Make中文教程：字符串函数（String functions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0a8e69dc-39c3-488f-b726-cb8c28d83171", "label": "Make中文教程：忽略错误处理器（Ignore Error ...", "title": "Make中文教程：忽略错误处理器（Ignore Error Handler）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "5cd20543-78f6-40d9-a303-b890761b9a10", "label": "自媒体出海全攻略：新手也能赚美金！（5万字）", "title": "自媒体出海全攻略：新手也能赚美金！（5万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "b4c1b904-0efc-4049-abb2-8665ad97e5a8", "label": "Make中文教程：日期时间格式符号（Tokens for d...", "title": "Make中文教程：日期时间格式符号（Tokens for date/time formatting）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "1cec4f93-eec2-4afe-a861-7c89921d809e", "label": "英文提示词示例合集", "title": "英文提示词示例合集\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "2cc0815e-d9af-4412-a44f-f377d30c676a", "label": "Make中文教程：Pinecone", "title": "Make中文教程：Pinecone\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "10267ff5-0bcf-463f-8b41-9bfdf79cfdc1", "label": "2025 大模型选择实践指南：翔宇教你如何选择大模型", "title": "2025 大模型选择实践指南：翔宇教你如何选择大模型\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "c76a17f9-b768-44e5-a8aa-df207fa7a6b4", "label": "全网AI公开教程与资源汇总（会员独家）", "title": "全网AI公开教程与资源汇总（会员独家）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "deb47733-d2bd-4f73-89f0-5dec59cf46d0", "label": "【范例+模板】手把手教你写出高点击的YouTube描述！（1...", "title": "【范例+模板】手把手教你写出高点击的YouTube描述！（1万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "18ae22f8-edc0-4749-baec-84ca7fd5c190", "label": "HTTP第三方API调用：智谱AI", "title": "HTTP第三方API调用：智谱AI\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "f0d4614c-b0cd-4d07-9943-83decff7f214", "label": "Medium创收全攻略：Medium 平台全面介绍（4万字）", "title": "Medium创收全攻略：Medium 平台全面介绍（4万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "8b999885-680d-4df0-882d-d4b2ad0e58c4", "label": "翔宇工作流SEO实战手册：副标题（H2-H6）撰写全攻略", "title": "翔宇工作流SEO实战手册：副标题（H2-H6）撰写全攻略\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "24e38260-9118-414b-a761-a0f7b02e7b48", "label": "随时掌握全网热榜：手把手搭建个人热点 API", "title": "随时掌握全网热榜：手把手搭建个人热点 API\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "928a75e7-4f3f-4186-b394-c34fcdbb8ce1", "label": "如何利用大模型设计高点击率YouTube封面：封面生成AI提...", "title": "如何利用大模型设计高点击率YouTube封面：封面生成AI提示词生成完全指南（1万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "17cb50a4-b256-46cf-90aa-c874f34b980a", "label": "全新大模型上线！免费平替OpenAI，支持Make和结构化输...", "title": "全新大模型上线！免费平替OpenAI，支持Make和结构化输出！\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "5366f2cf-1071-455c-a7ea-868220167b39", "label": "Make17.PDF翻译自动化：利用Make打造反思翻译工作...", "title": "Make17.PDF翻译自动化：利用Make打造反思翻译工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "150bbddf-8dbd-4898-b5db-21c947998710", "label": "Make26.偷偷赚美元？微信公众号深度改写+ Medium...", "title": "Make26.偷偷赚美元？微信公众号深度改写+ Medium创收玩法曝光！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "e705f627-6857-4cf8-84e2-e3b8d9265caa", "label": "Make中文教程：操作（Operations）", "title": "Make中文教程：操作（Operations）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "e68ad2a5-0a4b-4787-a078-8417fff9dc10", "label": "Make中文教程：自定义场景属性（Custom scenar...", "title": "Make中文教程：自定义场景属性（Custom scenario properties）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0004527c-2a6c-412b-b623-a7e7ede12736", "label": "Make调用xAI Gork手把手教程，充值 5 美元，每月...", "title": "Make调用xAI Gork手把手教程，充值 5 美元，每月 150 美元免费额度！\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "fa4d3750-3420-486b-8f95-f6273b874987", "label": "n8n 30.n8n RAG 全自动知识库副业工作流，轻松月...", "title": "n8n 30.n8n RAG 全自动知识库副业工作流，轻松月入过万？\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "f38f2628-53cf-4c7f-ab6c-c3af367be372", "label": "从小白到高手：翔宇如何高效撰写提示词？", "title": "从小白到高手：翔宇如何高效撰写提示词？\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "260695d9-36d6-43f2-b7f9-70dfd254d4e4", "label": "Zeabur 一键部署 n8n 全流程图文教程（新手必看）", "title": "Zeabur 一键部署 n8n 全流程图文教程（新手必看）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "4e0d02d3-31a6-43c4-8f25-24a16988ceba", "label": "Make中文教程：场景执行流程（Scenario execu...", "title": "Make中文教程：场景执行流程（Scenario execution flow）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "bdc9f626-f2e7-4931-a467-00388e81aa2e", "label": "如何通过 Prompt 获取并整理所有 LLM 系统提示词", "title": "如何通过 Prompt 获取并整理所有 LLM 系统提示词\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "efbea7de-c182-4c23-b26d-92efc3f56111", "label": "Make19.3分钟速成：利用Make和Flux打造图文并茂...", "title": "Make19.3分钟速成：利用Make和Flux打造图文并茂的儿童绘本\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "6d724aa2-6e37-471b-b848-fb82a0674e7d", "label": "又一个Make原生的有免费额度大模型来啦！", "title": "又一个Make原生的有免费额度大模型来啦！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ec65cfde-8d69-43c7-8cb6-1e0d6ecfb31d", "label": "用 n8n + RAG 打造赚钱机器 💰 从 0 开始变现你...", "title": "用 n8n + RAG 打造赚钱机器 💰 从 0 开始变现你的知识\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "efd6a3ef-ccd4-4c7e-b45c-97989f98a490", "label": "HTTP第三方API调用：Fal.ai", "title": "HTTP第三方API调用：Fal.ai\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "61a0fd41-3664-4700-af94-a0307abc619a", "label": "n8n 29.n8n自动化赚钱全流程拆解：小白也能做的副业系...", "title": "n8n 29.n8n自动化赚钱全流程拆解：小白也能做的副业系统\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "7a0757f4-6e17-49e3-9297-d76074854088", "label": "Make自动化新手常见问题解答 (FAQ)", "title": "Make自动化新手常见问题解答 (FAQ)\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "19544559-034d-431a-8584-4a80c013a2e8", "label": "Make可调用免费API 1", "title": "Make可调用免费API 1\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "2b5991a7-fc95-41f6-b35b-dd45d4bef6f1", "label": "Make中文教程：术语表详解（Glossary）-重要", "title": "Make中文教程：术语表详解（Glossary）-重要\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "bbbc4483-31ca-4308-91ab-010e9cbb2b13", "label": "免费Instagram自动化采集Make工作流", "title": "免费Instagram自动化采集Make工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "d4199829-cc22-41b2-aee9-7658cd378d06", "label": "OpenAI API 充值手把手教程", "title": "OpenAI API 充值手把手教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "674c3437-68b3-4fa7-8557-9e0bd4771a9b", "label": "Make调用谷歌Gmail、Drive、Doc服务手把手完整...", "title": "Make调用谷歌Gmail、Drive、Doc服务手把手完整配置教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "80797a10-7626-4487-8288-eac9fe120276", "label": "为什么自动化工作流需要Follow？（会员独家）", "title": "为什么自动化工作流需要Follow？（会员独家）\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "a4d271ac-65c0-40ca-acfa-e8e8967c9ea9", "label": "Make20.Make工作流改造方法论：一个工作流如何变成1...", "title": "Make20.Make工作流改造方法论：一个工作流如何变成1000个工作流？\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "b6354771-82cf-4f73-9589-dfac08bcef3d", "label": "Make15.Firecrawl爬虫实操：Make和Fire...", "title": "Make15.Firecrawl爬虫实操：Make和Firecrawl全自动撰写博客文章引流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "fd2c579f-cd52-40ea-a36c-373c3d8ce2f2", "label": "n8n 图片上传 Cloudinary 工作流", "title": "n8n 图片上传 Cloudinary 工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "4bfefd3f-16f6-42be-9ad5-bc11610c2f5f", "label": "HTTP第三方API调用：DeepSeek", "title": "HTTP第三方API调用：DeepSeek\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "23b032d2-7948-4942-80ad-80c059ab6d98", "label": "AI 视频神器 Fal.AI 限时送 $20，码住不亏！兑换...", "title": "AI 视频神器 Fal.AI 限时送 $20，码住不亏！兑换教程来了\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "0b5cb75e-8d1d-4658-a1e2-30360fcdbe93", "label": "Make工作流秒出仿写提示词：效率爆表！", "title": "Make工作流秒出仿写提示词：效率爆表！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "da9d39c4-0005-4f06-b05e-b1764ac467dd", "label": "Make中文教程：未完成执行（Incomplete exec...", "title": "Make中文教程：未完成执行（Incomplete executions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0bcf3c73-817e-4f19-966d-09f7891eb097", "label": "HTTP第三方API调用：Jina", "title": "HTTP第三方API调用：Jina\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "4a896e91-3121-45e1-b625-e7e9f379d01d", "label": "HTTP第三方API调用：Google Gemini", "title": "HTTP第三方API调用：Google Gemini\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "e166755d-c49f-4b61-abc5-72e4739c5bc9", "label": "HTTP第三方API调用：302.AI", "title": "HTTP第三方API调用：302.AI\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "14252912-0e24-4a95-850d-51d1c0f0678e", "label": "Make22.小红书与Instagram自动化运营：一键生成...", "title": "Make22.小红书与Instagram自动化运营：一键生成真实感美女图片与视频\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "8221e7d2-0991-44f8-8b09-e56cf2be289a", "label": "Make中文教程：Make开发工具（Make DevTool...", "title": "Make中文教程：Make开发工具（Make DevTool）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "353a96f4-cb67-48a6-9101-c4d3b5f739cd", "label": "视频 31 教程：手机 Opencat 设置 n8n MCP...", "title": "视频 31 教程：手机 Opencat 设置 n8n MCP 服务器手把手教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "724ee1e8-49bd-4e2a-9a80-8340f77b0424", "label": "Make中文教程：HTTP", "title": "Make中文教程：HTTP\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "23f0e896-127b-4023-b646-a3af86be9387", "label": "Make使用指南5：映射教程", "title": "Make使用指南5：映射教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "0d60a7f1-8dac-47ee-8e50-01ce78bfec2b", "label": "Make中文教程：XML", "title": "Make中文教程：XML\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "5bef1368-7cd1-4a0a-9133-b76e57c30312", "label": "Make中文教程：回滚错误处理器（Rollback Erro...", "title": "Make中文教程：回滚错误处理器（Rollback Error Handler）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6d3b37f4-7b16-4536-b216-f56796456c5b", "label": "Make中文教程：模块设置（Module settings）", "title": "Make中文教程：模块设置（Module settings）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0414bbf6-0caf-4522-9dfd-a71a37288348", "label": "Make中文教程：Make 中错误和警告的介绍（Introd...", "title": "Make中文教程：Make 中错误和警告的介绍（Introduction to errors and warnings in Make）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "c16a7e4b-4326-4172-a368-120bf0b51121", "label": "300 个免费 API 合集来啦，Make 工作流超强帮手！", "title": "300 个免费 API 合集来啦，Make 工作流超强帮手！\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "4b5f09c2-5caa-4d53-ae52-8743cb6a9a82", "label": "Make中文教程：日期和时间函数(Date and time...", "title": "Make中文教程：日期和时间函数(Date and time functions)\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "3d055190-2759-4002-9f83-30c2d924c61f", "label": "微信公众号自动推送草稿箱Make工作流", "title": "微信公众号自动推送草稿箱Make工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "43f9b855-8f2d-4af6-8b70-7a3cdaafce03", "label": "OnDemand平台 50 美金福利领取手把手教程", "title": "OnDemand平台 50 美金福利领取手把手教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "5215e888-2382-4968-88e8-83650b56faa9", "label": "翔宇 n8n RAG 知识库职业篇：热门职业知识库构建指南", "title": "翔宇 n8n RAG 知识库职业篇：热门职业知识库构建指南\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "63e41ddd-e41b-43e0-9e6e-bf2a31c729d3", "label": "Make中文教程：创建场景（Creating a scena...", "title": "Make中文教程：创建场景（Creating a scenario）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "f3dff274-89ce-476a-af52-502de7c92955", "label": "Make13.完整教程：Make和EXA AI全自动撰写微信...", "title": "Make13.完整教程：Make和EXA AI全自动撰写微信公众号图文及小红书笔记\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "0ebefa66-2e75-4fd4-8143-233de9f38bdb", "label": "Make28. 只需输入字幕！Youtube标题、描述、封面...", "title": "Make28. 只需输入字幕！Youtube标题、描述、封面全自动生成，Make工作流效率逆天！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ef4415dc-2e7d-477a-a88e-7e8f3dbff588", "label": "Make中文教程：Make中的错误类型（Types of e...", "title": "Make中文教程：Make中的错误类型（Types of errors in Make）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "174d3f29-0bb1-4c45-94ca-389f63ac9040", "label": "Make3.Jina Reader api实操：如何利用ma...", "title": "Make3.Jina Reader api实操：如何利用make自动采集OpenAI官网新闻\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "e79d9707-4a10-439a-8df9-2c5dc712f361", "label": "Make中文教程：数组映射（Mapping arrays）", "title": "Make中文教程：数组映射（Mapping arrays）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7f3fc119-c059-4149-8c2e-0493ef63a72e", "label": "Make中文教程：中断错误处理器（Break error h...", "title": "Make中文教程：中断错误处理器（Break error handler）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "c138f529-258d-42fe-a054-97a756a5ab10", "label": "Make中文教程：数据结构（Data structures）", "title": "Make中文教程：数据结构（Data structures）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "3fb1a892-cc7b-4775-943c-9aa1d414897b", "label": "RSS订阅工具汇总（会员独家）", "title": "RSS订阅工具汇总（会员独家）\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "86e6a93a-3920-4663-bbe9-adb86aa8b134", "label": "Make5.高效Notion自动化实操：使用Make实现数据...", "title": "Make5.高效Notion自动化实操：使用Make实现数据库自动关联、分类、整理，告别手动整理\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "d7e45c3b-c381-429b-be3e-6738b28849f5", "label": "微信公众号RSS订阅指南 （会员独家）", "title": "微信公众号RSS订阅指南 （会员独家）\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "abf4627a-c32f-427e-8689-4321995fff80", "label": "Make 低成本调用阿里DeepSeek R1手把手教程（免...", "title": "Make 低成本调用阿里DeepSeek R1手把手教程（免费百万 Token）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "8dfad570-fa10-4d03-968c-91598ff48df9", "label": "Make中文教程：修复缺失数据错误（Fixing missi...", "title": "Make中文教程：修复缺失数据错误（Fixing missing data errors）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "fedcf2cd-6afd-4085-90ef-26de5e558453", "label": "Make中文教程：场景输入（Scenario inputs）", "title": "Make中文教程：场景输入（Scenario inputs）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "aea31564-dcdc-4d39-9024-94d55ebbb772", "label": "Make中文教程：证书和密钥(Certificates an...", "title": "Make中文教程：证书和密钥(Certificates and keys)\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "026bcba6-6940-4ad9-a0ab-bdcfe7d1656b", "label": "Make中文教程：子场景（Subscenarios）", "title": "Make中文教程：子场景（Subscenarios）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "531bafb6-84ee-4d35-87ea-140d03787a95", "label": "n8n 32.SEO 自动化揭秘：n8n 手把手教程，3 步...", "title": "n8n 32.SEO 自动化揭秘：n8n 手把手教程，3 步搞定跨境电商关键词挖掘\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "c38bd38a-e4a0-4277-8923-1db9f4d9fdae", "label": "HTTP第三方API调用：Firecrawl", "title": "HTTP第三方API调用：Firecrawl\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8ea25079-8474-4c03-bdc6-5db7f076d9f0", "label": "Make中文教程：场景详情（Scenario detail）", "title": "Make中文教程：场景详情（Scenario detail）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6fbea6cc-58b9-43a5-b20f-6c301226c143", "label": "Make14.爆款提示词工厂：Make自动化生成高质量写作提...", "title": "Make14.爆款提示词工厂：Make自动化生成高质量写作提示词\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "8b2da730-ca2f-4012-b0f1-0f9f191a4b74", "label": "Make7.Kimi API实操办公自动化：Make工作流自...", "title": "Make7.Kimi API实操办公自动化：Make工作流自动总结PDF文档\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "38103885-d879-4e38-97e7-37d915b268c4", "label": "Make中文教程：变量（Variables）", "title": "Make中文教程：变量（Variables）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "59bc66ba-61e2-43b5-baf5-273721387f04", "label": "Make中文教程：映射（Mapping）", "title": "Make中文教程：映射（Mapping）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "20abaee2-6cd1-49af-9898-0997ca422b38", "label": "YouTube标题创作方法：写作技巧与情绪钩子 | 新手必学...", "title": "YouTube标题创作方法：写作技巧与情绪钩子 | 新手必学（1万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "40e20b1e-b69e-4a1a-b739-cbf06d7a3e5c", "label": "Make中文教程：场景调度（Scheduling a sce...", "title": "Make中文教程：场景调度（Scheduling a scenario）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "c706c6a9-4729-4c24-8f7f-96b8ebf2b14f", "label": "火山引擎调用 DeepSeek R1 大模型手把手教程", "title": "火山引擎调用 DeepSeek R1 大模型手把手教程\\n入链: 1\\n出链: 1", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 1}, {"id": "cfed3d6b-5795-4bec-9fd9-57c4096d988d", "label": "OpenRouter原生模块登录Make，支持结构化输出", "title": "OpenRouter原生模块登录Make，支持结构化输出\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "ecae07b7-5c1f-4a34-82b7-01f5a5c93fee", "label": "Make中文教程： Claude（Anthropic Cla...", "title": "Make中文教程： Claude（Anthropic Claude）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "2ac9bd72-cecf-4b03-b9c2-879091cf494f", "label": "Make中文教程：使用自定义OAuth客户端连接到Googl...", "title": "Make中文教程：使用自定义OAuth客户端连接到Google服务（Connecting to Google services using custom OAuth client）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6644c74c-970c-43bf-a6e1-ad8ebaa532aa", "label": "Make中文教程：OpenAI模块常见错误及排查指南", "title": "Make中文教程：OpenAI模块常见错误及排查指南\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "874d0617-7c5f-4358-8778-3622422d0f05", "label": "Make中文教程：网络钩子（Webhooks）", "title": "Make中文教程：网络钩子（Webhooks）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6b16b4b4-d172-49a0-bd51-8795d4fb71b2", "label": "Make中文教程：场景执行、循环和阶段（Scenario e...", "title": "Make中文教程：场景执行、循环和阶段（Scenario execution, cycles, and phases）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "fe78bec9-691b-492c-9a42-df526ba222f9", "label": "Make中文教程：数学函数（Math variables）", "title": "Make中文教程：数学函数（Math variables）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "5a148f3a-20bf-42a9-828a-fa3e8344a01b", "label": "视频29配套：面向职业的 AI 自动化工作流（n8n）", "title": "视频29配套：面向职业的 AI 自动化工作流（n8n）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "def7f5c1-2bb9-418f-a21f-024426652789", "label": "Make中文教程：数据存储（Data store）", "title": "Make中文教程：数据存储（Data store）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "91b66074-7cd2-4e09-90fd-79eae33a0914", "label": "Make中文教程：如何恢复一个之前的场景版本（How to ...", "title": "Make中文教程：如何恢复一个之前的场景版本（How to restore a previous scenario version）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "c48a3630-a770-448e-a906-da3a5b13d7bc", "label": "Make4.小红书自动化：如何利用Make制作个人自媒体中心...", "title": "Make4.小红书自动化：如何利用Make制作个人自媒体中心，批量生成爆款笔记\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "caa604d3-ec6d-419f-9390-c3b16df594a3", "label": "Make中文教程：JSON", "title": "Make中文教程：JSON\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7937d6b3-c1ff-41f7-8c55-ddb2964f2a39", "label": "Make中文教程：应用生命周期（App lifecycle）", "title": "Make中文教程：应用生命周期（App lifecycle）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "102339f0-c063-46ef-9409-7f70e3452054", "label": "Kluster.ai 新手25美元福利领取完整教程", "title": "Kluster.ai 新手25美元福利领取完整教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "d8d66816-1061-4699-8887-6aab14029834", "label": "n8n 31.效率翻10倍！n8n+MCP实操指南：极速接入...", "title": "n8n 31.效率翻10倍！n8n+MCP实操指南：极速接入上百AI大模型秒出风格化图片\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "28c6187f-a083-4abe-a2a3-12b5780de08b", "label": "Make中文教程：流程控制（Flow control）", "title": "Make中文教程：流程控制（Flow control）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7397f304-2a96-409d-8dd9-99552e8d834e", "label": "免费推文自动化采集Make工作流", "title": "免费推文自动化采集Make工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "6c271a10-03a8-4b52-a926-34c12c9bd085", "label": "Make中文教程：场景执行历史（Scenario execu...", "title": "Make中文教程：场景执行历史（Scenario execution history）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "c45b5744-c215-47ef-acfe-573c0d1987e3", "label": "Make中文教程：场景设置（Scenario setting...", "title": "Make中文教程：场景设置（Scenario settings）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "47feac50-be3d-4a0e-ba4d-a1a4fb530325", "label": "去除AI味Make自动化工作流：大幅降低 AI 率", "title": "去除AI味Make自动化工作流：大幅降低 AI 率\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "dfc0b5be-6f38-4d27-84ee-7d1e28b3ceca", "label": "Make中文教程：聚合器（Aggregator）", "title": "Make中文教程：聚合器（Aggregator）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "41bc923e-3634-4352-91d5-b8f5590d6d6f", "label": "Make中文教程：修复连接错误（Fixing connect...", "title": "Make中文教程：修复连接错误（Fixing connection errors）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8edac204-a4b6-46d4-ae2c-92129799fba6", "label": "HTTP第三方API调用：MINIMAX（15元免费额度）", "title": "HTTP第三方API调用：MINIMAX（15元免费额度）\\n入链: 1\\n出链: 1", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 1}, {"id": "ce9457a5-9ce9-4c7f-8afb-9e00eb0084dd", "label": "每月25美元免费额度的大模型API来了！", "title": "每月25美元免费额度的大模型API来了！\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "fde4c2ca-e5c5-4442-b449-be91061d6bf9", "label": "Make21. 写推文不再难！128字浓缩千字精华，轻松实现...", "title": "Make21. 写推文不再难！128字浓缩千字精华，轻松实现推特运营自动化！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "3b737e14-af82-4055-aaea-1f78de767282", "label": "Make中文教程：Make 中错误处理的概述（Overvie...", "title": "Make中文教程：Make 中错误处理的概述（Overview of error handling in Make）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6ca9b633-5b29-48c9-af31-a4caa670f83e", "label": "Make中文教程：自定义函数（Custom function...", "title": "Make中文教程：自定义函数（Custom functions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "01475e6f-f47f-46e1-a955-0f5c1268a2e0", "label": "Make中文教程：Inoreader", "title": "Make中文教程：Inoreader\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "a304d69a-a613-446d-a2bc-4cab981fe6f2", "label": "Medium创收全攻略：手把手带你做财经IP，4步搭建高影响...", "title": "Medium创收全攻略：手把手带你做财经IP，4步搭建高影响力个人品牌！（1万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "e449beb2-3b75-478b-b3e1-0d939017d17d", "label": "Medium创收全攻略：如何找到最适合你的创作赛道！（4万字...", "title": "Medium创收全攻略：如何找到最适合你的创作赛道！（4万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "76e48380-16a9-4389-bfbd-757f8a367d24", "label": "Make中文教程： 提交错误处理程序（Commit Erro...", "title": "Make中文教程： 提交错误处理程序（Commit Error Handler）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "31c7d443-34e4-4363-903d-d07f00b4f4ea", "label": "Make2.从 Apify到 Notion：如何利用make...", "title": "Make2.从 Apify到 Notion：如何利用make实现自动化Youtube视频采集\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "1280c602-7ae5-43c7-82ba-f6bd0a9b22f2", "label": "Mistral 免费25美元 Firecrawl 免费 10...", "title": "Mistral 免费25美元 Firecrawl 免费 100 美元领取方法手把手教程\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "15a2a697-c232-488c-8841-d63009dfb889", "label": "2025年自媒体创作与变现全流程教程（1万字）", "title": "2025年自媒体创作与变现全流程教程（1万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "95a668ff-b73c-4b7b-b018-ff6386474c3a", "label": "Make中文教程：数组函数（Array functions）", "title": "Make中文教程：数组函数（Array functions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8ddc6dcd-e8cc-411e-8df1-0b8eff851bfe", "label": "Make9.微信公众号图文混排文章自动化实战：利用Make ...", "title": "Make9.微信公众号图文混排文章自动化实战：利用Make 批量制作\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "12d34904-9c2e-4d51-9247-7d8644c544f8", "label": "Make中文教程：项目数据类型（Item data type...", "title": "Make中文教程：项目数据类型（Item data types）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "9d334e30-590c-4084-879a-41b86aa6823a", "label": "Make工作流必备搜索 API：EXA 免费 80刀额度领取...", "title": "Make工作流必备搜索 API：EXA 免费 80刀额度领取手把手教程\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "a60dc728-6404-4ad3-b117-96a607de6a3a", "label": "Make中文教程：模块类型（Types of modules...", "title": "Make中文教程：模块类型（Types of modules）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "cf22e4de-074d-4d1c-9e71-650d46bbac38", "label": "Make中文教程：路由器（Router）", "title": "Make中文教程：路由器（Router）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "2e535cdf-3142-444c-98ea-792bc69dfa9e", "label": "Make1.从 RSS 到 Notion：如何利用make实...", "title": "Make1.从 RSS 到 Notion：如何利用make实现自动更新知识库\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "e8f3487b-f769-456d-9bc4-ffd4725b5031", "label": "视频 30 工作流：RAG 按时间订阅账号管理系统", "title": "视频 30 工作流：RAG 按时间订阅账号管理系统\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "08d3b581-7ef8-4930-a27e-bec1aaa7a3b0", "label": "HTTP第三方API调用：EXA AI", "title": "HTTP第三方API调用：EXA AI\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "008e7fa9-1cd4-4769-b2c1-4597f846afe6", "label": "Make基础教程快捷工具合集（会员独家）", "title": "Make基础教程快捷工具合集（会员独家）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "9210a3f5-35e0-4417-a7b6-03e13b18d458", "label": "Make中文教程：谷歌文档（Google Docs）", "title": "Make中文教程：谷歌文档（Google Docs）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "bc73f863-d636-4519-9ca4-b7c949d0567e", "label": "HTTP第三方API调用：阿里通义千问（千万tokens免费...", "title": "HTTP第三方API调用：阿里通义千问（千万tokens免费额度）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "4c23ecfc-5344-4df3-988b-9ceab7b71a17", "label": "Make中文教程：使用函数（Using functions）", "title": "Make中文教程：使用函数（Using functions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "aff1991d-5ef0-47b9-94fe-e8dc1d55e2dd", "label": "Make12.小红书头像赛道自动化实操：用Make和Repl...", "title": "Make12.小红书头像赛道自动化实操：用Make和Replicate制作人物卡通头像\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "94801611-12fd-4249-bdfd-a5e13acae829", "label": "Make中文教程：汇聚器（Converger）", "title": "Make中文教程：汇聚器（Converger）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ccb6690f-c14f-4b43-8184-7add50a8967b", "label": "Make中文教程：场景编辑器（Scenario editor...", "title": "Make中文教程：场景编辑器（Scenario editor）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "aca03f5f-b237-4e30-ba83-a54fde0b1afd", "label": "两款免费AI大模型API，轻松集成到Make！", "title": "两款免费AI大模型API，轻松集成到Make！\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "92158494-8d2f-4c2b-afc8-4804b47d40e6", "label": "Make中文教程：指数退避（Exponential Back...", "title": "Make中文教程：指数退避（Exponential Backoff）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "label": "会员必看：Make与 n8n 小报童使用指南", "title": "会员必看：Make与 n8n 小报童使用指南\\n入链: 0\\n出链: 9", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 9}, {"id": "0d864ebb-61e6-456c-8958-7ea3477cabc6", "label": "Make中文教程：工具（Tools）", "title": "Make中文教程：工具（Tools）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "e2235352-7695-4380-8b18-9b88bbc07db4", "label": "Make AI Tools模块使用教程", "title": "Make AI Tools模块使用教程\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0f6092a1-4ca6-4f03-a2ea-72ce08259459", "label": "Make中文教程：PDF.co", "title": "Make中文教程：PDF.co\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "764e665c-ac49-47a0-aabd-81e873b15ea0", "label": "AI 提示词模版1000+（会员独家）", "title": "AI 提示词模版1000+（会员独家）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "ccd27632-3a2c-4838-b165-8a622bae00bd", "label": "Make中文教程：X推特模块授权", "title": "Make中文教程：X推特模块授权\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "e0907337-32e5-4890-bcec-84f2b3fbb1e5", "label": "HTTP第三方API调用：豆包大模型（50万免费Token）", "title": "HTTP第三方API调用：豆包大模型（50万免费Token）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "dd05ed5b-10d0-4fb3-ae5c-e6e014831429", "label": "Make中文教程：日期时间解析符号（Tokens for d...", "title": "Make中文教程：日期时间解析符号（Tokens for date/time parsing）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "d7558027-6ab2-4060-b0e5-0b6c1a580cd1", "label": "Make中文教程：谷歌云盘（Google Drive）", "title": "Make中文教程：谷歌云盘（Google Drive）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "03d8dedf-c0ca-42ca-9f52-93e613e604a8", "label": "抖音自动化采集 Make工作流", "title": "抖音自动化采集 Make工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "6e2e1cb8-2d9a-472a-a659-4667fd0c6bec", "label": "Make中文教程：HTML/CSS 转图像（HTML/CSS...", "title": "Make中文教程：HTML/CSS 转图像（HTML/CSS to Image）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "03a57a85-017d-4433-8b69-85ac73779039", "label": "会员必看：Make工作流模板导入教程（会员独家）", "title": "会员必看：Make工作流模板导入教程（会员独家）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "fa3c50e7-7488-4843-b51b-c248f2de60c3", "label": "Make 学习过程中的常见问题汇总", "title": "Make 学习过程中的常见问题汇总\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "23d9e293-c4d8-4f7a-a04e-9b2115ea444f", "label": "Make中文教程：文件处理（Working with fil...", "title": "Make中文教程：文件处理（Working with files）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "167ee104-0aa3-4e8b-945d-0cf97b001c27", "label": "Make中文教程：Make中的警告类型（Types of w...", "title": "Make中文教程：Make中的警告类型（Types of warnings in Make）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "a6be0aba-a083-4986-a18c-8c57123cea88", "label": "Make中文教程：常用函数（General function...", "title": "Make中文教程：常用函数（General functions）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "4c1f0037-04e6-4aa0-89cf-661a72ec3542", "label": "Make11.写作自动化实操：用Make和OpenRoute...", "title": "Make11.写作自动化实操：用Make和OpenRouter批量制作万字长文\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "2e22c214-ccd9-4046-a2e0-304dc8e63212", "label": "Make中文教程：替换Google Sheets模块（Rep...", "title": "Make中文教程：替换Google Sheets模块（Replacing Google Sheets legacy Modules with New Modules）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "fd269c61-2bbc-4c17-ab46-45d86f99f431", "label": "HTTP第三方API调用：Deepbricks", "title": "HTTP第三方API调用：Deepbricks\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "11bf7095-64f6-4759-b1f0-50dc428b3351", "label": "教你利用 n8n 打造行业 RAG 知识库，实现知识变现新突...", "title": "教你利用 n8n 打造行业 RAG 知识库，实现知识变现新突破！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "18171d34-1b3b-42bc-8818-da7b4fa6e9d3", "label": "Make中文教程：键盘快捷键（Keyboard shortc...", "title": "Make中文教程：键盘快捷键（Keyboard shortcuts）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7b22c05d-79b3-4a94-a24f-156d13dc94c6", "label": "Make工作流图床必备：Cloudflare R2 存储桶手...", "title": "Make工作流图床必备：Cloudflare R2 存储桶手把手使用教程\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "9f1660f1-3dcb-4794-a8bf-f2ce1ee9e3d0", "label": "Make10.科研自动化实操：用Make工具批量自动进行文献...", "title": "Make10.科研自动化实操：用Make工具批量自动进行文献总结\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "fe700235-d409-4147-8595-fc81f53f5364", "label": "Make中文教程：活跃和非活跃场景（Active and i...", "title": "Make中文教程：活跃和非活跃场景（Active and inactive scenarios）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "2fe22e2e-cb71-49d2-8a70-e910d20287d6", "label": "Make中文教程：OpenAI (ChatGPT、Whisp...", "title": "Make中文教程：OpenAI (ChatGPT、Whisper、DALL-E)\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "35a47495-eb66-4203-9d4b-88f10e917413", "label": "Make中文教程：恢复错误处理程序（Resume Error...", "title": "Make中文教程：恢复错误处理程序（Resume Error Handler）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "32bb66d1-01dc-454b-9be6-3d7ed7adc83b", "label": "IP 人设完整报告生成Make自动化工作流", "title": "IP 人设完整报告生成Make自动化工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "61198455-9ad2-4f25-88c1-4e972b238735", "label": "Make中文教程：如何利用Text Parser与正则表达式...", "title": "Make中文教程：如何利用Text Parser与正则表达式进行文本处理\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "92ec0b28-79d9-499e-b29f-04bcf9848889", "label": "Make中文教程：5分钟搞定第一个工作流", "title": "Make中文教程：5分钟搞定第一个工作流\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "cc85a02e-6949-43e1-8899-060887e0d2b9", "label": "Make中文教程：Apify", "title": "Make中文教程：Apify\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8ffd5b01-2fff-4b46-a10b-bfdc382ce9f1", "label": "最简单的 n8n RAG 系统（练手专用）", "title": "最简单的 n8n RAG 系统（练手专用）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "93681d59-288a-4c82-b39f-1a477b6bda0c", "label": "Make16.小红书图文笔记批量制作，Make帮你实现图片创...", "title": "Make16.小红书图文笔记批量制作，Make帮你实现图片创作自动化\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "3bb62417-18ea-4dad-8889-a3d9f23d563e", "label": "Make中文教程：电子邮件（Email）", "title": "Make中文教程：电子邮件（Email）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "81f0e174-7cec-4708-92ec-569cc483de27", "label": "Make中文教程：过滤（Filtering）", "title": "Make中文教程：过滤（Filtering）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "0ef447b6-545c-4a82-9977-8fba18d728cb", "label": "Make可调用免费API 2", "title": "Make可调用免费API 2\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8e7b4c95-b220-44b4-ad3c-24e8af9f5013", "label": "Make中文指南：迭代器（Iterator）", "title": "Make中文指南：迭代器（Iterator）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "a0fa3c3a-7a7e-4d65-b49c-894dbc328947", "label": "Make中文教程：Make工具升级Gmail（Upgradi...", "title": "Make中文教程：Make工具升级Gmail（Upgrading Gmail and Email App Versions Using Make DevTool）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6458ee4a-08c1-4093-8e5b-a163b1c8e491", "label": "视频 31 教程：电脑 Cherry Studio 设置 n...", "title": "视频 31 教程：电脑 Cherry Studio 设置 n8n MCP 服务器手把手教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "4c985f41-3e51-4ce3-91b2-febe2347755c", "label": "Make中文教程：Cloudinary", "title": "Make中文教程：Cloudinary\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "cf65ccbf-0dc1-4f2d-a99a-607dfde558e0", "label": "HTTP第三方API调用：Anthropic Claude官...", "title": "HTTP第三方API调用：Anthropic Claude官方\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "7e523204-a124-4236-b4ba-3064628d7343", "label": "Make+n8n实战教程目录", "title": "Make+n8n实战教程目录\\n入链: 0\\n出链: 137", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 137}, {"id": "ca80bd41-96e7-47f5-b2ff-7ec0f0f4b1f4", "label": "Make6.播客自动化实操：用Make自动制作每日新闻播客", "title": "Make6.播客自动化实操：用Make自动制作每日新闻播客\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ee464dd2-e9a3-4967-b0d5-722f82ad84ad", "label": "Make中文教程：Hugging Face", "title": "Make中文教程：Hugging Face\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "d3666e81-aa64-4ae3-8b0f-d20f012e0851", "label": "Make27.DeepSeek 新V3 到底多猛？小绿书、小...", "title": "Make27.DeepSeek 新V3 到底多猛？小绿书、小红书爆款图文笔记一键批量生成！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "a80faa63-6ae5-4fbc-885e-7408dd47c280", "label": "Make中文教程：图像（Image）", "title": "Make中文教程：图像（Image）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "f734bd26-de1c-432b-bfbe-eb3c7cec2c0b", "label": "Make中文教程：丢弃操作（Throw）", "title": "Make中文教程：丢弃操作（Throw）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "2d77f7a6-ce1d-447e-ad63-0df63b660190", "label": "Make中文教程：微信公众号（WeChat Official...", "title": "Make中文教程：微信公众号（WeChat Official Account）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "528c34b5-2e20-432d-911f-f5fc26ac1ed3", "label": "Make中文教程：文本解析器（Text Parser）", "title": "Make中文教程：文本解析器（Text Parser）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "9978f033-8af0-4886-80d4-7ee3a130983c", "label": "Make中文教程：Instagram", "title": "Make中文教程：Instagram\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "d70ed33f-92c6-4e59-8cff-3bb09a9c20e0", "label": "视频27配套图片风格提示词 Make自动化工作流", "title": "视频27配套图片风格提示词 Make自动化工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "086cfd74-e51a-40e8-9c0b-107ecdce6726", "label": "Make8.零基础教程：如何使用Notion和Make搭建自...", "title": "Make8.零基础教程：如何使用Notion和Make搭建自动化电子图书馆\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "29f3a7bc-095e-47e3-92bd-8e22e8933529", "label": "翔宇工作流 SEO 实战手册：关键词（Keywords）挖掘...", "title": "翔宇工作流 SEO 实战手册：关键词（Keywords）挖掘与应用全攻略\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ec1d40be-7d59-461d-8122-70956896c664", "label": "YouTube变现完整指南：新手从0到1实用教程（3 万字）", "title": "YouTube变现完整指南：新手从0到1实用教程（3 万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "bfd29479-69cb-4caa-be46-5c9ccd7ebacf", "label": "写作相关提示词", "title": "写作相关提示词\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "3eca3296-f550-4773-a55a-2ab40651f816", "label": "HTTP第三方API调用：博查", "title": "HTTP第三方API调用：博查\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "8c7cc322-dff4-4d66-bdd4-1a85b570d146", "label": "Make23.提示词生成新方法！用Make自动化生成，只有今...", "title": "Make23.提示词生成新方法！用Make自动化生成，只有今天能学到的独家技巧！\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "6a24e665-c9b0-43a3-86df-9774e643860e", "label": "小福利，免费领取 Gemini Advanced 会员（慎重...", "title": "小福利，免费领取 Gemini Advanced 会员（慎重）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "face36c3-4310-459f-824a-489541375786", "label": "免费用Make的方法来啦！（会员独家）", "title": "免费用Make的方法来啦！（会员独家）\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "a1c777f7-4098-4849-9934-6793ce3a2241", "label": "微信公众号Make工作流-方案2", "title": "微信公众号Make工作流-方案2\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "98b3a55d-43b3-4e14-a2ee-ca85e4c4384f", "label": "Make中文教程：类型强制转换（Type coercion）", "title": "Make中文教程：类型强制转换（Type coercion）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "cbef1790-46c0-4f9f-bcab-481d0f79cf1a", "label": "Make中文教程：替换旧模块为新模块（Replacing L...", "title": "Make中文教程：替换旧模块为新模块（Replacing Legacy Modules with New Modules）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "b2dc546e-3d1d-47b9-b3c4-0ff48e1c32e7", "label": "Make中文教程：HTTP模块常见错误及排查指南", "title": "Make中文教程：HTTP模块常见错误及排查指南\\n入链: 1\\n出链: 1", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 1}, {"id": "85a322c1-bdde-4807-837c-4c5fe7d04908", "label": "n8n 数据映射超详细入门教程", "title": "n8n 数据映射超详细入门教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "cd6503e5-19b8-497a-94f9-d9007c4c8467", "label": "Make24.AI写作革命！DeepSeek自动生成10万+...", "title": "Make24.AI写作革命！DeepSeek自动生成10万+爆文工作流教程\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "ad7268ab-1d84-442c-b2a4-af9ed1561ce8", "label": "HTTP第三方API调用：Kimi", "title": "HTTP第三方API调用：Kimi\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "06054fad-16b5-4841-9bde-8e9fbf4c795d", "label": "Make中文教程：PDF4me", "title": "Make中文教程：PDF4me\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "e484a4fd-b52c-4be4-ad87-f1be7b9552f4", "label": "Medium创收全攻略：以翔宇为例的IP设计与变现教程（1 ...", "title": "Medium创收全攻略：以翔宇为例的IP设计与变现教程（1 万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "59a2dfb8-d4d7-4c76-bfef-2b81e4cb0187", "label": "Make中文教程：精准控制数据流（Selecting the...", "title": "Make中文教程：精准控制数据流（Selecting the first bundle）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "59e0ee29-fbfb-4547-9c69-8d49b2e8cf95", "label": "Make中文教程：Notion", "title": "Make中文教程：Notion\\n入链: 2\\n出链: 0", "value": 11, "color": "#95A5A6", "inbound_count": 2, "outbound_count": 0}, {"id": "00f2a006-cd75-4b44-877e-97a41e9240fc", "label": "HTTP第三方API调用：Replicate", "title": "HTTP第三方API调用：Replicate\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "f8c3f3a2-1d6d-4f76-98ab-5b9791d0978b", "label": "新手指南：如何设计高点击率的 YouTube视频封面（2万字...", "title": "新手指南：如何设计高点击率的 YouTube视频封面（2万字）\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "5dcf39c4-a0c2-4cc5-9391-77a91ceac939", "label": "Make中文教程：Notion模块常见错误及排查指南", "title": "Make中文教程：Notion模块常见错误及排查指南\\n入链: 1\\n出链: 1", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 1}, {"id": "bf05b784-09b7-47c7-a55f-b5d5b624c812", "label": "Make中文教程合集（会员独家）", "title": "Make中文教程合集（会员独家）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "3b24449f-c32d-4b65-be29-b8c0b452cc18", "label": "Make18.跨境电商必备：Make与RAG打造文章自动化创...", "title": "Make18.跨境电商必备：Make与RAG打造文章自动化创作工作流\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "2586a279-66fe-4f0d-a0db-4934943ba398", "label": "科研相关提示词集合", "title": "科研相关提示词集合\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "4e2a9a24-9d5d-4cb4-bfa7-73a5c236ed93", "label": "自媒体热点追踪工具汇总", "title": "自媒体热点追踪工具汇总\\n入链: 1\\n出链: 1", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 1}, {"id": "11058111-b8d0-4134-b62d-e7db5a721b76", "label": "HTTP第三方API调用：OpenRouter", "title": "HTTP第三方API调用：OpenRouter\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "38bec8b4-a245-4312-86a8-b1c7633e1b27", "label": "翔宇工作流 SEO 实战手册：标题 （Title）撰写全攻略", "title": "翔宇工作流 SEO 实战手册：标题 （Title）撰写全攻略\\n入链: 0\\n出链: 0", "value": 5, "color": "#95A5A6", "inbound_count": 0, "outbound_count": 0}, {"id": "76eba93b-3055-4ef6-bf8a-804f61362835", "label": "Make可调用免费API 3", "title": "Make可调用免费API 3\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "6cf2fc75-4197-4058-b643-ab9c7c7353e5", "label": "HTTP第三方API调用：硅基流动", "title": "HTTP第三方API调用：硅基流动\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}, {"id": "fe077b5c-f718-4658-bf84-e76f2099e864", "label": "Make中文教程：场景模板（Scenario templat...", "title": "Make中文教程：场景模板（Scenario templates）\\n入链: 1\\n出链: 0", "value": 8, "color": "#95A5A6", "inbound_count": 1, "outbound_count": 0}]);
        const edges = new vis.DataSet([{"from": "c706c6a9-4729-4c24-8f7f-96b8ebf2b14f", "to": "4bfefd3f-16f6-42be-9ad5-bc11610c2f5f", "width": 2.7, "title": "类型: markdown_link\\n权重: 0.90"}, {"from": "8edac204-a4b6-46d4-ae2c-92129799fba6", "to": "0bcf3c73-817e-4f19-966d-09f7891eb097", "width": 2.7, "title": "类型: markdown_link\\n权重: 0.90"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "03a57a85-017d-4433-8b69-85ac73779039", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "face36c3-4310-459f-824a-489541375786", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "d7e45c3b-c381-429b-be3e-6738b28849f5", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "3fb1a892-cc7b-4775-943c-9aa1d414897b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "80797a10-7626-4487-8288-eac9fe120276", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "1280c602-7ae5-43c7-82ba-f6bd0a9b22f2", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "9d334e30-590c-4084-879a-41b86aa6823a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "0004527c-2a6c-412b-b623-a7e7ede12736", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7c407ce2-2043-40fe-a72d-2c08d97a4c6d", "to": "ce9457a5-9ce9-4c7f-8afb-9e00eb0084dd", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "92ec0b28-79d9-499e-b29f-04bcf9848889", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fa3c50e7-7488-4843-b51b-c248f2de60c3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "764e665c-ac49-47a0-aabd-81e873b15ea0", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c76a17f9-b768-44e5-a8aa-df207fa7a6b4", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "d7e45c3b-c381-429b-be3e-6738b28849f5", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "3fb1a892-cc7b-4775-943c-9aa1d414897b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "80797a10-7626-4487-8288-eac9fe120276", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "008e7fa9-1cd4-4769-b2c1-4597f846afe6", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "face36c3-4310-459f-824a-489541375786", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "bf05b784-09b7-47c7-a55f-b5d5b624c812", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4e2a9a24-9d5d-4cb4-bfa7-73a5c236ed93", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c45b5744-c215-47ef-acfe-573c0d1987e3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fe077b5c-f718-4658-bf84-e76f2099e864", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "40e20b1e-b69e-4a1a-b739-cbf06d7a3e5c", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "ccb6690f-c14f-4b43-8184-7add50a8967b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "8ea25079-8474-4c03-bdc6-5db7f076d9f0", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c45b5744-c215-47ef-acfe-573c0d1987e3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "e68ad2a5-0a4b-4787-a078-8417fff9dc10", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "e705f627-6857-4cf8-84e2-e3b8d9265caa", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fedcf2cd-6afd-4085-90ef-26de5e558453", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fe700235-d409-4147-8595-fc81f53f5364", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "91b66074-7cd2-4e09-90fd-79eae33a0914", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6c271a10-03a8-4b52-a926-34c12c9bd085", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4e0d02d3-31a6-43c4-8f25-24a16988ceba", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6b16b4b4-d172-49a0-bd51-8795d4fb71b2", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "da9d39c4-0005-4f06-b05e-b1764ac467dd", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "81f0e174-7cec-4708-92ec-569cc483de27", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "18171d34-1b3b-42bc-8818-da7b4fa6e9d3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "026bcba6-6940-4ad9-a0ab-bdcfe7d1656b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "59bc66ba-61e2-43b5-baf5-273721387f04", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "e79d9707-4a10-439a-8df9-2c5dc712f361", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "12d34904-9c2e-4d51-9247-7d8644c544f8", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "98b3a55d-43b3-4e14-a2ee-ca85e4c4384f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "23d9e293-c4d8-4f7a-a04e-9b2115ea444f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "a60dc728-6404-4ad3-b117-96a607de6a3a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "cf22e4de-074d-4d1c-9e71-650d46bbac38", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "dfc0b5be-6f38-4d27-84ee-7d1e28b3ceca", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "59a2dfb8-d4d7-4c76-bfef-2b81e4cb0187", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6d3b37f4-7b16-4536-b216-f56796456c5b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "7937d6b3-c1ff-41f7-8c55-ddb2964f2a39", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2b5991a7-fc95-41f6-b35b-dd45d4bef6f1", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "61198455-9ad2-4f25-88c1-4e972b238735", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "cbef1790-46c0-4f9f-bcab-481d0f79cf1a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2e22c214-ccd9-4046-a2e0-304dc8e63212", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "a0fa3c3a-7a7e-4d65-b49c-894dbc328947", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "e2235352-7695-4380-8b18-9b88bbc07db4", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "a6be0aba-a083-4986-a18c-8c57123cea88", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4c23ecfc-5344-4df3-988b-9ceab7b71a17", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fe78bec9-691b-492c-9a42-df526ba222f9", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "94e5fae1-755a-47e6-a266-6d1968489586", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "95a668ff-b73c-4b7b-b018-ff6386474c3a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6ca9b633-5b29-48c9-af31-a4caa670f83e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "38103885-d879-4e38-97e7-37d915b268c4", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fe78bec9-691b-492c-9a42-df526ba222f9", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "dd05ed5b-10d0-4fb3-ae5c-e6e014831429", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "b4c1b904-0efc-4049-abb2-8665ad97e5a8", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0d864ebb-61e6-456c-8958-7ea3477cabc6", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "874d0617-7c5f-4358-8778-3622422d0f05", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "528c34b5-2e20-432d-911f-f5fc26ac1ed3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "28c6187f-a083-4abe-a2a3-12b5780de08b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c138f529-258d-42fe-a054-97a756a5ab10", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "def7f5c1-2bb9-418f-a21f-024426652789", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "8221e7d2-0991-44f8-8b09-e56cf2be289a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "caa604d3-ec6d-419f-9390-c3b16df594a3", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0d60a7f1-8dac-47ee-8e50-01ce78bfec2b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "3b737e14-af82-4055-aaea-1f78de767282", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0414bbf6-0caf-4522-9dfd-a71a37288348", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "ef4415dc-2e7d-477a-a88e-7e8f3dbff588", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "167ee104-0aa3-4e8b-945d-0cf97b001c27", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "8dfad570-fa10-4d03-968c-91598ff48df9", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "5bef1368-7cd1-4a0a-9133-b76e57c30312", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "35a47495-eb66-4203-9d4b-88f10e917413", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0a8e69dc-39c3-488f-b726-cb8c28d83171", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "76e48380-16a9-4389-bfbd-757f8a367d24", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "7f3fc119-c059-4149-8c2e-0493ef63a72e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "41bc923e-3634-4352-91d5-b8f5590d6d6f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "b3812a9d-8937-4ec4-bc1a-85f9a0b3bb23", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "f734bd26-de1c-432b-bfbe-eb3c7cec2c0b", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "92158494-8d2f-4c2b-afc8-4804b47d40e6", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6644c74c-970c-43bf-a6e1-ad8ebaa532aa", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "724ee1e8-49bd-4e2a-9a80-8340f77b0424", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "b2dc546e-3d1d-47b9-b3c4-0ff48e1c32e7", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "59e0ee29-fbfb-4547-9c69-8d49b2e8cf95", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "5dcf39c4-a0c2-4cc5-9391-77a91ceac939", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "59e0ee29-fbfb-4547-9c69-8d49b2e8cf95", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2d77f7a6-ce1d-447e-ad63-0df63b660190", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "724ee1e8-49bd-4e2a-9a80-8340f77b0424", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "9210a3f5-35e0-4417-a7b6-03e13b18d458", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "06054fad-16b5-4841-9bde-8e9fbf4c795d", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6e2e1cb8-2d9a-472a-a659-4667fd0c6bec", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "01475e6f-f47f-46e1-a955-0f5c1268a2e0", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "a80faa63-6ae5-4fbc-885e-7408dd47c280", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "3bb62417-18ea-4dad-8889-a3d9f23d563e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "d7558027-6ab2-4060-b0e5-0b6c1a580cd1", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "46e40c58-7a50-4201-a728-e446a231b0d2", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "ee464dd2-e9a3-4967-b0d5-722f82ad84ad", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2ac9bd72-cecf-4b03-b9c2-879091cf494f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2cc0815e-d9af-4412-a44f-f377d30c676a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "9978f033-8af0-4886-80d4-7ee3a130983c", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "cc85a02e-6949-43e1-8899-060887e0d2b9", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4c985f41-3e51-4ce3-91b2-febe2347755c", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "11058111-b8d0-4134-b62d-e7db5a721b76", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "08d3b581-7ef8-4930-a27e-bec1aaa7a3b0", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0bcf3c73-817e-4f19-966d-09f7891eb097", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "ad7268ab-1d84-442c-b2a4-af9ed1561ce8", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "3eca3296-f550-4773-a55a-2ab40651f816", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4bfefd3f-16f6-42be-9ad5-bc11610c2f5f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c38bd38a-e4a0-4277-8923-1db9f4d9fdae", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "fd269c61-2bbc-4c17-ab46-45d86f99f431", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "00f2a006-cd75-4b44-877e-97a41e9240fc", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "18ae22f8-edc0-4749-baec-84ca7fd5c190", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "4a896e91-3121-45e1-b625-e7e9f379d01d", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "6cf2fc75-4197-4058-b643-ab9c7c7353e5", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "cf65ccbf-0dc1-4f2d-a99a-607dfde558e0", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "bc73f863-d636-4519-9ca4-b7c949d0567e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "e0907337-32e5-4890-bcec-84f2b3fbb1e5", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "8edac204-a4b6-46d4-ae2c-92129799fba6", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "2586a279-66fe-4f0d-a0db-4934943ba398", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "7019f508-603c-4817-a95b-fc9aaa6d7fe8", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "1cec4f93-eec2-4afe-a861-7c89921d809e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "7b720417-fcb6-42c8-83fc-2536109cdd68", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "bfd29479-69cb-4caa-be46-5c9ccd7ebacf", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "b1a8f97d-685e-4bd6-bb74-f26159d34316", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "bdc9f626-f2e7-4931-a467-00388e81aa2e", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "19544559-034d-431a-8584-4a80c013a2e8", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0ef447b6-545c-4a82-9977-8fba18d728cb", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "76eba93b-3055-4ef6-bf8a-804f61362835", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "aca03f5f-b237-4e30-ba83-a54fde0b1afd", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "ce9457a5-9ce9-4c7f-8afb-9e00eb0084dd", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c16a7e4b-4326-4172-a368-120bf0b51121", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "cfed3d6b-5795-4bec-9fd9-57c4096d988d", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "17cb50a4-b256-46cf-90aa-c874f34b980a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "1280c602-7ae5-43c7-82ba-f6bd0a9b22f2", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "0004527c-2a6c-412b-b623-a7e7ede12736", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "c706c6a9-4729-4c24-8f7f-96b8ebf2b14f", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "9d334e30-590c-4084-879a-41b86aa6823a", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "7e523204-a124-4236-b4ba-3064628d7343", "to": "7b22c05d-79b3-4a94-a24f-156d13dc94c6", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "b2dc546e-3d1d-47b9-b3c4-0ff48e1c32e7", "to": "724ee1e8-49bd-4e2a-9a80-8340f77b0424", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "5dcf39c4-a0c2-4cc5-9391-77a91ceac939", "to": "59e0ee29-fbfb-4547-9c69-8d49b2e8cf95", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}, {"from": "4e2a9a24-9d5d-4cb4-bfa7-73a5c236ed93", "to": "24e38260-9118-414b-a761-a0f7b02e7b48", "width": 3.0, "title": "类型: exact_match\\n权重: 1.00"}]);
        
        // 网络配置
        const options = {
            nodes: {
                shape: 'dot',
                scaling: {
                    min: 10,
                    max: 30
                },
                font: {
                    size: 12,
                    face: 'Microsoft YaHei'
                }
            },
            edges: {
                width: 0.15,
                color: {inherit: 'from'},
                smooth: {
                    type: 'continuous'
                },
                arrows: {
                    to: {enabled: true, scaleFactor: 0.5}
                }
            },
            physics: {
                enabled: true,
                stabilization: {iterations: 100},
                barnesHut: {
                    gravitationalConstant: -8000,
                    centralGravity: 0.3,
                    springLength: 95,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            interaction: {
                hover: true,
                tooltipDelay: 200
            }
        };
        
        // 创建网络
        const container = document.getElementById('network');
        const data = {nodes: nodes, edges: edges};
        const network = new vis.Network(container, data, options);
        
        // 添加事件监听
        network.on("click", function (params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                alert(`文章: ${node.label}\n入链: ${node.inbound_count}\n出链: ${node.outbound_count}`);
            }
        });
        
        // 稳定后的回调
        network.once("stabilizationIterationsDone", function () {
            console.log("网络布局稳定完成");
        });
    </script>
</body>
</html>
