import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用多策略权重计算服务
 * 
 * 支持功能：
 * 1. 任意数量策略的权重优化（>=2个策略）
 * 2. 基于资源消耗的智能调整优先级
 * 3. 自适应缩放因子生成
 * 4. 多维度权重调整策略
 */
public class UniversalWeightCalculationService {
    
    /**
     * 通用权重计算算法 - 支持任意数量策略
     */
    public WeightCalculationResult calculateOptimalWeights(double currentQps, DynamicStrategyConfig config) {
        long startTime = System.currentTimeMillis();
        List<AdjustmentStep> steps = new ArrayList<>();
        
        try {
            System.out.println("\n=== 通用多策略权重计算开始 ===");
            System.out.println("策略数量: " + config.getStrategies().size());
            System.out.println("策略列表: " + config.getStrategies().keySet());
            
            // 1. 计算安全容量
            double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
            System.out.println("安全容量: " + safeMaxQps);
            
            // 2. 验证目标权重可行性
            double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
            double targetQps = currentQps * targetMultiplier;
            
            System.out.println("目标权重: " + config.getTargetWeights());
            System.out.println("目标倍数: " + String.format("%.2f", targetMultiplier));
            System.out.println("目标QPS: " + String.format("%.0f", targetQps));
            
            if (targetQps <= safeMaxQps) {
                System.out.println("✅ 目标QPS在安全范围内，直接使用目标权重");
                return buildSuccessResult(config.getTargetWeights(), targetQps, safeMaxQps, 
                    CalculationStatus.TARGET_ACHIEVED, "目标权重可直接使用", steps, startTime);
            }
            
            System.out.println("❌ 目标QPS超出安全容量，启动多策略优化算法");
            
            // 3. 启动多策略优化算法
            Map<String, Double> optimizedWeights = optimizeMultiStrategyWeights(currentQps, safeMaxQps, config, steps);
            
            if (optimizedWeights != null) {
                double optimizedQps = currentQps * calculateMultiplier(optimizedWeights, config);
                System.out.println("✅ 找到多策略优化权重: " + formatWeights(optimizedWeights));
                return buildSuccessResult(optimizedWeights, optimizedQps, safeMaxQps, 
                    CalculationStatus.CAPACITY_OPTIMIZED, "通过多策略优化满足容量约束", steps, startTime);
            }
            
            // 4. 使用紧急保护权重
            System.out.println("⚠️ 无法找到可行的优化方案，启用紧急保护权重");
            Map<String, Double> emergencyWeights = config.getEmergencyConfig().getWeights();
            double emergencyQps = currentQps * calculateMultiplier(emergencyWeights, config);
            
            return buildSuccessResult(emergencyWeights, emergencyQps, safeMaxQps, 
                CalculationStatus.EMERGENCY_PROTECTION, "启用紧急保护权重", steps, startTime);
                
        } catch (Exception e) {
            System.err.println("权重计算失败: " + e.getMessage());
            return buildFailureResult(e.getMessage(), startTime);
        }
    }
    
    /**
     * 多策略权重优化算法
     */
    private Map<String, Double> optimizeMultiStrategyWeights(double currentQps, double safeMaxQps, 
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {
        
        double maxMultiplier = safeMaxQps / currentQps;
        System.out.println("最大允许倍数: " + String.format("%.2f", maxMultiplier));
        
        // 1. 获取策略优先级（按资源消耗降序）
        List<StrategyPriority> priorities = getStrategyPriorities(config);
        System.out.println("策略优先级: " + priorities.stream()
            .map(p -> p.strategyName + "(batchNum=" + p.batchNum + ")")
            .collect(Collectors.joining(", ")));
        
        // 2. 生成自适应缩放因子
        List<Double> scaleFactors = generateAdaptiveScaleFactors(currentQps, safeMaxQps, config);
        System.out.println("缩放因子: " + scaleFactors);
        
        // 3. 多策略调整策略
        System.out.println("\n--- 开始多策略权重调整 ---");
        
        // 策略1：单策略调整（逐个减少高资源消耗策略）
        Map<String, Double> result = trySingleStrategyAdjustment(
            config.getTargetWeights(), priorities, scaleFactors, maxMultiplier, currentQps, config, steps);
        if (result != null) return result;
        
        // 策略2：多策略组合调整
        result = tryMultiStrategyAdjustment(
            config.getTargetWeights(), priorities, scaleFactors, maxMultiplier, currentQps, config, steps);
        if (result != null) return result;
        
        return null;
    }
    
    /**
     * 单策略调整尝试
     */
    private Map<String, Double> trySingleStrategyAdjustment(
            Map<String, Double> originalWeights, List<StrategyPriority> priorities, 
            List<Double> scaleFactors, double maxMultiplier, double currentQps,
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {
        
        System.out.println("尝试单策略调整...");
        
        for (StrategyPriority priority : priorities) {
            if (priority.currentWeight <= 0.05) continue; // 保留最小权重
            
            for (double scaleFactor : scaleFactors) {
                Map<String, Double> adjustedWeights = adjustSingleStrategy(
                    originalWeights, priority.strategyName, scaleFactor);
                
                double multiplier = calculateMultiplier(adjustedWeights, config);
                double expectedQps = currentQps * multiplier;
                boolean feasible = multiplier <= maxMultiplier;
                
                // 记录调整步骤
                steps.add(new AdjustmentStep(
                    String.format("单策略调整: %s权重缩放至%.1f%%", priority.strategyName, scaleFactor * 100),
                    new HashMap<>(adjustedWeights),
                    multiplier,
                    expectedQps,
                    feasible
                ));
                
                System.out.printf("  %s缩放%.2f: 倍数=%.2f, QPS=%.0f, 可行=%s\n", 
                    priority.strategyName, scaleFactor, multiplier, expectedQps, feasible ? "✅" : "❌");
                
                if (feasible) {
                    System.out.println("✅ 单策略调整成功");
                    return adjustedWeights;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 多策略组合调整尝试
     */
    private Map<String, Double> tryMultiStrategyAdjustment(
            Map<String, Double> originalWeights, List<StrategyPriority> priorities, 
            List<Double> scaleFactors, double maxMultiplier, double currentQps,
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {
        
        System.out.println("尝试多策略组合调整...");
        
        // 选择前两个高资源消耗策略进行组合调整
        if (priorities.size() >= 2) {
            StrategyPriority first = priorities.get(0);
            StrategyPriority second = priorities.get(1);
            
            for (double scale1 : scaleFactors) {
                if (first.currentWeight * scale1 < 0.05) continue;
                
                for (double scale2 : scaleFactors) {
                    if (second.currentWeight * scale2 < 0.05) continue;
                    
                    Map<String, Double> adjustedWeights = adjustMultipleStrategies(
                        originalWeights, 
                        Arrays.asList(first.strategyName, second.strategyName),
                        Arrays.asList(scale1, scale2));
                    
                    double multiplier = calculateMultiplier(adjustedWeights, config);
                    double expectedQps = currentQps * multiplier;
                    boolean feasible = multiplier <= maxMultiplier;
                    
                    // 记录调整步骤
                    steps.add(new AdjustmentStep(
                        String.format("组合调整: %s缩放%.1f%%, %s缩放%.1f%%", 
                            first.strategyName, scale1 * 100, second.strategyName, scale2 * 100),
                        new HashMap<>(adjustedWeights),
                        multiplier,
                        expectedQps,
                        feasible
                    ));
                    
                    if (feasible) {
                        System.out.printf("✅ 组合调整成功: %s=%.2f, %s=%.2f\n", 
                            first.strategyName, scale1, second.strategyName, scale2);
                        return adjustedWeights;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 调整单个策略权重
     */
    private Map<String, Double> adjustSingleStrategy(Map<String, Double> originalWeights, 
            String targetStrategy, double scaleFactor) {
        
        Map<String, Double> adjustedWeights = new HashMap<>(originalWeights);
        
        double originalWeight = originalWeights.get(targetStrategy);
        double newWeight = originalWeight * scaleFactor;
        double weightReduction = originalWeight - newWeight;
        
        // 设置目标策略的新权重
        adjustedWeights.put(targetStrategy, newWeight);
        
        // 将减少的权重平均分配给其他策略
        int otherStrategiesCount = originalWeights.size() - 1;
        if (otherStrategiesCount > 0) {
            double weightIncreasePerStrategy = weightReduction / otherStrategiesCount;
            
            for (Map.Entry<String, Double> entry : originalWeights.entrySet()) {
                if (!entry.getKey().equals(targetStrategy)) {
                    double currentWeight = entry.getValue();
                    adjustedWeights.put(entry.getKey(), currentWeight + weightIncreasePerStrategy);
                }
            }
        }
        
        return adjustedWeights;
    }
    
    /**
     * 调整多个策略权重
     */
    private Map<String, Double> adjustMultipleStrategies(Map<String, Double> originalWeights, 
            List<String> targetStrategies, List<Double> scaleFactors) {
        
        Map<String, Double> adjustedWeights = new HashMap<>(originalWeights);
        double totalWeightReduction = 0;
        
        // 计算总的权重减少量
        for (int i = 0; i < targetStrategies.size(); i++) {
            String strategy = targetStrategies.get(i);
            double scaleFactor = scaleFactors.get(i);
            
            double originalWeight = originalWeights.get(strategy);
            double newWeight = originalWeight * scaleFactor;
            double weightReduction = originalWeight - newWeight;
            
            adjustedWeights.put(strategy, newWeight);
            totalWeightReduction += weightReduction;
        }
        
        // 将减少的权重平均分配给其他策略
        List<String> otherStrategies = originalWeights.keySet().stream()
            .filter(s -> !targetStrategies.contains(s))
            .collect(Collectors.toList());
        
        if (!otherStrategies.isEmpty()) {
            double weightIncreasePerStrategy = totalWeightReduction / otherStrategies.size();
            
            for (String strategy : otherStrategies) {
                double currentWeight = originalWeights.get(strategy);
                adjustedWeights.put(strategy, currentWeight + weightIncreasePerStrategy);
            }
        }
        
        return adjustedWeights;
    }
    
    /**
     * 获取策略优先级（按资源消耗降序）
     */
    private List<StrategyPriority> getStrategyPriorities(DynamicStrategyConfig config) {
        List<StrategyPriority> priorities = new ArrayList<>();
        
        for (Map.Entry<String, StrategyInfo> entry : config.getStrategies().entrySet()) {
            String strategyName = entry.getKey();
            int batchNum = entry.getValue().getBatchNum();
            double currentWeight = config.getTargetWeights().get(strategyName);
            
            priorities.add(new StrategyPriority(strategyName, batchNum, currentWeight));
        }
        
        // 按batchNum降序排列（优先调整高资源消耗策略）
        priorities.sort((a, b) -> Integer.compare(b.batchNum, a.batchNum));
        
        return priorities;
    }
    
    /**
     * 生成自适应缩放因子
     */
    private List<Double> generateAdaptiveScaleFactors(double currentQps, double safeMaxQps, 
            DynamicStrategyConfig config) {
        
        List<Double> factors = new ArrayList<>();
        double adjustmentStep = config.getAdjustmentStep();
        
        // 计算负载压力
        double loadPressure = currentQps / safeMaxQps;
        
        if (loadPressure < 0.7) {
            // 低负载：细粒度调整
            double fineStep = adjustmentStep / 2;
            for (double factor = 1.0 - fineStep; factor >= 0.1; factor -= fineStep) {
                factors.add(Math.round(factor * 100.0) / 100.0);
            }
        } else if (loadPressure < 0.9) {
            // 中负载：标准调整
            for (double factor = 1.0 - adjustmentStep; factor >= 0.1; factor -= adjustmentStep) {
                factors.add(Math.round(factor * 100.0) / 100.0);
            }
        } else {
            // 高负载：粗粒度快速调整
            double coarseStep = adjustmentStep * 2;
            for (double factor = 1.0 - coarseStep; factor >= 0.1; factor -= coarseStep) {
                factors.add(Math.round(factor * 100.0) / 100.0);
            }
        }
        
        // 确保包含关键节点
        if (!factors.contains(0.5)) factors.add(0.5);
        if (!factors.contains(0.2)) factors.add(0.2);
        if (!factors.contains(0.1)) factors.add(0.1);
        
        // 排序并去重
        return factors.stream().distinct().sorted(Collections.reverseOrder()).collect(Collectors.toList());
    }
    
    /**
     * 计算权重倍数
     */
    private double calculateMultiplier(Map<String, Double> weights, DynamicStrategyConfig config) {
        double multiplier = 0.0;
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            multiplier += weight * batchNum;
        }
        return multiplier;
    }
    
    /**
     * 构建成功结果
     */
    private WeightCalculationResult buildSuccessResult(Map<String, Double> finalWeights, 
            double expectedQps, double safeMaxQps, CalculationStatus status, String reason,
            List<AdjustmentStep> steps, long startTime) {
        
        long calculationTime = System.currentTimeMillis() - startTime;
        double resourceUtilization = expectedQps / safeMaxQps;
        
        return new WeightCalculationResult(status, finalWeights, expectedQps, 
            resourceUtilization, reason, calculationTime, steps);
    }
    
    /**
     * 构建失败结果
     */
    private WeightCalculationResult buildFailureResult(String errorMessage, long startTime) {
        long calculationTime = System.currentTimeMillis() - startTime;
        return new WeightCalculationResult(CalculationStatus.CALCULATION_FAILED, 
            new HashMap<>(), 0, 0, errorMessage, calculationTime, new ArrayList<>());
    }
    
    /**
     * 格式化权重显示
     */
    private String formatWeights(Map<String, Double> weights) {
        return weights.entrySet().stream()
            .map(e -> e.getKey() + "=" + String.format("%.2f", e.getValue()))
            .collect(Collectors.joining(", ", "{", "}"));
    }
}
